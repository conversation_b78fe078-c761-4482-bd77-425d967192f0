import{R as _,r as oe,a as Bo}from"./vendor-5f6cd04d.js";function H(n){this.content=n}H.prototype={constructor:H,find:function(n){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===n)return e;return-1},get:function(n){var e=this.find(n);return e==-1?void 0:this.content[e+1]},update:function(n,e,t){var r=t&&t!=n?this.remove(t):this,i=r.find(n),s=r.content.slice();return i==-1?s.push(t||n,e):(s[i+1]=e,t&&(s[i]=t)),new H(s)},remove:function(n){var e=this.find(n);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new H(t)},addToStart:function(n,e){return new H([n,e].concat(this.remove(n).content))},addToEnd:function(n,e){var t=this.remove(n).content.slice();return t.push(n,e),new H(t)},addBefore:function(n,e,t){var r=this.remove(e),i=r.content.slice(),s=r.find(n);return i.splice(s==-1?i.length:s,0,e,t),new H(i)},forEach:function(n){for(var e=0;e<this.content.length;e+=2)n(this.content[e],this.content[e+1])},prepend:function(n){return n=H.from(n),n.size?new H(n.content.concat(this.subtract(n).content)):this},append:function(n){return n=H.from(n),n.size?new H(this.subtract(n).content.concat(n.content)):this},subtract:function(n){var e=this;n=H.from(n);for(var t=0;t<n.content.length;t+=2)e=e.remove(n.content[t]);return e},toObject:function(){var n={};return this.forEach(function(e,t){n[e]=t}),n},get size(){return this.content.length>>1}};H.from=function(n){if(n instanceof H)return n;var e=[];if(n)for(var t in n)e.push(t,n[t]);return new H(e)};function Ui(n,e,t){for(let r=0;;r++){if(r==n.childCount||r==e.childCount)return n.childCount==e.childCount?null:t;let i=n.child(r),s=e.child(r);if(i==s){t+=i.nodeSize;continue}if(!i.sameMarkup(s))return t;if(i.isText&&i.text!=s.text){for(let o=0;i.text[o]==s.text[o];o++)t++;return t}if(i.content.size||s.content.size){let o=Ui(i.content,s.content,t+1);if(o!=null)return o}t+=i.nodeSize}}function Gi(n,e,t,r){for(let i=n.childCount,s=e.childCount;;){if(i==0||s==0)return i==s?null:{a:t,b:r};let o=n.child(--i),l=e.child(--s),a=o.nodeSize;if(o==l){t-=a,r-=a;continue}if(!o.sameMarkup(l))return{a:t,b:r};if(o.isText&&o.text!=l.text){let c=0,u=Math.min(o.text.length,l.text.length);for(;c<u&&o.text[o.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,r--;return{a:t,b:r}}if(o.content.size||l.content.size){let c=Gi(o.content,l.content,t-1,r-1);if(c)return c}t-=a,r-=a}}class b{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let r=0;r<e.length;r++)this.size+=e[r].nodeSize}nodesBetween(e,t,r,i=0,s){for(let o=0,l=0;l<t;o++){let a=this.content[o],c=l+a.nodeSize;if(c>e&&r(a,i+l,s||null,o)!==!1&&a.content.size){let u=l+1;a.nodesBetween(Math.max(0,e-u),Math.min(a.content.size,t-u),r,i+u)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,r,i){let s="",o=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?i?typeof i=="function"?i(l):i:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&r&&(o?o=!1:s+=r),s+=c},0),s}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,r=e.firstChild,i=this.content.slice(),s=0;for(t.isText&&t.sameMarkup(r)&&(i[i.length-1]=t.withText(t.text+r.text),s=1);s<e.content.length;s++)i.push(e.content[s]);return new b(i,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let r=[],i=0;if(t>e)for(let s=0,o=0;o<t;s++){let l=this.content[s],a=o+l.nodeSize;a>e&&((o<e||a>t)&&(l.isText?l=l.cut(Math.max(0,e-o),Math.min(l.text.length,t-o)):l=l.cut(Math.max(0,e-o-1),Math.min(l.content.size,t-o-1))),r.push(l),i+=l.nodeSize),o=a}return new b(r,i)}cutByIndex(e,t){return e==t?b.empty:e==0&&t==this.content.length?this:new b(this.content.slice(e,t))}replaceChild(e,t){let r=this.content[e];if(r==t)return this;let i=this.content.slice(),s=this.size+t.nodeSize-r.nodeSize;return i[e]=t,new b(i,s)}addToStart(e){return new b([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new b(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,r=0;t<this.content.length;t++){let i=this.content[t];e(i,r,t),r+=i.nodeSize}}findDiffStart(e,t=0){return Ui(this,e,t)}findDiffEnd(e,t=this.size,r=e.size){return Gi(this,e,t,r)}findIndex(e,t=-1){if(e==0)return zt(0,e);if(e==this.size)return zt(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let r=0,i=0;;r++){let s=this.child(r),o=i+s.nodeSize;if(o>=e)return o==e||t>0?zt(r+1,o):zt(r,i);i=o}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return b.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new b(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return b.empty;let t,r=0;for(let i=0;i<e.length;i++){let s=e[i];r+=s.nodeSize,i&&s.isText&&e[i-1].sameMarkup(s)?(t||(t=e.slice(0,i)),t[t.length-1]=s.withText(t[t.length-1].text+s.text)):t&&t.push(s)}return new b(t||e,r)}static from(e){if(!e)return b.empty;if(e instanceof b)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new b([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}b.empty=new b([],0);const xn={index:0,offset:0};function zt(n,e){return xn.index=n,xn.offset=e,xn}function Ut(n,e){if(n===e)return!0;if(!(n&&typeof n=="object")||!(e&&typeof e=="object"))return!1;let t=Array.isArray(n);if(Array.isArray(e)!=t)return!1;if(t){if(n.length!=e.length)return!1;for(let r=0;r<n.length;r++)if(!Ut(n[r],e[r]))return!1}else{for(let r in n)if(!(r in e)||!Ut(n[r],e[r]))return!1;for(let r in e)if(!(r in n))return!1}return!0}let D=class jn{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,r=!1;for(let i=0;i<e.length;i++){let s=e[i];if(this.eq(s))return e;if(this.type.excludes(s.type))t||(t=e.slice(0,i));else{if(s.type.excludes(this.type))return e;!r&&s.type.rank>this.type.rank&&(t||(t=e.slice(0,i)),t.push(this),r=!0),t&&t.push(s)}}return t||(t=e.slice()),r||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Ut(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let r=e.marks[t.type];if(!r)throw new RangeError(`There is no mark type ${t.type} in this schema`);let i=r.create(t.attrs);return r.checkAttrs(i.attrs),i}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let r=0;r<e.length;r++)if(!e[r].eq(t[r]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return jn.none;if(e instanceof jn)return[e];let t=e.slice();return t.sort((r,i)=>r.type.rank-i.type.rank),t}};D.none=[];class Gt extends Error{}class k{constructor(e,t,r){this.content=e,this.openStart=t,this.openEnd=r}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let r=Xi(this.content,e+this.openStart,t);return r&&new k(r,this.openStart,this.openEnd)}removeBetween(e,t){return new k(Yi(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return k.empty;let r=t.openStart||0,i=t.openEnd||0;if(typeof r!="number"||typeof i!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new k(b.fromJSON(e,t.content),r,i)}static maxOpen(e,t=!0){let r=0,i=0;for(let s=e.firstChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.firstChild)r++;for(let s=e.lastChild;s&&!s.isLeaf&&(t||!s.type.spec.isolating);s=s.lastChild)i++;return new k(e,r,i)}}k.empty=new k(b.empty,0,0);function Yi(n,e,t){let{index:r,offset:i}=n.findIndex(e),s=n.maybeChild(r),{index:o,offset:l}=n.findIndex(t);if(i==e||s.isText){if(l!=t&&!n.child(o).isText)throw new RangeError("Removing non-flat range");return n.cut(0,e).append(n.cut(t))}if(r!=o)throw new RangeError("Removing non-flat range");return n.replaceChild(r,s.copy(Yi(s.content,e-i-1,t-i-1)))}function Xi(n,e,t,r){let{index:i,offset:s}=n.findIndex(e),o=n.maybeChild(i);if(s==e||o.isText)return r&&!r.canReplace(i,i,t)?null:n.cut(0,e).append(t).append(n.cut(e));let l=Xi(o.content,e-s-1,t);return l&&n.replaceChild(i,o.copy(l))}function zo(n,e,t){if(t.openStart>n.depth)throw new Gt("Inserted content deeper than insertion position");if(n.depth-t.openStart!=e.depth-t.openEnd)throw new Gt("Inconsistent open depths");return Zi(n,e,t,0)}function Zi(n,e,t,r){let i=n.index(r),s=n.node(r);if(i==e.index(r)&&r<n.depth-t.openStart){let o=Zi(n,e,t,r+1);return s.copy(s.content.replaceChild(i,o))}else if(t.content.size)if(!t.openStart&&!t.openEnd&&n.depth==r&&e.depth==r){let o=n.parent,l=o.content;return Je(o,l.cut(0,n.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}else{let{start:o,end:l}=Lo(t,n);return Je(s,es(n,o,l,e,r))}else return Je(s,Yt(n,e,r))}function Qi(n,e){if(!e.type.compatibleContent(n.type))throw new Gt("Cannot join "+e.type.name+" onto "+n.type.name)}function Jn(n,e,t){let r=n.node(t);return Qi(r,e.node(t)),r}function je(n,e){let t=e.length-1;t>=0&&n.isText&&n.sameMarkup(e[t])?e[t]=n.withText(e[t].text+n.text):e.push(n)}function kt(n,e,t,r){let i=(e||n).node(t),s=0,o=e?e.index(t):i.childCount;n&&(s=n.index(t),n.depth>t?s++:n.textOffset&&(je(n.nodeAfter,r),s++));for(let l=s;l<o;l++)je(i.child(l),r);e&&e.depth==t&&e.textOffset&&je(e.nodeBefore,r)}function Je(n,e){return n.type.checkContent(e),n.copy(e)}function es(n,e,t,r,i){let s=n.depth>i&&Jn(n,e,i+1),o=r.depth>i&&Jn(t,r,i+1),l=[];return kt(null,n,i,l),s&&o&&e.index(i)==t.index(i)?(Qi(s,o),je(Je(s,es(n,e,t,r,i+1)),l)):(s&&je(Je(s,Yt(n,e,i+1)),l),kt(e,t,i,l),o&&je(Je(o,Yt(t,r,i+1)),l)),kt(r,null,i,l),new b(l)}function Yt(n,e,t){let r=[];if(kt(null,n,t,r),n.depth>t){let i=Jn(n,e,t+1);je(Je(i,Yt(n,e,t+1)),r)}return kt(e,null,t,r),new b(r)}function Lo(n,e){let t=e.depth-n.openStart,i=e.node(t).copy(n.content);for(let s=t-1;s>=0;s--)i=e.node(s).copy(b.from(i));return{start:i.resolveNoCache(n.openStart+t),end:i.resolveNoCache(i.content.size-n.openEnd-t)}}class Ct{constructor(e,t,r){this.pos=e,this.path=t,this.parentOffset=r,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[this.resolveDepth(e)*3]}index(e){return this.path[this.resolveDepth(e)*3+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e==this.depth&&!this.textOffset?0:1)}start(e){return e=this.resolveDepth(e),e==0?0:this.path[e*3-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]}after(e){if(e=this.resolveDepth(e),!e)throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[e*3-1]+this.path[e*3].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let r=this.pos-this.path[this.path.length-1],i=e.child(t);return r?e.child(t).cut(r):i}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let r=this.path[t*3],i=t==0?0:this.path[t*3-1]+1;for(let s=0;s<e;s++)i+=r.child(s).nodeSize;return i}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return D.none;if(this.textOffset)return e.child(t).marks;let r=e.maybeChild(t-1),i=e.maybeChild(t);if(!r){let l=r;r=i,i=l}let s=r.marks;for(var o=0;o<s.length;o++)s[o].type.spec.inclusive===!1&&(!i||!s[o].isInSet(i.marks))&&(s=s[o--].removeFromSet(s));return s}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let r=t.marks,i=e.parent.maybeChild(e.index());for(var s=0;s<r.length;s++)r[s].type.spec.inclusive===!1&&(!i||!r[s].isInSet(i.marks))&&(r=r[s--].removeFromSet(r));return r}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let r=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);r>=0;r--)if(e.pos<=this.end(r)&&(!t||t(this.node(r))))return new Xt(this,e,r);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let r=[],i=0,s=t;for(let o=e;;){let{index:l,offset:a}=o.content.findIndex(s),c=s-a;if(r.push(o,l,i+a),!c||(o=o.child(l),o.isText))break;s=c-1,i+=a+1}return new Ct(t,r,s)}static resolveCached(e,t){let r=Pr.get(e);if(r)for(let s=0;s<r.elts.length;s++){let o=r.elts[s];if(o.pos==t)return o}else Pr.set(e,r=new Fo);let i=r.elts[r.i]=Ct.resolve(e,t);return r.i=(r.i+1)%Vo,i}}class Fo{constructor(){this.elts=[],this.i=0}}const Vo=12,Pr=new WeakMap;class Xt{constructor(e,t,r){this.$from=e,this.$to=t,this.depth=r}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const $o=Object.create(null);let Ne=class qn{constructor(e,t,r,i=D.none){this.type=e,this.attrs=t,this.marks=i,this.content=r||b.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,r,i=0){this.content.nodesBetween(e,t,r,i,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,r,i){return this.content.textBetween(e,t,r,i)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,r){return this.type==e&&Ut(this.attrs,t||e.defaultAttrs||$o)&&D.sameSet(this.marks,r||D.none)}copy(e=null){return e==this.content?this:new qn(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new qn(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,r=!1){if(e==t)return k.empty;let i=this.resolve(e),s=this.resolve(t),o=r?0:i.sharedDepth(t),l=i.start(o),c=i.node(o).content.cut(i.pos-l,s.pos-l);return new k(c,i.depth-o,s.depth-o)}replace(e,t,r){return zo(this.resolve(e),this.resolve(t),r)}nodeAt(e){for(let t=this;;){let{index:r,offset:i}=t.content.findIndex(e);if(t=t.maybeChild(r),!t)return null;if(i==e||t.isText)return t;e-=i+1}}childAfter(e){let{index:t,offset:r}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:r}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:r}=this.content.findIndex(e);if(r<e)return{node:this.content.child(t),index:t,offset:r};let i=this.content.child(t-1);return{node:i,index:t-1,offset:r-i.nodeSize}}resolve(e){return Ct.resolveCached(this,e)}resolveNoCache(e){return Ct.resolve(this,e)}rangeHasMark(e,t,r){let i=!1;return t>e&&this.nodesBetween(e,t,s=>(r.isInSet(s.marks)&&(i=!0),!i)),i}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),ts(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,r=b.empty,i=0,s=r.childCount){let o=this.contentMatchAt(e).matchFragment(r,i,s),l=o&&o.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=i;a<s;a++)if(!this.type.allowsMarks(r.child(a).marks))return!1;return!0}canReplaceWith(e,t,r,i){if(i&&!this.type.allowsMarks(i))return!1;let s=this.contentMatchAt(e).matchType(r),o=s&&s.matchFragment(this.content,t);return o?o.validEnd:!1}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=D.none;for(let t=0;t<this.marks.length;t++){let r=this.marks[t];r.type.checkAttrs(r.attrs),e=r.addToSet(e)}if(!D.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let r;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");r=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,r)}let i=b.fromJSON(e,t.content),s=e.nodeType(t.type).create(t.attrs,i,r);return s.type.checkAttrs(s.attrs),s}};Ne.prototype.text=void 0;class Zt extends Ne{constructor(e,t,r,i){if(super(e,t,null,i),!r)throw new RangeError("Empty text nodes are not allowed");this.text=r}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):ts(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new Zt(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new Zt(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function ts(n,e){for(let t=n.length-1;t>=0;t--)e=n[t].type.name+"("+e+")";return e}class Ue{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let r=new Ho(e,t);if(r.next==null)return Ue.empty;let i=ns(r);r.next&&r.err("Unexpected trailing text");let s=Uo(_o(i));return Go(s,r),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,r=e.childCount){let i=this;for(let s=t;i&&s<r;s++)i=i.matchType(e.child(s).type);return i}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let r=0;r<e.next.length;r++)if(this.next[t].type==e.next[r].type)return!0;return!1}fillBefore(e,t=!1,r=0){let i=[this];function s(o,l){let a=o.matchFragment(e,r);if(a&&(!t||a.validEnd))return b.from(l.map(c=>c.createAndFill()));for(let c=0;c<o.next.length;c++){let{type:u,next:d}=o.next[c];if(!(u.isText||u.hasRequiredAttrs())&&i.indexOf(d)==-1){i.push(d);let f=s(d,l.concat(u));if(f)return f}}return null}return s(this,[])}findWrapping(e){for(let r=0;r<this.wrapCache.length;r+=2)if(this.wrapCache[r]==e)return this.wrapCache[r+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),r=[{match:this,type:null,via:null}];for(;r.length;){let i=r.shift(),s=i.match;if(s.matchType(e)){let o=[];for(let l=i;l.type;l=l.via)o.push(l.type);return o.reverse()}for(let o=0;o<s.next.length;o++){let{type:l,next:a}=s.next[o];!l.isLeaf&&!l.hasRequiredAttrs()&&!(l.name in t)&&(!i.type||a.validEnd)&&(r.push({match:l.contentMatch,type:l,via:i}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];function t(r){e.push(r);for(let i=0;i<r.next.length;i++)e.indexOf(r.next[i].next)==-1&&t(r.next[i].next)}return t(this),e.map((r,i)=>{let s=i+(r.validEnd?"*":" ")+" ";for(let o=0;o<r.next.length;o++)s+=(o?", ":"")+r.next[o].type.name+"->"+e.indexOf(r.next[o].next);return s}).join(`
`)}}Ue.empty=new Ue(!0);class Ho{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function ns(n){let e=[];do e.push(Wo(n));while(n.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function Wo(n){let e=[];do e.push(jo(n));while(n.next&&n.next!=")"&&n.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function jo(n){let e=Ko(n);for(;;)if(n.eat("+"))e={type:"plus",expr:e};else if(n.eat("*"))e={type:"star",expr:e};else if(n.eat("?"))e={type:"opt",expr:e};else if(n.eat("{"))e=Jo(n,e);else break;return e}function Br(n){/\D/.test(n.next)&&n.err("Expected number, got '"+n.next+"'");let e=Number(n.next);return n.pos++,e}function Jo(n,e){let t=Br(n),r=t;return n.eat(",")&&(n.next!="}"?r=Br(n):r=-1),n.eat("}")||n.err("Unclosed braced range"),{type:"range",min:t,max:r,expr:e}}function qo(n,e){let t=n.nodeTypes,r=t[e];if(r)return[r];let i=[];for(let s in t){let o=t[s];o.isInGroup(e)&&i.push(o)}return i.length==0&&n.err("No node type or group '"+e+"' found"),i}function Ko(n){if(n.eat("(")){let e=ns(n);return n.eat(")")||n.err("Missing closing paren"),e}else if(/\W/.test(n.next))n.err("Unexpected token '"+n.next+"'");else{let e=qo(n,n.next).map(t=>(n.inline==null?n.inline=t.isInline:n.inline!=t.isInline&&n.err("Mixing inline and block content"),{type:"name",value:t}));return n.pos++,e.length==1?e[0]:{type:"choice",exprs:e}}}function _o(n){let e=[[]];return i(s(n,0),t()),e;function t(){return e.push([])-1}function r(o,l,a){let c={term:a,to:l};return e[o].push(c),c}function i(o,l){o.forEach(a=>a.to=l)}function s(o,l){if(o.type=="choice")return o.exprs.reduce((a,c)=>a.concat(s(c,l)),[]);if(o.type=="seq")for(let a=0;;a++){let c=s(o.exprs[a],l);if(a==o.exprs.length-1)return c;i(c,l=t())}else if(o.type=="star"){let a=t();return r(l,a),i(s(o.expr,a),a),[r(a)]}else if(o.type=="plus"){let a=t();return i(s(o.expr,l),a),i(s(o.expr,a),a),[r(a)]}else{if(o.type=="opt")return[r(l)].concat(s(o.expr,l));if(o.type=="range"){let a=l;for(let c=0;c<o.min;c++){let u=t();i(s(o.expr,a),u),a=u}if(o.max==-1)i(s(o.expr,a),a);else for(let c=o.min;c<o.max;c++){let u=t();r(a,u),i(s(o.expr,a),u),a=u}return[r(a)]}else{if(o.type=="name")return[r(l,void 0,o.value)];throw new Error("Unknown expr type")}}}}function rs(n,e){return e-n}function zr(n,e){let t=[];return r(e),t.sort(rs);function r(i){let s=n[i];if(s.length==1&&!s[0].term)return r(s[0].to);t.push(i);for(let o=0;o<s.length;o++){let{term:l,to:a}=s[o];!l&&t.indexOf(a)==-1&&r(a)}}}function Uo(n){let e=Object.create(null);return t(zr(n,0));function t(r){let i=[];r.forEach(o=>{n[o].forEach(({term:l,to:a})=>{if(!l)return;let c;for(let u=0;u<i.length;u++)i[u][0]==l&&(c=i[u][1]);zr(n,a).forEach(u=>{c||i.push([l,c=[]]),c.indexOf(u)==-1&&c.push(u)})})});let s=e[r.join(",")]=new Ue(r.indexOf(n.length-1)>-1);for(let o=0;o<i.length;o++){let l=i[o][1].sort(rs);s.next.push({type:i[o][0],next:e[l.join(",")]||t(l)})}return s}}function Go(n,e){for(let t=0,r=[n];t<r.length;t++){let i=r[t],s=!i.validEnd,o=[];for(let l=0;l<i.next.length;l++){let{type:a,next:c}=i.next[l];o.push(a.name),s&&!(a.isText||a.hasRequiredAttrs())&&(s=!1),r.indexOf(c)==-1&&r.push(c)}s&&e.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}function is(n){let e=Object.create(null);for(let t in n){let r=n[t];if(!r.hasDefault)return null;e[t]=r.default}return e}function ss(n,e){let t=Object.create(null);for(let r in n){let i=e&&e[r];if(i===void 0){let s=n[r];if(s.hasDefault)i=s.default;else throw new RangeError("No value supplied for attribute "+r)}t[r]=i}return t}function ls(n,e,t,r){for(let i in e)if(!(i in n))throw new RangeError(`Unsupported attribute ${i} for ${t} of type ${i}`);for(let i in n){let s=n[i];s.validate&&s.validate(e[i])}}function as(n,e){let t=Object.create(null);if(e)for(let r in e)t[r]=new Xo(n,r,e[r]);return t}let Lr=class cs{constructor(e,t,r){this.name=e,this.schema=t,this.spec=r,this.markSet=null,this.groups=r.group?r.group.split(" "):[],this.attrs=as(e,r.attrs),this.defaultAttrs=is(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(r.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==Ue.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:ss(this.attrs,e)}create(e=null,t,r){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Ne(this,this.computeAttrs(e),b.from(t),D.setFrom(r))}createChecked(e=null,t,r){return t=b.from(t),this.checkContent(t),new Ne(this,this.computeAttrs(e),t,D.setFrom(r))}createAndFill(e=null,t,r){if(e=this.computeAttrs(e),t=b.from(t),t.size){let o=this.contentMatch.fillBefore(t);if(!o)return null;t=o.append(t)}let i=this.contentMatch.matchFragment(t),s=i&&i.fillBefore(b.empty,!0);return s?new Ne(this,e,t.append(s),D.setFrom(r)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let r=0;r<e.childCount;r++)if(!this.allowsMarks(e.child(r).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){ls(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let r=0;r<e.length;r++)this.allowsMarkType(e[r].type)?t&&t.push(e[r]):t||(t=e.slice(0,r));return t?t.length?t:D.none:e}static compile(e,t){let r=Object.create(null);e.forEach((s,o)=>r[s]=new cs(s,t,o));let i=t.spec.topNode||"doc";if(!r[i])throw new RangeError("Schema is missing its top node type ('"+i+"')");if(!r.text)throw new RangeError("Every schema needs a 'text' type");for(let s in r.text.attrs)throw new RangeError("The text node type should not have attributes");return r}};function Yo(n,e,t){let r=t.split("|");return i=>{let s=i===null?"null":typeof i;if(r.indexOf(s)<0)throw new RangeError(`Expected value of type ${r} for attribute ${e} on type ${n}, got ${s}`)}}class Xo{constructor(e,t,r){this.hasDefault=Object.prototype.hasOwnProperty.call(r,"default"),this.default=r.default,this.validate=typeof r.validate=="string"?Yo(e,t,r.validate):r.validate}get isRequired(){return!this.hasDefault}}class an{constructor(e,t,r,i){this.name=e,this.rank=t,this.schema=r,this.spec=i,this.attrs=as(e,i.attrs),this.excluded=null;let s=is(this.attrs);this.instance=s?new D(this,s):null}create(e=null){return!e&&this.instance?this.instance:new D(this,ss(this.attrs,e))}static compile(e,t){let r=Object.create(null),i=0;return e.forEach((s,o)=>r[s]=new an(s,i++,t,o)),r}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){ls(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class us{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let i in e)t[i]=e[i];t.nodes=H.from(e.nodes),t.marks=H.from(e.marks||{}),this.nodes=Lr.compile(this.spec.nodes,this),this.marks=an.compile(this.spec.marks,this);let r=Object.create(null);for(let i in this.nodes){if(i in this.marks)throw new RangeError(i+" can not be both a node and a mark");let s=this.nodes[i],o=s.spec.content||"",l=s.spec.marks;if(s.contentMatch=r[o]||(r[o]=Ue.parse(o,this.nodes)),s.inlineContent=s.contentMatch.inlineContent,s.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!s.isInline||!s.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=s}s.markSet=l=="_"?null:l?Fr(this,l.split(" ")):l==""||!s.inlineContent?[]:null}for(let i in this.marks){let s=this.marks[i],o=s.spec.excludes;s.excluded=o==null?[s]:o==""?[]:Fr(this,o.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,r,i){if(typeof e=="string")e=this.nodeType(e);else if(e instanceof Lr){if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}else throw new RangeError("Invalid node type: "+e);return e.createChecked(t,r,i)}text(e,t){let r=this.nodes.text;return new Zt(r,r.defaultAttrs,e,D.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Ne.fromJSON(this,e)}markFromJSON(e){return D.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function Fr(n,e){let t=[];for(let r=0;r<e.length;r++){let i=e[r],s=n.marks[i],o=s;if(s)t.push(s);else for(let l in n.marks){let a=n.marks[l];(i=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(i)>-1)&&t.push(o=a)}if(!o)throw new SyntaxError("Unknown mark type: '"+e[r]+"'")}return t}function Zo(n){return n.tag!=null}function Qo(n){return n.style!=null}class ve{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let r=this.matchedStyles=[];t.forEach(i=>{if(Zo(i))this.tags.push(i);else if(Qo(i)){let s=/[^=]*/.exec(i.style)[0];r.indexOf(s)<0&&r.push(s),this.styles.push(i)}}),this.normalizeLists=!this.tags.some(i=>{if(!/^(ul|ol)\b/.test(i.tag)||!i.node)return!1;let s=e.nodes[i.node];return s.contentMatch.matchType(s)})}parse(e,t={}){let r=new $r(this,t,!1);return r.addAll(e,D.none,t.from,t.to),r.finish()}parseSlice(e,t={}){let r=new $r(this,t,!0);return r.addAll(e,D.none,t.from,t.to),k.maxOpen(r.finish())}matchTag(e,t,r){for(let i=r?this.tags.indexOf(r)+1:0;i<this.tags.length;i++){let s=this.tags[i];if(nl(e,s.tag)&&(s.namespace===void 0||e.namespaceURI==s.namespace)&&(!s.context||t.matchesContext(s.context))){if(s.getAttrs){let o=s.getAttrs(e);if(o===!1)continue;s.attrs=o||void 0}return s}}}matchStyle(e,t,r,i){for(let s=i?this.styles.indexOf(i)+1:0;s<this.styles.length;s++){let o=this.styles[s],l=o.style;if(!(l.indexOf(e)!=0||o.context&&!r.matchesContext(o.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(o.getAttrs){let a=o.getAttrs(t);if(a===!1)continue;o.attrs=a||void 0}return o}}}static schemaRules(e){let t=[];function r(i){let s=i.priority==null?50:i.priority,o=0;for(;o<t.length;o++){let l=t[o];if((l.priority==null?50:l.priority)<s)break}t.splice(o,0,i)}for(let i in e.marks){let s=e.marks[i].spec.parseDOM;s&&s.forEach(o=>{r(o=Hr(o)),o.mark||o.ignore||o.clearMark||(o.mark=i)})}for(let i in e.nodes){let s=e.nodes[i].spec.parseDOM;s&&s.forEach(o=>{r(o=Hr(o)),o.node||o.ignore||o.mark||(o.node=i)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new ve(e,ve.schemaRules(e)))}}const ds={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},el={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},fs={ol:!0,ul:!0},wt=1,Kn=2,St=4;function Vr(n,e,t){return e!=null?(e?wt:0)|(e==="full"?Kn:0):n&&n.whitespace=="pre"?wt|Kn:t&~St}class Lt{constructor(e,t,r,i,s,o){this.type=e,this.attrs=t,this.marks=r,this.solid=i,this.options=o,this.content=[],this.activeMarks=D.none,this.match=s||(o&St?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(b.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let r=this.type.contentMatch,i;return(i=r.findWrapping(e.type))?(this.match=r,i):null}}return this.match.findWrapping(e.type)}finish(e){if(!(this.options&wt)){let r=this.content[this.content.length-1],i;if(r&&r.isText&&(i=/[ \t\r\n\u000c]+$/.exec(r.text))){let s=r;r.text.length==i[0].length?this.content.pop():this.content[this.content.length-1]=s.withText(s.text.slice(0,s.text.length-i[0].length))}}let t=b.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(b.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!ds.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class $r{constructor(e,t,r){this.parser=e,this.options=t,this.isOpen=r,this.open=0,this.localPreserveWS=!1;let i=t.topNode,s,o=Vr(null,t.preserveWhitespace,0)|(r?St:0);i?s=new Lt(i.type,i.attrs,D.none,!0,t.topMatch||i.type.contentMatch,o):r?s=new Lt(null,null,D.none,!0,null,o):s=new Lt(e.schema.topNodeType,null,D.none,!0,null,o),this.nodes=[s],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let r=e.nodeValue,i=this.top,s=i.options&Kn?"full":this.localPreserveWS||(i.options&wt)>0;if(s==="full"||i.inlineContext(e)||/[^ \t\r\n\u000c]/.test(r)){if(s)s!=="full"?r=r.replace(/\r?\n|\r/g," "):r=r.replace(/\r\n?/g,`
`);else if(r=r.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(r)&&this.open==this.nodes.length-1){let o=i.content[i.content.length-1],l=e.previousSibling;(!o||l&&l.nodeName=="BR"||o.isText&&/[ \t\r\n\u000c]$/.test(o.text))&&(r=r.slice(1))}r&&this.insertNode(this.parser.schema.text(r),t,!/\S/.test(r)),this.findInText(e)}else this.findInside(e)}addElement(e,t,r){let i=this.localPreserveWS,s=this.top;(e.tagName=="PRE"||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),l;fs.hasOwnProperty(o)&&this.parser.normalizeLists&&tl(e);let a=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(l=this.parser.matchTag(e,this,r));e:if(a?a.ignore:el.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!a||a.skip||a.closeParent){a&&a.closeParent?this.open=Math.max(0,this.open-1):a&&a.skip.nodeType&&(e=a.skip);let c,u=this.needsBlock;if(ds.hasOwnProperty(o))s.content.length&&s.content[0].isInline&&this.open&&(this.open--,s=this.top),c=!0,s.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let d=a&&a.skip?t:this.readStyles(e,t);d&&this.addAll(e,d),c&&this.sync(s),this.needsBlock=u}else{let c=this.readStyles(e,t);c&&this.addElementByRule(e,a,c,a.consuming===!1?l:void 0)}this.localPreserveWS=i}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName=="BR"&&(!this.top.type||!this.top.type.inlineContent)&&this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let r=e.style;if(r&&r.length)for(let i=0;i<this.parser.matchedStyles.length;i++){let s=this.parser.matchedStyles[i],o=r.getPropertyValue(s);if(o)for(let l=void 0;;){let a=this.parser.matchStyle(s,o,this,l);if(!a)break;if(a.ignore)return null;if(a.clearMark?t=t.filter(c=>!a.clearMark(c)):t=t.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming===!1)l=a;else break}}return t}addElementByRule(e,t,r,i){let s,o;if(t.node)if(o=this.parser.schema.nodes[t.node],o.isLeaf)this.insertNode(o.create(t.attrs),r,e.nodeName=="BR")||this.leafFallback(e,r);else{let a=this.enter(o,t.attrs||null,r,t.preserveWhitespace);a&&(s=!0,r=a)}else{let a=this.parser.schema.marks[t.mark];r=r.concat(a.create(t.attrs))}let l=this.top;if(o&&o.isLeaf)this.findInside(e);else if(i)this.addElement(e,r,i);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,r,!1));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a,r),this.findAround(e,a,!1)}s&&this.sync(l)&&this.open--}addAll(e,t,r,i){let s=r||0;for(let o=r?e.childNodes[r]:e.firstChild,l=i==null?null:e.childNodes[i];o!=l;o=o.nextSibling,++s)this.findAtPoint(e,s),this.addDOM(o,t);this.findAtPoint(e,s)}findPlace(e,t,r){let i,s;for(let o=this.open,l=0;o>=0;o--){let a=this.nodes[o],c=a.findWrapping(e);if(c&&(!i||i.length>c.length+l)&&(i=c,s=a,!c.length))break;if(a.solid){if(r)break;l+=2}}if(!i)return null;this.sync(s);for(let o=0;o<i.length;o++)t=this.enterInner(i[o],null,t,!1);return t}insertNode(e,t,r){if(e.isInline&&this.needsBlock&&!this.top.type){let s=this.textblockFromContext();s&&(t=this.enterInner(s,null,t))}let i=this.findPlace(e,t,r);if(i){this.closeExtra();let s=this.top;s.match&&(s.match=s.match.matchType(e.type));let o=D.none;for(let l of i.concat(e.marks))(s.type?s.type.allowsMarkType(l.type):Wr(l.type,e.type))&&(o=l.addToSet(o));return s.content.push(e.mark(o)),!0}return!1}enter(e,t,r,i){let s=this.findPlace(e.create(t),r,!1);return s&&(s=this.enterInner(e,t,r,!0,i)),s}enterInner(e,t,r,i=!1,s){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let l=Vr(e,s,o.options);o.options&St&&o.content.length==0&&(l|=St);let a=D.none;return r=r.filter(c=>(o.type?o.type.allowsMarkType(c.type):Wr(c.type,e))?(a=c.addToSet(a),!1):!0),this.nodes.push(new Lt(e,t,a,i,null,l)),this.open++,r}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--){if(this.nodes[t]==e)return this.open=t,!0;this.localPreserveWS&&(this.nodes[t].options|=wt)}return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let r=this.nodes[t].content;for(let i=r.length-1;i>=0;i--)e+=r[i].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let r=0;r<this.find.length;r++)this.find[r].node==e&&this.find[r].offset==t&&(this.find[r].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,r){if(e!=t&&this.find)for(let i=0;i<this.find.length;i++)this.find[i].pos==null&&e.nodeType==1&&e.contains(this.find[i].node)&&t.compareDocumentPosition(this.find[i].node)&(r?2:4)&&(this.find[i].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),r=this.options.context,i=!this.isOpen&&(!r||r.parent.type==this.nodes[0].type),s=-(r?r.depth+1:0)+(i?0:1),o=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=s;a--)if(o(l-1,a))return!0;return!1}else{let u=a>0||a==0&&i?this.nodes[a].type:r&&a>=s?r.node(a-s).type:null;if(!u||u.name!=c&&!u.isInGroup(c))return!1;a--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let r=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(r&&r.isTextblock&&r.defaultAttrs)return r}for(let t in this.parser.schema.nodes){let r=this.parser.schema.nodes[t];if(r.isTextblock&&r.defaultAttrs)return r}}}function tl(n){for(let e=n.firstChild,t=null;e;e=e.nextSibling){let r=e.nodeType==1?e.nodeName.toLowerCase():null;r&&fs.hasOwnProperty(r)&&t?(t.appendChild(e),e=t):r=="li"?t=e:r&&(t=null)}}function nl(n,e){return(n.matches||n.msMatchesSelector||n.webkitMatchesSelector||n.mozMatchesSelector).call(n,e)}function Hr(n){let e={};for(let t in n)e[t]=n[t];return e}function Wr(n,e){let t=e.schema.nodes;for(let r in t){let i=t[r];if(!i.allowsMarkType(n))continue;let s=[],o=l=>{s.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:u}=l.edge(a);if(c==e||s.indexOf(u)<0&&o(u))return!0}};if(o(i.contentMatch))return!0}}class Ze{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},r){r||(r=Mn(t).createDocumentFragment());let i=r,s=[];return e.forEach(o=>{if(s.length||o.marks.length){let l=0,a=0;for(;l<s.length&&a<o.marks.length;){let c=o.marks[a];if(!this.marks[c.type.name]){a++;continue}if(!c.eq(s[l][0])||c.type.spec.spanning===!1)break;l++,a++}for(;l<s.length;)i=s.pop()[1];for(;a<o.marks.length;){let c=o.marks[a++],u=this.serializeMark(c,o.isInline,t);u&&(s.push([c,i]),i.appendChild(u.dom),i=u.contentDOM||u.dom)}}i.appendChild(this.serializeNodeInner(o,t))}),r}serializeNodeInner(e,t){let{dom:r,contentDOM:i}=Jt(Mn(t),this.nodes[e.type.name](e),null,e.attrs);if(i){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,i)}return r}serializeNode(e,t={}){let r=this.serializeNodeInner(e,t);for(let i=e.marks.length-1;i>=0;i--){let s=this.serializeMark(e.marks[i],e.isInline,t);s&&((s.contentDOM||s.dom).appendChild(r),r=s.dom)}return r}serializeMark(e,t,r={}){let i=this.marks[e.type.name];return i&&Jt(Mn(r),i(e,t),null,e.attrs)}static renderSpec(e,t,r=null,i){return Jt(e,t,r,i)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new Ze(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=jr(e.nodes);return t.text||(t.text=r=>r.text),t}static marksFromSchema(e){return jr(e.marks)}}function jr(n){let e={};for(let t in n){let r=n[t].spec.toDOM;r&&(e[t]=r)}return e}function Mn(n){return n.document||window.document}const Jr=new WeakMap;function rl(n){let e=Jr.get(n);return e===void 0&&Jr.set(n,e=il(n)),e}function il(n){let e=null;function t(r){if(r&&typeof r=="object")if(Array.isArray(r))if(typeof r[0]=="string")e||(e=[]),e.push(r);else for(let i=0;i<r.length;i++)t(r[i]);else for(let i in r)t(r[i])}return t(n),e}function Jt(n,e,t,r){if(typeof e=="string")return{dom:n.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let i=e[0],s;if(typeof i!="string")throw new RangeError("Invalid array passed to renderSpec");if(r&&(s=rl(r))&&s.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let o=i.indexOf(" ");o>0&&(t=i.slice(0,o),i=i.slice(o+1));let l,a=t?n.createElementNS(t,i):n.createElement(i),c=e[1],u=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){u=2;for(let d in c)if(c[d]!=null){let f=d.indexOf(" ");f>0?a.setAttributeNS(d.slice(0,f),d.slice(f+1),c[d]):a.setAttribute(d,c[d])}}for(let d=u;d<e.length;d++){let f=e[d];if(f===0){if(d<e.length-1||d>u)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}else{let{dom:h,contentDOM:p}=Jt(n,f,t,r);if(a.appendChild(h),p){if(l)throw new RangeError("Multiple content holes");l=p}}}return{dom:a,contentDOM:l}}const hs=65535,ps=Math.pow(2,16);function sl(n,e){return n+e*ps}function qr(n){return n&hs}function ol(n){return(n-(n&hs))/ps}const ms=1,gs=2,qt=4,ys=8;class _n{constructor(e,t,r){this.pos=e,this.delInfo=t,this.recover=r}get deleted(){return(this.delInfo&ys)>0}get deletedBefore(){return(this.delInfo&(ms|qt))>0}get deletedAfter(){return(this.delInfo&(gs|qt))>0}get deletedAcross(){return(this.delInfo&qt)>0}}class re{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&re.empty)return re.empty}recover(e){let t=0,r=qr(e);if(!this.inverted)for(let i=0;i<r;i++)t+=this.ranges[i*3+2]-this.ranges[i*3+1];return this.ranges[r*3]+t+ol(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,r){let i=0,s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?i:0);if(a>e)break;let c=this.ranges[l+s],u=this.ranges[l+o],d=a+c;if(e<=d){let f=c?e==a?-1:e==d?1:t:t,h=a+i+(f<0?0:u);if(r)return h;let p=e==(t<0?a:d)?null:sl(l/3,e-a),m=e==a?gs:e==d?ms:qt;return(t<0?e!=a:e!=d)&&(m|=ys),new _n(h,m,p)}i+=u-c}return r?e+i:new _n(e+i,0,null)}touches(e,t){let r=0,i=qr(t),s=this.inverted?2:1,o=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>e)break;let c=this.ranges[l+s],u=a+c;if(e<=u&&l==i*3)return!0;r+=this.ranges[l+o]-c}return!1}forEach(e){let t=this.inverted?2:1,r=this.inverted?1:2;for(let i=0,s=0;i<this.ranges.length;i+=3){let o=this.ranges[i],l=o-(this.inverted?s:0),a=o+(this.inverted?0:s),c=this.ranges[i+t],u=this.ranges[i+r];e(l,l+c,a,a+u),s+=u-c}}invert(){return new re(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?re.empty:new re(e<0?[0,-e,0]:[0,0,e])}}re.empty=new re([]);class Tt{constructor(e,t,r=0,i=e?e.length:0){this.mirror=t,this.from=r,this.to=i,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new Tt(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),t!=null&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,r=this._maps.length;t<e._maps.length;t++){let i=e.getMirror(t);this.appendMap(e._maps[t],i!=null&&i<t?r+i:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,r=this._maps.length+e._maps.length;t>=0;t--){let i=e.getMirror(t);this.appendMap(e._maps[t].invert(),i!=null&&i>t?r-i-1:void 0)}}invert(){let e=new Tt;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let r=this.from;r<this.to;r++)e=this._maps[r].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,r){let i=0;for(let s=this.from;s<this.to;s++){let o=this._maps[s],l=o.mapResult(e,t);if(l.recover!=null){let a=this.getMirror(s);if(a!=null&&a>s&&a<this.to){s=a,e=this._maps[a].recover(l.recover);continue}}i|=l.delInfo,e=l.pos}return r?e:new _n(e,i,null)}}const Cn=Object.create(null);class K{getMap(){return re.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let r=Cn[t.stepType];if(!r)throw new RangeError(`No step type ${t.stepType} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in Cn)throw new RangeError("Duplicate use of step JSON ID "+e);return Cn[e]=t,t.prototype.jsonID=e,t}}class B{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new B(e,null)}static fail(e){return new B(null,e)}static fromReplace(e,t,r,i){try{return B.ok(e.replace(t,r,i))}catch(s){if(s instanceof Gt)return B.fail(s.message);throw s}}}function ur(n,e,t){let r=[];for(let i=0;i<n.childCount;i++){let s=n.child(i);s.content.size&&(s=s.copy(ur(s.content,e,s))),s.isInline&&(s=e(s,t,i)),r.push(s)}return b.fromArray(r)}class Te extends K{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=e.resolve(this.from),i=r.node(r.sharedDepth(this.to)),s=new k(ur(t.content,(o,l)=>!o.isAtom||!l.type.allowsMarkType(this.mark.type)?o:o.mark(this.mark.addToSet(o.marks)),i),t.openStart,t.openEnd);return B.fromReplace(e,this.from,this.to,s)}invert(){return new he(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new Te(t.pos,r.pos,this.mark)}merge(e){return e instanceof Te&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Te(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new Te(t.from,t.to,e.markFromJSON(t.mark))}}K.jsonID("addMark",Te);class he extends K{constructor(e,t,r){super(),this.from=e,this.to=t,this.mark=r}apply(e){let t=e.slice(this.from,this.to),r=new k(ur(t.content,i=>i.mark(this.mark.removeFromSet(i.marks)),e),t.openStart,t.openEnd);return B.fromReplace(e,this.from,this.to,r)}invert(){return new Te(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deleted&&r.deleted||t.pos>=r.pos?null:new he(t.pos,r.pos,this.mark)}merge(e){return e instanceof he&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new he(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new he(t.from,t.to,e.markFromJSON(t.mark))}}K.jsonID("removeMark",he);class Oe extends K{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return B.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return B.fromReplace(e,this.pos,this.pos+1,new k(b.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let r=this.mark.addToSet(t.marks);if(r.length==t.marks.length){for(let i=0;i<t.marks.length;i++)if(!t.marks[i].isInSet(r))return new Oe(this.pos,t.marks[i]);return new Oe(this.pos,this.mark)}}return new Ge(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Oe(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new Oe(t.pos,e.markFromJSON(t.mark))}}K.jsonID("addNodeMark",Oe);class Ge extends K{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return B.fail("No node at mark step's position");let r=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return B.fromReplace(e,this.pos,this.pos+1,new k(b.from(r),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return!t||!this.mark.isInSet(t.marks)?this:new Oe(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Ge(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new Ge(t.pos,e.markFromJSON(t.mark))}}K.jsonID("removeNodeMark",Ge);class L extends K{constructor(e,t,r,i=!1){super(),this.from=e,this.to=t,this.slice=r,this.structure=i}apply(e){return this.structure&&Un(e,this.from,this.to)?B.fail("Structure replace would overwrite content"):B.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new re([this.from,this.to-this.from,this.slice.size])}invert(e){return new L(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1);return t.deletedAcross&&r.deletedAcross?null:new L(t.pos,Math.max(t.pos,r.pos),this.slice,this.structure)}merge(e){if(!(e instanceof L)||e.structure||this.structure)return null;if(this.from+this.slice.size==e.from&&!this.slice.openEnd&&!e.slice.openStart){let t=this.slice.size+e.slice.size==0?k.empty:new k(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new L(this.from,this.to+(e.to-e.from),t,this.structure)}else if(e.to==this.from&&!this.slice.openStart&&!e.slice.openEnd){let t=this.slice.size+e.slice.size==0?k.empty:new k(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new L(e.from,this.to,t,this.structure)}else return null}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new L(t.from,t.to,k.fromJSON(e,t.slice),!!t.structure)}}K.jsonID("replace",L);class F extends K{constructor(e,t,r,i,s,o,l=!1){super(),this.from=e,this.to=t,this.gapFrom=r,this.gapTo=i,this.slice=s,this.insert=o,this.structure=l}apply(e){if(this.structure&&(Un(e,this.from,this.gapFrom)||Un(e,this.gapTo,this.to)))return B.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return B.fail("Gap is not a flat range");let r=this.slice.insertAt(this.insert,t.content);return r?B.fromReplace(e,this.from,this.to,r):B.fail("Content does not fit in gap")}getMap(){return new re([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new F(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),r=e.mapResult(this.to,-1),i=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),s=this.to==this.gapTo?r.pos:e.map(this.gapTo,1);return t.deletedAcross&&r.deletedAcross||i<t.pos||s>r.pos?null:new F(t.pos,r.pos,i,s,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new F(t.from,t.to,t.gapFrom,t.gapTo,k.fromJSON(e,t.slice),t.insert,!!t.structure)}}K.jsonID("replaceAround",F);function Un(n,e,t){let r=n.resolve(e),i=t-e,s=r.depth;for(;i>0&&s>0&&r.indexAfter(s)==r.node(s).childCount;)s--,i--;if(i>0){let o=r.node(s).maybeChild(r.indexAfter(s));for(;i>0;){if(!o||o.isLeaf)return!0;o=o.firstChild,i--}}return!1}function ll(n,e,t,r){let i=[],s=[],o,l;n.doc.nodesBetween(e,t,(a,c,u)=>{if(!a.isInline)return;let d=a.marks;if(!r.isInSet(d)&&u.type.allowsMarkType(r.type)){let f=Math.max(c,e),h=Math.min(c+a.nodeSize,t),p=r.addToSet(d);for(let m=0;m<d.length;m++)d[m].isInSet(p)||(o&&o.to==f&&o.mark.eq(d[m])?o.to=h:i.push(o=new he(f,h,d[m])));l&&l.to==f?l.to=h:s.push(l=new Te(f,h,r))}}),i.forEach(a=>n.step(a)),s.forEach(a=>n.step(a))}function al(n,e,t,r){let i=[],s=0;n.doc.nodesBetween(e,t,(o,l)=>{if(!o.isInline)return;s++;let a=null;if(r instanceof an){let c=o.marks,u;for(;u=r.isInSet(c);)(a||(a=[])).push(u),c=u.removeFromSet(c)}else r?r.isInSet(o.marks)&&(a=[r]):a=o.marks;if(a&&a.length){let c=Math.min(l+o.nodeSize,t);for(let u=0;u<a.length;u++){let d=a[u],f;for(let h=0;h<i.length;h++){let p=i[h];p.step==s-1&&d.eq(i[h].style)&&(f=p)}f?(f.to=c,f.step=s):i.push({style:d,from:Math.max(l,e),to:c,step:s})}}}),i.forEach(o=>n.step(new he(o.from,o.to,o.style)))}function dr(n,e,t,r=t.contentMatch,i=!0){let s=n.doc.nodeAt(e),o=[],l=e+1;for(let a=0;a<s.childCount;a++){let c=s.child(a),u=l+c.nodeSize,d=r.matchType(c.type);if(!d)o.push(new L(l,u,k.empty));else{r=d;for(let f=0;f<c.marks.length;f++)t.allowsMarkType(c.marks[f].type)||n.step(new he(l,u,c.marks[f]));if(i&&c.isText&&t.whitespace!="pre"){let f,h=/\r?\n|\r/g,p;for(;f=h.exec(c.text);)p||(p=new k(b.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),o.push(new L(l+f.index,l+f.index+f[0].length,p))}}l=u}if(!r.validEnd){let a=r.fillBefore(b.empty,!0);n.replace(l,l,new k(a,0,0))}for(let a=o.length-1;a>=0;a--)n.step(o[a])}function cl(n,e,t){return(e==0||n.canReplace(e,n.childCount))&&(t==n.childCount||n.canReplace(0,t))}function pt(n){let t=n.parent.content.cutByIndex(n.startIndex,n.endIndex);for(let r=n.depth;;--r){let i=n.$from.node(r),s=n.$from.index(r),o=n.$to.indexAfter(r);if(r<n.depth&&i.canReplace(s,o,t))return r;if(r==0||i.type.spec.isolating||!cl(i,s,o))break}return null}function ul(n,e,t){let{$from:r,$to:i,depth:s}=e,o=r.before(s+1),l=i.after(s+1),a=o,c=l,u=b.empty,d=0;for(let p=s,m=!1;p>t;p--)m||r.index(p)>0?(m=!0,u=b.from(r.node(p).copy(u)),d++):a--;let f=b.empty,h=0;for(let p=s,m=!1;p>t;p--)m||i.after(p+1)<i.end(p)?(m=!0,f=b.from(i.node(p).copy(f)),h++):c++;n.step(new F(a,c,o,l,new k(u.append(f),d,h),u.size-d,!0))}function fr(n,e,t=null,r=n){let i=dl(n,e),s=i&&fl(r,e);return s?i.map(Kr).concat({type:e,attrs:t}).concat(s.map(Kr)):null}function Kr(n){return{type:n,attrs:null}}function dl(n,e){let{parent:t,startIndex:r,endIndex:i}=n,s=t.contentMatchAt(r).findWrapping(e);if(!s)return null;let o=s.length?s[0]:e;return t.canReplaceWith(r,i,o)?s:null}function fl(n,e){let{parent:t,startIndex:r,endIndex:i}=n,s=t.child(r),o=e.contentMatch.findWrapping(s.type);if(!o)return null;let a=(o.length?o[o.length-1]:e).contentMatch;for(let c=r;a&&c<i;c++)a=a.matchType(t.child(c).type);return!a||!a.validEnd?null:o}function hl(n,e,t){let r=b.empty;for(let o=t.length-1;o>=0;o--){if(r.size){let l=t[o].type.contentMatch.matchFragment(r);if(!l||!l.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=b.from(t[o].type.create(t[o].attrs,r))}let i=e.start,s=e.end;n.step(new F(i,s,i,s,new k(r,0,0),t.length,!0))}function pl(n,e,t,r,i){if(!r.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let s=n.steps.length;n.doc.nodesBetween(e,t,(o,l)=>{let a=typeof i=="function"?i(o):i;if(o.isTextblock&&!o.hasMarkup(r,a)&&ml(n.doc,n.mapping.slice(s).map(l),r)){let c=null;if(r.schema.linebreakReplacement){let h=r.whitespace=="pre",p=!!r.contentMatch.matchType(r.schema.linebreakReplacement);h&&!p?c=!1:!h&&p&&(c=!0)}c===!1&&ks(n,o,l,s),dr(n,n.mapping.slice(s).map(l,1),r,void 0,c===null);let u=n.mapping.slice(s),d=u.map(l,1),f=u.map(l+o.nodeSize,1);return n.step(new F(d,f,d+1,f-1,new k(b.from(r.create(a,null,o.marks)),0,0),1,!0)),c===!0&&bs(n,o,l,s),!1}})}function bs(n,e,t,r){e.forEach((i,s)=>{if(i.isText){let o,l=/\r?\n|\r/g;for(;o=l.exec(i.text);){let a=n.mapping.slice(r).map(t+1+s+o.index);n.replaceWith(a,a+1,e.type.schema.linebreakReplacement.create())}}})}function ks(n,e,t,r){e.forEach((i,s)=>{if(i.type==i.type.schema.linebreakReplacement){let o=n.mapping.slice(r).map(t+1+s);n.replaceWith(o,o+1,e.type.schema.text(`
`))}})}function ml(n,e,t){let r=n.resolve(e),i=r.index();return r.parent.canReplaceWith(i,i+1,t)}function gl(n,e,t,r,i){let s=n.doc.nodeAt(e);if(!s)throw new RangeError("No node at given position");t||(t=s.type);let o=t.create(r,null,i||s.marks);if(s.isLeaf)return n.replaceWith(e,e+s.nodeSize,o);if(!t.validContent(s.content))throw new RangeError("Invalid content for node type "+t.name);n.step(new F(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new k(b.from(o),0,0),1,!0))}function st(n,e,t=1,r){let i=n.resolve(e),s=i.depth-t,o=r&&r[r.length-1]||i.parent;if(s<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!o.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let c=i.depth-1,u=t-2;c>s;c--,u--){let d=i.node(c),f=i.index(c);if(d.type.spec.isolating)return!1;let h=d.content.cutByIndex(f,d.childCount),p=r&&r[u+1];p&&(h=h.replaceChild(0,p.type.create(p.attrs)));let m=r&&r[u]||d;if(!d.canReplace(f+1,d.childCount)||!m.type.validContent(h))return!1}let l=i.indexAfter(s),a=r&&r[0];return i.node(s).canReplaceWith(l,l,a?a.type:i.node(s+1).type)}function yl(n,e,t=1,r){let i=n.doc.resolve(e),s=b.empty,o=b.empty;for(let l=i.depth,a=i.depth-t,c=t-1;l>a;l--,c--){s=b.from(i.node(l).copy(s));let u=r&&r[c];o=b.from(u?u.type.create(u.attrs,o):i.node(l).copy(o))}n.step(new L(e,e,new k(s.append(o),t,t),!0))}function Be(n,e){let t=n.resolve(e),r=t.index();return Ss(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(r,r+1)}function bl(n,e){e.content.size||n.type.compatibleContent(e.type);let t=n.contentMatchAt(n.childCount),{linebreakReplacement:r}=n.type.schema;for(let i=0;i<e.childCount;i++){let s=e.child(i),o=s.type==r?n.type.schema.nodes.text:s.type;if(t=t.matchType(o),!t||!n.type.allowsMarks(s.marks))return!1}return t.validEnd}function Ss(n,e){return!!(n&&e&&!n.isLeaf&&bl(n,e))}function cn(n,e,t=-1){let r=n.resolve(e);for(let i=r.depth;;i--){let s,o,l=r.index(i);if(i==r.depth?(s=r.nodeBefore,o=r.nodeAfter):t>0?(s=r.node(i+1),l++,o=r.node(i).maybeChild(l)):(s=r.node(i).maybeChild(l-1),o=r.node(i+1)),s&&!s.isTextblock&&Ss(s,o)&&r.node(i).canReplace(l,l+1))return e;if(i==0)break;e=t<0?r.before(i):r.after(i)}}function kl(n,e,t){let r=null,{linebreakReplacement:i}=n.doc.type.schema,s=n.doc.resolve(e-t),o=s.node().type;if(i&&o.inlineContent){let u=o.whitespace=="pre",d=!!o.contentMatch.matchType(i);u&&!d?r=!1:!u&&d&&(r=!0)}let l=n.steps.length;if(r===!1){let u=n.doc.resolve(e+t);ks(n,u.node(),u.before(),l)}o.inlineContent&&dr(n,e+t-1,o,s.node().contentMatchAt(s.index()),r==null);let a=n.mapping.slice(l),c=a.map(e-t);if(n.step(new L(c,a.map(e+t,-1),k.empty,!0)),r===!0){let u=n.doc.resolve(c);bs(n,u.node(),u.before(),n.steps.length)}return n}function Sl(n,e,t){let r=n.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),t))return e;if(r.parentOffset==0)for(let i=r.depth-1;i>=0;i--){let s=r.index(i);if(r.node(i).canReplaceWith(s,s,t))return r.before(i+1);if(s>0)return null}if(r.parentOffset==r.parent.content.size)for(let i=r.depth-1;i>=0;i--){let s=r.indexAfter(i);if(r.node(i).canReplaceWith(s,s,t))return r.after(i+1);if(s<r.node(i).childCount)return null}return null}function xs(n,e,t){let r=n.resolve(e);if(!t.content.size)return e;let i=t.content;for(let s=0;s<t.openStart;s++)i=i.firstChild.content;for(let s=1;s<=(t.openStart==0&&t.size?2:1);s++)for(let o=r.depth;o>=0;o--){let l=o==r.depth?0:r.pos<=(r.start(o+1)+r.end(o+1))/2?-1:1,a=r.index(o)+(l>0?1:0),c=r.node(o),u=!1;if(s==1)u=c.canReplace(a,a,i);else{let d=c.contentMatchAt(a).findWrapping(i.firstChild.type);u=d&&c.canReplaceWith(a,a,d[0])}if(u)return l==0?r.pos:l<0?r.before(o+1):r.after(o+1)}return null}function un(n,e,t=e,r=k.empty){if(e==t&&!r.size)return null;let i=n.resolve(e),s=n.resolve(t);return Ms(i,s,r)?new L(e,t,r):new xl(i,s,r).fit()}function Ms(n,e,t){return!t.openStart&&!t.openEnd&&n.start()==e.start()&&n.parent.canReplace(n.index(),e.index(),t.content)}class xl{constructor(e,t,r){this.$from=e,this.$to=t,this.unplaced=r,this.frontier=[],this.placed=b.empty;for(let i=0;i<=e.depth;i++){let s=e.node(i);this.frontier.push({type:s.type,match:s.contentMatchAt(e.indexAfter(i))})}for(let i=e.depth;i>0;i--)this.placed=b.from(e.node(i).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,r=this.$from,i=this.close(e<0?this.$to:r.doc.resolve(e));if(!i)return null;let s=this.placed,o=r.depth,l=i.depth;for(;o&&l&&s.childCount==1;)s=s.firstChild.content,o--,l--;let a=new k(s,o,l);return e>-1?new F(r.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||r.pos!=this.$to.pos?new L(r.pos,i.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,r=0,i=this.unplaced.openEnd;r<e;r++){let s=t.firstChild;if(t.childCount>1&&(i=0),s.type.spec.isolating&&i<=r){e=r;break}t=s.content}for(let t=1;t<=2;t++)for(let r=t==1?e:this.unplaced.openStart;r>=0;r--){let i,s=null;r?(s=wn(this.unplaced.content,r-1).firstChild,i=s.content):i=this.unplaced.content;let o=i.firstChild;for(let l=this.depth;l>=0;l--){let{type:a,match:c}=this.frontier[l],u,d=null;if(t==1&&(o?c.matchType(o.type)||(d=c.fillBefore(b.from(o),!1)):s&&a.compatibleContent(s.type)))return{sliceDepth:r,frontierDepth:l,parent:s,inject:d};if(t==2&&o&&(u=c.findWrapping(o.type)))return{sliceDepth:r,frontierDepth:l,parent:s,wrap:u};if(s&&c.matchType(s.type))break}}}openMore(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=wn(e,t);return!i.childCount||i.firstChild.isLeaf?!1:(this.unplaced=new k(e,t+1,Math.max(r,i.size+t>=e.size-r?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:r}=this.unplaced,i=wn(e,t);if(i.childCount<=1&&t>0){let s=e.size-t<=t+i.size;this.unplaced=new k(gt(e,t-1,1),t-1,s?t-1:r)}else this.unplaced=new k(gt(e,t,1),t,r)}placeNodes({sliceDepth:e,frontierDepth:t,parent:r,inject:i,wrap:s}){for(;this.depth>t;)this.closeFrontierNode();if(s)for(let m=0;m<s.length;m++)this.openFrontierNode(s[m]);let o=this.unplaced,l=r?r.content:o.content,a=o.openStart-e,c=0,u=[],{match:d,type:f}=this.frontier[t];if(i){for(let m=0;m<i.childCount;m++)u.push(i.child(m));d=d.matchFragment(i)}let h=l.size+e-(o.content.size-o.openEnd);for(;c<l.childCount;){let m=l.child(c),g=d.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(d=g,u.push(Cs(m.mark(f.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?h:-1)))}let p=c==l.childCount;p||(h=-1),this.placed=yt(this.placed,t,b.from(u)),this.frontier[t].match=d,p&&h<0&&r&&r.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<h;m++){let y=g.lastChild;this.frontier.push({type:y.type,match:y.contentMatchAt(y.childCount)}),g=y.content}this.unplaced=p?e==0?k.empty:new k(gt(o.content,e-1,1),e-1,h<0?o.openEnd:e-1):new k(gt(o.content,e,c),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!Tn(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return-1;let{depth:r}=this.$to,i=this.$to.after(r);for(;r>1&&i==this.$to.end(--r);)++i;return i}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:r,type:i}=this.frontier[t],s=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=Tn(e,t,i,r,s);if(o){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],u=Tn(e,l,c,a,!0);if(!u||u.childCount)continue e}return{depth:t,fit:o,move:s?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=yt(this.placed,t.depth,t.fit)),e=t.move;for(let r=t.depth+1;r<=e.depth;r++){let i=e.node(r),s=i.type.contentMatch.fillBefore(i.content,!0,e.index(r));this.openFrontierNode(i.type,i.attrs,s)}return e}openFrontierNode(e,t=null,r){let i=this.frontier[this.depth];i.match=i.match.matchType(e),this.placed=yt(this.placed,this.depth,b.from(e.create(t,r))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(b.empty,!0);t.childCount&&(this.placed=yt(this.placed,this.frontier.length,t))}}function gt(n,e,t){return e==0?n.cutByIndex(t,n.childCount):n.replaceChild(0,n.firstChild.copy(gt(n.firstChild.content,e-1,t)))}function yt(n,e,t){return e==0?n.append(t):n.replaceChild(n.childCount-1,n.lastChild.copy(yt(n.lastChild.content,e-1,t)))}function wn(n,e){for(let t=0;t<e;t++)n=n.firstChild.content;return n}function Cs(n,e,t){if(e<=0)return n;let r=n.content;return e>1&&(r=r.replaceChild(0,Cs(r.firstChild,e-1,r.childCount==1?t-1:0))),e>0&&(r=n.type.contentMatch.fillBefore(r).append(r),t<=0&&(r=r.append(n.type.contentMatch.matchFragment(r).fillBefore(b.empty,!0)))),n.copy(r)}function Tn(n,e,t,r,i){let s=n.node(e),o=i?n.indexAfter(e):n.index(e);if(o==s.childCount&&!t.compatibleContent(s.type))return null;let l=r.fillBefore(s.content,!0,o);return l&&!Ml(t,s.content,o)?l:null}function Ml(n,e,t){for(let r=t;r<e.childCount;r++)if(!n.allowsMarks(e.child(r).marks))return!0;return!1}function Cl(n){return n.spec.defining||n.spec.definingForContent}function wl(n,e,t,r){if(!r.size)return n.deleteRange(e,t);let i=n.doc.resolve(e),s=n.doc.resolve(t);if(Ms(i,s,r))return n.step(new L(e,t,r));let o=Ts(i,n.doc.resolve(t));o[o.length-1]==0&&o.pop();let l=-(i.depth+1);o.unshift(l);for(let f=i.depth,h=i.pos-1;f>0;f--,h--){let p=i.node(f).type.spec;if(p.defining||p.definingAsContext||p.isolating)break;o.indexOf(f)>-1?l=f:i.before(f)==h&&o.splice(1,0,-f)}let a=o.indexOf(l),c=[],u=r.openStart;for(let f=r.content,h=0;;h++){let p=f.firstChild;if(c.push(p),h==r.openStart)break;f=p.content}for(let f=u-1;f>=0;f--){let h=c[f],p=Cl(h.type);if(p&&!h.sameMarkup(i.node(Math.abs(l)-1)))u=f;else if(p||!h.type.isTextblock)break}for(let f=r.openStart;f>=0;f--){let h=(f+u+1)%(r.openStart+1),p=c[h];if(p)for(let m=0;m<o.length;m++){let g=o[(m+a)%o.length],y=!0;g<0&&(y=!1,g=-g);let M=i.node(g-1),T=i.index(g-1);if(M.canReplaceWith(T,T,p.type,p.marks))return n.replace(i.before(g),y?s.after(g):t,new k(ws(r.content,0,r.openStart,h),h,r.openEnd))}}let d=n.steps.length;for(let f=o.length-1;f>=0&&(n.replace(e,t,r),!(n.steps.length>d));f--){let h=o[f];h<0||(e=i.before(h),t=s.after(h))}}function ws(n,e,t,r,i){if(e<t){let s=n.firstChild;n=n.replaceChild(0,s.copy(ws(s.content,e+1,t,r,s)))}if(e>r){let s=i.contentMatchAt(0),o=s.fillBefore(n).append(n);n=o.append(s.matchFragment(o).fillBefore(b.empty,!0))}return n}function Tl(n,e,t,r){if(!r.isInline&&e==t&&n.doc.resolve(e).parent.content.size){let i=Sl(n.doc,e,r.type);i!=null&&(e=t=i)}n.replaceRange(e,t,new k(b.from(r),0,0))}function Ol(n,e,t){let r=n.doc.resolve(e),i=n.doc.resolve(t),s=Ts(r,i);for(let o=0;o<s.length;o++){let l=s[o],a=o==s.length-1;if(a&&l==0||r.node(l).type.contentMatch.validEnd)return n.delete(r.start(l),i.end(l));if(l>0&&(a||r.node(l-1).canReplace(r.index(l-1),i.indexAfter(l-1))))return n.delete(r.before(l),i.after(l))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(e-r.start(o)==r.depth-o&&t>r.end(o)&&i.end(o)-t!=i.depth-o&&r.start(o-1)==i.start(o-1)&&r.node(o-1).canReplace(r.index(o-1),i.index(o-1)))return n.delete(r.before(o),t);n.delete(e,t)}function Ts(n,e){let t=[],r=Math.min(n.depth,e.depth);for(let i=r;i>=0;i--){let s=n.start(i);if(s<n.pos-(n.depth-i)||e.end(i)>e.pos+(e.depth-i)||n.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(s==e.start(i)||i==n.depth&&i==e.depth&&n.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==s-1)&&t.push(i)}return t}class ot extends K{constructor(e,t,r){super(),this.pos=e,this.attr=t,this.value=r}apply(e){let t=e.nodeAt(this.pos);if(!t)return B.fail("No node at attribute step's position");let r=Object.create(null);for(let s in t.attrs)r[s]=t.attrs[s];r[this.attr]=this.value;let i=t.type.create(r,null,t.marks);return B.fromReplace(e,this.pos,this.pos+1,new k(b.from(i),0,t.isLeaf?0:1))}getMap(){return re.empty}invert(e){return new ot(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new ot(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new ot(t.pos,t.attr,t.value)}}K.jsonID("attr",ot);class Ot extends K{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let i in e.attrs)t[i]=e.attrs[i];t[this.attr]=this.value;let r=e.type.create(t,e.content,e.marks);return B.ok(r)}getMap(){return re.empty}invert(e){return new Ot(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new Ot(t.attr,t.value)}}K.jsonID("docAttr",Ot);let at=class extends Error{};at=function n(e){let t=Error.call(this,e);return t.__proto__=n.prototype,t};at.prototype=Object.create(Error.prototype);at.prototype.constructor=at;at.prototype.name="TransformError";class Os{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Tt}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new at(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,r=k.empty){let i=un(this.doc,e,t,r);return i&&this.step(i),this}replaceWith(e,t,r){return this.replace(e,t,new k(b.from(r),0,0))}delete(e,t){return this.replace(e,t,k.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,r){return wl(this,e,t,r),this}replaceRangeWith(e,t,r){return Tl(this,e,t,r),this}deleteRange(e,t){return Ol(this,e,t),this}lift(e,t){return ul(this,e,t),this}join(e,t=1){return kl(this,e,t),this}wrap(e,t){return hl(this,e,t),this}setBlockType(e,t=e,r,i=null){return pl(this,e,t,r,i),this}setNodeMarkup(e,t,r=null,i){return gl(this,e,t,r,i),this}setNodeAttribute(e,t,r){return this.step(new ot(e,t,r)),this}setDocAttribute(e,t){return this.step(new Ot(e,t)),this}addNodeMark(e,t){return this.step(new Oe(e,t)),this}removeNodeMark(e,t){let r=this.doc.nodeAt(e);if(!r)throw new RangeError("No node at position "+e);if(t instanceof D)t.isInSet(r.marks)&&this.step(new Ge(e,t));else{let i=r.marks,s,o=[];for(;s=t.isInSet(i);)o.push(new Ge(e,s)),i=s.removeFromSet(i);for(let l=o.length-1;l>=0;l--)this.step(o[l])}return this}split(e,t=1,r){return yl(this,e,t,r),this}addMark(e,t,r){return ll(this,e,t,r),this}removeMark(e,t,r){return al(this,e,t,r),this}clearIncompatible(e,t,r){return dr(this,e,t,r),this}}const On=Object.create(null);class O{constructor(e,t,r){this.$anchor=e,this.$head=t,this.ranges=r||[new El(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=k.empty){let r=t.content.lastChild,i=null;for(let l=0;l<t.openEnd;l++)i=r,r=r.lastChild;let s=e.steps.length,o=this.ranges;for(let l=0;l<o.length;l++){let{$from:a,$to:c}=o[l],u=e.mapping.slice(s);e.replaceRange(u.map(a.pos),u.map(c.pos),l?k.empty:t),l==0&&Gr(e,s,(r?r.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(e,t){let r=e.steps.length,i=this.ranges;for(let s=0;s<i.length;s++){let{$from:o,$to:l}=i[s],a=e.mapping.slice(r),c=a.map(o.pos),u=a.map(l.pos);s?e.deleteRange(c,u):(e.replaceRangeWith(c,u,t),Gr(e,r,t.isInline?-1:1))}}static findFrom(e,t,r=!1){let i=e.parent.inlineContent?new w(e):tt(e.node(0),e.parent,e.pos,e.index(),t,r);if(i)return i;for(let s=e.depth-1;s>=0;s--){let o=t<0?tt(e.node(0),e.node(s),e.before(s+1),e.index(s),t,r):tt(e.node(0),e.node(s),e.after(s+1),e.index(s)+1,t,r);if(o)return o}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new ae(e.node(0))}static atStart(e){return tt(e,e,0,0,1)||new ae(e)}static atEnd(e){return tt(e,e,e.content.size,e.childCount,-1)||new ae(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let r=On[t.type];if(!r)throw new RangeError(`No selection type ${t.type} defined`);return r.fromJSON(e,t)}static jsonID(e,t){if(e in On)throw new RangeError("Duplicate use of selection JSON ID "+e);return On[e]=t,t.prototype.jsonID=e,t}getBookmark(){return w.between(this.$anchor,this.$head).getBookmark()}}O.prototype.visible=!0;class El{constructor(e,t){this.$from=e,this.$to=t}}let _r=!1;function Ur(n){!_r&&!n.parent.inlineContent&&(_r=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+n.parent.type.name+")"))}class w extends O{constructor(e,t=e){Ur(e),Ur(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let r=e.resolve(t.map(this.head));if(!r.parent.inlineContent)return O.near(r);let i=e.resolve(t.map(this.anchor));return new w(i.parent.inlineContent?i:r,r)}replace(e,t=k.empty){if(super.replace(e,t),t==k.empty){let r=this.$from.marksAcross(this.$to);r&&e.ensureMarks(r)}}eq(e){return e instanceof w&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new dn(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new w(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,r=t){let i=e.resolve(t);return new this(i,r==t?i:e.resolve(r))}static between(e,t,r){let i=e.pos-t.pos;if((!r||i)&&(r=i>=0?1:-1),!t.parent.inlineContent){let s=O.findFrom(t,r,!0)||O.findFrom(t,-r,!0);if(s)t=s.$head;else return O.near(t,r)}return e.parent.inlineContent||(i==0?e=t:(e=(O.findFrom(e,-r,!0)||O.findFrom(e,r,!0)).$anchor,e.pos<t.pos!=i<0&&(e=t))),new w(e,t)}}O.jsonID("text",w);class dn{constructor(e,t){this.anchor=e,this.head=t}map(e){return new dn(e.map(this.anchor),e.map(this.head))}resolve(e){return w.between(e.resolve(this.anchor),e.resolve(this.head))}}class x extends O{constructor(e){let t=e.nodeAfter,r=e.node(0).resolve(e.pos+t.nodeSize);super(e,r),this.node=t}map(e,t){let{deleted:r,pos:i}=t.mapResult(this.anchor),s=e.resolve(i);return r?O.near(s):new x(s)}content(){return new k(b.from(this.node),0,0)}eq(e){return e instanceof x&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new hr(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new x(e.resolve(t.anchor))}static create(e,t){return new x(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}x.prototype.visible=!1;O.jsonID("node",x);class hr{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:r}=e.mapResult(this.anchor);return t?new dn(r,r):new hr(r)}resolve(e){let t=e.resolve(this.anchor),r=t.nodeAfter;return r&&x.isSelectable(r)?new x(t):O.near(t)}}class ae extends O{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=k.empty){if(t==k.empty){e.delete(0,e.doc.content.size);let r=O.atStart(e.doc);r.eq(e.selection)||e.setSelection(r)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new ae(e)}map(e){return new ae(e)}eq(e){return e instanceof ae}getBookmark(){return Nl}}O.jsonID("all",ae);const Nl={map(){return this},resolve(n){return new ae(n)}};function tt(n,e,t,r,i,s=!1){if(e.inlineContent)return w.create(n,t);for(let o=r-(i>0?0:1);i>0?o<e.childCount:o>=0;o+=i){let l=e.child(o);if(l.isAtom){if(!s&&x.isSelectable(l))return x.create(n,t-(i<0?l.nodeSize:0))}else{let a=tt(n,l,t+i,i<0?l.childCount:0,i,s);if(a)return a}t+=l.nodeSize*i}return null}function Gr(n,e,t){let r=n.steps.length-1;if(r<e)return;let i=n.steps[r];if(!(i instanceof L||i instanceof F))return;let s=n.mapping.maps[r],o;s.forEach((l,a,c,u)=>{o==null&&(o=u)}),n.setSelection(O.near(n.doc.resolve(o),t))}const Yr=1,Ft=2,Xr=4;class vl extends Os{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(this.updated|Yr)&~Ft,this.storedMarks=null,this}get selectionSet(){return(this.updated&Yr)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=Ft,this}ensureMarks(e){return D.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(this.updated&Ft)>0}addStep(e,t){super.addStep(e,t),this.updated=this.updated&~Ft,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let r=this.selection;return t&&(e=e.mark(this.storedMarks||(r.empty?r.$from.marks():r.$from.marksAcross(r.$to)||D.none))),r.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,r){let i=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(i.text(e),!0):this.deleteSelection();{if(r==null&&(r=t),r=r??t,!e)return this.deleteRange(t,r);let s=this.storedMarks;if(!s){let o=this.doc.resolve(t);s=r==t?o.marks():o.marksAcross(this.doc.resolve(r))}return this.replaceRangeWith(t,r,i.text(e,s)),this.selection.empty||this.setSelection(O.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=Xr,this}get scrolledIntoView(){return(this.updated&Xr)>0}}function Zr(n,e){return!e||!n?n:n.bind(e)}class bt{constructor(e,t,r){this.name=e,this.init=Zr(t.init,r),this.apply=Zr(t.apply,r)}}const Al=[new bt("doc",{init(n){return n.doc||n.schema.topNodeType.createAndFill()},apply(n){return n.doc}}),new bt("selection",{init(n,e){return n.selection||O.atStart(e.doc)},apply(n){return n.selection}}),new bt("storedMarks",{init(n){return n.storedMarks||null},apply(n,e,t,r){return r.selection.$cursor?n.storedMarks:null}}),new bt("scrollToSelection",{init(){return 0},apply(n,e){return n.scrolledIntoView?e+1:e}})];class En{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=Al.slice(),t&&t.forEach(r=>{if(this.pluginsByKey[r.key])throw new RangeError("Adding different instances of a keyed plugin ("+r.key+")");this.plugins.push(r),this.pluginsByKey[r.key]=r,r.spec.state&&this.fields.push(new bt(r.key,r.spec.state,r))})}}class rt{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let r=0;r<this.config.plugins.length;r++)if(r!=t){let i=this.config.plugins[r];if(i.spec.filterTransaction&&!i.spec.filterTransaction.call(i,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],r=this.applyInner(e),i=null;for(;;){let s=!1;for(let o=0;o<this.config.plugins.length;o++){let l=this.config.plugins[o];if(l.spec.appendTransaction){let a=i?i[o].n:0,c=i?i[o].state:this,u=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,r);if(u&&r.filterTransaction(u,o)){if(u.setMeta("appendedTransaction",e),!i){i=[];for(let d=0;d<this.config.plugins.length;d++)i.push(d<o?{state:r,n:t.length}:{state:this,n:0})}t.push(u),r=r.applyInner(u),s=!0}i&&(i[o]={state:r,n:t.length})}}if(!s)return{state:r,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new rt(this.config),r=this.config.fields;for(let i=0;i<r.length;i++){let s=r[i];t[s.name]=s.apply(e,this[s.name],this,t)}return t}get tr(){return new vl(this)}static create(e){let t=new En(e.doc?e.doc.type.schema:e.schema,e.plugins),r=new rt(t);for(let i=0;i<t.fields.length;i++)r[t.fields[i].name]=t.fields[i].init(e,r);return r}reconfigure(e){let t=new En(this.schema,e.plugins),r=t.fields,i=new rt(t);for(let s=0;s<r.length;s++){let o=r[s].name;i[o]=this.hasOwnProperty(o)?this[o]:r[s].init(e,i)}return i}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(r=>r.toJSON())),e&&typeof e=="object")for(let r in e){if(r=="doc"||r=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let i=e[r],s=i.spec.state;s&&s.toJSON&&(t[r]=s.toJSON.call(i,this[i.key]))}return t}static fromJSON(e,t,r){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let i=new En(e.schema,e.plugins),s=new rt(i);return i.fields.forEach(o=>{if(o.name=="doc")s.doc=Ne.fromJSON(e.schema,t.doc);else if(o.name=="selection")s.selection=O.fromJSON(s.doc,t.selection);else if(o.name=="storedMarks")t.storedMarks&&(s.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(r)for(let l in r){let a=r[l],c=a.spec.state;if(a.key==o.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l)){s[o.name]=c.fromJSON.call(a,e,t[l],s);return}}s[o.name]=o.init(e,s)}}),s}}function Es(n,e,t){for(let r in n){let i=n[r];i instanceof Function?i=i.bind(e):r=="handleDOMEvents"&&(i=Es(i,e,{})),t[r]=i}return t}class ne{constructor(e){this.spec=e,this.props={},e.props&&Es(e.props,this,this.props),this.key=e.key?e.key.key:Ns("plugin")}getState(e){return e[this.key]}}const Nn=Object.create(null);function Ns(n){return n in Nn?n+"$"+ ++Nn[n]:(Nn[n]=0,n+"$")}class ge{constructor(e="key"){this.key=Ns(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const W=function(n){for(var e=0;;e++)if(n=n.previousSibling,!n)return e},ct=function(n){let e=n.assignedSlot||n.parentNode;return e&&e.nodeType==11?e.host:e};let Gn=null;const be=function(n,e,t){let r=Gn||(Gn=document.createRange());return r.setEnd(n,t??n.nodeValue.length),r.setStart(n,e||0),r},Dl=function(){Gn=null},Ye=function(n,e,t,r){return t&&(Qr(n,e,t,r,-1)||Qr(n,e,t,r,1))},Il=/^(img|br|input|textarea|hr)$/i;function Qr(n,e,t,r,i){for(var s;;){if(n==t&&e==r)return!0;if(e==(i<0?0:se(n))){let o=n.parentNode;if(!o||o.nodeType!=1||Rt(n)||Il.test(n.nodeName)||n.contentEditable=="false")return!1;e=W(n)+(i<0?0:1),n=o}else if(n.nodeType==1){let o=n.childNodes[e+(i<0?-1:0)];if(o.nodeType==1&&o.contentEditable=="false")if(!((s=o.pmViewDesc)===null||s===void 0)&&s.ignoreForSelection)e+=i;else return!1;else n=o,e=i<0?se(n):0}else return!1}}function se(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function Rl(n,e){for(;;){if(n.nodeType==3&&e)return n;if(n.nodeType==1&&e>0){if(n.contentEditable=="false")return null;n=n.childNodes[e-1],e=se(n)}else if(n.parentNode&&!Rt(n))e=W(n),n=n.parentNode;else return null}}function Pl(n,e){for(;;){if(n.nodeType==3&&e<n.nodeValue.length)return n;if(n.nodeType==1&&e<n.childNodes.length){if(n.contentEditable=="false")return null;n=n.childNodes[e],e=0}else if(n.parentNode&&!Rt(n))e=W(n)+1,n=n.parentNode;else return null}}function Bl(n,e,t){for(let r=e==0,i=e==se(n);r||i;){if(n==t)return!0;let s=W(n);if(n=n.parentNode,!n)return!1;r=r&&s==0,i=i&&s==se(n)}}function Rt(n){let e;for(let t=n;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==n||e.contentDOM==n)}const fn=function(n){return n.focusNode&&Ye(n.focusNode,n.focusOffset,n.anchorNode,n.anchorOffset)};function Fe(n,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=n,t.key=t.code=e,t}function zl(n){let e=n.activeElement;for(;e&&e.shadowRoot;)e=e.shadowRoot.activeElement;return e}function Ll(n,e,t){if(n.caretPositionFromPoint)try{let r=n.caretPositionFromPoint(e,t);if(r)return{node:r.offsetNode,offset:Math.min(se(r.offsetNode),r.offset)}}catch{}if(n.caretRangeFromPoint){let r=n.caretRangeFromPoint(e,t);if(r)return{node:r.startContainer,offset:Math.min(se(r.startContainer),r.startOffset)}}}const pe=typeof navigator<"u"?navigator:null,ei=typeof document<"u"?document:null,ze=pe&&pe.userAgent||"",Yn=/Edge\/(\d+)/.exec(ze),vs=/MSIE \d/.exec(ze),Xn=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(ze),ee=!!(vs||Xn||Yn),Ae=vs?document.documentMode:Xn?+Xn[1]:Yn?+Yn[1]:0,de=!ee&&/gecko\/(\d+)/i.test(ze);de&&+(/Firefox\/(\d+)/.exec(ze)||[0,0])[1];const Zn=!ee&&/Chrome\/(\d+)/.exec(ze),q=!!Zn,As=Zn?+Zn[1]:0,U=!ee&&!!pe&&/Apple Computer/.test(pe.vendor),ut=U&&(/Mobile\/\w+/.test(ze)||!!pe&&pe.maxTouchPoints>2),ie=ut||(pe?/Mac/.test(pe.platform):!1),Fl=pe?/Win/.test(pe.platform):!1,ke=/Android \d/.test(ze),Pt=!!ei&&"webkitFontSmoothing"in ei.documentElement.style,Vl=Pt?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function $l(n){let e=n.defaultView&&n.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:n.documentElement.clientWidth,top:0,bottom:n.documentElement.clientHeight}}function ye(n,e){return typeof n=="number"?n:n[e]}function Hl(n){let e=n.getBoundingClientRect(),t=e.width/n.offsetWidth||1,r=e.height/n.offsetHeight||1;return{left:e.left,right:e.left+n.clientWidth*t,top:e.top,bottom:e.top+n.clientHeight*r}}function ti(n,e,t){let r=n.someProp("scrollThreshold")||0,i=n.someProp("scrollMargin")||5,s=n.dom.ownerDocument;for(let o=t||n.dom;o;){if(o.nodeType!=1){o=ct(o);continue}let l=o,a=l==s.body,c=a?$l(s):Hl(l),u=0,d=0;if(e.top<c.top+ye(r,"top")?d=-(c.top-e.top+ye(i,"top")):e.bottom>c.bottom-ye(r,"bottom")&&(d=e.bottom-e.top>c.bottom-c.top?e.top+ye(i,"top")-c.top:e.bottom-c.bottom+ye(i,"bottom")),e.left<c.left+ye(r,"left")?u=-(c.left-e.left+ye(i,"left")):e.right>c.right-ye(r,"right")&&(u=e.right-c.right+ye(i,"right")),u||d)if(a)s.defaultView.scrollBy(u,d);else{let h=l.scrollLeft,p=l.scrollTop;d&&(l.scrollTop+=d),u&&(l.scrollLeft+=u);let m=l.scrollLeft-h,g=l.scrollTop-p;e={left:e.left-m,top:e.top-g,right:e.right-m,bottom:e.bottom-g}}let f=a?"fixed":getComputedStyle(o).position;if(/^(fixed|sticky)$/.test(f))break;o=f=="absolute"?o.offsetParent:ct(o)}}function Wl(n){let e=n.dom.getBoundingClientRect(),t=Math.max(0,e.top),r,i;for(let s=(e.left+e.right)/2,o=t+1;o<Math.min(innerHeight,e.bottom);o+=5){let l=n.root.elementFromPoint(s,o);if(!l||l==n.dom||!n.dom.contains(l))continue;let a=l.getBoundingClientRect();if(a.top>=t-20){r=l,i=a.top;break}}return{refDOM:r,refTop:i,stack:Ds(n.dom)}}function Ds(n){let e=[],t=n.ownerDocument;for(let r=n;r&&(e.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),n!=t);r=ct(r));return e}function jl({refDOM:n,refTop:e,stack:t}){let r=n?n.getBoundingClientRect().top:0;Is(t,r==0?0:r-e)}function Is(n,e){for(let t=0;t<n.length;t++){let{dom:r,top:i,left:s}=n[t];r.scrollTop!=i+e&&(r.scrollTop=i+e),r.scrollLeft!=s&&(r.scrollLeft=s)}}let Qe=null;function Jl(n){if(n.setActive)return n.setActive();if(Qe)return n.focus(Qe);let e=Ds(n);n.focus(Qe==null?{get preventScroll(){return Qe={preventScroll:!0},!0}}:void 0),Qe||(Qe=!1,Is(e,0))}function Rs(n,e){let t,r=2e8,i,s=0,o=e.top,l=e.top,a,c;for(let u=n.firstChild,d=0;u;u=u.nextSibling,d++){let f;if(u.nodeType==1)f=u.getClientRects();else if(u.nodeType==3)f=be(u).getClientRects();else continue;for(let h=0;h<f.length;h++){let p=f[h];if(p.top<=o&&p.bottom>=l){o=Math.max(p.bottom,o),l=Math.min(p.top,l);let m=p.left>e.left?p.left-e.left:p.right<e.left?e.left-p.right:0;if(m<r){t=u,r=m,i=m&&t.nodeType==3?{left:p.right<e.left?p.right:p.left,top:e.top}:e,u.nodeType==1&&m&&(s=d+(e.left>=(p.left+p.right)/2?1:0));continue}}else p.top>e.top&&!a&&p.left<=e.left&&p.right>=e.left&&(a=u,c={left:Math.max(p.left,Math.min(p.right,e.left)),top:p.top});!t&&(e.left>=p.right&&e.top>=p.top||e.left>=p.left&&e.top>=p.bottom)&&(s=d+1)}}return!t&&a&&(t=a,i=c,r=0),t&&t.nodeType==3?ql(t,i):!t||r&&t.nodeType==1?{node:n,offset:s}:Rs(t,i)}function ql(n,e){let t=n.nodeValue.length,r=document.createRange();for(let i=0;i<t;i++){r.setEnd(n,i+1),r.setStart(n,i);let s=xe(r,1);if(s.top!=s.bottom&&pr(e,s))return{node:n,offset:i+(e.left>=(s.left+s.right)/2?1:0)}}return{node:n,offset:0}}function pr(n,e){return n.left>=e.left-1&&n.left<=e.right+1&&n.top>=e.top-1&&n.top<=e.bottom+1}function Kl(n,e){let t=n.parentNode;return t&&/^li$/i.test(t.nodeName)&&e.left<n.getBoundingClientRect().left?t:n}function _l(n,e,t){let{node:r,offset:i}=Rs(e,t),s=-1;if(r.nodeType==1&&!r.firstChild){let o=r.getBoundingClientRect();s=o.left!=o.right&&t.left>(o.left+o.right)/2?1:-1}return n.docView.posFromDOM(r,i,s)}function Ul(n,e,t,r){let i=-1;for(let s=e,o=!1;s!=n.dom;){let l=n.docView.nearestDesc(s,!0),a;if(!l)return null;if(l.dom.nodeType==1&&(l.node.isBlock&&l.parent||!l.contentDOM)&&((a=l.dom.getBoundingClientRect()).width||a.height)&&(l.node.isBlock&&l.parent&&(!o&&a.left>r.left||a.top>r.top?i=l.posBefore:(!o&&a.right<r.left||a.bottom<r.top)&&(i=l.posAfter),o=!0),!l.contentDOM&&i<0&&!l.node.isText))return(l.node.isBlock?r.top<(a.top+a.bottom)/2:r.left<(a.left+a.right)/2)?l.posBefore:l.posAfter;s=l.dom.parentNode}return i>-1?i:n.docView.posFromDOM(e,t,-1)}function Ps(n,e,t){let r=n.childNodes.length;if(r&&t.top<t.bottom)for(let i=Math.max(0,Math.min(r-1,Math.floor(r*(e.top-t.top)/(t.bottom-t.top))-2)),s=i;;){let o=n.childNodes[s];if(o.nodeType==1){let l=o.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(pr(e,c))return Ps(o,e,c)}}if((s=(s+1)%r)==i)break}return n}function Gl(n,e){let t=n.dom.ownerDocument,r,i=0,s=Ll(t,e.left,e.top);s&&({node:r,offset:i}=s);let o=(n.root.elementFromPoint?n.root:t).elementFromPoint(e.left,e.top),l;if(!o||!n.dom.contains(o.nodeType!=1?o.parentNode:o)){let c=n.dom.getBoundingClientRect();if(!pr(e,c)||(o=Ps(n.dom,e,c),!o))return null}if(U)for(let c=o;r&&c;c=ct(c))c.draggable&&(r=void 0);if(o=Kl(o,e),r){if(de&&r.nodeType==1&&(i=Math.min(i,r.childNodes.length),i<r.childNodes.length)){let u=r.childNodes[i],d;u.nodeName=="IMG"&&(d=u.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&i++}let c;Pt&&i&&r.nodeType==1&&(c=r.childNodes[i-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&i--,r==n.dom&&i==r.childNodes.length-1&&r.lastChild.nodeType==1&&e.top>r.lastChild.getBoundingClientRect().bottom?l=n.state.doc.content.size:(i==0||r.nodeType!=1||r.childNodes[i-1].nodeName!="BR")&&(l=Ul(n,r,i,e))}l==null&&(l=_l(n,o,e));let a=n.docView.nearestDesc(o,!0);return{pos:l,inside:a?a.posAtStart-a.border:-1}}function ni(n){return n.top<n.bottom||n.left<n.right}function xe(n,e){let t=n.getClientRects();if(t.length){let r=t[e<0?0:t.length-1];if(ni(r))return r}return Array.prototype.find.call(t,ni)||n.getBoundingClientRect()}const Yl=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Bs(n,e,t){let{node:r,offset:i,atom:s}=n.docView.domFromPos(e,t<0?-1:1),o=Pt||de;if(r.nodeType==3)if(o&&(Yl.test(r.nodeValue)||(t<0?!i:i==r.nodeValue.length))){let a=xe(be(r,i,i),t);if(de&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let c=xe(be(r,i-1,i-1),-1);if(c.top==a.top){let u=xe(be(r,i,i+1),-1);if(u.top!=a.top)return mt(u,u.left<c.left)}}return a}else{let a=i,c=i,u=t<0?1:-1;return t<0&&!i?(c++,u=-1):t>=0&&i==r.nodeValue.length?(a--,u=1):t<0?a--:c++,mt(xe(be(r,a,c),u),u<0)}if(!n.state.doc.resolve(e-(s||0)).parent.inlineContent){if(s==null&&i&&(t<0||i==se(r))){let a=r.childNodes[i-1];if(a.nodeType==1)return vn(a.getBoundingClientRect(),!1)}if(s==null&&i<se(r)){let a=r.childNodes[i];if(a.nodeType==1)return vn(a.getBoundingClientRect(),!0)}return vn(r.getBoundingClientRect(),t>=0)}if(s==null&&i&&(t<0||i==se(r))){let a=r.childNodes[i-1],c=a.nodeType==3?be(a,se(a)-(o?0:1)):a.nodeType==1&&(a.nodeName!="BR"||!a.nextSibling)?a:null;if(c)return mt(xe(c,1),!1)}if(s==null&&i<se(r)){let a=r.childNodes[i];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let c=a?a.nodeType==3?be(a,0,o?0:1):a.nodeType==1?a:null:null;if(c)return mt(xe(c,-1),!0)}return mt(xe(r.nodeType==3?be(r):r,-t),t>=0)}function mt(n,e){if(n.width==0)return n;let t=e?n.left:n.right;return{top:n.top,bottom:n.bottom,left:t,right:t}}function vn(n,e){if(n.height==0)return n;let t=e?n.top:n.bottom;return{top:t,bottom:t,left:n.left,right:n.right}}function zs(n,e,t){let r=n.state,i=n.root.activeElement;r!=e&&n.updateState(e),i!=n.dom&&n.focus();try{return t()}finally{r!=e&&n.updateState(r),i!=n.dom&&i&&i.focus()}}function Xl(n,e,t){let r=e.selection,i=t=="up"?r.$from:r.$to;return zs(n,e,()=>{let{node:s}=n.docView.domFromPos(i.pos,t=="up"?-1:1);for(;;){let l=n.docView.nearestDesc(s,!0);if(!l)break;if(l.node.isBlock){s=l.contentDOM||l.dom;break}s=l.dom.parentNode}let o=Bs(n,i.pos,1);for(let l=s.firstChild;l;l=l.nextSibling){let a;if(l.nodeType==1)a=l.getClientRects();else if(l.nodeType==3)a=be(l,0,l.nodeValue.length).getClientRects();else continue;for(let c=0;c<a.length;c++){let u=a[c];if(u.bottom>u.top+1&&(t=="up"?o.top-u.top>(u.bottom-o.top)*2:u.bottom-o.bottom>(o.bottom-u.top)*2))return!1}}return!0})}const Zl=/[\u0590-\u08ac]/;function Ql(n,e,t){let{$head:r}=e.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,s=!i,o=i==r.parent.content.size,l=n.domSelection();return l?!Zl.test(r.parent.textContent)||!l.modify?t=="left"||t=="backward"?s:o:zs(n,e,()=>{let{focusNode:a,focusOffset:c,anchorNode:u,anchorOffset:d}=n.domSelectionRange(),f=l.caretBidiLevel;l.modify("move",t,"character");let h=r.depth?n.docView.domAfterPos(r.before()):n.dom,{focusNode:p,focusOffset:m}=n.domSelectionRange(),g=p&&!h.contains(p.nodeType==1?p:p.parentNode)||a==p&&c==m;try{l.collapse(u,d),a&&(a!=u||c!=d)&&l.extend&&l.extend(a,c)}catch{}return f!=null&&(l.caretBidiLevel=f),g}):r.pos==r.start()||r.pos==r.end()}let ri=null,ii=null,si=!1;function ea(n,e,t){return ri==e&&ii==t?si:(ri=e,ii=t,si=t=="up"||t=="down"?Xl(n,e,t):Ql(n,e,t))}const ce=0,oi=1,$e=2,me=3;class Bt{constructor(e,t,r,i){this.parent=e,this.children=t,this.dom=r,this.contentDOM=i,this.dirty=ce,r.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,r){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,r=this.posAtStart;;t++){let i=this.children[t];if(i==e)return r;r+=i.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,r){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode))if(r<0){let s,o;if(e==this.contentDOM)s=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.previousSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.previousSibling;return s?this.posBeforeChild(o)+o.size:this.posAtStart}else{let s,o;if(e==this.contentDOM)s=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;s=e.nextSibling}for(;s&&!((o=s.pmViewDesc)&&o.parent==this);)s=s.nextSibling;return s?this.posBeforeChild(o):this.posAtEnd}let i;if(e==this.dom&&this.contentDOM)i=t>W(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))i=e.compareDocumentPosition(this.contentDOM)&2;else if(this.dom.firstChild){if(t==0)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!1;break}if(s.previousSibling)break}if(i==null&&t==e.childNodes.length)for(let s=e;;s=s.parentNode){if(s==this.dom){i=!0;break}if(s.nextSibling)break}}return i??r>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let r=!0,i=e;i;i=i.parentNode){let s=this.getDesc(i),o;if(s&&(!t||s.node))if(r&&(o=s.nodeDOM)&&!(o.nodeType==1?o.contains(e.nodeType==1?e:e.parentNode):o==e))r=!1;else return s}}getDesc(e){let t=e.pmViewDesc;for(let r=t;r;r=r.parent)if(r==this)return t}posFromDOM(e,t,r){for(let i=e;i;i=i.parentNode){let s=this.getDesc(i);if(s)return s.localPosFromDOM(e,t,r)}return-1}descAt(e){for(let t=0,r=0;t<this.children.length;t++){let i=this.children[t],s=r+i.size;if(r==e&&s!=r){for(;!i.border&&i.children.length;)for(let o=0;o<i.children.length;o++){let l=i.children[o];if(l.size){i=l;break}}return i}if(e<s)return i.descAt(e-r-i.border);r=s}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let r=0,i=0;for(let s=0;r<this.children.length;r++){let o=this.children[r],l=s+o.size;if(l>e||o instanceof Fs){i=e-s;break}s=l}if(i)return this.children[r].domFromPos(i-this.children[r].border,t);for(let s;r&&!(s=this.children[r-1]).size&&s instanceof Ls&&s.side>=0;r--);if(t<=0){let s,o=!0;for(;s=r?this.children[r-1]:null,!(!s||s.dom.parentNode==this.contentDOM);r--,o=!1);return s&&t&&o&&!s.border&&!s.domAtom?s.domFromPos(s.size,t):{node:this.contentDOM,offset:s?W(s.dom)+1:0}}else{let s,o=!0;for(;s=r<this.children.length?this.children[r]:null,!(!s||s.dom.parentNode==this.contentDOM);r++,o=!1);return s&&o&&!s.border&&!s.domAtom?s.domFromPos(0,t):{node:this.contentDOM,offset:s?W(s.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,r=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let i=-1,s=-1;for(let o=r,l=0;;l++){let a=this.children[l],c=o+a.size;if(i==-1&&e<=c){let u=o+a.border;if(e>=u&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,u);e=o;for(let d=l;d>0;d--){let f=this.children[d-1];if(f.size&&f.dom.parentNode==this.contentDOM&&!f.emptyChildAt(1)){i=W(f.dom)+1;break}e-=f.size}i==-1&&(i=0)}if(i>-1&&(c>t||l==this.children.length-1)){t=c;for(let u=l+1;u<this.children.length;u++){let d=this.children[u];if(d.size&&d.dom.parentNode==this.contentDOM&&!d.emptyChildAt(-1)){s=W(d.dom);break}t+=d.size}s==-1&&(s=this.contentDOM.childNodes.length);break}o=c}return{node:this.contentDOM,from:e,to:t,fromOffset:i,toOffset:s}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:r}=this.domFromPos(e,0);if(t.nodeType!=1||r==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[r]}setSelection(e,t,r,i=!1){let s=Math.min(e,t),o=Math.max(e,t);for(let h=0,p=0;h<this.children.length;h++){let m=this.children[h],g=p+m.size;if(s>p&&o<g)return m.setSelection(e-p-m.border,t-p-m.border,r,i);p=g}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=r.root.getSelection(),u=r.domSelectionRange(),d=!1;if((de||U)&&e==t){let{node:h,offset:p}=l;if(h.nodeType==3){if(d=!!(p&&h.nodeValue[p-1]==`
`),d&&p==h.nodeValue.length)for(let m=h,g;m;m=m.parentNode){if(g=m.nextSibling){g.nodeName=="BR"&&(l=a={node:g.parentNode,offset:W(g)+1});break}let y=m.pmViewDesc;if(y&&y.node&&y.node.isBlock)break}}else{let m=h.childNodes[p-1];d=m&&(m.nodeName=="BR"||m.contentEditable=="false")}}if(de&&u.focusNode&&u.focusNode!=a.node&&u.focusNode.nodeType==1){let h=u.focusNode.childNodes[u.focusOffset];h&&h.contentEditable=="false"&&(i=!0)}if(!(i||d&&U)&&Ye(l.node,l.offset,u.anchorNode,u.anchorOffset)&&Ye(a.node,a.offset,u.focusNode,u.focusOffset))return;let f=!1;if((c.extend||e==t)&&!d){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),f=!0}catch{}}if(!f){if(e>t){let p=l;l=a,a=p}let h=document.createRange();h.setEnd(a.node,a.offset),h.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(h)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let r=0,i=0;i<this.children.length;i++){let s=this.children[i],o=r+s.size;if(r==o?e<=o&&t>=r:e<o&&t>r){let l=r+s.border,a=o-s.border;if(e>=l&&t<=a){this.dirty=e==r||t==o?$e:oi,e==l&&t==a&&(s.contentLost||s.dom.parentNode!=this.contentDOM)?s.dirty=me:s.markDirty(e-l,t-l);return}else s.dirty=s.dom==s.contentDOM&&s.dom.parentNode==this.contentDOM&&!s.children.length?$e:me}r=o}this.dirty=$e}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let r=e==1?$e:oi;t.dirty<r&&(t.dirty=r)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(e){return!1}}class Ls extends Bt{constructor(e,t,r,i){let s,o=t.type.toDOM;if(typeof o=="function"&&(o=o(r,()=>{if(!s)return i;if(s.parent)return s.parent.posBeforeChild(s)})),!t.type.spec.raw){if(o.nodeType!=1){let l=document.createElement("span");l.appendChild(o),o=l}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,s=this}matchesWidget(e){return this.dirty==ce&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return t?t(e):!1}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class ta extends Bt{constructor(e,t,r,i){super(e,[],t,null),this.textDOM=r,this.text=i}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class Xe extends Bt{constructor(e,t,r,i,s){super(e,[],r,i),this.mark=t,this.spec=s}static create(e,t,r,i){let s=i.nodeViews[t.type.name],o=s&&s(t,i,r);return(!o||!o.dom)&&(o=Ze.renderSpec(document,t.type.spec.toDOM(t,r),null,t.attrs)),new Xe(e,t,o.dom,o.contentDOM||o.dom,o)}parseRule(){return this.dirty&me||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=me&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=ce){let r=this.parent;for(;!r.node;)r=r.parent;r.dirty<this.dirty&&(r.dirty=this.dirty),this.dirty=ce}}slice(e,t,r){let i=Xe.create(this.parent,this.mark,!0,r),s=this.children,o=this.size;t<o&&(s=er(s,t,o,r)),e>0&&(s=er(s,0,e,r));for(let l=0;l<s.length;l++)s[l].parent=i;return i.children=s,i}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class De extends Bt{constructor(e,t,r,i,s,o,l,a,c){super(e,[],s,o),this.node=t,this.outerDeco=r,this.innerDeco=i,this.nodeDOM=l}static create(e,t,r,i,s,o){let l=s.nodeViews[t.type.name],a,c=l&&l(t,s,()=>{if(!a)return o;if(a.parent)return a.parent.posBeforeChild(a)},r,i),u=c&&c.dom,d=c&&c.contentDOM;if(t.isText){if(!u)u=document.createTextNode(t.text);else if(u.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else u||({dom:u,contentDOM:d}=Ze.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs));!d&&!t.isText&&u.nodeName!="BR"&&(u.hasAttribute("contenteditable")||(u.contentEditable="false"),t.type.spec.draggable&&(u.draggable=!0));let f=u;return u=Hs(u,r,t),c?a=new na(e,t,r,i,u,d||null,f,c,s,o+1):t.isText?new hn(e,t,r,i,u,f,s):new De(e,t,r,i,u,d||null,f,s,o+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),!this.contentDOM)e.getContent=()=>this.node.content;else if(!this.contentLost)e.contentElement=this.contentDOM;else{for(let t=this.children.length-1;t>=0;t--){let r=this.children[t];if(this.dom.contains(r.dom.parentNode)){e.contentElement=r.dom.parentNode;break}}e.contentElement||(e.getContent=()=>b.empty)}return e}matchesNode(e,t,r){return this.dirty==ce&&e.eq(this.node)&&Qt(t,this.outerDeco)&&r.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let r=this.node.inlineContent,i=t,s=e.composing?this.localCompositionInfo(e,t):null,o=s&&s.pos>-1?s:null,l=s&&s.pos<0,a=new ia(this,o&&o.node,e);la(this.node,this.innerDeco,(c,u,d)=>{c.spec.marks?a.syncToMarks(c.spec.marks,r,e):c.type.side>=0&&!d&&a.syncToMarks(u==this.node.childCount?D.none:this.node.child(u).marks,r,e),a.placeWidget(c,e,i)},(c,u,d,f)=>{a.syncToMarks(c.marks,r,e);let h;a.findNodeMatch(c,u,d,f)||l&&e.state.selection.from>i&&e.state.selection.to<i+c.nodeSize&&(h=a.findIndexWithChild(s.node))>-1&&a.updateNodeAt(c,u,d,h,e)||a.updateNextNode(c,u,d,e,f,i)||a.addNode(c,u,d,e,i),i+=c.nodeSize}),a.syncToMarks([],r,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==$e)&&(o&&this.protectLocalComposition(e,o),Vs(this.contentDOM,this.children,e),ut&&aa(this.dom))}localCompositionInfo(e,t){let{from:r,to:i}=e.state.selection;if(!(e.state.selection instanceof w)||r<t||i>t+this.node.content.size)return null;let s=e.input.compositionNode;if(!s||!this.dom.contains(s.parentNode))return null;if(this.node.inlineContent){let o=s.nodeValue,l=ca(this.node.content,o,r-t,i-t);return l<0?null:{node:s,pos:l,text:o}}else return{node:s,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:r,text:i}){if(this.getDesc(t))return;let s=t;for(;s.parentNode!=this.contentDOM;s=s.parentNode){for(;s.previousSibling;)s.parentNode.removeChild(s.previousSibling);for(;s.nextSibling;)s.parentNode.removeChild(s.nextSibling);s.pmViewDesc&&(s.pmViewDesc=void 0)}let o=new ta(this,s,t,i);e.input.compositionNodes.push(o),this.children=er(this.children,r,r+i.length,e,o)}update(e,t,r,i){return this.dirty==me||!e.sameMarkup(this.node)?!1:(this.updateInner(e,t,r,i),!0)}updateInner(e,t,r,i){this.updateOuterDeco(t),this.node=e,this.innerDeco=r,this.contentDOM&&this.updateChildren(i,this.posAtStart),this.dirty=ce}updateOuterDeco(e){if(Qt(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,r=this.dom;this.dom=$s(this.dom,this.nodeDOM,Qn(this.outerDeco,this.node,t),Qn(e,this.node,t)),this.dom!=r&&(r.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function li(n,e,t,r,i){Hs(r,e,n);let s=new De(void 0,n,e,t,r,r,r,i,0);return s.contentDOM&&s.updateChildren(i,0),s}class hn extends De{constructor(e,t,r,i,s,o,l){super(e,t,r,i,s,null,o,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,r,i){return this.dirty==me||this.dirty!=ce&&!this.inParent()||!e.sameMarkup(this.node)?!1:(this.updateOuterDeco(t),(this.dirty!=ce||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,i.trackWrites==this.nodeDOM&&(i.trackWrites=null)),this.node=e,this.dirty=ce,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,r){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,r)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,r){let i=this.node.cut(e,t),s=document.createTextNode(i.text);return new hn(this.parent,i,this.outerDeco,this.innerDeco,s,s,r)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(e==0||t==this.nodeDOM.nodeValue.length)&&(this.dirty=me)}get domAtom(){return!1}isText(e){return this.node.text==e}}class Fs extends Bt{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==ce&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class na extends De{constructor(e,t,r,i,s,o,l,a,c,u){super(e,t,r,i,s,o,l,c,u),this.spec=a}update(e,t,r,i){if(this.dirty==me)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let s=this.spec.update(e,t,r);return s&&this.updateInner(e,t,r,i),s}else return!this.contentDOM&&!e.isLeaf?!1:super.update(e,t,r,i)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,r,i){this.spec.setSelection?this.spec.setSelection(e,t,r.root):super.setSelection(e,t,r,i)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return this.spec.stopEvent?this.spec.stopEvent(e):!1}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function Vs(n,e,t){let r=n.firstChild,i=!1;for(let s=0;s<e.length;s++){let o=e[s],l=o.dom;if(l.parentNode==n){for(;l!=r;)r=ai(r),i=!0;r=r.nextSibling}else i=!0,n.insertBefore(l,r);if(o instanceof Xe){let a=r?r.previousSibling:n.lastChild;Vs(o.contentDOM,o.children,t),r=a?a.nextSibling:n.firstChild}}for(;r;)r=ai(r),i=!0;i&&t.trackWrites==n&&(t.trackWrites=null)}const xt=function(n){n&&(this.nodeName=n)};xt.prototype=Object.create(null);const He=[new xt];function Qn(n,e,t){if(n.length==0)return He;let r=t?He[0]:new xt,i=[r];for(let s=0;s<n.length;s++){let o=n[s].type.attrs;if(o){o.nodeName&&i.push(r=new xt(o.nodeName));for(let l in o){let a=o[l];a!=null&&(t&&i.length==1&&i.push(r=new xt(e.isInline?"span":"div")),l=="class"?r.class=(r.class?r.class+" ":"")+a:l=="style"?r.style=(r.style?r.style+";":"")+a:l!="nodeName"&&(r[l]=a))}}}return i}function $s(n,e,t,r){if(t==He&&r==He)return e;let i=e;for(let s=0;s<r.length;s++){let o=r[s],l=t[s];if(s){let a;l&&l.nodeName==o.nodeName&&i!=n&&(a=i.parentNode)&&a.nodeName.toLowerCase()==o.nodeName||(a=document.createElement(o.nodeName),a.pmIsDeco=!0,a.appendChild(i),l=He[0]),i=a}ra(i,l||He[0],o)}return i}function ra(n,e,t){for(let r in e)r!="class"&&r!="style"&&r!="nodeName"&&!(r in t)&&n.removeAttribute(r);for(let r in t)r!="class"&&r!="style"&&r!="nodeName"&&t[r]!=e[r]&&n.setAttribute(r,t[r]);if(e.class!=t.class){let r=e.class?e.class.split(" ").filter(Boolean):[],i=t.class?t.class.split(" ").filter(Boolean):[];for(let s=0;s<r.length;s++)i.indexOf(r[s])==-1&&n.classList.remove(r[s]);for(let s=0;s<i.length;s++)r.indexOf(i[s])==-1&&n.classList.add(i[s]);n.classList.length==0&&n.removeAttribute("class")}if(e.style!=t.style){if(e.style){let r=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,i;for(;i=r.exec(e.style);)n.style.removeProperty(i[1])}t.style&&(n.style.cssText+=t.style)}}function Hs(n,e,t){return $s(n,n,He,Qn(e,t,n.nodeType!=1))}function Qt(n,e){if(n.length!=e.length)return!1;for(let t=0;t<n.length;t++)if(!n[t].type.eq(e[t].type))return!1;return!0}function ai(n){let e=n.nextSibling;return n.parentNode.removeChild(n),e}class ia{constructor(e,t,r){this.lock=t,this.view=r,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=sa(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let r=e;r<t;r++)this.top.children[r].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,r){let i=0,s=this.stack.length>>1,o=Math.min(s,e.length);for(;i<o&&(i==s-1?this.top:this.stack[i+1<<1]).matchesMark(e[i])&&e[i].type.spec.spanning!==!1;)i++;for(;i<s;)this.destroyRest(),this.top.dirty=ce,this.index=this.stack.pop(),this.top=this.stack.pop(),s--;for(;s<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[s])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=Xe.create(this.top,e[s],t,r);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,s++}}findNodeMatch(e,t,r,i){let s=-1,o;if(i>=this.preMatch.index&&(o=this.preMatch.matches[i-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,r))s=this.top.children.indexOf(o,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,r)&&!this.preMatch.matched.has(c)){s=l;break}}return s<0?!1:(this.destroyBetween(this.index,s),this.index++,!0)}updateNodeAt(e,t,r,i,s){let o=this.top.children[i];return o.dirty==me&&o.dom==o.contentDOM&&(o.dirty=$e),o.update(e,t,r,s)?(this.destroyBetween(this.index,i),this.index++,!0):!1}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let r=e.pmViewDesc;if(r){for(let i=this.index;i<this.top.children.length;i++)if(this.top.children[i]==r)return i}return-1}e=t}}updateNextNode(e,t,r,i,s,o){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof De){let c=this.preMatch.matched.get(a);if(c!=null&&c!=s)return!1;let u=a.dom,d,f=this.isLocked(u)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=me&&Qt(t,a.outerDeco));if(!f&&a.update(e,t,r,i))return this.destroyBetween(this.index,l),a.dom!=u&&(this.changed=!0),this.index++,!0;if(!f&&(d=this.recreateWrapper(a,e,t,r,i,o)))return this.destroyBetween(this.index,l),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=$e,d.updateChildren(i,o+1),d.dirty=ce),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,r,i,s,o){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!Qt(r,e.outerDeco)||!i.eq(e.innerDeco))return null;let l=De.create(this.top,t,r,i,s,o);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,r,i,s){let o=De.create(this.top,e,t,r,i,s);o.contentDOM&&o.updateChildren(i,s+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(e,t,r){let i=this.index<this.top.children.length?this.top.children[this.index]:null;if(i&&i.matchesWidget(e)&&(e==i.widget||!i.widget.type.toDOM.parentNode))this.index++;else{let s=new Ls(this.top,e,t,r);this.top.children.splice(this.index++,0,s),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof Xe;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof hn)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((U||q)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let r=document.createElement(e);e=="IMG"&&(r.className="ProseMirror-separator",r.alt=""),e=="BR"&&(r.className="ProseMirror-trailingBreak");let i=new Fs(this.top,[],r,null);t!=this.top?t.children.push(i):t.children.splice(this.index++,0,i),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}}function sa(n,e){let t=e,r=t.children.length,i=n.childCount,s=new Map,o=[];e:for(;i>0;){let l;for(;;)if(r){let c=t.children[r-1];if(c instanceof Xe)t=c,r=c.children.length;else{l=c,r--;break}}else{if(t==e)break e;r=t.parent.children.indexOf(t),t=t.parent}let a=l.node;if(a){if(a!=n.child(i-1))break;--i,s.set(l,i),o.push(l)}}return{index:i,matched:s,matches:o.reverse()}}function oa(n,e){return n.type.side-e.type.side}function la(n,e,t,r){let i=e.locals(n),s=0;if(i.length==0){for(let c=0;c<n.childCount;c++){let u=n.child(c);r(u,i,e.forChild(s,u),c),s+=u.nodeSize}return}let o=0,l=[],a=null;for(let c=0;;){let u,d;for(;o<i.length&&i[o].to==s;){let g=i[o++];g.widget&&(u?(d||(d=[u])).push(g):u=g)}if(u)if(d){d.sort(oa);for(let g=0;g<d.length;g++)t(d[g],c,!!a)}else t(u,c,!!a);let f,h;if(a)h=-1,f=a,a=null;else if(c<n.childCount)h=c,f=n.child(c++);else break;for(let g=0;g<l.length;g++)l[g].to<=s&&l.splice(g--,1);for(;o<i.length&&i[o].from<=s&&i[o].to>s;)l.push(i[o++]);let p=s+f.nodeSize;if(f.isText){let g=p;o<i.length&&i[o].from<g&&(g=i[o].from);for(let y=0;y<l.length;y++)l[y].to<g&&(g=l[y].to);g<p&&(a=f.cut(g-s),f=f.cut(0,g-s),p=g,h=-1)}else for(;o<i.length&&i[o].to<p;)o++;let m=f.isInline&&!f.isLeaf?l.filter(g=>!g.inline):l.slice();r(f,m,e.forChild(s,f),h),s=p}}function aa(n){if(n.nodeName=="UL"||n.nodeName=="OL"){let e=n.style.cssText;n.style.cssText=e+"; list-style: square !important",window.getComputedStyle(n).listStyle,n.style.cssText=e}}function ca(n,e,t,r){for(let i=0,s=0;i<n.childCount&&s<=r;){let o=n.child(i++),l=s;if(s+=o.nodeSize,!o.isText)continue;let a=o.text;for(;i<n.childCount;){let c=n.child(i++);if(s+=c.nodeSize,!c.isText)break;a+=c.text}if(s>=t){if(s>=r&&a.slice(r-e.length-l,r-l)==e)return r-e.length;let c=l<r?a.lastIndexOf(e,r-l-1):-1;if(c>=0&&c+e.length+l>=t)return l+c;if(t==r&&a.length>=r+e.length-l&&a.slice(r-l,r-l+e.length)==e)return r}}return-1}function er(n,e,t,r,i){let s=[];for(let o=0,l=0;o<n.length;o++){let a=n[o],c=l,u=l+=a.size;c>=t||u<=e?s.push(a):(c<e&&s.push(a.slice(0,e-c,r)),i&&(s.push(i),i=void 0),u>t&&s.push(a.slice(t-c,a.size,r)))}return s}function mr(n,e=null){let t=n.domSelectionRange(),r=n.state.doc;if(!t.focusNode)return null;let i=n.docView.nearestDesc(t.focusNode),s=i&&i.size==0,o=n.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(o<0)return null;let l=r.resolve(o),a,c;if(fn(t)){for(a=o;i&&!i.node;)i=i.parent;let d=i.node;if(i&&d.isAtom&&x.isSelectable(d)&&i.parent&&!(d.isInline&&Bl(t.focusNode,t.focusOffset,i.dom))){let f=i.posBefore;c=new x(o==f?l:r.resolve(f))}}else{if(t instanceof n.dom.ownerDocument.defaultView.Selection&&t.rangeCount>1){let d=o,f=o;for(let h=0;h<t.rangeCount;h++){let p=t.getRangeAt(h);d=Math.min(d,n.docView.posFromDOM(p.startContainer,p.startOffset,1)),f=Math.max(f,n.docView.posFromDOM(p.endContainer,p.endOffset,-1))}if(d<0)return null;[a,o]=f==n.state.selection.anchor?[f,d]:[d,f],l=r.resolve(o)}else a=n.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(a<0)return null}let u=r.resolve(a);if(!c){let d=e=="pointer"||n.state.selection.head<l.pos&&!s?1:-1;c=gr(n,u,l,d)}return c}function Ws(n){return n.editable?n.hasFocus():Js(n)&&document.activeElement&&document.activeElement.contains(n.dom)}function Se(n,e=!1){let t=n.state.selection;if(js(n,t),!!Ws(n)){if(!e&&n.input.mouseDown&&n.input.mouseDown.allowDefault&&q){let r=n.domSelectionRange(),i=n.domObserver.currentSelection;if(r.anchorNode&&i.anchorNode&&Ye(r.anchorNode,r.anchorOffset,i.anchorNode,i.anchorOffset)){n.input.mouseDown.delayedSelectionSync=!0,n.domObserver.setCurSelection();return}}if(n.domObserver.disconnectSelection(),n.cursorWrapper)da(n);else{let{anchor:r,head:i}=t,s,o;ci&&!(t instanceof w)&&(t.$from.parent.inlineContent||(s=ui(n,t.from)),!t.empty&&!t.$from.parent.inlineContent&&(o=ui(n,t.to))),n.docView.setSelection(r,i,n,e),ci&&(s&&di(s),o&&di(o)),t.visible?n.dom.classList.remove("ProseMirror-hideselection"):(n.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&ua(n))}n.domObserver.setCurSelection(),n.domObserver.connectSelection()}}const ci=U||q&&As<63;function ui(n,e){let{node:t,offset:r}=n.docView.domFromPos(e,0),i=r<t.childNodes.length?t.childNodes[r]:null,s=r?t.childNodes[r-1]:null;if(U&&i&&i.contentEditable=="false")return An(i);if((!i||i.contentEditable=="false")&&(!s||s.contentEditable=="false")){if(i)return An(i);if(s)return An(s)}}function An(n){return n.contentEditable="true",U&&n.draggable&&(n.draggable=!1,n.wasDraggable=!0),n}function di(n){n.contentEditable="false",n.wasDraggable&&(n.draggable=!0,n.wasDraggable=null)}function ua(n){let e=n.dom.ownerDocument;e.removeEventListener("selectionchange",n.input.hideSelectionGuard);let t=n.domSelectionRange(),r=t.anchorNode,i=t.anchorOffset;e.addEventListener("selectionchange",n.input.hideSelectionGuard=()=>{(t.anchorNode!=r||t.anchorOffset!=i)&&(e.removeEventListener("selectionchange",n.input.hideSelectionGuard),setTimeout(()=>{(!Ws(n)||n.state.selection.visible)&&n.dom.classList.remove("ProseMirror-hideselection")},20))})}function da(n){let e=n.domSelection(),t=document.createRange();if(!e)return;let r=n.cursorWrapper.dom,i=r.nodeName=="IMG";i?t.setStart(r.parentNode,W(r)+1):t.setStart(r,0),t.collapse(!0),e.removeAllRanges(),e.addRange(t),!i&&!n.state.selection.visible&&ee&&Ae<=11&&(r.disabled=!0,r.disabled=!1)}function js(n,e){if(e instanceof x){let t=n.docView.descAt(e.from);t!=n.lastSelectedViewDesc&&(fi(n),t&&t.selectNode(),n.lastSelectedViewDesc=t)}else fi(n)}function fi(n){n.lastSelectedViewDesc&&(n.lastSelectedViewDesc.parent&&n.lastSelectedViewDesc.deselectNode(),n.lastSelectedViewDesc=void 0)}function gr(n,e,t,r){return n.someProp("createSelectionBetween",i=>i(n,e,t))||w.between(e,t,r)}function hi(n){return n.editable&&!n.hasFocus()?!1:Js(n)}function Js(n){let e=n.domSelectionRange();if(!e.anchorNode)return!1;try{return n.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(n.editable||n.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function fa(n){let e=n.docView.domFromPos(n.state.selection.anchor,0),t=n.domSelectionRange();return Ye(e.node,e.offset,t.anchorNode,t.anchorOffset)}function tr(n,e){let{$anchor:t,$head:r}=n.selection,i=e>0?t.max(r):t.min(r),s=i.parent.inlineContent?i.depth?n.doc.resolve(e>0?i.after():i.before()):null:i;return s&&O.findFrom(s,e)}function Me(n,e){return n.dispatch(n.state.tr.setSelection(e).scrollIntoView()),!0}function pi(n,e,t){let r=n.state.selection;if(r instanceof w)if(t.indexOf("s")>-1){let{$head:i}=r,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText||!s.isLeaf)return!1;let o=n.state.doc.resolve(i.pos+s.nodeSize*(e<0?-1:1));return Me(n,new w(r.$anchor,o))}else if(r.empty){if(n.endOfTextblock(e>0?"forward":"backward")){let i=tr(n.state,e);return i&&i instanceof x?Me(n,i):!1}else if(!(ie&&t.indexOf("m")>-1)){let i=r.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter,o;if(!s||s.isText)return!1;let l=e<0?i.pos-s.nodeSize:i.pos;return s.isAtom||(o=n.docView.descAt(l))&&!o.contentDOM?x.isSelectable(s)?Me(n,new x(e<0?n.state.doc.resolve(i.pos-s.nodeSize):i)):Pt?Me(n,new w(n.state.doc.resolve(e<0?l:l+s.nodeSize))):!1:!1}}else return!1;else{if(r instanceof x&&r.node.isInline)return Me(n,new w(e>0?r.$to:r.$from));{let i=tr(n.state,e);return i?Me(n,i):!1}}}function en(n){return n.nodeType==3?n.nodeValue.length:n.childNodes.length}function Mt(n,e){let t=n.pmViewDesc;return t&&t.size==0&&(e<0||n.nextSibling||n.nodeName!="BR")}function et(n,e){return e<0?ha(n):pa(n)}function ha(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i,s,o=!1;for(de&&t.nodeType==1&&r<en(t)&&Mt(t.childNodes[r],-1)&&(o=!0);;)if(r>0){if(t.nodeType!=1)break;{let l=t.childNodes[r-1];if(Mt(l,-1))i=t,s=--r;else if(l.nodeType==3)t=l,r=t.nodeValue.length;else break}}else{if(qs(t))break;{let l=t.previousSibling;for(;l&&Mt(l,-1);)i=t.parentNode,s=W(l),l=l.previousSibling;if(l)t=l,r=en(t);else{if(t=t.parentNode,t==n.dom)break;r=0}}}o?nr(n,t,r):i&&nr(n,i,s)}function pa(n){let e=n.domSelectionRange(),t=e.focusNode,r=e.focusOffset;if(!t)return;let i=en(t),s,o;for(;;)if(r<i){if(t.nodeType!=1)break;let l=t.childNodes[r];if(Mt(l,1))s=t,o=++r;else break}else{if(qs(t))break;{let l=t.nextSibling;for(;l&&Mt(l,1);)s=l.parentNode,o=W(l)+1,l=l.nextSibling;if(l)t=l,r=0,i=en(t);else{if(t=t.parentNode,t==n.dom)break;r=i=0}}}s&&nr(n,s,o)}function qs(n){let e=n.pmViewDesc;return e&&e.node&&e.node.isBlock}function ma(n,e){for(;n&&e==n.childNodes.length&&!Rt(n);)e=W(n)+1,n=n.parentNode;for(;n&&e<n.childNodes.length;){let t=n.childNodes[e];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=0}}function ga(n,e){for(;n&&!e&&!Rt(n);)e=W(n),n=n.parentNode;for(;n&&e;){let t=n.childNodes[e-1];if(t.nodeType==3)return t;if(t.nodeType==1&&t.contentEditable=="false")break;n=t,e=n.childNodes.length}}function nr(n,e,t){if(e.nodeType!=3){let s,o;(o=ma(e,t))?(e=o,t=0):(s=ga(e,t))&&(e=s,t=s.nodeValue.length)}let r=n.domSelection();if(!r)return;if(fn(r)){let s=document.createRange();s.setEnd(e,t),s.setStart(e,t),r.removeAllRanges(),r.addRange(s)}else r.extend&&r.extend(e,t);n.domObserver.setCurSelection();let{state:i}=n;setTimeout(()=>{n.state==i&&Se(n)},50)}function mi(n,e){let t=n.state.doc.resolve(e);if(!(q||Fl)&&t.parent.inlineContent){let i=n.coordsAtPos(e);if(e>t.start()){let s=n.coordsAtPos(e-1),o=(s.top+s.bottom)/2;if(o>i.top&&o<i.bottom&&Math.abs(s.left-i.left)>1)return s.left<i.left?"ltr":"rtl"}if(e<t.end()){let s=n.coordsAtPos(e+1),o=(s.top+s.bottom)/2;if(o>i.top&&o<i.bottom&&Math.abs(s.left-i.left)>1)return s.left>i.left?"ltr":"rtl"}}return getComputedStyle(n.dom).direction=="rtl"?"rtl":"ltr"}function gi(n,e,t){let r=n.state.selection;if(r instanceof w&&!r.empty||t.indexOf("s")>-1||ie&&t.indexOf("m")>-1)return!1;let{$from:i,$to:s}=r;if(!i.parent.inlineContent||n.endOfTextblock(e<0?"up":"down")){let o=tr(n.state,e);if(o&&o instanceof x)return Me(n,o)}if(!i.parent.inlineContent){let o=e<0?i:s,l=r instanceof ae?O.near(o,e):O.findFrom(o,e);return l?Me(n,l):!1}return!1}function yi(n,e){if(!(n.state.selection instanceof w))return!0;let{$head:t,$anchor:r,empty:i}=n.state.selection;if(!t.sameParent(r))return!0;if(!i)return!1;if(n.endOfTextblock(e>0?"forward":"backward"))return!0;let s=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(s&&!s.isText){let o=n.state.tr;return e<0?o.delete(t.pos-s.nodeSize,t.pos):o.delete(t.pos,t.pos+s.nodeSize),n.dispatch(o),!0}return!1}function bi(n,e,t){n.domObserver.stop(),e.contentEditable=t,n.domObserver.start()}function ya(n){if(!U||n.state.selection.$head.parentOffset>0)return!1;let{focusNode:e,focusOffset:t}=n.domSelectionRange();if(e&&e.nodeType==1&&t==0&&e.firstChild&&e.firstChild.contentEditable=="false"){let r=e.firstChild;bi(n,r,"true"),setTimeout(()=>bi(n,r,"false"),20)}return!1}function ba(n){let e="";return n.ctrlKey&&(e+="c"),n.metaKey&&(e+="m"),n.altKey&&(e+="a"),n.shiftKey&&(e+="s"),e}function ka(n,e){let t=e.keyCode,r=ba(e);if(t==8||ie&&t==72&&r=="c")return yi(n,-1)||et(n,-1);if(t==46&&!e.shiftKey||ie&&t==68&&r=="c")return yi(n,1)||et(n,1);if(t==13||t==27)return!0;if(t==37||ie&&t==66&&r=="c"){let i=t==37?mi(n,n.state.selection.from)=="ltr"?-1:1:-1;return pi(n,i,r)||et(n,i)}else if(t==39||ie&&t==70&&r=="c"){let i=t==39?mi(n,n.state.selection.from)=="ltr"?1:-1:1;return pi(n,i,r)||et(n,i)}else{if(t==38||ie&&t==80&&r=="c")return gi(n,-1,r)||et(n,-1);if(t==40||ie&&t==78&&r=="c")return ya(n)||gi(n,1,r)||et(n,1);if(r==(ie?"m":"c")&&(t==66||t==73||t==89||t==90))return!0}return!1}function yr(n,e){n.someProp("transformCopied",h=>{e=h(e,n)});let t=[],{content:r,openStart:i,openEnd:s}=e;for(;i>1&&s>1&&r.childCount==1&&r.firstChild.childCount==1;){i--,s--;let h=r.firstChild;t.push(h.type.name,h.attrs!=h.type.defaultAttrs?h.attrs:null),r=h.content}let o=n.someProp("clipboardSerializer")||Ze.fromSchema(n.state.schema),l=Xs(),a=l.createElement("div");a.appendChild(o.serializeFragment(r,{document:l}));let c=a.firstChild,u,d=0;for(;c&&c.nodeType==1&&(u=Ys[c.nodeName.toLowerCase()]);){for(let h=u.length-1;h>=0;h--){let p=l.createElement(u[h]);for(;a.firstChild;)p.appendChild(a.firstChild);a.appendChild(p),d++}c=a.firstChild}c&&c.nodeType==1&&c.setAttribute("data-pm-slice",`${i} ${s}${d?` -${d}`:""} ${JSON.stringify(t)}`);let f=n.someProp("clipboardTextSerializer",h=>h(e,n))||e.content.textBetween(0,e.content.size,`

`);return{dom:a,text:f,slice:e}}function Ks(n,e,t,r,i){let s=i.parent.type.spec.code,o,l;if(!t&&!e)return null;let a=e&&(r||s||!t);if(a){if(n.someProp("transformPastedText",f=>{e=f(e,s||r,n)}),s)return e?new k(b.from(n.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):k.empty;let d=n.someProp("clipboardTextParser",f=>f(e,i,r,n));if(d)l=d;else{let f=i.marks(),{schema:h}=n.state,p=Ze.fromSchema(h);o=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=o.appendChild(document.createElement("p"));m&&g.appendChild(p.serializeNode(h.text(m,f)))})}}else n.someProp("transformPastedHTML",d=>{t=d(t,n)}),o=Ca(t),Pt&&wa(o);let c=o&&o.querySelector("[data-pm-slice]"),u=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(u&&u[3])for(let d=+u[3];d>0;d--){let f=o.firstChild;for(;f&&f.nodeType!=1;)f=f.nextSibling;if(!f)break;o=f}if(l||(l=(n.someProp("clipboardParser")||n.someProp("domParser")||ve.fromSchema(n.state.schema)).parseSlice(o,{preserveWhitespace:!!(a||u),context:i,ruleFromNode(f){return f.nodeName=="BR"&&!f.nextSibling&&f.parentNode&&!Sa.test(f.parentNode.nodeName)?{ignore:!0}:null}})),u)l=Ta(ki(l,+u[1],+u[2]),u[4]);else if(l=k.maxOpen(xa(l.content,i),!0),l.openStart||l.openEnd){let d=0,f=0;for(let h=l.content.firstChild;d<l.openStart&&!h.type.spec.isolating;d++,h=h.firstChild);for(let h=l.content.lastChild;f<l.openEnd&&!h.type.spec.isolating;f++,h=h.lastChild);l=ki(l,d,f)}return n.someProp("transformPasted",d=>{l=d(l,n)}),l}const Sa=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function xa(n,e){if(n.childCount<2)return n;for(let t=e.depth;t>=0;t--){let i=e.node(t).contentMatchAt(e.index(t)),s,o=[];if(n.forEach(l=>{if(!o)return;let a=i.findWrapping(l.type),c;if(!a)return o=null;if(c=o.length&&s.length&&Us(a,s,l,o[o.length-1],0))o[o.length-1]=c;else{o.length&&(o[o.length-1]=Gs(o[o.length-1],s.length));let u=_s(l,a);o.push(u),i=i.matchType(u.type),s=a}}),o)return b.from(o)}return n}function _s(n,e,t=0){for(let r=e.length-1;r>=t;r--)n=e[r].create(null,b.from(n));return n}function Us(n,e,t,r,i){if(i<n.length&&i<e.length&&n[i]==e[i]){let s=Us(n,e,t,r.lastChild,i+1);if(s)return r.copy(r.content.replaceChild(r.childCount-1,s));if(r.contentMatchAt(r.childCount).matchType(i==n.length-1?t.type:n[i+1]))return r.copy(r.content.append(b.from(_s(t,n,i+1))))}}function Gs(n,e){if(e==0)return n;let t=n.content.replaceChild(n.childCount-1,Gs(n.lastChild,e-1)),r=n.contentMatchAt(n.childCount).fillBefore(b.empty,!0);return n.copy(t.append(r))}function rr(n,e,t,r,i,s){let o=e<0?n.firstChild:n.lastChild,l=o.content;return n.childCount>1&&(s=0),i<r-1&&(l=rr(l,e,t,r,i+1,s)),i>=t&&(l=e<0?o.contentMatchAt(0).fillBefore(l,s<=i).append(l):l.append(o.contentMatchAt(o.childCount).fillBefore(b.empty,!0))),n.replaceChild(e<0?0:n.childCount-1,o.copy(l))}function ki(n,e,t){return e<n.openStart&&(n=new k(rr(n.content,-1,e,n.openStart,0,n.openEnd),e,n.openEnd)),t<n.openEnd&&(n=new k(rr(n.content,1,t,n.openEnd,0,0),n.openStart,t)),n}const Ys={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let Si=null;function Xs(){return Si||(Si=document.implementation.createHTMLDocument("title"))}let Dn=null;function Ma(n){let e=window.trustedTypes;return e?(Dn||(Dn=e.defaultPolicy||e.createPolicy("ProseMirrorClipboard",{createHTML:t=>t})),Dn.createHTML(n)):n}function Ca(n){let e=/^(\s*<meta [^>]*>)*/.exec(n);e&&(n=n.slice(e[0].length));let t=Xs().createElement("div"),r=/<([a-z][^>\s]+)/i.exec(n),i;if((i=r&&Ys[r[1].toLowerCase()])&&(n=i.map(s=>"<"+s+">").join("")+n+i.map(s=>"</"+s+">").reverse().join("")),t.innerHTML=Ma(n),i)for(let s=0;s<i.length;s++)t=t.querySelector(i[s])||t;return t}function wa(n){let e=n.querySelectorAll(q?"span:not([class]):not([style])":"span.Apple-converted-space");for(let t=0;t<e.length;t++){let r=e[t];r.childNodes.length==1&&r.textContent==" "&&r.parentNode&&r.parentNode.replaceChild(n.ownerDocument.createTextNode(" "),r)}}function Ta(n,e){if(!n.size)return n;let t=n.content.firstChild.type.schema,r;try{r=JSON.parse(e)}catch{return n}let{content:i,openStart:s,openEnd:o}=n;for(let l=r.length-2;l>=0;l-=2){let a=t.nodes[r[l]];if(!a||a.hasRequiredAttrs())break;i=b.from(a.create(r[l+1],i)),s++,o++}return new k(i,s,o)}const G={},Y={},Oa={touchstart:!0,touchmove:!0};class Ea{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function Na(n){for(let e in G){let t=G[e];n.dom.addEventListener(e,n.input.eventHandlers[e]=r=>{Aa(n,r)&&!br(n,r)&&(n.editable||!(r.type in Y))&&t(n,r)},Oa[e]?{passive:!0}:void 0)}U&&n.dom.addEventListener("input",()=>null),ir(n)}function Ee(n,e){n.input.lastSelectionOrigin=e,n.input.lastSelectionTime=Date.now()}function va(n){n.domObserver.stop();for(let e in n.input.eventHandlers)n.dom.removeEventListener(e,n.input.eventHandlers[e]);clearTimeout(n.input.composingTimeout),clearTimeout(n.input.lastIOSEnterFallbackTimeout)}function ir(n){n.someProp("handleDOMEvents",e=>{for(let t in e)n.input.eventHandlers[t]||n.dom.addEventListener(t,n.input.eventHandlers[t]=r=>br(n,r))})}function br(n,e){return n.someProp("handleDOMEvents",t=>{let r=t[e.type];return r?r(n,e)||e.defaultPrevented:!1})}function Aa(n,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=n.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function Da(n,e){!br(n,e)&&G[e.type]&&(n.editable||!(e.type in Y))&&G[e.type](n,e)}Y.keydown=(n,e)=>{let t=e;if(n.input.shiftKey=t.keyCode==16||t.shiftKey,!Qs(n,t)&&(n.input.lastKeyCode=t.keyCode,n.input.lastKeyCodeTime=Date.now(),!(ke&&q&&t.keyCode==13)))if(t.keyCode!=229&&n.domObserver.forceFlush(),ut&&t.keyCode==13&&!t.ctrlKey&&!t.altKey&&!t.metaKey){let r=Date.now();n.input.lastIOSEnter=r,n.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{n.input.lastIOSEnter==r&&(n.someProp("handleKeyDown",i=>i(n,Fe(13,"Enter"))),n.input.lastIOSEnter=0)},200)}else n.someProp("handleKeyDown",r=>r(n,t))||ka(n,t)?t.preventDefault():Ee(n,"key")};Y.keyup=(n,e)=>{e.keyCode==16&&(n.input.shiftKey=!1)};Y.keypress=(n,e)=>{let t=e;if(Qs(n,t)||!t.charCode||t.ctrlKey&&!t.altKey||ie&&t.metaKey)return;if(n.someProp("handleKeyPress",i=>i(n,t))){t.preventDefault();return}let r=n.state.selection;if(!(r instanceof w)||!r.$from.sameParent(r.$to)){let i=String.fromCharCode(t.charCode),s=()=>n.state.tr.insertText(i).scrollIntoView();!/[\r\n]/.test(i)&&!n.someProp("handleTextInput",o=>o(n,r.$from.pos,r.$to.pos,i,s))&&n.dispatch(s()),t.preventDefault()}};function pn(n){return{left:n.clientX,top:n.clientY}}function Ia(n,e){let t=e.x-n.clientX,r=e.y-n.clientY;return t*t+r*r<100}function kr(n,e,t,r,i){if(r==-1)return!1;let s=n.state.doc.resolve(r);for(let o=s.depth+1;o>0;o--)if(n.someProp(e,l=>o>s.depth?l(n,t,s.nodeAfter,s.before(o),i,!0):l(n,t,s.node(o),s.before(o),i,!1)))return!0;return!1}function lt(n,e,t){if(n.focused||n.focus(),n.state.selection.eq(e))return;let r=n.state.tr.setSelection(e);t=="pointer"&&r.setMeta("pointer",!0),n.dispatch(r)}function Ra(n,e){if(e==-1)return!1;let t=n.state.doc.resolve(e),r=t.nodeAfter;return r&&r.isAtom&&x.isSelectable(r)?(lt(n,new x(t),"pointer"),!0):!1}function Pa(n,e){if(e==-1)return!1;let t=n.state.selection,r,i;t instanceof x&&(r=t.node);let s=n.state.doc.resolve(e);for(let o=s.depth+1;o>0;o--){let l=o>s.depth?s.nodeAfter:s.node(o);if(x.isSelectable(l)){r&&t.$from.depth>0&&o>=t.$from.depth&&s.before(t.$from.depth+1)==t.$from.pos?i=s.before(t.$from.depth):i=s.before(o);break}}return i!=null?(lt(n,x.create(n.state.doc,i),"pointer"),!0):!1}function Ba(n,e,t,r,i){return kr(n,"handleClickOn",e,t,r)||n.someProp("handleClick",s=>s(n,e,r))||(i?Pa(n,t):Ra(n,t))}function za(n,e,t,r){return kr(n,"handleDoubleClickOn",e,t,r)||n.someProp("handleDoubleClick",i=>i(n,e,r))}function La(n,e,t,r){return kr(n,"handleTripleClickOn",e,t,r)||n.someProp("handleTripleClick",i=>i(n,e,r))||Fa(n,t,r)}function Fa(n,e,t){if(t.button!=0)return!1;let r=n.state.doc;if(e==-1)return r.inlineContent?(lt(n,w.create(r,0,r.content.size),"pointer"),!0):!1;let i=r.resolve(e);for(let s=i.depth+1;s>0;s--){let o=s>i.depth?i.nodeAfter:i.node(s),l=i.before(s);if(o.inlineContent)lt(n,w.create(r,l+1,l+1+o.content.size),"pointer");else if(x.isSelectable(o))lt(n,x.create(r,l),"pointer");else continue;return!0}}function Sr(n){return tn(n)}const Zs=ie?"metaKey":"ctrlKey";G.mousedown=(n,e)=>{let t=e;n.input.shiftKey=t.shiftKey;let r=Sr(n),i=Date.now(),s="singleClick";i-n.input.lastClick.time<500&&Ia(t,n.input.lastClick)&&!t[Zs]&&n.input.lastClick.button==t.button&&(n.input.lastClick.type=="singleClick"?s="doubleClick":n.input.lastClick.type=="doubleClick"&&(s="tripleClick")),n.input.lastClick={time:i,x:t.clientX,y:t.clientY,type:s,button:t.button};let o=n.posAtCoords(pn(t));o&&(s=="singleClick"?(n.input.mouseDown&&n.input.mouseDown.done(),n.input.mouseDown=new Va(n,o,t,!!r)):(s=="doubleClick"?za:La)(n,o.pos,o.inside,t)?t.preventDefault():Ee(n,"pointer"))};class Va{constructor(e,t,r,i){this.view=e,this.pos=t,this.event=r,this.flushed=i,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!r[Zs],this.allowDefault=r.shiftKey;let s,o;if(t.inside>-1)s=e.state.doc.nodeAt(t.inside),o=t.inside;else{let u=e.state.doc.resolve(t.pos);s=u.parent,o=u.depth?u.before():0}const l=i?null:r.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a&&a.dom.nodeType==1?a.dom:null;let{selection:c}=e.state;(r.button==0&&s.type.spec.draggable&&s.type.spec.selectable!==!1||c instanceof x&&c.from<=o&&c.to>o)&&(this.mightDrag={node:s,pos:o,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&de&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),Ee(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>Se(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(pn(e))),this.updateAllowDefault(e),this.allowDefault||!t?Ee(this.view,"pointer"):Ba(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||U&&this.mightDrag&&!this.mightDrag.node.isAtom||q&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(lt(this.view,O.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):Ee(this.view,"pointer")}move(e){this.updateAllowDefault(e),Ee(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}G.touchstart=n=>{n.input.lastTouch=Date.now(),Sr(n),Ee(n,"pointer")};G.touchmove=n=>{n.input.lastTouch=Date.now(),Ee(n,"pointer")};G.contextmenu=n=>Sr(n);function Qs(n,e){return n.composing?!0:U&&Math.abs(e.timeStamp-n.input.compositionEndedAt)<500?(n.input.compositionEndedAt=-2e8,!0):!1}const $a=ke?5e3:-1;Y.compositionstart=Y.compositionupdate=n=>{if(!n.composing){n.domObserver.flush();let{state:e}=n,t=e.selection.$to;if(e.selection instanceof w&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(r=>r.type.spec.inclusive===!1)))n.markCursor=n.state.storedMarks||t.marks(),tn(n,!0),n.markCursor=null;else if(tn(n,!e.selection.empty),de&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let r=n.domSelectionRange();for(let i=r.focusNode,s=r.focusOffset;i&&i.nodeType==1&&s!=0;){let o=s<0?i.lastChild:i.childNodes[s-1];if(!o)break;if(o.nodeType==3){let l=n.domSelection();l&&l.collapse(o,o.nodeValue.length);break}else i=o,s=-1}}n.input.composing=!0}eo(n,$a)};Y.compositionend=(n,e)=>{n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=e.timeStamp,n.input.compositionPendingChanges=n.domObserver.pendingRecords().length?n.input.compositionID:0,n.input.compositionNode=null,n.input.compositionPendingChanges&&Promise.resolve().then(()=>n.domObserver.flush()),n.input.compositionID++,eo(n,20))};function eo(n,e){clearTimeout(n.input.composingTimeout),e>-1&&(n.input.composingTimeout=setTimeout(()=>tn(n),e))}function to(n){for(n.composing&&(n.input.composing=!1,n.input.compositionEndedAt=Wa());n.input.compositionNodes.length>0;)n.input.compositionNodes.pop().markParentsDirty()}function Ha(n){let e=n.domSelectionRange();if(!e.focusNode)return null;let t=Rl(e.focusNode,e.focusOffset),r=Pl(e.focusNode,e.focusOffset);if(t&&r&&t!=r){let i=r.pmViewDesc,s=n.domObserver.lastChangedTextNode;if(t==s||r==s)return s;if(!i||!i.isText(r.nodeValue))return r;if(n.input.compositionNode==r){let o=t.pmViewDesc;if(!(!o||!o.isText(t.nodeValue)))return r}}return t||r}function Wa(){let n=document.createEvent("Event");return n.initEvent("event",!0,!0),n.timeStamp}function tn(n,e=!1){if(!(ke&&n.domObserver.flushingSoon>=0)){if(n.domObserver.forceFlush(),to(n),e||n.docView&&n.docView.dirty){let t=mr(n),r=n.state.selection;return t&&!t.eq(r)?n.dispatch(n.state.tr.setSelection(t)):(n.markCursor||e)&&!r.$from.node(r.$from.sharedDepth(r.to)).inlineContent?n.dispatch(n.state.tr.deleteSelection()):n.updateState(n.state),!0}return!1}}function ja(n,e){if(!n.dom.parentNode)return;let t=n.dom.parentNode.appendChild(document.createElement("div"));t.appendChild(e),t.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(e),n.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{t.parentNode&&t.parentNode.removeChild(t),n.focus()},50)}const Et=ee&&Ae<15||ut&&Vl<604;G.copy=Y.cut=(n,e)=>{let t=e,r=n.state.selection,i=t.type=="cut";if(r.empty)return;let s=Et?null:t.clipboardData,o=r.content(),{dom:l,text:a}=yr(n,o);s?(t.preventDefault(),s.clearData(),s.setData("text/html",l.innerHTML),s.setData("text/plain",a)):ja(n,l),i&&n.dispatch(n.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))};function Ja(n){return n.openStart==0&&n.openEnd==0&&n.content.childCount==1?n.content.firstChild:null}function qa(n,e){if(!n.dom.parentNode)return;let t=n.input.shiftKey||n.state.selection.$from.parent.type.spec.code,r=n.dom.parentNode.appendChild(document.createElement(t?"textarea":"div"));t||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=n.input.shiftKey&&n.input.lastKeyCode!=45;setTimeout(()=>{n.focus(),r.parentNode&&r.parentNode.removeChild(r),t?Nt(n,r.value,null,i,e):Nt(n,r.textContent,r.innerHTML,i,e)},50)}function Nt(n,e,t,r,i){let s=Ks(n,e,t,r,n.state.selection.$from);if(n.someProp("handlePaste",a=>a(n,i,s||k.empty)))return!0;if(!s)return!1;let o=Ja(s),l=o?n.state.tr.replaceSelectionWith(o,r):n.state.tr.replaceSelection(s);return n.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function no(n){let e=n.getData("text/plain")||n.getData("Text");if(e)return e;let t=n.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}Y.paste=(n,e)=>{let t=e;if(n.composing&&!ke)return;let r=Et?null:t.clipboardData,i=n.input.shiftKey&&n.input.lastKeyCode!=45;r&&Nt(n,no(r),r.getData("text/html"),i,t)?t.preventDefault():qa(n,t)};class ro{constructor(e,t,r){this.slice=e,this.move=t,this.node=r}}const Ka=ie?"altKey":"ctrlKey";function io(n,e){let t=n.someProp("dragCopies",r=>!r(e));return t??!e[Ka]}G.dragstart=(n,e)=>{let t=e,r=n.input.mouseDown;if(r&&r.done(),!t.dataTransfer)return;let i=n.state.selection,s=i.empty?null:n.posAtCoords(pn(t)),o;if(!(s&&s.pos>=i.from&&s.pos<=(i instanceof x?i.to-1:i.to))){if(r&&r.mightDrag)o=x.create(n.state.doc,r.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let d=n.docView.nearestDesc(t.target,!0);d&&d.node.type.spec.draggable&&d!=n.docView&&(o=x.create(n.state.doc,d.posBefore))}}let l=(o||n.state.selection).content(),{dom:a,text:c,slice:u}=yr(n,l);(!t.dataTransfer.files.length||!q||As>120)&&t.dataTransfer.clearData(),t.dataTransfer.setData(Et?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",Et||t.dataTransfer.setData("text/plain",c),n.dragging=new ro(u,io(n,t),o)};G.dragend=n=>{let e=n.dragging;window.setTimeout(()=>{n.dragging==e&&(n.dragging=null)},50)};Y.dragover=Y.dragenter=(n,e)=>e.preventDefault();Y.drop=(n,e)=>{let t=e,r=n.dragging;if(n.dragging=null,!t.dataTransfer)return;let i=n.posAtCoords(pn(t));if(!i)return;let s=n.state.doc.resolve(i.pos),o=r&&r.slice;o?n.someProp("transformPasted",p=>{o=p(o,n)}):o=Ks(n,no(t.dataTransfer),Et?null:t.dataTransfer.getData("text/html"),!1,s);let l=!!(r&&io(n,t));if(n.someProp("handleDrop",p=>p(n,t,o||k.empty,l))){t.preventDefault();return}if(!o)return;t.preventDefault();let a=o?xs(n.state.doc,s.pos,o):s.pos;a==null&&(a=s.pos);let c=n.state.tr;if(l){let{node:p}=r;p?p.replace(c):c.deleteSelection()}let u=c.mapping.map(a),d=o.openStart==0&&o.openEnd==0&&o.content.childCount==1,f=c.doc;if(d?c.replaceRangeWith(u,u,o.content.firstChild):c.replaceRange(u,u,o),c.doc.eq(f))return;let h=c.doc.resolve(u);if(d&&x.isSelectable(o.content.firstChild)&&h.nodeAfter&&h.nodeAfter.sameMarkup(o.content.firstChild))c.setSelection(new x(h));else{let p=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,y,M)=>p=M),c.setSelection(gr(n,h,c.doc.resolve(p)))}n.focus(),n.dispatch(c.setMeta("uiEvent","drop"))};G.focus=n=>{n.input.lastFocus=Date.now(),n.focused||(n.domObserver.stop(),n.dom.classList.add("ProseMirror-focused"),n.domObserver.start(),n.focused=!0,setTimeout(()=>{n.docView&&n.hasFocus()&&!n.domObserver.currentSelection.eq(n.domSelectionRange())&&Se(n)},20))};G.blur=(n,e)=>{let t=e;n.focused&&(n.domObserver.stop(),n.dom.classList.remove("ProseMirror-focused"),n.domObserver.start(),t.relatedTarget&&n.dom.contains(t.relatedTarget)&&n.domObserver.currentSelection.clear(),n.focused=!1)};G.beforeinput=(n,e)=>{if(q&&ke&&e.inputType=="deleteContentBackward"){n.domObserver.flushSoon();let{domChangeCount:r}=n.input;setTimeout(()=>{if(n.input.domChangeCount!=r||(n.dom.blur(),n.focus(),n.someProp("handleKeyDown",s=>s(n,Fe(8,"Backspace")))))return;let{$cursor:i}=n.state.selection;i&&i.pos>0&&n.dispatch(n.state.tr.delete(i.pos-1,i.pos).scrollIntoView())},50)}};for(let n in Y)G[n]=Y[n];function vt(n,e){if(n==e)return!0;for(let t in n)if(n[t]!==e[t])return!1;for(let t in e)if(!(t in n))return!1;return!0}class nn{constructor(e,t){this.toDOM=e,this.spec=t||qe,this.side=this.spec.side||0}map(e,t,r,i){let{pos:s,deleted:o}=e.mapResult(t.from+i,this.side<0?-1:1);return o?null:new le(s-r,s-r,this)}valid(){return!0}eq(e){return this==e||e instanceof nn&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&vt(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class Ie{constructor(e,t){this.attrs=e,this.spec=t||qe}map(e,t,r,i){let s=e.map(t.from+i,this.spec.inclusiveStart?-1:1)-r,o=e.map(t.to+i,this.spec.inclusiveEnd?1:-1)-r;return s>=o?null:new le(s,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof Ie&&vt(this.attrs,e.attrs)&&vt(this.spec,e.spec)}static is(e){return e.type instanceof Ie}destroy(){}}class xr{constructor(e,t){this.attrs=e,this.spec=t||qe}map(e,t,r,i){let s=e.mapResult(t.from+i,1);if(s.deleted)return null;let o=e.mapResult(t.to+i,-1);return o.deleted||o.pos<=s.pos?null:new le(s.pos-r,o.pos-r,this)}valid(e,t){let{index:r,offset:i}=e.content.findIndex(t.from),s;return i==t.from&&!(s=e.child(r)).isText&&i+s.nodeSize==t.to}eq(e){return this==e||e instanceof xr&&vt(this.attrs,e.attrs)&&vt(this.spec,e.spec)}destroy(){}}class le{constructor(e,t,r){this.from=e,this.to=t,this.type=r}copy(e,t){return new le(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,r){return this.type.map(e,this,t,r)}static widget(e,t,r){return new le(e,e,new nn(t,r))}static inline(e,t,r,i){return new le(e,t,new Ie(r,i))}static node(e,t,r,i){return new le(e,t,new xr(r,i))}get spec(){return this.type.spec}get inline(){return this.type instanceof Ie}get widget(){return this.type instanceof nn}}const nt=[],qe={};class z{constructor(e,t){this.local=e.length?e:nt,this.children=t.length?t:nt}static create(e,t){return t.length?rn(t,e,0,qe):J}find(e,t,r){let i=[];return this.findInner(e??0,t??1e9,i,0,r),i}findInner(e,t,r,i,s){for(let o=0;o<this.local.length;o++){let l=this.local[o];l.from<=t&&l.to>=e&&(!s||s(l.spec))&&r.push(l.copy(l.from+i,l.to+i))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let l=this.children[o]+1;this.children[o+2].findInner(e-l,t-l,r,i+l,s)}}map(e,t,r){return this==J||e.maps.length==0?this:this.mapInner(e,t,0,0,r||qe)}mapInner(e,t,r,i,s){let o;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,r,i);a&&a.type.valid(t,a)?(o||(o=[])).push(a):s.onRemove&&s.onRemove(this.local[l].spec)}return this.children.length?_a(this.children,o||[],e,t,r,i,s):o?new z(o.sort(Ke),nt):J}add(e,t){return t.length?this==J?z.create(e,t):this.addInner(e,t,0):this}addInner(e,t,r){let i,s=0;e.forEach((l,a)=>{let c=a+r,u;if(u=oo(t,l,c)){for(i||(i=this.children.slice());s<i.length&&i[s]<a;)s+=3;i[s]==a?i[s+2]=i[s+2].addInner(l,u,c+1):i.splice(s,0,a,a+l.nodeSize,rn(u,l,c+1,qe)),s+=3}});let o=so(s?lo(t):t,-r);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||o.splice(l--,1);return new z(o.length?this.local.concat(o).sort(Ke):this.local,i||this.children)}remove(e){return e.length==0||this==J?this:this.removeInner(e,0)}removeInner(e,t){let r=this.children,i=this.local;for(let s=0;s<r.length;s+=3){let o,l=r[s]+t,a=r[s+1]+t;for(let u=0,d;u<e.length;u++)(d=e[u])&&d.from>l&&d.to<a&&(e[u]=null,(o||(o=[])).push(d));if(!o)continue;r==this.children&&(r=this.children.slice());let c=r[s+2].removeInner(o,l+1);c!=J?r[s+2]=c:(r.splice(s,3),s-=3)}if(i.length){for(let s=0,o;s<e.length;s++)if(o=e[s])for(let l=0;l<i.length;l++)i[l].eq(o,t)&&(i==this.local&&(i=this.local.slice()),i.splice(l--,1))}return r==this.children&&i==this.local?this:i.length||r.length?new z(i,r):J}forChild(e,t){if(this==J)return this;if(t.isLeaf)return z.empty;let r,i;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(r=this.children[l+2]);break}let s=e+1,o=s+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<o&&a.to>s&&a.type instanceof Ie){let c=Math.max(s,a.from)-s,u=Math.min(o,a.to)-s;c<u&&(i||(i=[])).push(a.copy(c,u))}}if(i){let l=new z(i.sort(Ke),nt);return r?new we([l,r]):l}return r||J}eq(e){if(this==e)return!0;if(!(e instanceof z)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return Mr(this.localsInner(e))}localsInner(e){if(this==J)return nt;if(e.inlineContent||!this.local.some(Ie.is))return this.local;let t=[];for(let r=0;r<this.local.length;r++)this.local[r].type instanceof Ie||t.push(this.local[r]);return t}forEachSet(e){e(this)}}z.empty=new z([],[]);z.removeOverlap=Mr;const J=z.empty;class we{constructor(e){this.members=e}map(e,t){const r=this.members.map(i=>i.map(e,t,qe));return we.from(r)}forChild(e,t){if(t.isLeaf)return z.empty;let r=[];for(let i=0;i<this.members.length;i++){let s=this.members[i].forChild(e,t);s!=J&&(s instanceof we?r=r.concat(s.members):r.push(s))}return we.from(r)}eq(e){if(!(e instanceof we)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,r=!0;for(let i=0;i<this.members.length;i++){let s=this.members[i].localsInner(e);if(s.length)if(!t)t=s;else{r&&(t=t.slice(),r=!1);for(let o=0;o<s.length;o++)t.push(s[o])}}return t?Mr(r?t:t.sort(Ke)):nt}static from(e){switch(e.length){case 0:return J;case 1:return e[0];default:return new we(e.every(t=>t instanceof z)?e:e.reduce((t,r)=>t.concat(r instanceof z?r:r.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function _a(n,e,t,r,i,s,o){let l=n.slice();for(let c=0,u=s;c<t.maps.length;c++){let d=0;t.maps[c].forEach((f,h,p,m)=>{let g=m-p-(h-f);for(let y=0;y<l.length;y+=3){let M=l[y+1];if(M<0||f>M+u-d)continue;let T=l[y]+u-d;h>=T?l[y+1]=f<=T?-2:-1:f>=u&&g&&(l[y]+=g,l[y+1]+=g)}d+=g}),u=t.maps[c].map(u,-1)}let a=!1;for(let c=0;c<l.length;c+=3)if(l[c+1]<0){if(l[c+1]==-2){a=!0,l[c+1]=-1;continue}let u=t.map(n[c]+s),d=u-i;if(d<0||d>=r.content.size){a=!0;continue}let f=t.map(n[c+1]+s,-1),h=f-i,{index:p,offset:m}=r.content.findIndex(d),g=r.maybeChild(p);if(g&&m==d&&m+g.nodeSize==h){let y=l[c+2].mapInner(t,g,u+1,n[c]+s+1,o);y!=J?(l[c]=d,l[c+1]=h,l[c+2]=y):(l[c+1]=-2,a=!0)}else a=!0}if(a){let c=Ua(l,n,e,t,i,s,o),u=rn(c,r,0,o);e=u.local;for(let d=0;d<l.length;d+=3)l[d+1]<0&&(l.splice(d,3),d-=3);for(let d=0,f=0;d<u.children.length;d+=3){let h=u.children[d];for(;f<l.length&&l[f]<h;)f+=3;l.splice(f,0,u.children[d],u.children[d+1],u.children[d+2])}}return new z(e.sort(Ke),l)}function so(n,e){if(!e||!n.length)return n;let t=[];for(let r=0;r<n.length;r++){let i=n[r];t.push(new le(i.from+e,i.to+e,i.type))}return t}function Ua(n,e,t,r,i,s,o){function l(a,c){for(let u=0;u<a.local.length;u++){let d=a.local[u].map(r,i,c);d?t.push(d):o.onRemove&&o.onRemove(a.local[u].spec)}for(let u=0;u<a.children.length;u+=3)l(a.children[u+2],a.children[u]+c+1)}for(let a=0;a<n.length;a+=3)n[a+1]==-1&&l(n[a+2],e[a]+s+1);return t}function oo(n,e,t){if(e.isLeaf)return null;let r=t+e.nodeSize,i=null;for(let s=0,o;s<n.length;s++)(o=n[s])&&o.from>t&&o.to<r&&((i||(i=[])).push(o),n[s]=null);return i}function lo(n){let e=[];for(let t=0;t<n.length;t++)n[t]!=null&&e.push(n[t]);return e}function rn(n,e,t,r){let i=[],s=!1;e.forEach((l,a)=>{let c=oo(n,l,a+t);if(c){s=!0;let u=rn(c,l,t+a+1,r);u!=J&&i.push(a,a+l.nodeSize,u)}});let o=so(s?lo(n):n,-t).sort(Ke);for(let l=0;l<o.length;l++)o[l].type.valid(e,o[l])||(r.onRemove&&r.onRemove(o[l].spec),o.splice(l--,1));return o.length||i.length?new z(o,i):J}function Ke(n,e){return n.from-e.from||n.to-e.to}function Mr(n){let e=n;for(let t=0;t<e.length-1;t++){let r=e[t];if(r.from!=r.to)for(let i=t+1;i<e.length;i++){let s=e[i];if(s.from==r.from){s.to!=r.to&&(e==n&&(e=n.slice()),e[i]=s.copy(s.from,r.to),xi(e,i+1,s.copy(r.to,s.to)));continue}else{s.from<r.to&&(e==n&&(e=n.slice()),e[t]=r.copy(r.from,s.from),xi(e,i,r.copy(s.from,r.to)));break}}}return e}function xi(n,e,t){for(;e<n.length&&Ke(t,n[e])>0;)e++;n.splice(e,0,t)}function In(n){let e=[];return n.someProp("decorations",t=>{let r=t(n.state);r&&r!=J&&e.push(r)}),n.cursorWrapper&&e.push(z.create(n.state.doc,[n.cursorWrapper.deco])),we.from(e)}const Ga={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},Ya=ee&&Ae<=11;class Xa{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class Za{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new Xa,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(r=>{for(let i=0;i<r.length;i++)this.queue.push(r[i]);ee&&Ae<=11&&r.some(i=>i.type=="childList"&&i.removedNodes.length||i.type=="characterData"&&i.oldValue.length>i.target.nodeValue.length)?this.flushSoon():this.flush()}),Ya&&(this.onCharData=r=>{this.queue.push({target:r.target,type:"characterData",oldValue:r.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,Ga)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(hi(this.view)){if(this.suppressingSelectionUpdates)return Se(this.view);if(ee&&Ae<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&Ye(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,r;for(let s=e.focusNode;s;s=ct(s))t.add(s);for(let s=e.anchorNode;s;s=ct(s))if(t.has(s)){r=s;break}let i=r&&this.view.docView.nearestDesc(r);if(i&&i.ignoreMutation({type:"selection",target:r.nodeType==3?r.parentNode:r}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let r=e.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(r)&&hi(e)&&!this.ignoreSelectionChange(r),s=-1,o=-1,l=!1,a=[];if(e.editable)for(let u=0;u<t.length;u++){let d=this.registerMutation(t[u],a);d&&(s=s<0?d.from:Math.min(d.from,s),o=o<0?d.to:Math.max(d.to,o),d.typeOver&&(l=!0))}if(de&&a.length){let u=a.filter(d=>d.nodeName=="BR");if(u.length==2){let[d,f]=u;d.parentNode&&d.parentNode.parentNode==f.parentNode?f.remove():d.remove()}else{let{focusNode:d}=this.currentSelection;for(let f of u){let h=f.parentNode;h&&h.nodeName=="LI"&&(!d||tc(e,d)!=h)&&f.remove()}}}let c=null;s<0&&i&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&fn(r)&&(c=mr(e))&&c.eq(O.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,Se(e),this.currentSelection.set(r),e.scrollToSelection()):(s>-1||i)&&(s>-1&&(e.docView.markDirty(s,o),Qa(e)),this.handleDOMChange(s,o,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(r)||Se(e),this.currentSelection.set(r))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let r=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(r==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!r||r.ignoreMutation(e))return null;if(e.type=="childList"){for(let u=0;u<e.addedNodes.length;u++){let d=e.addedNodes[u];t.push(d),d.nodeType==3&&(this.lastChangedTextNode=d)}if(r.contentDOM&&r.contentDOM!=r.dom&&!r.contentDOM.contains(e.target))return{from:r.posBefore,to:r.posAfter};let i=e.previousSibling,s=e.nextSibling;if(ee&&Ae<=11&&e.addedNodes.length)for(let u=0;u<e.addedNodes.length;u++){let{previousSibling:d,nextSibling:f}=e.addedNodes[u];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(i=d),(!f||Array.prototype.indexOf.call(e.addedNodes,f)<0)&&(s=f)}let o=i&&i.parentNode==e.target?W(i)+1:0,l=r.localPosFromDOM(e.target,o,-1),a=s&&s.parentNode==e.target?W(s):e.target.childNodes.length,c=r.localPosFromDOM(e.target,a,1);return{from:l,to:c}}else return e.type=="attributes"?{from:r.posAtStart-r.border,to:r.posAtEnd+r.border}:(this.lastChangedTextNode=e.target,{from:r.posAtStart,to:r.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let Mi=new WeakMap,Ci=!1;function Qa(n){if(!Mi.has(n)&&(Mi.set(n,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(n.dom).whiteSpace)!==-1)){if(n.requiresGeckoHackNode=de,Ci)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),Ci=!0}}function wi(n,e){let t=e.startContainer,r=e.startOffset,i=e.endContainer,s=e.endOffset,o=n.domAtPos(n.state.selection.anchor);return Ye(o.node,o.offset,i,s)&&([t,r,i,s]=[i,s,t,r]),{anchorNode:t,anchorOffset:r,focusNode:i,focusOffset:s}}function ec(n,e){if(e.getComposedRanges){let i=e.getComposedRanges(n.root)[0];if(i)return wi(n,i)}let t;function r(i){i.preventDefault(),i.stopImmediatePropagation(),t=i.getTargetRanges()[0]}return n.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),n.dom.removeEventListener("beforeinput",r,!0),t?wi(n,t):null}function tc(n,e){for(let t=e.parentNode;t&&t!=n.dom;t=t.parentNode){let r=n.docView.nearestDesc(t,!0);if(r&&r.node.isBlock)return t}return null}function nc(n,e,t){let{node:r,fromOffset:i,toOffset:s,from:o,to:l}=n.docView.parseRange(e,t),a=n.domSelectionRange(),c,u=a.anchorNode;if(u&&n.dom.contains(u.nodeType==1?u:u.parentNode)&&(c=[{node:u,offset:a.anchorOffset}],fn(a)||c.push({node:a.focusNode,offset:a.focusOffset})),q&&n.input.lastKeyCode===8)for(let g=s;g>i;g--){let y=r.childNodes[g-1],M=y.pmViewDesc;if(y.nodeName=="BR"&&!M){s=g;break}if(!M||M.size)break}let d=n.state.doc,f=n.someProp("domParser")||ve.fromSchema(n.state.schema),h=d.resolve(o),p=null,m=f.parse(r,{topNode:h.parent,topMatch:h.parent.contentMatchAt(h.index()),topOpen:!0,from:i,to:s,preserveWhitespace:h.parent.type.whitespace=="pre"?"full":!0,findPositions:c,ruleFromNode:rc,context:h});if(c&&c[0].pos!=null){let g=c[0].pos,y=c[1]&&c[1].pos;y==null&&(y=g),p={anchor:g+o,head:y+o}}return{doc:m,sel:p,from:o,to:l}}function rc(n){let e=n.pmViewDesc;if(e)return e.parseRule();if(n.nodeName=="BR"&&n.parentNode){if(U&&/^(ul|ol)$/i.test(n.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}else if(n.parentNode.lastChild==n||U&&/^(tr|table)$/i.test(n.parentNode.nodeName))return{ignore:!0}}else if(n.nodeName=="IMG"&&n.getAttribute("mark-placeholder"))return{ignore:!0};return null}const ic=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function sc(n,e,t,r,i){let s=n.input.compositionPendingChanges||(n.composing?n.input.compositionID:0);if(n.input.compositionPendingChanges=0,e<0){let C=n.input.lastSelectionTime>Date.now()-50?n.input.lastSelectionOrigin:null,P=mr(n,C);if(P&&!n.state.selection.eq(P)){if(q&&ke&&n.input.lastKeyCode===13&&Date.now()-100<n.input.lastKeyCodeTime&&n.someProp("handleKeyDown",Po=>Po(n,Fe(13,"Enter"))))return;let Q=n.state.tr.setSelection(P);C=="pointer"?Q.setMeta("pointer",!0):C=="key"&&Q.scrollIntoView(),s&&Q.setMeta("composition",s),n.dispatch(Q)}return}let o=n.state.doc.resolve(e),l=o.sharedDepth(t);e=o.before(l+1),t=n.state.doc.resolve(t).after(l+1);let a=n.state.selection,c=nc(n,e,t),u=n.state.doc,d=u.slice(c.from,c.to),f,h;n.input.lastKeyCode===8&&Date.now()-100<n.input.lastKeyCodeTime?(f=n.state.selection.to,h="end"):(f=n.state.selection.from,h="start"),n.input.lastKeyCode=null;let p=ac(d.content,c.doc.content,c.from,f,h);if(p&&n.input.domChangeCount++,(ut&&n.input.lastIOSEnter>Date.now()-225||ke)&&i.some(C=>C.nodeType==1&&!ic.test(C.nodeName))&&(!p||p.endA>=p.endB)&&n.someProp("handleKeyDown",C=>C(n,Fe(13,"Enter")))){n.input.lastIOSEnter=0;return}if(!p)if(r&&a instanceof w&&!a.empty&&a.$head.sameParent(a.$anchor)&&!n.composing&&!(c.sel&&c.sel.anchor!=c.sel.head))p={start:a.from,endA:a.to,endB:a.to};else{if(c.sel){let C=Ti(n,n.state.doc,c.sel);if(C&&!C.eq(n.state.selection)){let P=n.state.tr.setSelection(C);s&&P.setMeta("composition",s),n.dispatch(P)}}return}n.state.selection.from<n.state.selection.to&&p.start==p.endB&&n.state.selection instanceof w&&(p.start>n.state.selection.from&&p.start<=n.state.selection.from+2&&n.state.selection.from>=c.from?p.start=n.state.selection.from:p.endA<n.state.selection.to&&p.endA>=n.state.selection.to-2&&n.state.selection.to<=c.to&&(p.endB+=n.state.selection.to-p.endA,p.endA=n.state.selection.to)),ee&&Ae<=11&&p.endB==p.start+1&&p.endA==p.start&&p.start>c.from&&c.doc.textBetween(p.start-c.from-1,p.start-c.from+1)=="  "&&(p.start--,p.endA--,p.endB--);let m=c.doc.resolveNoCache(p.start-c.from),g=c.doc.resolveNoCache(p.endB-c.from),y=u.resolve(p.start),M=m.sameParent(g)&&m.parent.inlineContent&&y.end()>=p.endA,T;if((ut&&n.input.lastIOSEnter>Date.now()-225&&(!M||i.some(C=>C.nodeName=="DIV"||C.nodeName=="P"))||!M&&m.pos<c.doc.content.size&&(!m.sameParent(g)||!m.parent.inlineContent)&&!/\S/.test(c.doc.textBetween(m.pos,g.pos,"",""))&&(T=O.findFrom(c.doc.resolve(m.pos+1),1,!0))&&T.head>m.pos)&&n.someProp("handleKeyDown",C=>C(n,Fe(13,"Enter")))){n.input.lastIOSEnter=0;return}if(n.state.selection.anchor>p.start&&lc(u,p.start,p.endA,m,g)&&n.someProp("handleKeyDown",C=>C(n,Fe(8,"Backspace")))){ke&&q&&n.domObserver.suppressSelectionUpdates();return}q&&p.endB==p.start&&(n.input.lastChromeDelete=Date.now()),ke&&!M&&m.start()!=g.start()&&g.parentOffset==0&&m.depth==g.depth&&c.sel&&c.sel.anchor==c.sel.head&&c.sel.head==p.endA&&(p.endB-=2,g=c.doc.resolveNoCache(p.endB-c.from),setTimeout(()=>{n.someProp("handleKeyDown",function(C){return C(n,Fe(13,"Enter"))})},20));let N=p.start,v=p.endA,A=C=>{let P=C||n.state.tr.replace(N,v,c.doc.slice(p.start-c.from,p.endB-c.from));if(c.sel){let Q=Ti(n,P.doc,c.sel);Q&&!(q&&n.composing&&Q.empty&&(p.start!=p.endB||n.input.lastChromeDelete<Date.now()-100)&&(Q.head==N||Q.head==P.mapping.map(v)-1)||ee&&Q.empty&&Q.head==N)&&P.setSelection(Q)}return s&&P.setMeta("composition",s),P.scrollIntoView()},I;if(M){if(m.pos==g.pos){ee&&Ae<=11&&m.parentOffset==0&&(n.domObserver.suppressSelectionUpdates(),setTimeout(()=>Se(n),20));let C=A(n.state.tr.delete(N,v)),P=u.resolve(p.start).marksAcross(u.resolve(p.endA));P&&C.ensureMarks(P),n.dispatch(C)}else if(p.endA==p.endB&&(I=oc(m.parent.content.cut(m.parentOffset,g.parentOffset),y.parent.content.cut(y.parentOffset,p.endA-y.start())))){let C=A(n.state.tr);I.type=="add"?C.addMark(N,v,I.mark):C.removeMark(N,v,I.mark),n.dispatch(C)}else if(m.parent.child(m.index()).isText&&m.index()==g.index()-(g.textOffset?0:1)){let C=m.parent.textBetween(m.parentOffset,g.parentOffset),P=()=>A(n.state.tr.insertText(C,N,v));n.someProp("handleTextInput",Q=>Q(n,N,v,C,P))||n.dispatch(P())}}else n.dispatch(A())}function Ti(n,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:gr(n,e.resolve(t.anchor),e.resolve(t.head))}function oc(n,e){let t=n.firstChild.marks,r=e.firstChild.marks,i=t,s=r,o,l,a;for(let u=0;u<r.length;u++)i=r[u].removeFromSet(i);for(let u=0;u<t.length;u++)s=t[u].removeFromSet(s);if(i.length==1&&s.length==0)l=i[0],o="add",a=u=>u.mark(l.addToSet(u.marks));else if(i.length==0&&s.length==1)l=s[0],o="remove",a=u=>u.mark(l.removeFromSet(u.marks));else return null;let c=[];for(let u=0;u<e.childCount;u++)c.push(a(e.child(u)));if(b.from(c).eq(n))return{mark:l,type:o}}function lc(n,e,t,r,i){if(t-e<=i.pos-r.pos||Rn(r,!0,!1)<i.pos)return!1;let s=n.resolve(e);if(!r.parent.isTextblock){let l=s.nodeAfter;return l!=null&&t==e+l.nodeSize}if(s.parentOffset<s.parent.content.size||!s.parent.isTextblock)return!1;let o=n.resolve(Rn(s,!0,!0));return!o.parent.isTextblock||o.pos>t||Rn(o,!0,!1)<t?!1:r.parent.content.cut(r.parentOffset).eq(o.parent.content)}function Rn(n,e,t){let r=n.depth,i=e?n.end():n.pos;for(;r>0&&(e||n.indexAfter(r)==n.node(r).childCount);)r--,i++,e=!1;if(t){let s=n.node(r).maybeChild(n.indexAfter(r));for(;s&&!s.isLeaf;)s=s.firstChild,i++}return i}function ac(n,e,t,r,i){let s=n.findDiffStart(e,t);if(s==null)return null;let{a:o,b:l}=n.findDiffEnd(e,t+n.size,t+e.size);if(i=="end"){let a=Math.max(0,s-Math.min(o,l));r-=o+a-s}if(o<s&&n.size<e.size){let a=r<=s&&r>=o?s-r:0;s-=a,s&&s<e.size&&Oi(e.textBetween(s-1,s+1))&&(s+=a?1:-1),l=s+(l-o),o=s}else if(l<s){let a=r<=s&&r>=l?s-r:0;s-=a,s&&s<n.size&&Oi(n.textBetween(s-1,s+1))&&(s+=a?1:-1),o=s+(o-l),l=s}return{start:s,endA:o,endB:l}}function Oi(n){if(n.length!=2)return!1;let e=n.charCodeAt(0),t=n.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class ao{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new Ea,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(Di),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=vi(this),Ni(this),this.nodeViews=Ai(this),this.docView=li(this.state.doc,Ei(this),In(this),this.dom,this),this.domObserver=new Za(this,(r,i,s,o)=>sc(this,r,i,s,o)),this.domObserver.start(),Na(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&ir(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(Di),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let r in this._props)t[r]=this._props[r];t.state=this.state;for(let r in e)t[r]=e[r];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var r;let i=this.state,s=!1,o=!1;e.storedMarks&&this.composing&&(to(this),o=!0),this.state=e;let l=i.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let h=Ai(this);uc(h,this.nodeViews)&&(this.nodeViews=h,s=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&ir(this),this.editable=vi(this),Ni(this);let a=In(this),c=Ei(this),u=i.plugins!=e.plugins&&!i.doc.eq(e.doc)?"reset":e.scrollToSelection>i.scrollToSelection?"to selection":"preserve",d=s||!this.docView.matchesNode(e.doc,c,a);(d||!e.selection.eq(i.selection))&&(o=!0);let f=u=="preserve"&&o&&this.dom.style.overflowAnchor==null&&Wl(this);if(o){this.domObserver.stop();let h=d&&(ee||q)&&!this.composing&&!i.selection.empty&&!e.selection.empty&&cc(i.selection,e.selection);if(d){let p=q?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=Ha(this)),(s||!this.docView.update(e.doc,c,a,this))&&(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=li(e.doc,c,a,this.dom,this)),p&&!this.trackWrites&&(h=!0)}h||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&fa(this))?Se(this,h):(js(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(i),!((r=this.dragging)===null||r===void 0)&&r.node&&!i.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,i),u=="reset"?this.dom.scrollTop=0:u=="to selection"?this.scrollToSelection():f&&jl(f)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!(!e||!this.dom.contains(e.nodeType==1?e:e.parentNode))){if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof x){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&ti(this,t.getBoundingClientRect(),e)}else ti(this,this.coordsAtPos(this.state.selection.head,1),e)}}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(!e||e.plugins!=this.state.plugins||this.directPlugins!=this.prevDirectPlugins){this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let r=this.directPlugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let r=this.state.plugins[t];r.spec.view&&this.pluginViews.push(r.spec.view(this))}}else for(let t=0;t<this.pluginViews.length;t++){let r=this.pluginViews[t];r.update&&r.update(this,e)}}updateDraggedNode(e,t){let r=e.node,i=-1;if(this.state.doc.nodeAt(r.from)==r.node)i=r.from;else{let s=r.from+(this.state.doc.content.size-t.doc.content.size);(s>0&&this.state.doc.nodeAt(s))==r.node&&(i=s)}this.dragging=new ro(e.slice,e.move,i<0?void 0:x.create(this.state.doc,i))}someProp(e,t){let r=this._props&&this._props[e],i;if(r!=null&&(i=t?t(r):r))return i;for(let o=0;o<this.directPlugins.length;o++){let l=this.directPlugins[o].props[e];if(l!=null&&(i=t?t(l):l))return i}let s=this.state.plugins;if(s)for(let o=0;o<s.length;o++){let l=s[o].props[e];if(l!=null&&(i=t?t(l):l))return i}}hasFocus(){if(ee){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&Jl(this.dom),Se(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return Gl(this,e)}coordsAtPos(e,t=1){return Bs(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,r=-1){let i=this.docView.posFromDOM(e,t,r);if(i==null)throw new RangeError("DOM position not inside the editor");return i}endOfTextblock(e,t){return ea(this,t||this.state,e)}pasteHTML(e,t){return Nt(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return Nt(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return yr(this,e)}destroy(){this.docView&&(va(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],In(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,Dl())}get isDestroyed(){return this.docView==null}dispatchEvent(e){return Da(this,e)}domSelectionRange(){let e=this.domSelection();return e?U&&this.root.nodeType===11&&zl(this.dom.ownerDocument)==this.dom&&ec(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}ao.prototype.dispatch=function(n){let e=this._props.dispatchTransaction;e?e.call(this,n):this.updateState(this.state.apply(n))};function Ei(n){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(n.editable),n.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(n.state)),t)for(let r in t)r=="class"?e.class+=" "+t[r]:r=="style"?e.style=(e.style?e.style+";":"")+t[r]:!e[r]&&r!="contenteditable"&&r!="nodeName"&&(e[r]=String(t[r]))}),e.translate||(e.translate="no"),[le.node(0,n.state.doc.content.size,e)]}function Ni(n){if(n.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),n.cursorWrapper={dom:e,deco:le.widget(n.state.selection.from,e,{raw:!0,marks:n.markCursor})}}else n.cursorWrapper=null}function vi(n){return!n.someProp("editable",e=>e(n.state)===!1)}function cc(n,e){let t=Math.min(n.$anchor.sharedDepth(n.head),e.$anchor.sharedDepth(e.head));return n.$anchor.start(t)!=e.$anchor.start(t)}function Ai(n){let e=Object.create(null);function t(r){for(let i in r)Object.prototype.hasOwnProperty.call(e,i)||(e[i]=r[i])}return n.someProp("nodeViews",t),n.someProp("markViews",t),e}function uc(n,e){let t=0,r=0;for(let i in n){if(n[i]!=e[i])return!0;t++}for(let i in e)r++;return t!=r}function Di(n){if(n.spec.state||n.spec.filterTransaction||n.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}var Re={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},sn={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},dc=typeof navigator<"u"&&/Mac/.test(navigator.platform),fc=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var j=0;j<10;j++)Re[48+j]=Re[96+j]=String(j);for(var j=1;j<=24;j++)Re[j+111]="F"+j;for(var j=65;j<=90;j++)Re[j]=String.fromCharCode(j+32),sn[j]=String.fromCharCode(j);for(var Pn in Re)sn.hasOwnProperty(Pn)||(sn[Pn]=Re[Pn]);function hc(n){var e=dc&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||fc&&n.shiftKey&&n.key&&n.key.length==1||n.key=="Unidentified",t=!e&&n.key||(n.shiftKey?sn:Re)[n.keyCode]||n.key||"Unidentified";return t=="Esc"&&(t="Escape"),t=="Del"&&(t="Delete"),t=="Left"&&(t="ArrowLeft"),t=="Up"&&(t="ArrowUp"),t=="Right"&&(t="ArrowRight"),t=="Down"&&(t="ArrowDown"),t}const pc=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),mc=typeof navigator<"u"&&/Win/.test(navigator.platform);function gc(n){let e=n.split(/-(?!$)/),t=e[e.length-1];t=="Space"&&(t=" ");let r,i,s,o;for(let l=0;l<e.length-1;l++){let a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))pc?o=!0:i=!0;else throw new Error("Unrecognized modifier name: "+a)}return r&&(t="Alt-"+t),i&&(t="Ctrl-"+t),o&&(t="Meta-"+t),s&&(t="Shift-"+t),t}function yc(n){let e=Object.create(null);for(let t in n)e[gc(t)]=n[t];return e}function Bn(n,e,t=!0){return e.altKey&&(n="Alt-"+n),e.ctrlKey&&(n="Ctrl-"+n),e.metaKey&&(n="Meta-"+n),t&&e.shiftKey&&(n="Shift-"+n),n}function bc(n){return new ne({props:{handleKeyDown:co(n)}})}function co(n){let e=yc(n);return function(t,r){let i=hc(r),s,o=e[Bn(i,r)];if(o&&o(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(r.shiftKey){let l=e[Bn(i,r,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((r.altKey||r.metaKey||r.ctrlKey)&&!(mc&&r.ctrlKey&&r.altKey)&&(s=Re[r.keyCode])&&s!=i){let l=e[Bn(s,r)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}const kc=(n,e)=>n.selection.empty?!1:(e&&e(n.tr.deleteSelection().scrollIntoView()),!0);function uo(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("backward",n):t.parentOffset>0)?null:t}const Sc=(n,e,t)=>{let r=uo(n,t);if(!r)return!1;let i=Cr(r);if(!i){let o=r.blockRange(),l=o&&pt(o);return l==null?!1:(e&&e(n.tr.lift(o,l).scrollIntoView()),!0)}let s=i.nodeBefore;if(mo(n,i,e,-1))return!0;if(r.parent.content.size==0&&(dt(s,"end")||x.isSelectable(s)))for(let o=r.depth;;o--){let l=un(n.doc,r.before(o),r.after(o),k.empty);if(l&&l.slice.size<l.to-l.from){if(e){let a=n.tr.step(l);a.setSelection(dt(s,"end")?O.findFrom(a.doc.resolve(a.mapping.map(i.pos,-1)),-1):x.create(a.doc,i.pos-s.nodeSize)),e(a.scrollIntoView())}return!0}if(o==1||r.node(o-1).childCount>1)break}return s.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos-s.nodeSize,i.pos).scrollIntoView()),!0):!1},xc=(n,e,t)=>{let r=uo(n,t);if(!r)return!1;let i=Cr(r);return i?fo(n,i,e):!1},Mc=(n,e,t)=>{let r=ho(n,t);if(!r)return!1;let i=wr(r);return i?fo(n,i,e):!1};function fo(n,e,t){let r=e.nodeBefore,i=r,s=e.pos-1;for(;!i.isTextblock;s--){if(i.type.spec.isolating)return!1;let u=i.lastChild;if(!u)return!1;i=u}let o=e.nodeAfter,l=o,a=e.pos+1;for(;!l.isTextblock;a++){if(l.type.spec.isolating)return!1;let u=l.firstChild;if(!u)return!1;l=u}let c=un(n.doc,s,a,k.empty);if(!c||c.from!=s||c instanceof L&&c.slice.size>=a-s)return!1;if(t){let u=n.tr.step(c);u.setSelection(w.create(u.doc,s)),t(u.scrollIntoView())}return!0}function dt(n,e,t=!1){for(let r=n;r;r=e=="start"?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(t&&r.childCount!=1)return!1}return!1}const Cc=(n,e,t)=>{let{$head:r,empty:i}=n.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("backward",n):r.parentOffset>0)return!1;s=Cr(r)}let o=s&&s.nodeBefore;return!o||!x.isSelectable(o)?!1:(e&&e(n.tr.setSelection(x.create(n.doc,s.pos-o.nodeSize)).scrollIntoView()),!0)};function Cr(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){if(n.index(e)>0)return n.doc.resolve(n.before(e+1));if(n.node(e).type.spec.isolating)break}return null}function ho(n,e){let{$cursor:t}=n.selection;return!t||(e?!e.endOfTextblock("forward",n):t.parentOffset<t.parent.content.size)?null:t}const wc=(n,e,t)=>{let r=ho(n,t);if(!r)return!1;let i=wr(r);if(!i)return!1;let s=i.nodeAfter;if(mo(n,i,e,1))return!0;if(r.parent.content.size==0&&(dt(s,"start")||x.isSelectable(s))){let o=un(n.doc,r.before(),r.after(),k.empty);if(o&&o.slice.size<o.to-o.from){if(e){let l=n.tr.step(o);l.setSelection(dt(s,"start")?O.findFrom(l.doc.resolve(l.mapping.map(i.pos)),1):x.create(l.doc,l.mapping.map(i.pos))),e(l.scrollIntoView())}return!0}}return s.isAtom&&i.depth==r.depth-1?(e&&e(n.tr.delete(i.pos,i.pos+s.nodeSize).scrollIntoView()),!0):!1},Tc=(n,e,t)=>{let{$head:r,empty:i}=n.selection,s=r;if(!i)return!1;if(r.parent.isTextblock){if(t?!t.endOfTextblock("forward",n):r.parentOffset<r.parent.content.size)return!1;s=wr(r)}let o=s&&s.nodeAfter;return!o||!x.isSelectable(o)?!1:(e&&e(n.tr.setSelection(x.create(n.doc,s.pos)).scrollIntoView()),!0)};function wr(n){if(!n.parent.type.spec.isolating)for(let e=n.depth-1;e>=0;e--){let t=n.node(e);if(n.index(e)+1<t.childCount)return n.doc.resolve(n.after(e+1));if(t.type.spec.isolating)break}return null}const Oc=(n,e)=>{let t=n.selection,r=t instanceof x,i;if(r){if(t.node.isTextblock||!Be(n.doc,t.from))return!1;i=t.from}else if(i=cn(n.doc,t.from,-1),i==null)return!1;if(e){let s=n.tr.join(i);r&&s.setSelection(x.create(s.doc,i-n.doc.resolve(i).nodeBefore.nodeSize)),e(s.scrollIntoView())}return!0},Ec=(n,e)=>{let t=n.selection,r;if(t instanceof x){if(t.node.isTextblock||!Be(n.doc,t.to))return!1;r=t.to}else if(r=cn(n.doc,t.to,1),r==null)return!1;return e&&e(n.tr.join(r).scrollIntoView()),!0},Nc=(n,e)=>{let{$from:t,$to:r}=n.selection,i=t.blockRange(r),s=i&&pt(i);return s==null?!1:(e&&e(n.tr.lift(i,s).scrollIntoView()),!0)},vc=(n,e)=>{let{$head:t,$anchor:r}=n.selection;return!t.parent.type.spec.code||!t.sameParent(r)?!1:(e&&e(n.tr.insertText(`
`).scrollIntoView()),!0)};function po(n){for(let e=0;e<n.edgeCount;e++){let{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}const Ac=(n,e)=>{let{$head:t,$anchor:r}=n.selection;if(!t.parent.type.spec.code||!t.sameParent(r))return!1;let i=t.node(-1),s=t.indexAfter(-1),o=po(i.contentMatchAt(s));if(!o||!i.canReplaceWith(s,s,o))return!1;if(e){let l=t.after(),a=n.tr.replaceWith(l,l,o.createAndFill());a.setSelection(O.near(a.doc.resolve(l),1)),e(a.scrollIntoView())}return!0},Dc=(n,e)=>{let t=n.selection,{$from:r,$to:i}=t;if(t instanceof ae||r.parent.inlineContent||i.parent.inlineContent)return!1;let s=po(i.parent.contentMatchAt(i.indexAfter()));if(!s||!s.isTextblock)return!1;if(e){let o=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,l=n.tr.insert(o,s.createAndFill());l.setSelection(w.create(l.doc,o+1)),e(l.scrollIntoView())}return!0},Ic=(n,e)=>{let{$cursor:t}=n.selection;if(!t||t.parent.content.size)return!1;if(t.depth>1&&t.after()!=t.end(-1)){let s=t.before();if(st(n.doc,s))return e&&e(n.tr.split(s).scrollIntoView()),!0}let r=t.blockRange(),i=r&&pt(r);return i==null?!1:(e&&e(n.tr.lift(r,i).scrollIntoView()),!0)},Rc=(n,e)=>{let{$from:t,to:r}=n.selection,i,s=t.sharedDepth(r);return s==0?!1:(i=t.before(s),e&&e(n.tr.setSelection(x.create(n.doc,i))),!0)};function Pc(n,e,t){let r=e.nodeBefore,i=e.nodeAfter,s=e.index();return!r||!i||!r.type.compatibleContent(i.type)?!1:!r.content.size&&e.parent.canReplace(s-1,s)?(t&&t(n.tr.delete(e.pos-r.nodeSize,e.pos).scrollIntoView()),!0):!e.parent.canReplace(s,s+1)||!(i.isTextblock||Be(n.doc,e.pos))?!1:(t&&t(n.tr.join(e.pos).scrollIntoView()),!0)}function mo(n,e,t,r){let i=e.nodeBefore,s=e.nodeAfter,o,l,a=i.type.spec.isolating||s.type.spec.isolating;if(!a&&Pc(n,e,t))return!0;let c=!a&&e.parent.canReplace(e.index(),e.index()+1);if(c&&(o=(l=i.contentMatchAt(i.childCount)).findWrapping(s.type))&&l.matchType(o[0]||s.type).validEnd){if(t){let h=e.pos+s.nodeSize,p=b.empty;for(let y=o.length-1;y>=0;y--)p=b.from(o[y].create(null,p));p=b.from(i.copy(p));let m=n.tr.step(new F(e.pos-1,h,e.pos,h,new k(p,1,0),o.length,!0)),g=m.doc.resolve(h+2*o.length);g.nodeAfter&&g.nodeAfter.type==i.type&&Be(m.doc,g.pos)&&m.join(g.pos),t(m.scrollIntoView())}return!0}let u=s.type.spec.isolating||r>0&&a?null:O.findFrom(e,1),d=u&&u.$from.blockRange(u.$to),f=d&&pt(d);if(f!=null&&f>=e.depth)return t&&t(n.tr.lift(d,f).scrollIntoView()),!0;if(c&&dt(s,"start",!0)&&dt(i,"end")){let h=i,p=[];for(;p.push(h),!h.isTextblock;)h=h.lastChild;let m=s,g=1;for(;!m.isTextblock;m=m.firstChild)g++;if(h.canReplace(h.childCount,h.childCount,m.content)){if(t){let y=b.empty;for(let T=p.length-1;T>=0;T--)y=b.from(p[T].copy(y));let M=n.tr.step(new F(e.pos-p.length,e.pos+s.nodeSize,e.pos+g,e.pos+s.nodeSize-g,new k(y,p.length,0),0,!0));t(M.scrollIntoView())}return!0}}return!1}function go(n){return function(e,t){let r=e.selection,i=n<0?r.$from:r.$to,s=i.depth;for(;i.node(s).isInline;){if(!s)return!1;s--}return i.node(s).isTextblock?(t&&t(e.tr.setSelection(w.create(e.doc,n<0?i.start(s):i.end(s)))),!0):!1}}const Bc=go(-1),zc=go(1);function Lc(n,e=null){return function(t,r){let{$from:i,$to:s}=t.selection,o=i.blockRange(s),l=o&&fr(o,n,e);return l?(r&&r(t.tr.wrap(o,l).scrollIntoView()),!0):!1}}function Ii(n,e=null){return function(t,r){let i=!1;for(let s=0;s<t.selection.ranges.length&&!i;s++){let{$from:{pos:o},$to:{pos:l}}=t.selection.ranges[s];t.doc.nodesBetween(o,l,(a,c)=>{if(i)return!1;if(!(!a.isTextblock||a.hasMarkup(n,e)))if(a.type==n)i=!0;else{let u=t.doc.resolve(c),d=u.index();i=u.parent.canReplaceWith(d,d+1,n)}})}if(!i)return!1;if(r){let s=t.tr;for(let o=0;o<t.selection.ranges.length;o++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[o];s.setBlockType(l,a,n,e)}r(s.scrollIntoView())}return!0}}typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform()=="darwin";function Fc(n,e=null){return function(t,r){let{$from:i,$to:s}=t.selection,o=i.blockRange(s);if(!o)return!1;let l=r?t.tr:null;return Vc(l,o,n,e)?(r&&r(l.scrollIntoView()),!0):!1}}function Vc(n,e,t,r=null){let i=!1,s=e,o=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(t)&&e.startIndex==0){if(e.$from.index(e.depth-1)==0)return!1;let a=o.resolve(e.start-2);s=new Xt(a,a,e.depth),e.endIndex<e.parent.childCount&&(e=new Xt(e.$from,o.resolve(e.$to.end(e.depth)),e.depth)),i=!0}let l=fr(s,t,r,e);return l?(n&&$c(n,e,l,i,t),!0):!1}function $c(n,e,t,r,i){let s=b.empty;for(let u=t.length-1;u>=0;u--)s=b.from(t[u].type.create(t[u].attrs,s));n.step(new F(e.start-(r?2:0),e.end,e.start,e.end,new k(s,0,0),t.length,!0));let o=0;for(let u=0;u<t.length;u++)t[u].type==i&&(o=u+1);let l=t.length-o,a=e.start+t.length-(r?2:0),c=e.parent;for(let u=e.startIndex,d=e.endIndex,f=!0;u<d;u++,f=!1)!f&&st(n.doc,a,l)&&(n.split(a,l),a+=2*l),a+=c.child(u).nodeSize;return n}function Hc(n){return function(e,t){let{$from:r,$to:i}=e.selection,s=r.blockRange(i,o=>o.childCount>0&&o.firstChild.type==n);return s?t?r.node(s.depth-1).type==n?Wc(e,t,n,s):jc(e,t,s):!0:!1}}function Wc(n,e,t,r){let i=n.tr,s=r.end,o=r.$to.end(r.depth);s<o&&(i.step(new F(s-1,o,s,o,new k(b.from(t.create(null,r.parent.copy())),1,0),1,!0)),r=new Xt(i.doc.resolve(r.$from.pos),i.doc.resolve(o),r.depth));const l=pt(r);if(l==null)return!1;i.lift(r,l);let a=i.doc.resolve(i.mapping.map(s,-1)-1);return Be(i.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&i.join(a.pos),e(i.scrollIntoView()),!0}function jc(n,e,t){let r=n.tr,i=t.parent;for(let h=t.end,p=t.endIndex-1,m=t.startIndex;p>m;p--)h-=i.child(p).nodeSize,r.delete(h-1,h+1);let s=r.doc.resolve(t.start),o=s.nodeAfter;if(r.mapping.map(t.end)!=t.start+s.nodeAfter.nodeSize)return!1;let l=t.startIndex==0,a=t.endIndex==i.childCount,c=s.node(-1),u=s.index(-1);if(!c.canReplace(u+(l?0:1),u+1,o.content.append(a?b.empty:b.from(i))))return!1;let d=s.pos,f=d+o.nodeSize;return r.step(new F(d-(l?1:0),f+(a?1:0),d+1,f-1,new k((l?b.empty:b.from(i.copy(b.empty))).append(a?b.empty:b.from(i.copy(b.empty))),l?0:1,a?0:1),l?0:1)),e(r.scrollIntoView()),!0}function Jc(n){return function(e,t){let{$from:r,$to:i}=e.selection,s=r.blockRange(i,c=>c.childCount>0&&c.firstChild.type==n);if(!s)return!1;let o=s.startIndex;if(o==0)return!1;let l=s.parent,a=l.child(o-1);if(a.type!=n)return!1;if(t){let c=a.lastChild&&a.lastChild.type==l.type,u=b.from(c?n.create():null),d=new k(b.from(n.create(null,b.from(l.type.create(null,u)))),c?3:1,0),f=s.start,h=s.end;t(e.tr.step(new F(f-(c?3:1),h,f,h,d,1,!0)).scrollIntoView())}return!0}}function mn(n){const{state:e,transaction:t}=n;let{selection:r}=t,{doc:i}=t,{storedMarks:s}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return s},get selection(){return r},get doc(){return i},get tr(){return r=t.selection,i=t.doc,s=t.storedMarks,t}}}class gn{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:e,editor:t,state:r}=this,{view:i}=t,{tr:s}=r,o=this.buildProps(s);return Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...u)=>{const d=a(...u)(o);return!s.getMeta("preventDispatch")&&!this.hasCustomState&&i.dispatch(s),d}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){const{rawCommands:r,editor:i,state:s}=this,{view:o}=i,l=[],a=!!e,c=e||s.tr,u=()=>(!a&&t&&!c.getMeta("preventDispatch")&&!this.hasCustomState&&o.dispatch(c),l.every(f=>f===!0)),d={...Object.fromEntries(Object.entries(r).map(([f,h])=>[f,(...m)=>{const g=this.buildProps(c,t),y=h(...m)(g);return l.push(y),d}])),run:u};return d}createCan(e){const{rawCommands:t,state:r}=this,i=!1,s=e||r.tr,o=this.buildProps(s,i);return{...Object.fromEntries(Object.entries(t).map(([a,c])=>[a,(...u)=>c(...u)({...o,dispatch:void 0})])),chain:()=>this.createChain(s,i)}}buildProps(e,t=!0){const{rawCommands:r,editor:i,state:s}=this,{view:o}=i,l={tr:e,editor:i,view:o,state:mn({state:s,transaction:e}),dispatch:t?()=>{}:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(r).map(([a,c])=>[a,(...u)=>c(...u)(l)]))}};return l}}class qc{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const r=this.callbacks[e];return r&&r.forEach(i=>i.apply(this,t)),this}off(e,t){const r=this.callbacks[e];return r&&(t?this.callbacks[e]=r.filter(i=>i!==t):delete this.callbacks[e]),this}once(e,t){const r=(...i)=>{this.off(e,r),t.apply(this,i)};return this.on(e,r)}removeAllListeners(){this.callbacks={}}}function S(n,e,t){return n.config[e]===void 0&&n.parent?S(n.parent,e,t):typeof n.config[e]=="function"?n.config[e].bind({...t,parent:n.parent?S(n.parent,e,t):null}):n.config[e]}function yn(n){const e=n.filter(i=>i.type==="extension"),t=n.filter(i=>i.type==="node"),r=n.filter(i=>i.type==="mark");return{baseExtensions:e,nodeExtensions:t,markExtensions:r}}function yo(n){const e=[],{nodeExtensions:t,markExtensions:r}=yn(n),i=[...t,...r],s={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return n.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage,extensions:i},a=S(o,"addGlobalAttributes",l);if(!a)return;a().forEach(u=>{u.types.forEach(d=>{Object.entries(u.attributes).forEach(([f,h])=>{e.push({type:d,name:f,attribute:{...s,...h}})})})})}),i.forEach(o=>{const l={name:o.name,options:o.options,storage:o.storage},a=S(o,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([u,d])=>{const f={...s,...d};typeof(f==null?void 0:f.default)=="function"&&(f.default=f.default()),f!=null&&f.isRequired&&(f==null?void 0:f.default)===void 0&&delete f.default,e.push({type:o.name,name:u,attribute:f})})}),e}function $(n,e){if(typeof n=="string"){if(!e.nodes[n])throw Error(`There is no node type named '${n}'. Maybe you forgot to add the extension?`);return e.nodes[n]}return n}function X(...n){return n.filter(e=>!!e).reduce((e,t)=>{const r={...e};return Object.entries(t).forEach(([i,s])=>{if(!r[i]){r[i]=s;return}if(i==="class"){const l=s?String(s).split(" "):[],a=r[i]?r[i].split(" "):[],c=l.filter(u=>!a.includes(u));r[i]=[...a,...c].join(" ")}else if(i==="style"){const l=s?s.split(";").map(u=>u.trim()).filter(Boolean):[],a=r[i]?r[i].split(";").map(u=>u.trim()).filter(Boolean):[],c=new Map;a.forEach(u=>{const[d,f]=u.split(":").map(h=>h.trim());c.set(d,f)}),l.forEach(u=>{const[d,f]=u.split(":").map(h=>h.trim());c.set(d,f)}),r[i]=Array.from(c.entries()).map(([u,d])=>`${u}: ${d}`).join("; ")}else r[i]=s}),r},{})}function sr(n,e){return e.filter(t=>t.type===n.type.name).filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(n.attrs)||{}:{[t.name]:n.attrs[t.name]}).reduce((t,r)=>X(t,r),{})}function bo(n){return typeof n=="function"}function E(n,e=void 0,...t){return bo(n)?e?n.bind(e)(...t):n(...t):n}function Kc(n={}){return Object.keys(n).length===0&&n.constructor===Object}function _c(n){return typeof n!="string"?n:n.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(n):n==="true"?!0:n==="false"?!1:n}function Ri(n,e){return"style"in n?n:{...n,getAttrs:t=>{const r=n.getAttrs?n.getAttrs(t):n.attrs;if(r===!1)return!1;const i=e.reduce((s,o)=>{const l=o.attribute.parseHTML?o.attribute.parseHTML(t):_c(t.getAttribute(o.name));return l==null?s:{...s,[o.name]:l}},{});return{...r,...i}}}}function Pi(n){return Object.fromEntries(Object.entries(n).filter(([e,t])=>e==="attrs"&&Kc(t)?!1:t!=null))}function Uc(n,e){var t;const r=yo(n),{nodeExtensions:i,markExtensions:s}=yn(n),o=(t=i.find(c=>S(c,"topNode")))===null||t===void 0?void 0:t.name,l=Object.fromEntries(i.map(c=>{const u=r.filter(y=>y.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=n.reduce((y,M)=>{const T=S(M,"extendNodeSchema",d);return{...y,...T?T(c):{}}},{}),h=Pi({...f,content:E(S(c,"content",d)),marks:E(S(c,"marks",d)),group:E(S(c,"group",d)),inline:E(S(c,"inline",d)),atom:E(S(c,"atom",d)),selectable:E(S(c,"selectable",d)),draggable:E(S(c,"draggable",d)),code:E(S(c,"code",d)),whitespace:E(S(c,"whitespace",d)),linebreakReplacement:E(S(c,"linebreakReplacement",d)),defining:E(S(c,"defining",d)),isolating:E(S(c,"isolating",d)),attrs:Object.fromEntries(u.map(y=>{var M;return[y.name,{default:(M=y==null?void 0:y.attribute)===null||M===void 0?void 0:M.default}]}))}),p=E(S(c,"parseHTML",d));p&&(h.parseDOM=p.map(y=>Ri(y,u)));const m=S(c,"renderHTML",d);m&&(h.toDOM=y=>m({node:y,HTMLAttributes:sr(y,u)}));const g=S(c,"renderText",d);return g&&(h.toText=g),[c.name,h]})),a=Object.fromEntries(s.map(c=>{const u=r.filter(g=>g.type===c.name),d={name:c.name,options:c.options,storage:c.storage,editor:e},f=n.reduce((g,y)=>{const M=S(y,"extendMarkSchema",d);return{...g,...M?M(c):{}}},{}),h=Pi({...f,inclusive:E(S(c,"inclusive",d)),excludes:E(S(c,"excludes",d)),group:E(S(c,"group",d)),spanning:E(S(c,"spanning",d)),code:E(S(c,"code",d)),attrs:Object.fromEntries(u.map(g=>{var y;return[g.name,{default:(y=g==null?void 0:g.attribute)===null||y===void 0?void 0:y.default}]}))}),p=E(S(c,"parseHTML",d));p&&(h.parseDOM=p.map(g=>Ri(g,u)));const m=S(c,"renderHTML",d);return m&&(h.toDOM=g=>m({mark:g,HTMLAttributes:sr(g,u)})),[c.name,h]}));return new us({topNode:o,nodes:l,marks:a})}function zn(n,e){return e.nodes[n]||e.marks[n]||null}function Bi(n,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===n.name):e}function Tr(n,e){const t=Ze.fromSchema(e).serializeFragment(n),i=document.implementation.createHTMLDocument().createElement("div");return i.appendChild(t),i.innerHTML}const Gc=(n,e=500)=>{let t="";const r=n.parentOffset;return n.parent.nodesBetween(Math.max(0,r-e),r,(i,s,o,l)=>{var a,c;const u=((c=(a=i.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:i,pos:s,parent:o,index:l}))||i.textContent||"%leaf%";t+=i.isAtom&&!i.isText?u:u.slice(0,Math.max(0,r-s))}),t};function Or(n){return Object.prototype.toString.call(n)==="[object RegExp]"}class bn{constructor(e){this.find=e.find,this.handler=e.handler}}const Yc=(n,e)=>{if(Or(e))return e.exec(n);const t=e(n);if(!t)return null;const r=[t.text];return r.index=t.index,r.input=n,r.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(t.replaceWith)),r};function Vt(n){var e;const{editor:t,from:r,to:i,text:s,rules:o,plugin:l}=n,{view:a}=t;if(a.composing)return!1;const c=a.state.doc.resolve(r);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(f=>f.type.spec.code))return!1;let u=!1;const d=Gc(c)+s;return o.forEach(f=>{if(u)return;const h=Yc(d,f.find);if(!h)return;const p=a.state.tr,m=mn({state:a.state,transaction:p}),g={from:r-(h[0].length-s.length),to:i},{commands:y,chain:M,can:T}=new gn({editor:t,state:m});f.handler({state:m,range:g,match:h,commands:y,chain:M,can:T})===null||!p.steps.length||(p.setMeta(l,{transform:p,from:r,to:i,text:s}),a.dispatch(p),u=!0)}),u}function Xc(n){const{editor:e,rules:t}=n,r=new ne({state:{init(){return null},apply(i,s,o){const l=i.getMeta(r);if(l)return l;const a=i.getMeta("applyInputRules");return!!a&&setTimeout(()=>{let{text:u}=a;typeof u=="string"?u=u:u=Tr(b.from(u),o.schema);const{from:d}=a,f=d+u.length;Vt({editor:e,from:d,to:f,text:u,rules:t,plugin:r})}),i.selectionSet||i.docChanged?null:s}},props:{handleTextInput(i,s,o,l){return Vt({editor:e,from:s,to:o,text:l,rules:t,plugin:r})},handleDOMEvents:{compositionend:i=>(setTimeout(()=>{const{$cursor:s}=i.state.selection;s&&Vt({editor:e,from:s.pos,to:s.pos,text:"",rules:t,plugin:r})}),!1)},handleKeyDown(i,s){if(s.key!=="Enter")return!1;const{$cursor:o}=i.state.selection;return o?Vt({editor:e,from:o.pos,to:o.pos,text:`
`,rules:t,plugin:r}):!1}},isInputRules:!0});return r}function Zc(n){return Object.prototype.toString.call(n).slice(8,-1)}function $t(n){return Zc(n)!=="Object"?!1:n.constructor===Object&&Object.getPrototypeOf(n)===Object.prototype}function kn(n,e){const t={...n};return $t(n)&&$t(e)&&Object.keys(e).forEach(r=>{$t(e[r])&&$t(n[r])?t[r]=kn(n[r],e[r]):t[r]=e[r]}),t}class Pe{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=E(S(this,"addOptions",{name:this.name}))),this.storage=E(S(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Pe(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>kn(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new Pe(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=E(S(t,"addOptions",{name:t.name})),t.storage=E(S(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:r}=e.state,i=e.state.selection.$from;if(i.pos===i.end()){const o=i.marks();if(!!!o.find(c=>(c==null?void 0:c.type.name)===t.name))return!1;const a=o.find(c=>(c==null?void 0:c.type.name)===t.name);return a&&r.removeStoredMark(a),r.insertText(" ",i.pos),e.view.dispatch(r),!0}return!1}}function Qc(n){return typeof n=="number"}class eu{constructor(e){this.find=e.find,this.handler=e.handler}}const tu=(n,e,t)=>{if(Or(e))return[...n.matchAll(e)];const r=e(n,t);return r?r.map(i=>{const s=[i.text];return s.index=i.index,s.input=n,s.data=i.data,i.replaceWith&&(i.text.includes(i.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),s.push(i.replaceWith)),s}):[]};function nu(n){const{editor:e,state:t,from:r,to:i,rule:s,pasteEvent:o,dropEvent:l}=n,{commands:a,chain:c,can:u}=new gn({editor:e,state:t}),d=[];return t.doc.nodesBetween(r,i,(h,p)=>{if(!h.isTextblock||h.type.spec.code)return;const m=Math.max(r,p),g=Math.min(i,p+h.content.size),y=h.textBetween(m-p,g-p,void 0,"￼");tu(y,s.find,o).forEach(T=>{if(T.index===void 0)return;const N=m+T.index+1,v=N+T[0].length,A={from:t.tr.mapping.map(N),to:t.tr.mapping.map(v)},I=s.handler({state:t,range:A,match:T,commands:a,chain:c,can:u,pasteEvent:o,dropEvent:l});d.push(I)})}),d.every(h=>h!==null)}let Ht=null;const ru=n=>{var e;const t=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(e=t.clipboardData)===null||e===void 0||e.setData("text/html",n),t};function iu(n){const{editor:e,rules:t}=n;let r=null,i=!1,s=!1,o=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l;try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}const a=({state:u,from:d,to:f,rule:h,pasteEvt:p})=>{const m=u.tr,g=mn({state:u,transaction:m});if(!(!nu({editor:e,state:g,from:Math.max(d-1,0),to:f.b-1,rule:h,pasteEvent:p,dropEvent:l})||!m.steps.length)){try{l=typeof DragEvent<"u"?new DragEvent("drop"):null}catch{l=null}return o=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m}};return t.map(u=>new ne({view(d){const f=p=>{var m;r=!((m=d.dom.parentElement)===null||m===void 0)&&m.contains(p.target)?d.dom.parentElement:null,r&&(Ht=e)},h=()=>{Ht&&(Ht=null)};return window.addEventListener("dragstart",f),window.addEventListener("dragend",h),{destroy(){window.removeEventListener("dragstart",f),window.removeEventListener("dragend",h)}}},props:{handleDOMEvents:{drop:(d,f)=>{if(s=r===d.dom.parentElement,l=f,!s){const h=Ht;h&&setTimeout(()=>{const p=h.state.selection;p&&h.commands.deleteRange({from:p.from,to:p.to})},10)}return!1},paste:(d,f)=>{var h;const p=(h=f.clipboardData)===null||h===void 0?void 0:h.getData("text/html");return o=f,i=!!(p!=null&&p.includes("data-pm-slice")),!1}}},appendTransaction:(d,f,h)=>{const p=d[0],m=p.getMeta("uiEvent")==="paste"&&!i,g=p.getMeta("uiEvent")==="drop"&&!s,y=p.getMeta("applyPasteRules"),M=!!y;if(!m&&!g&&!M)return;if(M){let{text:v}=y;typeof v=="string"?v=v:v=Tr(b.from(v),h.schema);const{from:A}=y,I=A+v.length,C=ru(v);return a({rule:u,state:h,from:A,to:{b:I},pasteEvt:C})}const T=f.doc.content.findDiffStart(h.doc.content),N=f.doc.content.findDiffEnd(h.doc.content);if(!(!Qc(T)||!N||T===N.b))return a({rule:u,state:h,from:T,to:N,pasteEvt:o})}}))}function su(n){const e=n.filter((t,r)=>n.indexOf(t)!==r);return Array.from(new Set(e))}class it{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=it.resolve(e),this.schema=Uc(this.extensions,t),this.setupExtensions()}static resolve(e){const t=it.sort(it.flatten(e)),r=su(t.map(i=>i.name));return r.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${r.map(i=>`'${i}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{const r={name:t.name,options:t.options,storage:t.storage},i=S(t,"addExtensions",r);return i?[t,...this.flatten(i())]:t}).flat(10)}static sort(e){return e.sort((r,i)=>{const s=S(r,"priority")||100,o=S(i,"priority")||100;return s>o?-1:s<o?1:0})}get commands(){return this.extensions.reduce((e,t)=>{const r={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:zn(t.name,this.schema)},i=S(t,"addCommands",r);return i?{...e,...i()}:e},{})}get plugins(){const{editor:e}=this,t=it.sort([...this.extensions].reverse()),r=[],i=[],s=t.map(o=>{const l={name:o.name,options:o.options,storage:o.storage,editor:e,type:zn(o.name,this.schema)},a=[],c=S(o,"addKeyboardShortcuts",l);let u={};if(o.type==="mark"&&S(o,"exitable",l)&&(u.ArrowRight=()=>Pe.handleExit({editor:e,mark:o})),c){const m=Object.fromEntries(Object.entries(c()).map(([g,y])=>[g,()=>y({editor:e})]));u={...u,...m}}const d=bc(u);a.push(d);const f=S(o,"addInputRules",l);Bi(o,e.options.enableInputRules)&&f&&r.push(...f());const h=S(o,"addPasteRules",l);Bi(o,e.options.enablePasteRules)&&h&&i.push(...h());const p=S(o,"addProseMirrorPlugins",l);if(p){const m=p();a.push(...m)}return a}).flat();return[Xc({editor:e,rules:r}),...iu({editor:e,rules:i}),...s]}get attributes(){return yo(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=yn(this.extensions);return Object.fromEntries(t.filter(r=>!!S(r,"addNodeView")).map(r=>{const i=this.attributes.filter(a=>a.type===r.name),s={name:r.name,options:r.options,storage:r.storage,editor:e,type:$(r.name,this.schema)},o=S(r,"addNodeView",s);if(!o)return[];const l=(a,c,u,d,f)=>{const h=sr(a,i);return o()({node:a,view:c,getPos:u,decorations:d,innerDecorations:f,editor:e,extension:r,HTMLAttributes:h})};return[r.name,l]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const r={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:zn(e.name,this.schema)};e.type==="mark"&&(!((t=E(S(e,"keepOnSplit",r)))!==null&&t!==void 0)||t)&&this.splittableMarks.push(e.name);const i=S(e,"onBeforeCreate",r),s=S(e,"onCreate",r),o=S(e,"onUpdate",r),l=S(e,"onSelectionUpdate",r),a=S(e,"onTransaction",r),c=S(e,"onFocus",r),u=S(e,"onBlur",r),d=S(e,"onDestroy",r);i&&this.editor.on("beforeCreate",i),s&&this.editor.on("create",s),o&&this.editor.on("update",o),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),u&&this.editor.on("blur",u),d&&this.editor.on("destroy",d)})}}class Z{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=E(S(this,"addOptions",{name:this.name}))),this.storage=E(S(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Z(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>kn(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new Z({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=E(S(t,"addOptions",{name:t.name})),t.storage=E(S(t,"addStorage",{name:t.name,options:t.options})),t}}function ko(n,e,t){const{from:r,to:i}=e,{blockSeparator:s=`

`,textSerializers:o={}}=t||{};let l="";return n.nodesBetween(r,i,(a,c,u,d)=>{var f;a.isBlock&&c>r&&(l+=s);const h=o==null?void 0:o[a.type.name];if(h)return u&&(l+=h({node:a,pos:c,parent:u,index:d,range:e})),!1;a.isText&&(l+=(f=a==null?void 0:a.text)===null||f===void 0?void 0:f.slice(Math.max(r,c)-c,i-c))}),l}function So(n){return Object.fromEntries(Object.entries(n.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}const ou=Z.create({name:"clipboardTextSerializer",addOptions(){return{blockSeparator:void 0}},addProseMirrorPlugins(){return[new ne({key:new ge("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:n}=this,{state:e,schema:t}=n,{doc:r,selection:i}=e,{ranges:s}=i,o=Math.min(...s.map(u=>u.$from.pos)),l=Math.max(...s.map(u=>u.$to.pos)),a=So(t);return ko(r,{from:o,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}}),lu=()=>({editor:n,view:e})=>(requestAnimationFrame(()=>{var t;n.isDestroyed||(e.dom.blur(),(t=window==null?void 0:window.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),au=(n=!1)=>({commands:e})=>e.setContent("",n),cu=()=>({state:n,tr:e,dispatch:t})=>{const{selection:r}=e,{ranges:i}=r;return t&&i.forEach(({$from:s,$to:o})=>{n.doc.nodesBetween(s.pos,o.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:u}=e,d=c.resolve(u.map(a)),f=c.resolve(u.map(a+l.nodeSize)),h=d.blockRange(f);if(!h)return;const p=pt(h);if(l.type.isTextblock){const{defaultType:m}=d.parent.contentMatchAt(d.index());e.setNodeMarkup(h.start,m)}(p||p===0)&&e.lift(h,p)})}),!0},uu=n=>e=>n(e),du=()=>({state:n,dispatch:e})=>Dc(n,e),fu=(n,e)=>({editor:t,tr:r})=>{const{state:i}=t,s=i.doc.slice(n.from,n.to);r.deleteRange(n.from,n.to);const o=r.mapping.map(e);return r.insert(o,s.content),r.setSelection(new w(r.doc.resolve(o-1))),!0},hu=()=>({tr:n,dispatch:e})=>{const{selection:t}=n,r=t.$anchor.node();if(r.content.size>0)return!1;const i=n.selection.$anchor;for(let s=i.depth;s>0;s-=1)if(i.node(s).type===r.type){if(e){const l=i.before(s),a=i.after(s);n.delete(l,a).scrollIntoView()}return!0}return!1},pu=n=>({tr:e,state:t,dispatch:r})=>{const i=$(n,t.schema),s=e.selection.$anchor;for(let o=s.depth;o>0;o-=1)if(s.node(o).type===i){if(r){const a=s.before(o),c=s.after(o);e.delete(a,c).scrollIntoView()}return!0}return!1},mu=n=>({tr:e,dispatch:t})=>{const{from:r,to:i}=n;return t&&e.delete(r,i),!0},gu=()=>({state:n,dispatch:e})=>kc(n,e),yu=()=>({commands:n})=>n.keyboardShortcut("Enter"),bu=()=>({state:n,dispatch:e})=>Ac(n,e);function on(n,e,t={strict:!0}){const r=Object.keys(e);return r.length?r.every(i=>t.strict?e[i]===n[i]:Or(e[i])?e[i].test(n[i]):e[i]===n[i]):!0}function xo(n,e,t={}){return n.find(r=>r.type===e&&on(Object.fromEntries(Object.keys(t).map(i=>[i,r.attrs[i]])),t))}function zi(n,e,t={}){return!!xo(n,e,t)}function Er(n,e,t){var r;if(!n||!e)return;let i=n.parent.childAfter(n.parentOffset);if((!i.node||!i.node.marks.some(u=>u.type===e))&&(i=n.parent.childBefore(n.parentOffset)),!i.node||!i.node.marks.some(u=>u.type===e)||(t=t||((r=i.node.marks[0])===null||r===void 0?void 0:r.attrs),!xo([...i.node.marks],e,t)))return;let o=i.index,l=n.start()+i.offset,a=o+1,c=l+i.node.nodeSize;for(;o>0&&zi([...n.parent.child(o-1).marks],e,t);)o-=1,l-=n.parent.child(o).nodeSize;for(;a<n.parent.childCount&&zi([...n.parent.child(a).marks],e,t);)c+=n.parent.child(a).nodeSize,a+=1;return{from:l,to:c}}function Le(n,e){if(typeof n=="string"){if(!e.marks[n])throw Error(`There is no mark type named '${n}'. Maybe you forgot to add the extension?`);return e.marks[n]}return n}const ku=(n,e={})=>({tr:t,state:r,dispatch:i})=>{const s=Le(n,r.schema),{doc:o,selection:l}=t,{$from:a,from:c,to:u}=l;if(i){const d=Er(a,s,e);if(d&&d.from<=c&&d.to>=u){const f=w.create(o,d.from,d.to);t.setSelection(f)}}return!0},Su=n=>e=>{const t=typeof n=="function"?n(e):n;for(let r=0;r<t.length;r+=1)if(t[r](e))return!0;return!1};function Mo(n){return n instanceof w}function We(n=0,e=0,t=0){return Math.min(Math.max(n,e),t)}function Co(n,e=null){if(!e)return null;const t=O.atStart(n),r=O.atEnd(n);if(e==="start"||e===!0)return t;if(e==="end")return r;const i=t.from,s=r.to;return e==="all"?w.create(n,We(0,i,s),We(n.content.size,i,s)):w.create(n,We(e,i,s),We(e,i,s))}function xu(){return navigator.platform==="Android"||/android/i.test(navigator.userAgent)}function Nr(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const Mu=(n=null,e={})=>({editor:t,view:r,tr:i,dispatch:s})=>{e={scrollIntoView:!0,...e};const o=()=>{(Nr()||xu())&&r.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(r.focus(),e!=null&&e.scrollIntoView&&t.commands.scrollIntoView())})};if(r.hasFocus()&&n===null||n===!1)return!0;if(s&&n===null&&!Mo(t.state.selection))return o(),!0;const l=Co(i.doc,n)||t.state.selection,a=t.state.selection.eq(l);return s&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),o()),!0},Cu=(n,e)=>t=>n.every((r,i)=>e(r,{...t,index:i})),wu=(n,e)=>({tr:t,commands:r})=>r.insertContentAt({from:t.selection.from,to:t.selection.to},n,e),wo=n=>{const e=n.childNodes;for(let t=e.length-1;t>=0;t-=1){const r=e[t];r.nodeType===3&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?n.removeChild(r):r.nodeType===1&&wo(r)}return n};function Wt(n){const e=`<body>${n}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return wo(t)}function At(n,e,t){if(n instanceof Ne||n instanceof b)return n;t={slice:!0,parseOptions:{},...t};const r=typeof n=="object"&&n!==null,i=typeof n=="string";if(r)try{if(Array.isArray(n)&&n.length>0)return b.fromArray(n.map(l=>e.nodeFromJSON(l)));const o=e.nodeFromJSON(n);return t.errorOnInvalidContent&&o.check(),o}catch(s){if(t.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:s});return console.warn("[tiptap warn]: Invalid content.","Passed value:",n,"Error:",s),At("",e,t)}if(i){if(t.errorOnInvalidContent){let o=!1,l="";const a=new us({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:c=>(o=!0,l=typeof c=="string"?c:c.outerHTML,null)}]}})});if(t.slice?ve.fromSchema(a).parseSlice(Wt(n),t.parseOptions):ve.fromSchema(a).parse(Wt(n),t.parseOptions),t.errorOnInvalidContent&&o)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${l}`)})}const s=ve.fromSchema(e);return t.slice?s.parseSlice(Wt(n),t.parseOptions).content:s.parse(Wt(n),t.parseOptions)}return At("",e,t)}function Tu(n,e,t){const r=n.steps.length-1;if(r<e)return;const i=n.steps[r];if(!(i instanceof L||i instanceof F))return;const s=n.mapping.maps[r];let o=0;s.forEach((l,a,c,u)=>{o===0&&(o=u)}),n.setSelection(O.near(n.doc.resolve(o),t))}const Ou=n=>!("type"in n),Eu=(n,e,t)=>({tr:r,dispatch:i,editor:s})=>{var o;if(i){t={parseOptions:s.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};let l;const a=g=>{s.emit("contentError",{editor:s,error:g,disableCollaboration:()=>{s.storage.collaboration&&(s.storage.collaboration.isDisabled=!0)}})},c={preserveWhitespace:"full",...t.parseOptions};if(!t.errorOnInvalidContent&&!s.options.enableContentCheck&&s.options.emitContentError)try{At(e,s.schema,{parseOptions:c,errorOnInvalidContent:!0})}catch(g){a(g)}try{l=At(e,s.schema,{parseOptions:c,errorOnInvalidContent:(o=t.errorOnInvalidContent)!==null&&o!==void 0?o:s.options.enableContentCheck})}catch(g){return a(g),!1}let{from:u,to:d}=typeof n=="number"?{from:n,to:n}:{from:n.from,to:n.to},f=!0,h=!0;if((Ou(l)?l:[l]).forEach(g=>{g.check(),f=f?g.isText&&g.marks.length===0:!1,h=h?g.isBlock:!1}),u===d&&h){const{parent:g}=r.doc.resolve(u);g.isTextblock&&!g.type.spec.code&&!g.childCount&&(u-=1,d+=1)}let m;if(f){if(Array.isArray(e))m=e.map(g=>g.text||"").join("");else if(e instanceof b){let g="";e.forEach(y=>{y.text&&(g+=y.text)}),m=g}else typeof e=="object"&&e&&e.text?m=e.text:m=e;r.insertText(m,u,d)}else m=l,r.replaceWith(u,d,m);t.updateSelection&&Tu(r,r.steps.length-1,-1),t.applyInputRules&&r.setMeta("applyInputRules",{from:u,text:m}),t.applyPasteRules&&r.setMeta("applyPasteRules",{from:u,text:m})}return!0},Nu=()=>({state:n,dispatch:e})=>Oc(n,e),vu=()=>({state:n,dispatch:e})=>Ec(n,e),Au=()=>({state:n,dispatch:e})=>Sc(n,e),Du=()=>({state:n,dispatch:e})=>wc(n,e),Iu=()=>({state:n,dispatch:e,tr:t})=>{try{const r=cn(n.doc,n.selection.$from.pos,-1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch{return!1}},Ru=()=>({state:n,dispatch:e,tr:t})=>{try{const r=cn(n.doc,n.selection.$from.pos,1);return r==null?!1:(t.join(r,2),e&&e(t),!0)}catch{return!1}},Pu=()=>({state:n,dispatch:e})=>xc(n,e),Bu=()=>({state:n,dispatch:e})=>Mc(n,e);function To(){return typeof navigator<"u"?/Mac/.test(navigator.platform):!1}function zu(n){const e=n.split(/-(?!$)/);let t=e[e.length-1];t==="Space"&&(t=" ");let r,i,s,o;for(let l=0;l<e.length-1;l+=1){const a=e[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))r=!0;else if(/^(c|ctrl|control)$/i.test(a))i=!0;else if(/^s(hift)?$/i.test(a))s=!0;else if(/^mod$/i.test(a))Nr()||To()?o=!0:i=!0;else throw new Error(`Unrecognized modifier name: ${a}`)}return r&&(t=`Alt-${t}`),i&&(t=`Ctrl-${t}`),o&&(t=`Meta-${t}`),s&&(t=`Shift-${t}`),t}const Lu=n=>({editor:e,view:t,tr:r,dispatch:i})=>{const s=zu(n).split(/-(?!$)/),o=s.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:o==="Space"?" ":o,altKey:s.includes("Alt"),ctrlKey:s.includes("Ctrl"),metaKey:s.includes("Meta"),shiftKey:s.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a==null||a.steps.forEach(c=>{const u=c.map(r.mapping);u&&i&&r.maybeStep(u)}),!0};function Dt(n,e,t={}){const{from:r,to:i,empty:s}=n.selection,o=e?$(e,n.schema):null,l=[];n.doc.nodesBetween(r,i,(d,f)=>{if(d.isText)return;const h=Math.max(r,f),p=Math.min(i,f+d.nodeSize);l.push({node:d,from:h,to:p})});const a=i-r,c=l.filter(d=>o?o.name===d.node.type.name:!0).filter(d=>on(d.node.attrs,t,{strict:!1}));return s?!!c.length:c.reduce((d,f)=>d+f.to-f.from,0)>=a}const Fu=(n,e={})=>({state:t,dispatch:r})=>{const i=$(n,t.schema);return Dt(t,i,e)?Nc(t,r):!1},Vu=()=>({state:n,dispatch:e})=>Ic(n,e),$u=n=>({state:e,dispatch:t})=>{const r=$(n,e.schema);return Hc(r)(e,t)},Hu=()=>({state:n,dispatch:e})=>vc(n,e);function Sn(n,e){return e.nodes[n]?"node":e.marks[n]?"mark":null}function Li(n,e){const t=typeof e=="string"?[e]:e;return Object.keys(n).reduce((r,i)=>(t.includes(i)||(r[i]=n[i]),r),{})}const Wu=(n,e)=>({tr:t,state:r,dispatch:i})=>{let s=null,o=null;const l=Sn(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(s=$(n,r.schema)),l==="mark"&&(o=Le(n,r.schema)),i&&t.selection.ranges.forEach(a=>{r.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,u)=>{s&&s===c.type&&t.setNodeMarkup(u,void 0,Li(c.attrs,e)),o&&c.marks.length&&c.marks.forEach(d=>{o===d.type&&t.addMark(u,u+c.nodeSize,o.create(Li(d.attrs,e)))})})}),!0):!1},ju=()=>({tr:n,dispatch:e})=>(e&&n.scrollIntoView(),!0),Ju=()=>({tr:n,dispatch:e})=>{if(e){const t=new ae(n.doc);n.setSelection(t)}return!0},qu=()=>({state:n,dispatch:e})=>Cc(n,e),Ku=()=>({state:n,dispatch:e})=>Tc(n,e),_u=()=>({state:n,dispatch:e})=>Rc(n,e),Uu=()=>({state:n,dispatch:e})=>zc(n,e),Gu=()=>({state:n,dispatch:e})=>Bc(n,e);function or(n,e,t={},r={}){return At(n,e,{slice:!1,parseOptions:t,errorOnInvalidContent:r.errorOnInvalidContent})}const Yu=(n,e=!1,t={},r={})=>({editor:i,tr:s,dispatch:o,commands:l})=>{var a,c;const{doc:u}=s;if(t.preserveWhitespace!=="full"){const d=or(n,i.schema,t,{errorOnInvalidContent:(a=r.errorOnInvalidContent)!==null&&a!==void 0?a:i.options.enableContentCheck});return o&&s.replaceWith(0,u.content.size,d).setMeta("preventUpdate",!e),!0}return o&&s.setMeta("preventUpdate",!e),l.insertContentAt({from:0,to:u.content.size},n,{parseOptions:t,errorOnInvalidContent:(c=r.errorOnInvalidContent)!==null&&c!==void 0?c:i.options.enableContentCheck})};function Oo(n,e){const t=Le(e,n.schema),{from:r,to:i,empty:s}=n.selection,o=[];s?(n.storedMarks&&o.push(...n.storedMarks),o.push(...n.selection.$head.marks())):n.doc.nodesBetween(r,i,a=>{o.push(...a.marks)});const l=o.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function ih(n,e){const t=new Os(n);return e.forEach(r=>{r.steps.forEach(i=>{t.step(i)})}),t}function Xu(n){for(let e=0;e<n.edgeCount;e+=1){const{type:t}=n.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function sh(n,e){const t=[];return n.descendants((r,i)=>{e(r)&&t.push({node:r,pos:i})}),t}function oh(n,e,t){const r=[];return n.nodesBetween(e.from,e.to,(i,s)=>{t(i)&&r.push({node:i,pos:s})}),r}function Zu(n,e){for(let t=n.depth;t>0;t-=1){const r=n.node(t);if(e(r))return{pos:t>0?n.before(t):0,start:n.start(t),depth:t,node:r}}}function vr(n){return e=>Zu(e.$from,n)}function Qu(n,e){const t={from:0,to:n.content.size};return ko(n,t,e)}function ed(n,e){const t=$(e,n.schema),{from:r,to:i}=n.selection,s=[];n.doc.nodesBetween(r,i,l=>{s.push(l)});const o=s.reverse().find(l=>l.type.name===t.name);return o?{...o.attrs}:{}}function td(n,e){const t=Sn(typeof e=="string"?e:e.name,n.schema);return t==="node"?ed(n,e):t==="mark"?Oo(n,e):{}}function nd(n,e=JSON.stringify){const t={};return n.filter(r=>{const i=e(r);return Object.prototype.hasOwnProperty.call(t,i)?!1:t[i]=!0})}function rd(n){const e=nd(n);return e.length===1?e:e.filter((t,r)=>!e.filter((s,o)=>o!==r).some(s=>t.oldRange.from>=s.oldRange.from&&t.oldRange.to<=s.oldRange.to&&t.newRange.from>=s.newRange.from&&t.newRange.to<=s.newRange.to))}function lh(n){const{mapping:e,steps:t}=n,r=[];return e.maps.forEach((i,s)=>{const o=[];if(i.ranges.length)i.forEach((l,a)=>{o.push({from:l,to:a})});else{const{from:l,to:a}=t[s];if(l===void 0||a===void 0)return;o.push({from:l,to:a})}o.forEach(({from:l,to:a})=>{const c=e.slice(s).map(l,-1),u=e.slice(s).map(a),d=e.invert().map(c,-1),f=e.invert().map(u);r.push({oldRange:{from:d,to:f},newRange:{from:c,to:u}})})}),rd(r)}function Eo(n,e,t){const r=[];return n===e?t.resolve(n).marks().forEach(i=>{const s=t.resolve(n),o=Er(s,i.type);o&&r.push({mark:i,...o})}):t.nodesBetween(n,e,(i,s)=>{!i||(i==null?void 0:i.nodeSize)===void 0||r.push(...i.marks.map(o=>({from:s,to:s+i.nodeSize,mark:o})))}),r}function Kt(n,e,t){return Object.fromEntries(Object.entries(t).filter(([r])=>{const i=n.find(s=>s.type===e&&s.name===r);return i?i.attribute.keepOnSplit:!1}))}function lr(n,e,t={}){const{empty:r,ranges:i}=n.selection,s=e?Le(e,n.schema):null;if(r)return!!(n.storedMarks||n.selection.$from.marks()).filter(d=>s?s.name===d.type.name:!0).find(d=>on(d.attrs,t,{strict:!1}));let o=0;const l=[];if(i.forEach(({$from:d,$to:f})=>{const h=d.pos,p=f.pos;n.doc.nodesBetween(h,p,(m,g)=>{if(!m.isText&&!m.marks.length)return;const y=Math.max(h,g),M=Math.min(p,g+m.nodeSize),T=M-y;o+=T,l.push(...m.marks.map(N=>({mark:N,from:y,to:M})))})}),o===0)return!1;const a=l.filter(d=>s?s.name===d.mark.type.name:!0).filter(d=>on(d.mark.attrs,t,{strict:!1})).reduce((d,f)=>d+f.to-f.from,0),c=l.filter(d=>s?d.mark.type!==s&&d.mark.type.excludes(s):!0).reduce((d,f)=>d+f.to-f.from,0);return(a>0?a+c:a)>=o}function id(n,e,t={}){if(!e)return Dt(n,null,t)||lr(n,null,t);const r=Sn(e,n.schema);return r==="node"?Dt(n,e,t):r==="mark"?lr(n,e,t):!1}function Fi(n,e){const{nodeExtensions:t}=yn(e),r=t.find(o=>o.name===n);if(!r)return!1;const i={name:r.name,options:r.options,storage:r.storage},s=E(S(r,"group",i));return typeof s!="string"?!1:s.split(" ").includes("list")}function Ar(n,{checkChildren:e=!0,ignoreWhitespace:t=!1}={}){var r;if(t){if(n.type.name==="hardBreak")return!0;if(n.isText)return/^\s*$/m.test((r=n.text)!==null&&r!==void 0?r:"")}if(n.isText)return!n.text;if(n.isAtom||n.isLeaf)return!1;if(n.content.childCount===0)return!0;if(e){let i=!0;return n.content.forEach(s=>{i!==!1&&(Ar(s,{ignoreWhitespace:t,checkChildren:e})||(i=!1))}),i}return!1}function sd(n){return n instanceof x}function od(n,e,t){var r;const{selection:i}=e;let s=null;if(Mo(i)&&(s=i.$cursor),s){const l=(r=n.storedMarks)!==null&&r!==void 0?r:s.marks();return!!t.isInSet(l)||!l.some(a=>a.type.excludes(t))}const{ranges:o}=i;return o.some(({$from:l,$to:a})=>{let c=l.depth===0?n.doc.inlineContent&&n.doc.type.allowsMarkType(t):!1;return n.doc.nodesBetween(l.pos,a.pos,(u,d,f)=>{if(c)return!1;if(u.isInline){const h=!f||f.type.allowsMarkType(t),p=!!t.isInSet(u.marks)||!u.marks.some(m=>m.type.excludes(t));c=h&&p}return!c}),c})}const ld=(n,e={})=>({tr:t,state:r,dispatch:i})=>{const{selection:s}=t,{empty:o,ranges:l}=s,a=Le(n,r.schema);if(i)if(o){const c=Oo(r,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{const u=c.$from.pos,d=c.$to.pos;r.doc.nodesBetween(u,d,(f,h)=>{const p=Math.max(h,u),m=Math.min(h+f.nodeSize,d);f.marks.find(y=>y.type===a)?f.marks.forEach(y=>{a===y.type&&t.addMark(p,m,a.create({...y.attrs,...e}))}):t.addMark(p,m,a.create(e))})});return od(r,t,a)},ad=(n,e)=>({tr:t})=>(t.setMeta(n,e),!0),cd=(n,e={})=>({state:t,dispatch:r,chain:i})=>{const s=$(n,t.schema);let o;return t.selection.$anchor.sameParent(t.selection.$head)&&(o=t.selection.$anchor.parent.attrs),s.isTextblock?i().command(({commands:l})=>Ii(s,{...o,...e})(t)?!0:l.clearNodes()).command(({state:l})=>Ii(s,{...o,...e})(l,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},ud=n=>({tr:e,dispatch:t})=>{if(t){const{doc:r}=e,i=We(n,0,r.content.size),s=x.create(r,i);e.setSelection(s)}return!0},dd=n=>({tr:e,dispatch:t})=>{if(t){const{doc:r}=e,{from:i,to:s}=typeof n=="number"?{from:n,to:n}:n,o=w.atStart(r).from,l=w.atEnd(r).to,a=We(i,o,l),c=We(s,o,l),u=w.create(r,a,c);e.setSelection(u)}return!0},fd=n=>({state:e,dispatch:t})=>{const r=$(n,e.schema);return Jc(r)(e,t)};function Vi(n,e){const t=n.storedMarks||n.selection.$to.parentOffset&&n.selection.$from.marks();if(t){const r=t.filter(i=>e==null?void 0:e.includes(i.type.name));n.tr.ensureMarks(r)}}const hd=({keepMarks:n=!0}={})=>({tr:e,state:t,dispatch:r,editor:i})=>{const{selection:s,doc:o}=e,{$from:l,$to:a}=s,c=i.extensionManager.attributes,u=Kt(c,l.node().type.name,l.node().attrs);if(s instanceof x&&s.node.isBlock)return!l.parentOffset||!st(o,l.pos)?!1:(r&&(n&&Vi(t,i.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;const d=a.parentOffset===a.parent.content.size,f=l.depth===0?void 0:Xu(l.node(-1).contentMatchAt(l.indexAfter(-1)));let h=d&&f?[{type:f,attrs:u}]:void 0,p=st(e.doc,e.mapping.map(l.pos),1,h);if(!h&&!p&&st(e.doc,e.mapping.map(l.pos),1,f?[{type:f}]:void 0)&&(p=!0,h=f?[{type:f,attrs:u}]:void 0),r){if(p&&(s instanceof w&&e.deleteSelection(),e.split(e.mapping.map(l.pos),1,h),f&&!d&&!l.parentOffset&&l.parent.type!==f)){const m=e.mapping.map(l.before()),g=e.doc.resolve(m);l.node(-1).canReplaceWith(g.index(),g.index()+1,f)&&e.setNodeMarkup(e.mapping.map(l.before()),f)}n&&Vi(t,i.extensionManager.splittableMarks),e.scrollIntoView()}return p},pd=(n,e={})=>({tr:t,state:r,dispatch:i,editor:s})=>{var o;const l=$(n,r.schema),{$from:a,$to:c}=r.selection,u=r.selection.node;if(u&&u.isBlock||a.depth<2||!a.sameParent(c))return!1;const d=a.node(-1);if(d.type!==l)return!1;const f=s.extensionManager.attributes;if(a.parent.content.size===0&&a.node(-1).childCount===a.indexAfter(-1)){if(a.depth===2||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(i){let y=b.empty;const M=a.index(-1)?1:a.index(-2)?2:3;for(let C=a.depth-M;C>=a.depth-3;C-=1)y=b.from(a.node(C).copy(y));const T=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,N={...Kt(f,a.node().type.name,a.node().attrs),...e},v=((o=l.contentMatch.defaultType)===null||o===void 0?void 0:o.createAndFill(N))||void 0;y=y.append(b.from(l.createAndFill(null,v)||void 0));const A=a.before(a.depth-(M-1));t.replace(A,a.after(-T),new k(y,4-M,0));let I=-1;t.doc.nodesBetween(A,t.doc.content.size,(C,P)=>{if(I>-1)return!1;C.isTextblock&&C.content.size===0&&(I=P+1)}),I>-1&&t.setSelection(w.near(t.doc.resolve(I))),t.scrollIntoView()}return!0}const h=c.pos===a.end()?d.contentMatchAt(0).defaultType:null,p={...Kt(f,d.type.name,d.attrs),...e},m={...Kt(f,a.node().type.name,a.node().attrs),...e};t.delete(a.pos,c.pos);const g=h?[{type:l,attrs:p},{type:h,attrs:m}]:[{type:l,attrs:p}];if(!st(t.doc,a.pos,2))return!1;if(i){const{selection:y,storedMarks:M}=r,{splittableMarks:T}=s.extensionManager,N=M||y.$to.parentOffset&&y.$from.marks();if(t.split(a.pos,2,g).scrollIntoView(),!N||!i)return!0;const v=N.filter(A=>T.includes(A.type.name));t.ensureMarks(v)}return!0},Ln=(n,e)=>{const t=vr(o=>o.type===e)(n.selection);if(!t)return!0;const r=n.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(r===void 0)return!0;const i=n.doc.nodeAt(r);return t.node.type===(i==null?void 0:i.type)&&Be(n.doc,t.pos)&&n.join(t.pos),!0},Fn=(n,e)=>{const t=vr(o=>o.type===e)(n.selection);if(!t)return!0;const r=n.doc.resolve(t.start).after(t.depth);if(r===void 0)return!0;const i=n.doc.nodeAt(r);return t.node.type===(i==null?void 0:i.type)&&Be(n.doc,r)&&n.join(r),!0},md=(n,e,t,r={})=>({editor:i,tr:s,state:o,dispatch:l,chain:a,commands:c,can:u})=>{const{extensions:d,splittableMarks:f}=i.extensionManager,h=$(n,o.schema),p=$(e,o.schema),{selection:m,storedMarks:g}=o,{$from:y,$to:M}=m,T=y.blockRange(M),N=g||m.$to.parentOffset&&m.$from.marks();if(!T)return!1;const v=vr(A=>Fi(A.type.name,d))(m);if(T.depth>=1&&v&&T.depth-v.depth<=1){if(v.node.type===h)return c.liftListItem(p);if(Fi(v.node.type.name,d)&&h.validContent(v.node.content)&&l)return a().command(()=>(s.setNodeMarkup(v.pos,h),!0)).command(()=>Ln(s,h)).command(()=>Fn(s,h)).run()}return!t||!N||!l?a().command(()=>u().wrapInList(h,r)?!0:c.clearNodes()).wrapInList(h,r).command(()=>Ln(s,h)).command(()=>Fn(s,h)).run():a().command(()=>{const A=u().wrapInList(h,r),I=N.filter(C=>f.includes(C.type.name));return s.ensureMarks(I),A?!0:c.clearNodes()}).wrapInList(h,r).command(()=>Ln(s,h)).command(()=>Fn(s,h)).run()},gd=(n,e={},t={})=>({state:r,commands:i})=>{const{extendEmptyMarkRange:s=!1}=t,o=Le(n,r.schema);return lr(r,o,e)?i.unsetMark(o,{extendEmptyMarkRange:s}):i.setMark(o,e)},yd=(n,e,t={})=>({state:r,commands:i})=>{const s=$(n,r.schema),o=$(e,r.schema),l=Dt(r,s,t);let a;return r.selection.$anchor.sameParent(r.selection.$head)&&(a=r.selection.$anchor.parent.attrs),l?i.setNode(o,a):i.setNode(s,{...a,...t})},bd=(n,e={})=>({state:t,commands:r})=>{const i=$(n,t.schema);return Dt(t,i,e)?r.lift(i):r.wrapIn(i,e)},kd=()=>({state:n,dispatch:e})=>{const t=n.plugins;for(let r=0;r<t.length;r+=1){const i=t[r];let s;if(i.spec.isInputRules&&(s=i.getState(n))){if(e){const o=n.tr,l=s.transform;for(let a=l.steps.length-1;a>=0;a-=1)o.step(l.steps[a].invert(l.docs[a]));if(s.text){const a=o.doc.resolve(s.from).marks();o.replaceWith(s.from,s.to,n.schema.text(s.text,a))}else o.delete(s.from,s.to)}return!0}}return!1},Sd=()=>({tr:n,dispatch:e})=>{const{selection:t}=n,{empty:r,ranges:i}=t;return r||e&&i.forEach(s=>{n.removeMark(s.$from.pos,s.$to.pos)}),!0},xd=(n,e={})=>({tr:t,state:r,dispatch:i})=>{var s;const{extendEmptyMarkRange:o=!1}=e,{selection:l}=t,a=Le(n,r.schema),{$from:c,empty:u,ranges:d}=l;if(!i)return!0;if(u&&o){let{from:f,to:h}=l;const p=(s=c.marks().find(g=>g.type===a))===null||s===void 0?void 0:s.attrs,m=Er(c,a,p);m&&(f=m.from,h=m.to),t.removeMark(f,h,a)}else d.forEach(f=>{t.removeMark(f.$from.pos,f.$to.pos,a)});return t.removeStoredMark(a),!0},Md=(n,e={})=>({tr:t,state:r,dispatch:i})=>{let s=null,o=null;const l=Sn(typeof n=="string"?n:n.name,r.schema);return l?(l==="node"&&(s=$(n,r.schema)),l==="mark"&&(o=Le(n,r.schema)),i&&t.selection.ranges.forEach(a=>{const c=a.$from.pos,u=a.$to.pos;let d,f,h,p;t.selection.empty?r.doc.nodesBetween(c,u,(m,g)=>{s&&s===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,u),d=g,f=m)}):r.doc.nodesBetween(c,u,(m,g)=>{g<c&&s&&s===m.type&&(h=Math.max(g,c),p=Math.min(g+m.nodeSize,u),d=g,f=m),g>=c&&g<=u&&(s&&s===m.type&&t.setNodeMarkup(g,void 0,{...m.attrs,...e}),o&&m.marks.length&&m.marks.forEach(y=>{if(o===y.type){const M=Math.max(g,c),T=Math.min(g+m.nodeSize,u);t.addMark(M,T,o.create({...y.attrs,...e}))}}))}),f&&(d!==void 0&&t.setNodeMarkup(d,void 0,{...f.attrs,...e}),o&&f.marks.length&&f.marks.forEach(m=>{o===m.type&&t.addMark(h,p,o.create({...m.attrs,...e}))}))}),!0):!1},Cd=(n,e={})=>({state:t,dispatch:r})=>{const i=$(n,t.schema);return Lc(i,e)(t,r)},wd=(n,e={})=>({state:t,dispatch:r})=>{const i=$(n,t.schema);return Fc(i,e)(t,r)};var Td=Object.freeze({__proto__:null,blur:lu,clearContent:au,clearNodes:cu,command:uu,createParagraphNear:du,cut:fu,deleteCurrentNode:hu,deleteNode:pu,deleteRange:mu,deleteSelection:gu,enter:yu,exitCode:bu,extendMarkRange:ku,first:Su,focus:Mu,forEach:Cu,insertContent:wu,insertContentAt:Eu,joinBackward:Au,joinDown:vu,joinForward:Du,joinItemBackward:Iu,joinItemForward:Ru,joinTextblockBackward:Pu,joinTextblockForward:Bu,joinUp:Nu,keyboardShortcut:Lu,lift:Fu,liftEmptyBlock:Vu,liftListItem:$u,newlineInCode:Hu,resetAttributes:Wu,scrollIntoView:ju,selectAll:Ju,selectNodeBackward:qu,selectNodeForward:Ku,selectParentNode:_u,selectTextblockEnd:Uu,selectTextblockStart:Gu,setContent:Yu,setMark:ld,setMeta:ad,setNode:cd,setNodeSelection:ud,setTextSelection:dd,sinkListItem:fd,splitBlock:hd,splitListItem:pd,toggleList:md,toggleMark:gd,toggleNode:yd,toggleWrap:bd,undoInputRule:kd,unsetAllMarks:Sd,unsetMark:xd,updateAttributes:Md,wrapIn:Cd,wrapInList:wd});const Od=Z.create({name:"commands",addCommands(){return{...Td}}}),Ed=Z.create({name:"drop",addProseMirrorPlugins(){return[new ne({key:new ge("tiptapDrop"),props:{handleDrop:(n,e,t,r)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:t,moved:r})}}})]}}),Nd=Z.create({name:"editable",addProseMirrorPlugins(){return[new ne({key:new ge("editable"),props:{editable:()=>this.editor.options.editable}})]}}),vd=new ge("focusEvents"),Ad=Z.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:n}=this;return[new ne({key:vd,props:{handleDOMEvents:{focus:(e,t)=>{n.isFocused=!0;const r=n.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1},blur:(e,t)=>{n.isFocused=!1;const r=n.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(r),!1}}}})]}}),Dd=Z.create({name:"keymap",addKeyboardShortcuts(){const n=()=>this.editor.commands.first(({commands:o})=>[()=>o.undoInputRule(),()=>o.command(({tr:l})=>{const{selection:a,doc:c}=l,{empty:u,$anchor:d}=a,{pos:f,parent:h}=d,p=d.parent.isTextblock&&f>0?l.doc.resolve(f-1):d,m=p.parent.type.spec.isolating,g=d.pos-d.parentOffset,y=m&&p.parent.childCount===1?g===d.pos:O.atStart(c).from===f;return!u||!h.type.isTextblock||h.textContent.length||!y||y&&d.parent.type.name==="paragraph"?!1:o.clearNodes()}),()=>o.deleteSelection(),()=>o.joinBackward(),()=>o.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:o})=>[()=>o.deleteSelection(),()=>o.deleteCurrentNode(),()=>o.joinForward(),()=>o.selectNodeForward()]),r={Enter:()=>this.editor.commands.first(({commands:o})=>[()=>o.newlineInCode(),()=>o.createParagraphNear(),()=>o.liftEmptyBlock(),()=>o.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:n,"Mod-Backspace":n,"Shift-Backspace":n,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},i={...r},s={...r,"Ctrl-h":n,"Alt-Backspace":n,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Nr()||To()?s:i},addProseMirrorPlugins(){return[new ne({key:new ge("clearDocument"),appendTransaction:(n,e,t)=>{if(n.some(m=>m.getMeta("composition")))return;const r=n.some(m=>m.docChanged)&&!e.doc.eq(t.doc),i=n.some(m=>m.getMeta("preventClearDocument"));if(!r||i)return;const{empty:s,from:o,to:l}=e.selection,a=O.atStart(e.doc).from,c=O.atEnd(e.doc).to;if(s||!(o===a&&l===c)||!Ar(t.doc))return;const f=t.tr,h=mn({state:t,transaction:f}),{commands:p}=new gn({editor:this.editor,state:h});if(p.clearNodes(),!!f.steps.length)return f}})]}}),Id=Z.create({name:"paste",addProseMirrorPlugins(){return[new ne({key:new ge("tiptapPaste"),props:{handlePaste:(n,e,t)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:t})}}})]}}),Rd=Z.create({name:"tabindex",addProseMirrorPlugins(){return[new ne({key:new ge("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class Ve{get name(){return this.node.type.name}constructor(e,t,r=!1,i=null){this.currentNode=null,this.actualDepth=null,this.isBlock=r,this.resolvedPos=e,this.editor=t,this.currentNode=i}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!==null&&e!==void 0?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,r=this.to;if(this.isBlock){if(this.content.size===0){console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);return}t=this.from+1,r=this.to-1}this.editor.commands.insertContentAt({from:t,to:r},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new Ve(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new Ve(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new Ve(e,this.editor)}get children(){const e=[];return this.node.content.forEach((t,r)=>{const i=t.isBlock&&!t.isTextblock,s=t.isAtom&&!t.isText,o=this.pos+r+(s?0:1),l=this.resolvedPos.doc.resolve(o);if(!i&&l.depth<=this.depth)return;const a=new Ve(l,this.editor,i,i?t:null);i&&(a.actualDepth=this.depth+1),e.push(new Ve(l,this.editor,i,i?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let r=null,i=this.parent;for(;i&&!r;){if(i.node.type.name===e)if(Object.keys(t).length>0){const s=i.node.attrs,o=Object.keys(t);for(let l=0;l<o.length;l+=1){const a=o[l];if(s[a]!==t[a])break}}else r=i;i=i.parent}return r}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},r=!1){let i=[];if(!this.children||this.children.length===0)return i;const s=Object.keys(t);return this.children.forEach(o=>{r&&i.length>0||(o.node.type.name===e&&s.every(a=>t[a]===o.node.attrs[a])&&i.push(o),!(r&&i.length>0)&&(i=i.concat(o.querySelectorAll(e,t,r))))}),i}setAttribute(e){const{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}const Pd=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;function Bd(n,e,t){const r=document.querySelector(`style[data-tiptap-style${t?`-${t}`:""}]`);if(r!==null)return r;const i=document.createElement("style");return e&&i.setAttribute("nonce",e),i.setAttribute(`data-tiptap-style${t?`-${t}`:""}`,""),i.innerHTML=n,document.getElementsByTagName("head")[0].appendChild(i),i}class zd extends qc{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:t})=>{throw t},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:t,slice:r,moved:i})=>this.options.onDrop(t,r,i)),this.on("paste",({event:t,slice:r})=>this.options.onPaste(t,r)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=Bd(Pd,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},!(!this.view||!this.state||this.isDestroyed)&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const r=bo(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],i=this.state.reconfigure({plugins:r});return this.view.updateState(i),i}unregisterPlugin(e){if(this.isDestroyed)return;const t=this.state.plugins;let r=t;if([].concat(e).forEach(s=>{const o=typeof s=="string"?`${s}$`:s.key;r=r.filter(l=>!l.key.startsWith(o))}),t.length===r.length)return;const i=this.state.reconfigure({plugins:r});return this.view.updateState(i),i}createExtensionManager(){var e,t;const i=[...this.options.enableCoreExtensions?[Nd,ou.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)===null||e===void 0?void 0:e.clipboardTextSerializer)===null||t===void 0?void 0:t.blockSeparator}),Od,Ad,Dd,Rd,Ed,Id].filter(s=>typeof this.options.enableCoreExtensions=="object"?this.options.enableCoreExtensions[s.name]!==!1:!0):[],...this.options.extensions].filter(s=>["extension","node","mark"].includes(s==null?void 0:s.type));this.extensionManager=new it(i,this)}createCommandManager(){this.commandManager=new gn({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=or(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(o){if(!(o instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(o.message))throw o;this.emit("contentError",{editor:this,error:o,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(l=>l.name!=="collaboration"),this.createExtensionManager()}}),t=or(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const r=Co(t,this.options.autofocus);this.view=new ao(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...(e=this.options.editorProps)===null||e===void 0?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:rt.create({doc:t,selection:r||void 0})});const i=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(i),this.createNodeViews(),this.prependClass();const s=this.view.dom;s.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(o=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(o)});return}const t=this.state.apply(e),r=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),r&&this.emit("selectionUpdate",{editor:this,transaction:e});const i=e.getMeta("focus"),s=e.getMeta("blur");i&&this.emit("focus",{editor:this,event:i.event,transaction:e}),s&&this.emit("blur",{editor:this,event:s.event,transaction:e}),!(!e.docChanged||e.getMeta("preventUpdate"))&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return td(this.state,e)}isActive(e,t){const r=typeof e=="string"?e:null,i=typeof e=="string"?t:e;return id(this.state,r,i)}getJSON(){return this.state.doc.toJSON()}getHTML(){return Tr(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:r={}}=e||{};return Qu(this.state.doc,{blockSeparator:t,textSerializers:{...So(this.schema),...r}})}get isEmpty(){return Ar(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}$node(e,t){var r;return((r=this.$doc)===null||r===void 0?void 0:r.querySelector(e,t))||null}$nodes(e,t){var r;return((r=this.$doc)===null||r===void 0?void 0:r.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new Ve(t,this)}get $doc(){return this.$pos(0)}}function ft(n){return new bn({find:n.find,handler:({state:e,range:t,match:r})=>{const i=E(n.getAttributes,void 0,r);if(i===!1||i===null)return null;const{tr:s}=e,o=r[r.length-1],l=r[0];if(o){const a=l.search(/\S/),c=t.from+l.indexOf(o),u=c+o.length;if(Eo(t.from,t.to,e.doc).filter(h=>h.mark.type.excluded.find(m=>m===n.type&&m!==h.mark.type)).filter(h=>h.to>c).length)return null;u<t.to&&s.delete(u,t.to),c>t.from&&s.delete(t.from+a,c);const f=t.from+a+o.length;s.addMark(t.from+a,f,n.type.create(i||{})),s.removeStoredMark(n.type)}}})}function Ld(n){return new bn({find:n.find,handler:({state:e,range:t,match:r})=>{const i=E(n.getAttributes,void 0,r)||{},{tr:s}=e,o=t.from;let l=t.to;const a=n.type.create(i);if(r[1]){const c=r[0].lastIndexOf(r[1]);let u=o+c;u>l?u=l:l=u+r[1].length;const d=r[0][r[0].length-1];s.insertText(d,o+r[0].length-1),s.replaceWith(u,l,a)}else if(r[0]){const c=n.type.isInline?o:o-1;s.insert(c,n.type.create(i)).delete(s.mapping.map(o),s.mapping.map(l))}s.scrollIntoView()}})}function ar(n){return new bn({find:n.find,handler:({state:e,range:t,match:r})=>{const i=e.doc.resolve(t.from),s=E(n.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),n.type))return null;e.tr.delete(t.from,t.to).setBlockType(t.from,t.from,n.type,s)}})}function It(n){return new bn({find:n.find,handler:({state:e,range:t,match:r,chain:i})=>{const s=E(n.getAttributes,void 0,r)||{},o=e.tr.delete(t.from,t.to),a=o.doc.resolve(t.from).blockRange(),c=a&&fr(a,n.type,s);if(!c)return null;if(o.wrap(a,c),n.keepMarks&&n.editor){const{selection:d,storedMarks:f}=e,{splittableMarks:h}=n.editor.extensionManager,p=f||d.$to.parentOffset&&d.$from.marks();if(p){const m=p.filter(g=>h.includes(g.type.name));o.ensureMarks(m)}}if(n.keepAttributes){const d=n.type.name==="bulletList"||n.type.name==="orderedList"?"listItem":"taskList";i().updateAttributes(d,s).run()}const u=o.doc.resolve(t.from-1).nodeBefore;u&&u.type===n.type&&Be(o.doc,t.from-1)&&(!n.joinPredicate||n.joinPredicate(r,u))&&o.join(t.from-1)}})}class te{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=E(S(this,"addOptions",{name:this.name}))),this.storage=E(S(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new te(e)}configure(e={}){const t=this.extend({...this.config,addOptions:()=>kn(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){const t=new te(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=E(S(t,"addOptions",{name:t.name})),t.storage=E(S(t,"addStorage",{name:t.name,options:t.options})),t}}function ht(n){return new eu({find:n.find,handler:({state:e,range:t,match:r,pasteEvent:i})=>{const s=E(n.getAttributes,void 0,r,i);if(s===!1||s===null)return null;const{tr:o}=e,l=r[r.length-1],a=r[0];let c=t.to;if(l){const u=a.search(/\S/),d=t.from+a.indexOf(l),f=d+l.length;if(Eo(t.from,t.to,e.doc).filter(p=>p.mark.type.excluded.find(g=>g===n.type&&g!==p.mark.type)).filter(p=>p.to>d).length)return null;f<t.to&&o.delete(f,t.to),d>t.from&&o.delete(t.from+u,d),c=t.from+u+l.length,o.addMark(t.from+u,c,n.type.create(s||{})),o.removeStoredMark(n.type)}}})}function Fd(n){return n&&n.__esModule&&Object.prototype.hasOwnProperty.call(n,"default")?n.default:n}var No={exports:{}},Vn={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $i;function Vd(){if($i)return Vn;$i=1;var n=_;function e(d,f){return d===f&&(d!==0||1/d===1/f)||d!==d&&f!==f}var t=typeof Object.is=="function"?Object.is:e,r=n.useState,i=n.useEffect,s=n.useLayoutEffect,o=n.useDebugValue;function l(d,f){var h=f(),p=r({inst:{value:h,getSnapshot:f}}),m=p[0].inst,g=p[1];return s(function(){m.value=h,m.getSnapshot=f,a(m)&&g({inst:m})},[d,h,f]),i(function(){return a(m)&&g({inst:m}),d(function(){a(m)&&g({inst:m})})},[d]),o(h),h}function a(d){var f=d.getSnapshot;d=d.value;try{var h=f();return!t(d,h)}catch{return!0}}function c(d,f){return f()}var u=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?c:l;return Vn.useSyncExternalStore=n.useSyncExternalStore!==void 0?n.useSyncExternalStore:u,Vn}No.exports=Vd();var Dr=No.exports;const $d=(...n)=>e=>{n.forEach(t=>{typeof t=="function"?t(e):t&&(t.current=e)})},Hd=({contentComponent:n})=>{const e=Dr.useSyncExternalStore(n.subscribe,n.getSnapshot,n.getServerSnapshot);return _.createElement(_.Fragment,null,Object.values(e))};function Wd(){const n=new Set;let e={};return{subscribe(t){return n.add(t),()=>{n.delete(t)}},getSnapshot(){return e},getServerSnapshot(){return e},setRenderer(t,r){e={...e,[t]:Bo.createPortal(r.reactElement,r.element,t)},n.forEach(i=>i())},removeRenderer(t){const r={...e};delete r[t],e=r,n.forEach(i=>i())}}}class jd extends _.Component{constructor(e){var t;super(e),this.editorContentRef=_.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(!((t=e.editor)===null||t===void 0)&&t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){const e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;const t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=Wd(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe(()=>{this.setState(r=>r.hasContentComponentInitialized?r:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){const e=this.props.editor;if(!e||(this.initialized=!1,e.isDestroyed||e.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),e.contentComponent=null,!e.options.element.firstChild))return;const t=document.createElement("div");t.append(...e.options.element.childNodes),e.setOptions({element:t})}render(){const{editor:e,innerRef:t,...r}=this.props;return _.createElement(_.Fragment,null,_.createElement("div",{ref:$d(t,this.editorContentRef),...r}),(e==null?void 0:e.contentComponent)&&_.createElement(Hd,{contentComponent:e.contentComponent}))}}const Jd=oe.forwardRef((n,e)=>{const t=_.useMemo(()=>Math.floor(Math.random()*4294967295).toString(),[n.editor]);return _.createElement(jd,{key:t,innerRef:e,...n})}),ah=_.memo(Jd);var qd=function n(e,t){if(e===t)return!0;if(e&&t&&typeof e=="object"&&typeof t=="object"){if(e.constructor!==t.constructor)return!1;var r,i,s;if(Array.isArray(e)){if(r=e.length,r!=t.length)return!1;for(i=r;i--!==0;)if(!n(e[i],t[i]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;for(i of e.entries())if(!t.has(i[0]))return!1;for(i of e.entries())if(!n(i[1],t.get(i[0])))return!1;return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;for(i of e.entries())if(!t.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){if(r=e.length,r!=t.length)return!1;for(i=r;i--!==0;)if(e[i]!==t[i])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();if(s=Object.keys(e),r=s.length,r!==Object.keys(t).length)return!1;for(i=r;i--!==0;)if(!Object.prototype.hasOwnProperty.call(t,s[i]))return!1;for(i=r;i--!==0;){var o=s[i];if(!(o==="_owner"&&e.$$typeof)&&!n(e[o],t[o]))return!1}return!0}return e!==e&&t!==t},Kd=Fd(qd),vo={exports:{}},$n={};/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hi;function _d(){if(Hi)return $n;Hi=1;var n=_,e=Dr;function t(c,u){return c===u&&(c!==0||1/c===1/u)||c!==c&&u!==u}var r=typeof Object.is=="function"?Object.is:t,i=e.useSyncExternalStore,s=n.useRef,o=n.useEffect,l=n.useMemo,a=n.useDebugValue;return $n.useSyncExternalStoreWithSelector=function(c,u,d,f,h){var p=s(null);if(p.current===null){var m={hasValue:!1,value:null};p.current=m}else m=p.current;p=l(function(){function y(A){if(!M){if(M=!0,T=A,A=f(A),h!==void 0&&m.hasValue){var I=m.value;if(h(I,A))return N=I}return N=A}if(I=N,r(T,A))return I;var C=f(A);return h!==void 0&&h(I,C)?I:(T=A,N=C)}var M=!1,T,N,v=d===void 0?null:d;return[function(){return y(u())},v===null?void 0:function(){return y(v())}]},[u,d,f,h]);var g=i(c,p[0],p[1]);return o(function(){m.hasValue=!0,m.value=g},[g]),a(g),g},$n}vo.exports=_d();var Ud=vo.exports;const Gd=typeof window<"u"?oe.useLayoutEffect:oe.useEffect;class Yd{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber?this.lastSnapshot:(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber},this.lastSnapshot)}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){const t=()=>{this.transactionNumber+=1,this.subscribers.forEach(i=>i())},r=this.editor;return r.on("transaction",t),()=>{r.off("transaction",t)}}}}function Xd(n){var e;const[t]=oe.useState(()=>new Yd(n.editor)),r=Ud.useSyncExternalStoreWithSelector(t.subscribe,t.getSnapshot,t.getServerSnapshot,n.selector,(e=n.equalityFn)!==null&&e!==void 0?e:Kd);return Gd(()=>t.watch(n.editor),[n.editor,t]),oe.useDebugValue(r),r}const Zd=!1,cr=typeof window>"u",Qd=cr||!!(typeof window<"u"&&window.next);class Ir{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(t=>t())}getInitialEditor(){if(this.options.current.immediatelyRender===void 0)return cr||Qd?null:this.createEditor();if(this.options.current.immediatelyRender&&cr&&Zd)throw new Error("Tiptap Error: SSR has been detected, and `immediatelyRender` has been set to `true` this is an unsupported configuration that may result in errors, explicitly set `immediatelyRender` to `false` to avoid hydration mismatches.");return this.options.current.immediatelyRender?this.createEditor():null}createEditor(){const e={...this.options.current,onBeforeCreate:(...r)=>{var i,s;return(s=(i=this.options.current).onBeforeCreate)===null||s===void 0?void 0:s.call(i,...r)},onBlur:(...r)=>{var i,s;return(s=(i=this.options.current).onBlur)===null||s===void 0?void 0:s.call(i,...r)},onCreate:(...r)=>{var i,s;return(s=(i=this.options.current).onCreate)===null||s===void 0?void 0:s.call(i,...r)},onDestroy:(...r)=>{var i,s;return(s=(i=this.options.current).onDestroy)===null||s===void 0?void 0:s.call(i,...r)},onFocus:(...r)=>{var i,s;return(s=(i=this.options.current).onFocus)===null||s===void 0?void 0:s.call(i,...r)},onSelectionUpdate:(...r)=>{var i,s;return(s=(i=this.options.current).onSelectionUpdate)===null||s===void 0?void 0:s.call(i,...r)},onTransaction:(...r)=>{var i,s;return(s=(i=this.options.current).onTransaction)===null||s===void 0?void 0:s.call(i,...r)},onUpdate:(...r)=>{var i,s;return(s=(i=this.options.current).onUpdate)===null||s===void 0?void 0:s.call(i,...r)},onContentError:(...r)=>{var i,s;return(s=(i=this.options.current).onContentError)===null||s===void 0?void 0:s.call(i,...r)},onDrop:(...r)=>{var i,s;return(s=(i=this.options.current).onDrop)===null||s===void 0?void 0:s.call(i,...r)},onPaste:(...r)=>{var i,s;return(s=(i=this.options.current).onPaste)===null||s===void 0?void 0:s.call(i,...r)}};return new zd(e)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every(r=>["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(r)?!0:r==="extensions"&&e.extensions&&t.extensions?e.extensions.length!==t.extensions.length?!1:e.extensions.every((i,s)=>{var o;return i===((o=t.extensions)===null||o===void 0?void 0:o[s])}):e[r]===t[r])}onRender(e){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&e.length===0?Ir.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(e),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(this.previousDeps===null){this.previousDeps=e;return}if(this.previousDeps.length===e.length&&this.previousDeps.every((r,i)=>r===e[i]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){const e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===e){t&&t.setOptions(this.options.current);return}t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))},1)}}function ch(n={},e=[]){const t=oe.useRef(n);t.current=n;const[r]=oe.useState(()=>new Ir(t)),i=Dr.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return oe.useDebugValue(i),oe.useEffect(r.onRender(e)),Xd({editor:i,selector:({transactionNumber:s})=>n.shouldRerenderOnTransaction===!1?null:n.immediatelyRender&&s===0?0:s+1}),i}const ef=oe.createContext({editor:null});ef.Consumer;const tf=oe.createContext({onDragStart:void 0}),nf=()=>oe.useContext(tf);_.forwardRef((n,e)=>{const{onDragStart:t}=nf(),r=n.as||"div";return _.createElement(r,{...n,ref:e,"data-node-view-wrapper":"",onDragStart:t,style:{whiteSpace:"normal",...n.style}})});const rf=/^\s*>\s$/,sf=te.create({name:"blockquote",addOptions(){return{HTMLAttributes:{}}},content:"block+",group:"block",defining:!0,parseHTML(){return[{tag:"blockquote"}]},renderHTML({HTMLAttributes:n}){return["blockquote",X(this.options.HTMLAttributes,n),0]},addCommands(){return{setBlockquote:()=>({commands:n})=>n.wrapIn(this.name),toggleBlockquote:()=>({commands:n})=>n.toggleWrap(this.name),unsetBlockquote:()=>({commands:n})=>n.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[It({find:rf,type:this.type})]}}),of=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,lf=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,af=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,cf=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,uf=Pe.create({name:"bold",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:n=>n.style.fontWeight!=="normal"&&null},{style:"font-weight=400",clearMark:n=>n.type.name===this.name},{style:"font-weight",getAttrs:n=>/^(bold(er)?|[5-9]\d{2,})$/.test(n)&&null}]},renderHTML({HTMLAttributes:n}){return["strong",X(this.options.HTMLAttributes,n),0]},addCommands(){return{setBold:()=>({commands:n})=>n.setMark(this.name),toggleBold:()=>({commands:n})=>n.toggleMark(this.name),unsetBold:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[ft({find:of,type:this.type}),ft({find:af,type:this.type})]},addPasteRules(){return[ht({find:lf,type:this.type}),ht({find:cf,type:this.type})]}}),df="listItem",Wi="textStyle",ji=/^\s*([-+*])\s$/,ff=te.create({name:"bulletList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:"ul"}]},renderHTML({HTMLAttributes:n}){return["ul",X(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleBulletList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(df,this.editor.getAttributes(Wi)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let n=It({find:ji,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(n=It({find:ji,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(Wi),editor:this.editor})),[n]}}),hf=/(^|[^`])`([^`]+)`(?!`)/,pf=/(^|[^`])`([^`]+)`(?!`)/g,mf=Pe.create({name:"code",addOptions(){return{HTMLAttributes:{}}},excludes:"_",code:!0,exitable:!0,parseHTML(){return[{tag:"code"}]},renderHTML({HTMLAttributes:n}){return["code",X(this.options.HTMLAttributes,n),0]},addCommands(){return{setCode:()=>({commands:n})=>n.setMark(this.name),toggleCode:()=>({commands:n})=>n.toggleMark(this.name),unsetCode:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[ft({find:hf,type:this.type})]},addPasteRules(){return[ht({find:pf,type:this.type})]}}),gf=/^```([a-z]+)?[\s\n]$/,yf=/^~~~([a-z]+)?[\s\n]$/,bf=te.create({name:"codeBlock",addOptions(){return{languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:n=>{var e;const{languageClassPrefix:t}=this.options,s=[...((e=n.firstElementChild)===null||e===void 0?void 0:e.classList)||[]].filter(o=>o.startsWith(t)).map(o=>o.replace(t,""))[0];return s||null},rendered:!1}}},parseHTML(){return[{tag:"pre",preserveWhitespace:"full"}]},renderHTML({node:n,HTMLAttributes:e}){return["pre",X(this.options.HTMLAttributes,e),["code",{class:n.attrs.language?this.options.languageClassPrefix+n.attrs.language:null},0]]},addCommands(){return{setCodeBlock:n=>({commands:e})=>e.setNode(this.name,n),toggleCodeBlock:n=>({commands:e})=>e.toggleNode(this.name,"paragraph",n)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:n,$anchor:e}=this.editor.state.selection,t=e.pos===1;return!n||e.parent.type.name!==this.name?!1:t||!e.parent.textContent.length?this.editor.commands.clearNodes():!1},Enter:({editor:n})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=n,{selection:t}=e,{$from:r,empty:i}=t;if(!i||r.parent.type!==this.type)return!1;const s=r.parentOffset===r.parent.nodeSize-2,o=r.parent.textContent.endsWith(`

`);return!s||!o?!1:n.chain().command(({tr:l})=>(l.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:n})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=n,{selection:t,doc:r}=e,{$from:i,empty:s}=t;if(!s||i.parent.type!==this.type||!(i.parentOffset===i.parent.nodeSize-2))return!1;const l=i.after();return l===void 0?!1:r.nodeAt(l)?n.commands.command(({tr:c})=>(c.setSelection(O.near(r.resolve(l))),!0)):n.commands.exitCode()}}},addInputRules(){return[ar({find:gf,type:this.type,getAttributes:n=>({language:n[1]})}),ar({find:yf,type:this.type,getAttributes:n=>({language:n[1]})})]},addProseMirrorPlugins(){return[new ne({key:new ge("codeBlockVSCodeHandler"),props:{handlePaste:(n,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;const t=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,s=i==null?void 0:i.mode;if(!t||!s)return!1;const{tr:o,schema:l}=n.state,a=l.text(t.replace(/\r\n?/g,`
`));return o.replaceSelectionWith(this.type.create({language:s},a)),o.selection.$from.parent.type!==this.type&&o.setSelection(w.near(o.doc.resolve(Math.max(0,o.selection.from-2)))),o.setMeta("paste",!0),n.dispatch(o),!0}}})]}}),kf=te.create({name:"doc",topNode:!0,content:"block+"});function Sf(n={}){return new ne({view(e){return new xf(e,n)}})}class xf{constructor(e,t){var r;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=(r=t.width)!==null&&r!==void 0?r:1,this.color=t.color===!1?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(i=>{let s=o=>{this[i](o)};return e.dom.addEventListener(i,s),{name:i,handler:s}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){this.cursorPos!=null&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,e==null?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e=this.editorView.state.doc.resolve(this.cursorPos),t=!e.parent.inlineContent,r,i=this.editorView.dom,s=i.getBoundingClientRect(),o=s.width/i.offsetWidth,l=s.height/i.offsetHeight;if(t){let d=e.nodeBefore,f=e.nodeAfter;if(d||f){let h=this.editorView.nodeDOM(this.cursorPos-(d?d.nodeSize:0));if(h){let p=h.getBoundingClientRect(),m=d?p.bottom:p.top;d&&f&&(m=(m+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let g=this.width/2*l;r={left:p.left,right:p.right,top:m-g,bottom:m+g}}}}if(!r){let d=this.editorView.coordsAtPos(this.cursorPos),f=this.width/2*o;r={left:d.left-f,right:d.left+f,top:d.top,bottom:d.bottom}}let a=this.editorView.dom.offsetParent;this.element||(this.element=a.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",t),this.element.classList.toggle("prosemirror-dropcursor-inline",!t);let c,u;if(!a||a==document.body&&getComputedStyle(a).position=="static")c=-pageXOffset,u=-pageYOffset;else{let d=a.getBoundingClientRect(),f=d.width/a.offsetWidth,h=d.height/a.offsetHeight;c=d.left-a.scrollLeft*f,u=d.top-a.scrollTop*h}this.element.style.left=(r.left-c)/o+"px",this.element.style.top=(r.top-u)/l+"px",this.element.style.width=(r.right-r.left)/o+"px",this.element.style.height=(r.bottom-r.top)/l+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),r=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),i=r&&r.type.spec.disableDropCursor,s=typeof i=="function"?i(this.editorView,t,e):i;if(t&&!s){let o=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let l=xs(this.editorView.state.doc,o,this.editorView.dragging.slice);l!=null&&(o=l)}this.setCursor(o),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}const Mf=Z.create({name:"dropCursor",addOptions(){return{color:"currentColor",width:1,class:void 0}},addProseMirrorPlugins(){return[Sf(this.options)]}});class R extends O{constructor(e){super(e,e)}map(e,t){let r=e.resolve(t.map(this.head));return R.valid(r)?new R(r):O.near(r)}content(){return k.empty}eq(e){return e instanceof R&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for GapCursor.fromJSON");return new R(e.resolve(t.pos))}getBookmark(){return new Rr(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!Cf(e)||!wf(e))return!1;let r=t.type.spec.allowGapCursor;if(r!=null)return r;let i=t.contentMatchAt(e.index()).defaultType;return i&&i.isTextblock}static findGapCursorFrom(e,t,r=!1){e:for(;;){if(!r&&R.valid(e))return e;let i=e.pos,s=null;for(let o=e.depth;;o--){let l=e.node(o);if(t>0?e.indexAfter(o)<l.childCount:e.index(o)>0){s=l.child(t>0?e.indexAfter(o):e.index(o)-1);break}else if(o==0)return null;i+=t;let a=e.doc.resolve(i);if(R.valid(a))return a}for(;;){let o=t>0?s.firstChild:s.lastChild;if(!o){if(s.isAtom&&!s.isText&&!x.isSelectable(s)){e=e.doc.resolve(i+s.nodeSize*t),r=!1;continue e}break}s=o,i+=t;let l=e.doc.resolve(i);if(R.valid(l))return l}return null}}}R.prototype.visible=!1;R.findFrom=R.findGapCursorFrom;O.jsonID("gapcursor",R);class Rr{constructor(e){this.pos=e}map(e){return new Rr(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return R.valid(t)?new R(t):O.near(t)}}function Cf(n){for(let e=n.depth;e>=0;e--){let t=n.index(e),r=n.node(e);if(t==0){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t-1);;i=i.lastChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function wf(n){for(let e=n.depth;e>=0;e--){let t=n.indexAfter(e),r=n.node(e);if(t==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let i=r.child(t);;i=i.firstChild){if(i.childCount==0&&!i.inlineContent||i.isAtom||i.type.spec.isolating)return!0;if(i.inlineContent)return!1}}return!0}function Tf(){return new ne({props:{decorations:vf,createSelectionBetween(n,e,t){return e.pos==t.pos&&R.valid(t)?new R(t):null},handleClick:Ef,handleKeyDown:Of,handleDOMEvents:{beforeinput:Nf}}})}const Of=co({ArrowLeft:jt("horiz",-1),ArrowRight:jt("horiz",1),ArrowUp:jt("vert",-1),ArrowDown:jt("vert",1)});function jt(n,e){const t=n=="vert"?e>0?"down":"up":e>0?"right":"left";return function(r,i,s){let o=r.selection,l=e>0?o.$to:o.$from,a=o.empty;if(o instanceof w){if(!s.endOfTextblock(t)||l.depth==0)return!1;a=!1,l=r.doc.resolve(e>0?l.after():l.before())}let c=R.findGapCursorFrom(l,e,a);return c?(i&&i(r.tr.setSelection(new R(c))),!0):!1}}function Ef(n,e,t){if(!n||!n.editable)return!1;let r=n.state.doc.resolve(e);if(!R.valid(r))return!1;let i=n.posAtCoords({left:t.clientX,top:t.clientY});return i&&i.inside>-1&&x.isSelectable(n.state.doc.nodeAt(i.inside))?!1:(n.dispatch(n.state.tr.setSelection(new R(r))),!0)}function Nf(n,e){if(e.inputType!="insertCompositionText"||!(n.state.selection instanceof R))return!1;let{$from:t}=n.state.selection,r=t.parent.contentMatchAt(t.index()).findWrapping(n.state.schema.nodes.text);if(!r)return!1;let i=b.empty;for(let o=r.length-1;o>=0;o--)i=b.from(r[o].createAndFill(null,i));let s=n.state.tr.replace(t.pos,t.pos,new k(i,0,0));return s.setSelection(w.near(s.doc.resolve(t.pos+1))),n.dispatch(s),!1}function vf(n){if(!(n.selection instanceof R))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",z.create(n.doc,[le.widget(n.selection.head,e,{key:"gapcursor"})])}const Af=Z.create({name:"gapCursor",addProseMirrorPlugins(){return[Tf()]},extendNodeSchema(n){var e;const t={name:n.name,options:n.options,storage:n.storage};return{allowGapCursor:(e=E(S(n,"allowGapCursor",t)))!==null&&e!==void 0?e:null}}}),Df=te.create({name:"hardBreak",addOptions(){return{keepMarks:!0,HTMLAttributes:{}}},inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML(){return[{tag:"br"}]},renderHTML({HTMLAttributes:n}){return["br",X(this.options.HTMLAttributes,n)]},renderText(){return`
`},addCommands(){return{setHardBreak:()=>({commands:n,chain:e,state:t,editor:r})=>n.first([()=>n.exitCode(),()=>n.command(()=>{const{selection:i,storedMarks:s}=t;if(i.$from.parent.type.spec.isolating)return!1;const{keepMarks:o}=this.options,{splittableMarks:l}=r.extensionManager,a=s||i.$to.parentOffset&&i.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:u})=>{if(u&&a&&o){const d=a.filter(f=>l.includes(f.type.name));c.ensureMarks(d)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),If=te.create({name:"heading",addOptions(){return{levels:[1,2,3,4,5,6],HTMLAttributes:{}}},content:"inline*",group:"block",defining:!0,addAttributes(){return{level:{default:1,rendered:!1}}},parseHTML(){return this.options.levels.map(n=>({tag:`h${n}`,attrs:{level:n}}))},renderHTML({node:n,HTMLAttributes:e}){return[`h${this.options.levels.includes(n.attrs.level)?n.attrs.level:this.options.levels[0]}`,X(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.setNode(this.name,n):!1,toggleHeading:n=>({commands:e})=>this.options.levels.includes(n.level)?e.toggleNode(this.name,"paragraph",n):!1}},addKeyboardShortcuts(){return this.options.levels.reduce((n,e)=>({...n,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}),{})},addInputRules(){return this.options.levels.map(n=>ar({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${n}})\\s$`),type:this.type,getAttributes:{level:n}}))}});var ln=200,V=function(){};V.prototype.append=function(e){return e.length?(e=V.from(e),!this.length&&e||e.length<ln&&this.leafAppend(e)||this.length<ln&&e.leafPrepend(this)||this.appendInner(e)):this};V.prototype.prepend=function(e){return e.length?V.from(e).append(this):this};V.prototype.appendInner=function(e){return new Rf(this,e)};V.prototype.slice=function(e,t){return e===void 0&&(e=0),t===void 0&&(t=this.length),e>=t?V.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))};V.prototype.get=function(e){if(!(e<0||e>=this.length))return this.getInner(e)};V.prototype.forEach=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length),t<=r?this.forEachInner(e,t,r,0):this.forEachInvertedInner(e,t,r,0)};V.prototype.map=function(e,t,r){t===void 0&&(t=0),r===void 0&&(r=this.length);var i=[];return this.forEach(function(s,o){return i.push(e(s,o))},t,r),i};V.from=function(e){return e instanceof V?e:e&&e.length?new Ao(e):V.empty};var Ao=function(n){function e(r){n.call(this),this.values=r}n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(i,s){return i==0&&s==this.length?this:new e(this.values.slice(i,s))},e.prototype.getInner=function(i){return this.values[i]},e.prototype.forEachInner=function(i,s,o,l){for(var a=s;a<o;a++)if(i(this.values[a],l+a)===!1)return!1},e.prototype.forEachInvertedInner=function(i,s,o,l){for(var a=s-1;a>=o;a--)if(i(this.values[a],l+a)===!1)return!1},e.prototype.leafAppend=function(i){if(this.length+i.length<=ln)return new e(this.values.concat(i.flatten()))},e.prototype.leafPrepend=function(i){if(this.length+i.length<=ln)return new e(i.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(V);V.empty=new Ao([]);var Rf=function(n){function e(t,r){n.call(this),this.left=t,this.right=r,this.length=t.length+r.length,this.depth=Math.max(t.depth,r.depth)+1}return n&&(e.__proto__=n),e.prototype=Object.create(n&&n.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(r){return r<this.left.length?this.left.get(r):this.right.get(r-this.left.length)},e.prototype.forEachInner=function(r,i,s,o){var l=this.left.length;if(i<l&&this.left.forEachInner(r,i,Math.min(s,l),o)===!1||s>l&&this.right.forEachInner(r,Math.max(i-l,0),Math.min(this.length,s)-l,o+l)===!1)return!1},e.prototype.forEachInvertedInner=function(r,i,s,o){var l=this.left.length;if(i>l&&this.right.forEachInvertedInner(r,i-l,Math.max(s,l)-l,o+l)===!1||s<l&&this.left.forEachInvertedInner(r,Math.min(i,l),s,o)===!1)return!1},e.prototype.sliceInner=function(r,i){if(r==0&&i==this.length)return this;var s=this.left.length;return i<=s?this.left.slice(r,i):r>=s?this.right.slice(r-s,i-s):this.left.slice(r,s).append(this.right.slice(0,i-s))},e.prototype.leafAppend=function(r){var i=this.right.leafAppend(r);if(i)return new e(this.left,i)},e.prototype.leafPrepend=function(r){var i=this.left.leafPrepend(r);if(i)return new e(i,this.right)},e.prototype.appendInner=function(r){return this.left.depth>=Math.max(this.right.depth,r.depth)+1?new e(this.left,new e(this.right,r)):new e(this,r)},e}(V);const Pf=500;class ue{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let r=this.items.length;for(;;r--)if(this.items.get(r-1).selection){--r;break}let i,s;t&&(i=this.remapping(r,this.items.length),s=i.maps.length);let o=e.tr,l,a,c=[],u=[];return this.items.forEach((d,f)=>{if(!d.step){i||(i=this.remapping(r,f+1),s=i.maps.length),s--,u.push(d);return}if(i){u.push(new fe(d.map));let h=d.step.map(i.slice(s)),p;h&&o.maybeStep(h).doc&&(p=o.mapping.maps[o.mapping.maps.length-1],c.push(new fe(p,void 0,void 0,c.length+u.length))),s--,p&&i.appendMap(p,s)}else o.maybeStep(d.step);if(d.selection)return l=i?d.selection.map(i.slice(s)):d.selection,a=new ue(this.items.slice(0,r).append(u.reverse().concat(c)),this.eventCount-1),!1},this.items.length,0),{remaining:a,transform:o,selection:l}}addTransform(e,t,r,i){let s=[],o=this.eventCount,l=this.items,a=!i&&l.length?l.get(l.length-1):null;for(let u=0;u<e.steps.length;u++){let d=e.steps[u].invert(e.docs[u]),f=new fe(e.mapping.maps[u],d,t),h;(h=a&&a.merge(f))&&(f=h,u?s.pop():l=l.slice(0,l.length-1)),s.push(f),t&&(o++,t=void 0),i||(a=f)}let c=o-r.depth;return c>zf&&(l=Bf(l,c),o-=c),new ue(l.append(s),o)}remapping(e,t){let r=new Tt;return this.items.forEach((i,s)=>{let o=i.mirrorOffset!=null&&s-i.mirrorOffset>=e?r.maps.length-i.mirrorOffset:void 0;r.appendMap(i.map,o)},e,t),r}addMaps(e){return this.eventCount==0?this:new ue(this.items.append(e.map(t=>new fe(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let r=[],i=Math.max(0,this.items.length-t),s=e.mapping,o=e.steps.length,l=this.eventCount;this.items.forEach(f=>{f.selection&&l--},i);let a=t;this.items.forEach(f=>{let h=s.getMirror(--a);if(h==null)return;o=Math.min(o,h);let p=s.maps[h];if(f.step){let m=e.steps[h].invert(e.docs[h]),g=f.selection&&f.selection.map(s.slice(a+1,h));g&&l++,r.push(new fe(p,m,g))}else r.push(new fe(p))},i);let c=[];for(let f=t;f<o;f++)c.push(new fe(s.maps[f]));let u=this.items.slice(0,i).append(c).append(r),d=new ue(u,l);return d.emptyItemCount()>Pf&&(d=d.compress(this.items.length-r.length)),d}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),r=t.maps.length,i=[],s=0;return this.items.forEach((o,l)=>{if(l>=e)i.push(o),o.selection&&s++;else if(o.step){let a=o.step.map(t.slice(r)),c=a&&a.getMap();if(r--,c&&t.appendMap(c,r),a){let u=o.selection&&o.selection.map(t.slice(r));u&&s++;let d=new fe(c.invert(),a,u),f,h=i.length-1;(f=i.length&&i[h].merge(d))?i[h]=f:i.push(d)}}else o.map&&r--},this.items.length,0),new ue(V.from(i.reverse()),s)}}ue.empty=new ue(V.empty,0);function Bf(n,e){let t;return n.forEach((r,i)=>{if(r.selection&&e--==0)return t=i,!1}),n.slice(t)}class fe{constructor(e,t,r,i){this.map=e,this.step=t,this.selection=r,this.mirrorOffset=i}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new fe(t.getMap().invert(),t,this.selection)}}}class Ce{constructor(e,t,r,i,s){this.done=e,this.undone=t,this.prevRanges=r,this.prevTime=i,this.prevComposition=s}}const zf=20;function Lf(n,e,t,r){let i=t.getMeta(_e),s;if(i)return i.historyState;t.getMeta($f)&&(n=new Ce(n.done,n.undone,null,0,-1));let o=t.getMeta("appendedTransaction");if(t.steps.length==0)return n;if(o&&o.getMeta(_e))return o.getMeta(_e).redo?new Ce(n.done.addTransform(t,void 0,r,_t(e)),n.undone,Ji(t.mapping.maps),n.prevTime,n.prevComposition):new Ce(n.done,n.undone.addTransform(t,void 0,r,_t(e)),null,n.prevTime,n.prevComposition);if(t.getMeta("addToHistory")!==!1&&!(o&&o.getMeta("addToHistory")===!1)){let l=t.getMeta("composition"),a=n.prevTime==0||!o&&n.prevComposition!=l&&(n.prevTime<(t.time||0)-r.newGroupDelay||!Ff(t,n.prevRanges)),c=o?Hn(n.prevRanges,t.mapping):Ji(t.mapping.maps);return new Ce(n.done.addTransform(t,a?e.selection.getBookmark():void 0,r,_t(e)),ue.empty,c,t.time,l??n.prevComposition)}else return(s=t.getMeta("rebased"))?new Ce(n.done.rebased(t,s),n.undone.rebased(t,s),Hn(n.prevRanges,t.mapping),n.prevTime,n.prevComposition):new Ce(n.done.addMaps(t.mapping.maps),n.undone.addMaps(t.mapping.maps),Hn(n.prevRanges,t.mapping),n.prevTime,n.prevComposition)}function Ff(n,e){if(!e)return!1;if(!n.docChanged)return!0;let t=!1;return n.mapping.maps[0].forEach((r,i)=>{for(let s=0;s<e.length;s+=2)r<=e[s+1]&&i>=e[s]&&(t=!0)}),t}function Ji(n){let e=[];for(let t=n.length-1;t>=0&&e.length==0;t--)n[t].forEach((r,i,s,o)=>e.push(s,o));return e}function Hn(n,e){if(!n)return null;let t=[];for(let r=0;r<n.length;r+=2){let i=e.map(n[r],1),s=e.map(n[r+1],-1);i<=s&&t.push(i,s)}return t}function Vf(n,e,t){let r=_t(e),i=_e.get(e).spec.config,s=(t?n.undone:n.done).popEvent(e,r);if(!s)return null;let o=s.selection.resolve(s.transform.doc),l=(t?n.done:n.undone).addTransform(s.transform,e.selection.getBookmark(),i,r),a=new Ce(t?l:s.remaining,t?s.remaining:l,null,0,-1);return s.transform.setSelection(o).setMeta(_e,{redo:t,historyState:a})}let Wn=!1,qi=null;function _t(n){let e=n.plugins;if(qi!=e){Wn=!1,qi=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){Wn=!0;break}}return Wn}const _e=new ge("history"),$f=new ge("closeHistory");function Hf(n={}){return n={depth:n.depth||100,newGroupDelay:n.newGroupDelay||500},new ne({key:_e,state:{init(){return new Ce(ue.empty,ue.empty,null,0,-1)},apply(e,t,r){return Lf(t,r,e,n)}},config:n,props:{handleDOMEvents:{beforeinput(e,t){let r=t.inputType,i=r=="historyUndo"?Io:r=="historyRedo"?Ro:null;return i?(t.preventDefault(),i(e.state,e.dispatch)):!1}}}})}function Do(n,e){return(t,r)=>{let i=_e.getState(t);if(!i||(n?i.undone:i.done).eventCount==0)return!1;if(r){let s=Vf(i,t,n);s&&r(e?s.scrollIntoView():s)}return!0}}const Io=Do(!1,!0),Ro=Do(!0,!0),Wf=Z.create({name:"history",addOptions(){return{depth:100,newGroupDelay:500}},addCommands(){return{undo:()=>({state:n,dispatch:e})=>Io(n,e),redo:()=>({state:n,dispatch:e})=>Ro(n,e)}},addProseMirrorPlugins(){return[Hf(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),jf=te.create({name:"horizontalRule",addOptions(){return{HTMLAttributes:{}}},group:"block",parseHTML(){return[{tag:"hr"}]},renderHTML({HTMLAttributes:n}){return["hr",X(this.options.HTMLAttributes,n)]},addCommands(){return{setHorizontalRule:()=>({chain:n,state:e})=>{const{selection:t}=e,{$from:r,$to:i}=t,s=n();return r.parentOffset===0?s.insertContentAt({from:Math.max(r.pos-1,0),to:i.pos},{type:this.name}):sd(t)?s.insertContentAt(i.pos,{type:this.name}):s.insertContent({type:this.name}),s.command(({tr:o,dispatch:l})=>{var a;if(l){const{$to:c}=o.selection,u=c.end();if(c.nodeAfter)c.nodeAfter.isTextblock?o.setSelection(w.create(o.doc,c.pos+1)):c.nodeAfter.isBlock?o.setSelection(x.create(o.doc,c.pos)):o.setSelection(w.create(o.doc,c.pos));else{const d=(a=c.parent.type.contentMatch.defaultType)===null||a===void 0?void 0:a.create();d&&(o.insert(u,d),o.setSelection(w.create(o.doc,u+1)))}o.scrollIntoView()}return!0}).run()}}},addInputRules(){return[Ld({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),Jf=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,qf=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,Kf=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,_f=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,Uf=Pe.create({name:"italic",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:n=>n.style.fontStyle!=="normal"&&null},{style:"font-style=normal",clearMark:n=>n.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:n}){return["em",X(this.options.HTMLAttributes,n),0]},addCommands(){return{setItalic:()=>({commands:n})=>n.setMark(this.name),toggleItalic:()=>({commands:n})=>n.toggleMark(this.name),unsetItalic:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[ft({find:Jf,type:this.type}),ft({find:Kf,type:this.type})]},addPasteRules(){return[ht({find:qf,type:this.type}),ht({find:_f,type:this.type})]}}),Gf=te.create({name:"listItem",addOptions(){return{HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}},content:"paragraph block*",defining:!0,parseHTML(){return[{tag:"li"}]},renderHTML({HTMLAttributes:n}){return["li",X(this.options.HTMLAttributes,n),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),Yf="listItem",Ki="textStyle",_i=/^(\d+)\.\s$/,Xf=te.create({name:"orderedList",addOptions(){return{itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}},group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes(){return{start:{default:1,parseHTML:n=>n.hasAttribute("start")?parseInt(n.getAttribute("start")||"",10):1},type:{default:null,parseHTML:n=>n.getAttribute("type")}}},parseHTML(){return[{tag:"ol"}]},renderHTML({HTMLAttributes:n}){const{start:e,...t}=n;return e===1?["ol",X(this.options.HTMLAttributes,t),0]:["ol",X(this.options.HTMLAttributes,n),0]},addCommands(){return{toggleOrderedList:()=>({commands:n,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes(Yf,this.editor.getAttributes(Ki)).run():n.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let n=It({find:_i,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(n=It({find:_i,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(Ki)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[n]}}),Zf=te.create({name:"paragraph",priority:1e3,addOptions(){return{HTMLAttributes:{}}},group:"block",content:"inline*",parseHTML(){return[{tag:"p"}]},renderHTML({HTMLAttributes:n}){return["p",X(this.options.HTMLAttributes,n),0]},addCommands(){return{setParagraph:()=>({commands:n})=>n.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),Qf=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,eh=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,th=Pe.create({name:"strike",addOptions(){return{HTMLAttributes:{}}},parseHTML(){return[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:n=>n.includes("line-through")?{}:!1}]},renderHTML({HTMLAttributes:n}){return["s",X(this.options.HTMLAttributes,n),0]},addCommands(){return{setStrike:()=>({commands:n})=>n.setMark(this.name),toggleStrike:()=>({commands:n})=>n.toggleMark(this.name),unsetStrike:()=>({commands:n})=>n.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[ft({find:Qf,type:this.type})]},addPasteRules(){return[ht({find:eh,type:this.type})]}}),nh=te.create({name:"text",group:"inline"}),uh=Z.create({name:"starterKit",addExtensions(){const n=[];return this.options.bold!==!1&&n.push(uf.configure(this.options.bold)),this.options.blockquote!==!1&&n.push(sf.configure(this.options.blockquote)),this.options.bulletList!==!1&&n.push(ff.configure(this.options.bulletList)),this.options.code!==!1&&n.push(mf.configure(this.options.code)),this.options.codeBlock!==!1&&n.push(bf.configure(this.options.codeBlock)),this.options.document!==!1&&n.push(kf.configure(this.options.document)),this.options.dropcursor!==!1&&n.push(Mf.configure(this.options.dropcursor)),this.options.gapcursor!==!1&&n.push(Af.configure(this.options.gapcursor)),this.options.hardBreak!==!1&&n.push(Df.configure(this.options.hardBreak)),this.options.heading!==!1&&n.push(If.configure(this.options.heading)),this.options.history!==!1&&n.push(Wf.configure(this.options.history)),this.options.horizontalRule!==!1&&n.push(jf.configure(this.options.horizontalRule)),this.options.italic!==!1&&n.push(Uf.configure(this.options.italic)),this.options.listItem!==!1&&n.push(Gf.configure(this.options.listItem)),this.options.orderedList!==!1&&n.push(Xf.configure(this.options.orderedList)),this.options.paragraph!==!1&&n.push(Zf.configure(this.options.paragraph)),this.options.strike!==!1&&n.push(th.configure(this.options.strike)),this.options.text!==!1&&n.push(nh.configure(this.options.text)),n}});export{bf as C,le as D,ah as E,Pe as M,te as N,ne as P,uh as S,ht as a,ge as b,ih as c,Eo as d,td as e,oh as f,lh as g,sh as h,z as i,X as m,Ld as n,ch as u};
//# sourceMappingURL=editor-cda9e262.js.map
