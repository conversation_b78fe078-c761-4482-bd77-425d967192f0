pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Macintosh
  Author: <PERSON> (http://www.kreativekorp.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme macintosh
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #000000  Default Background
base01  #404040  Lighter Background (Used for status bars, line number and folding marks)
base02  #404040  Selection Background
base03  #808080  Comments, Invisibles, Line Highlighting
base04  #808080  Dark Foreground (Used for status bars)
base05  #c0c0c0  Default Foreground, Caret, Delimiters, Operators
base06  #c0c0c0  Light Foreground (Not often used)
base07  #ffffff  Light Background (Not often used)
base08  #dd0907  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ff6403  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #fbf305  Classes, Markup Bold, Search Text Background
base0B  #1fb714  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #02abea  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #0000d3  Functions, Methods, Attribute IDs, Headings
base0E  #4700a5  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #90713a  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #c0c0c0;
  background: #000000
}
.hljs::selection,
.hljs ::selection {
  background-color: #404040;
  color: #c0c0c0
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #808080 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #808080
}
/* base04 - #808080 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #808080
}
/* base05 - #c0c0c0 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #c0c0c0
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #dd0907
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ff6403
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #fbf305
}
.hljs-strong {
  font-weight: bold;
  color: #fbf305
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #1fb714
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #02abea
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #0000d3
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #4700a5
}
.hljs-emphasis {
  color: #4700a5;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #90713a
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}