import{r as g,b as qa,c as Va,g as Xa,R as Ja}from"./vendor-5f6cd04d.js";import{N as Qa,m as ss,n as Za,M as Ya,a as ei,P as zr,b as Ur,c as ti,g as ri,f as si,d as ni,e as ai,C as ii,h as ns,D as li,i as oi,u as ci,S as di,E as ui}from"./editor-cda9e262.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const l of a)if(l.type==="childList")for(const i of l.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function s(a){const l={};return a.integrity&&(l.integrity=a.integrity),a.referrerPolicy&&(l.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?l.credentials="include":a.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function n(a){if(a.ep)return;a.ep=!0;const l=s(a);fetch(a.href,l)}})();var Bn={exports:{}},Hr={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var hi=g,gi=Symbol.for("react.element"),fi=Symbol.for("react.fragment"),xi=Object.prototype.hasOwnProperty,pi=hi.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,mi={key:!0,ref:!0,__self:!0,__source:!0};function Fn(e,t,s){var n,a={},l=null,i=null;s!==void 0&&(l=""+s),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(i=t.ref);for(n in t)xi.call(t,n)&&!mi.hasOwnProperty(n)&&(a[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)a[n]===void 0&&(a[n]=t[n]);return{$$typeof:gi,type:e,key:l,ref:i,props:a,_owner:pi.current}}Hr.Fragment=fi;Hr.jsx=Fn;Hr.jsxs=Fn;Bn.exports=Hr;var r=Bn.exports,as={},Qs=qa;as.createRoot=Qs.createRoot,as.hydrateRoot=Qs.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ot(){return Ot=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Ot.apply(this,arguments)}var Be;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Be||(Be={}));const Zs="popstate";function yi(e){e===void 0&&(e={});function t(n,a){let{pathname:l,search:i,hash:o}=n.location;return is("",{pathname:l,search:i,hash:o},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function s(n,a){return typeof a=="string"?a:nr(a)}return vi(t,s,null,e)}function oe(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Wn(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function bi(){return Math.random().toString(36).substr(2,8)}function Ys(e,t){return{usr:e.state,key:e.key,idx:t}}function is(e,t,s,n){return s===void 0&&(s=null),Ot({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?xt(t):t,{state:s,key:t&&t.key||n||bi()})}function nr(e){let{pathname:t="/",search:s="",hash:n=""}=e;return s&&s!=="?"&&(t+=s.charAt(0)==="?"?s:"?"+s),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function xt(e){let t={};if(e){let s=e.indexOf("#");s>=0&&(t.hash=e.substr(s),e=e.substr(0,s));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function vi(e,t,s,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:l=!1}=n,i=a.history,o=Be.Pop,c=null,h=d();h==null&&(h=0,i.replaceState(Ot({},i.state,{idx:h}),""));function d(){return(i.state||{idx:null}).idx}function f(){o=Be.Pop;let y=d(),k=y==null?null:y-h;h=y,c&&c({action:o,location:R.location,delta:k})}function b(y,k){o=Be.Push;let w=is(R.location,y,k);s&&s(w,y),h=d()+1;let L=Ys(w,h),G=R.createHref(w);try{i.pushState(L,"",G)}catch(O){if(O instanceof DOMException&&O.name==="DataCloneError")throw O;a.location.assign(G)}l&&c&&c({action:o,location:R.location,delta:1})}function m(y,k){o=Be.Replace;let w=is(R.location,y,k);s&&s(w,y),h=d();let L=Ys(w,h),G=R.createHref(w);i.replaceState(L,"",G),l&&c&&c({action:o,location:R.location,delta:0})}function j(y){let k=a.location.origin!=="null"?a.location.origin:a.location.href,w=typeof y=="string"?y:nr(y);return w=w.replace(/ $/,"%20"),oe(k,"No window.location.(origin|href) available to create URL for href: "+w),new URL(w,k)}let R={get action(){return o},get location(){return e(a,i)},listen(y){if(c)throw new Error("A history only accepts one active listener");return a.addEventListener(Zs,f),c=y,()=>{a.removeEventListener(Zs,f),c=null}},createHref(y){return t(a,y)},createURL:j,encodeLocation(y){let k=j(y);return{pathname:k.pathname,search:k.search,hash:k.hash}},push:b,replace:m,go(y){return i.go(y)}};return R}var en;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(en||(en={}));function ji(e,t,s){return s===void 0&&(s="/"),ki(e,t,s,!1)}function ki(e,t,s,n){let a=typeof t=="string"?xt(t):t,l=ws(a.pathname||"/",s);if(l==null)return null;let i=Kn(e);wi(i);let o=null;for(let c=0;o==null&&c<i.length;++c){let h=_i(l);o=Li(i[c],h,n)}return o}function Kn(e,t,s,n){t===void 0&&(t=[]),s===void 0&&(s=[]),n===void 0&&(n="");let a=(l,i,o)=>{let c={relativePath:o===void 0?l.path||"":o,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};c.relativePath.startsWith("/")&&(oe(c.relativePath.startsWith(n),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(n.length));let h=Ke([n,c.relativePath]),d=s.concat(c);l.children&&l.children.length>0&&(oe(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+h+'".')),Kn(l.children,t,d,h)),!(l.path==null&&!l.index)&&t.push({path:h,score:Mi(h,l.index),routesMeta:d})};return e.forEach((l,i)=>{var o;if(l.path===""||!((o=l.path)!=null&&o.includes("?")))a(l,i);else for(let c of Gn(l.path))a(l,i,c)}),t}function Gn(e){let t=e.split("/");if(t.length===0)return[];let[s,...n]=t,a=s.endsWith("?"),l=s.replace(/\?$/,"");if(n.length===0)return a?[l,""]:[l];let i=Gn(n.join("/")),o=[];return o.push(...i.map(c=>c===""?l:[l,c].join("/"))),a&&o.push(...i),o.map(c=>e.startsWith("/")&&c===""?"/":c)}function wi(e){e.sort((t,s)=>t.score!==s.score?s.score-t.score:Ti(t.routesMeta.map(n=>n.childrenIndex),s.routesMeta.map(n=>n.childrenIndex)))}const Ni=/^:[\w-]+$/,Ei=3,Si=2,Ci=1,Ai=10,Ri=-2,tn=e=>e==="*";function Mi(e,t){let s=e.split("/"),n=s.length;return s.some(tn)&&(n+=Ri),t&&(n+=Si),s.filter(a=>!tn(a)).reduce((a,l)=>a+(Ni.test(l)?Ei:l===""?Ci:Ai),n)}function Ti(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Li(e,t,s){s===void 0&&(s=!1);let{routesMeta:n}=e,a={},l="/",i=[];for(let o=0;o<n.length;++o){let c=n[o],h=o===n.length-1,d=l==="/"?t:t.slice(l.length)||"/",f=rn({path:c.relativePath,caseSensitive:c.caseSensitive,end:h},d),b=c.route;if(!f&&h&&s&&!n[n.length-1].route.index&&(f=rn({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},d)),!f)return null;Object.assign(a,f.params),i.push({params:a,pathname:Ke([l,f.pathname]),pathnameBase:Di(Ke([l,f.pathnameBase])),route:b}),f.pathnameBase!=="/"&&(l=Ke([l,f.pathnameBase]))}return i}function rn(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[s,n]=Oi(e.path,e.caseSensitive,e.end),a=t.match(s);if(!a)return null;let l=a[0],i=l.replace(/(.)\/+$/,"$1"),o=a.slice(1);return{params:n.reduce((h,d,f)=>{let{paramName:b,isOptional:m}=d;if(b==="*"){let R=o[f]||"";i=l.slice(0,l.length-R.length).replace(/(.)\/+$/,"$1")}const j=o[f];return m&&!j?h[b]=void 0:h[b]=(j||"").replace(/%2F/g,"/"),h},{}),pathname:l,pathnameBase:i,pattern:e}}function Oi(e,t,s){t===void 0&&(t=!1),s===void 0&&(s=!0),Wn(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,o,c)=>(n.push({paramName:o,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function _i(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Wn(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function ws(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let s=t.endsWith("/")?t.length-1:t.length,n=e.charAt(s);return n&&n!=="/"?null:e.slice(s)||"/"}function Ii(e,t){t===void 0&&(t="/");let{pathname:s,search:n="",hash:a=""}=typeof e=="string"?xt(e):e;return{pathname:s?s.startsWith("/")?s:Pi(s,t):t,search:zi(n),hash:Ui(a)}}function Pi(e,t){let s=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?s.length>1&&s.pop():a!=="."&&s.push(a)}),s.length>1?s.join("/"):"/"}function qr(e,t,s,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+s+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function $i(e){return e.filter((t,s)=>s===0||t.route.path&&t.route.path.length>0)}function qn(e,t){let s=$i(e);return t?s.map((n,a)=>a===s.length-1?n.pathname:n.pathnameBase):s.map(n=>n.pathnameBase)}function Vn(e,t,s,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=xt(e):(a=Ot({},e),oe(!a.pathname||!a.pathname.includes("?"),qr("?","pathname","search",a)),oe(!a.pathname||!a.pathname.includes("#"),qr("#","pathname","hash",a)),oe(!a.search||!a.search.includes("#"),qr("#","search","hash",a)));let l=e===""||a.pathname==="",i=l?"/":a.pathname,o;if(i==null)o=s;else{let f=t.length-1;if(!n&&i.startsWith("..")){let b=i.split("/");for(;b[0]==="..";)b.shift(),f-=1;a.pathname=b.join("/")}o=f>=0?t[f]:"/"}let c=Ii(a,o),h=i&&i!=="/"&&i.endsWith("/"),d=(l||i===".")&&s.endsWith("/");return!c.pathname.endsWith("/")&&(h||d)&&(c.pathname+="/"),c}const Ke=e=>e.join("/").replace(/\/\/+/g,"/"),Di=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),zi=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ui=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Hi(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Xn=["post","put","patch","delete"];new Set(Xn);const Bi=["get",...Xn];new Set(Bi);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function _t(){return _t=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},_t.apply(this,arguments)}const Ns=g.createContext(null),Fi=g.createContext(null),st=g.createContext(null),Br=g.createContext(null),qe=g.createContext({outlet:null,matches:[],isDataRoute:!1}),Jn=g.createContext(null);function Wi(e,t){let{relative:s}=t===void 0?{}:t;Bt()||oe(!1);let{basename:n,navigator:a}=g.useContext(st),{hash:l,pathname:i,search:o}=Zn(e,{relative:s}),c=i;return n!=="/"&&(c=i==="/"?n:Ke([n,i])),a.createHref({pathname:c,search:o,hash:l})}function Bt(){return g.useContext(Br)!=null}function pt(){return Bt()||oe(!1),g.useContext(Br).location}function Qn(e){g.useContext(st).static||g.useLayoutEffect(e)}function Ft(){let{isDataRoute:e}=g.useContext(qe);return e?sl():Ki()}function Ki(){Bt()||oe(!1);let e=g.useContext(Ns),{basename:t,future:s,navigator:n}=g.useContext(st),{matches:a}=g.useContext(qe),{pathname:l}=pt(),i=JSON.stringify(qn(a,s.v7_relativeSplatPath)),o=g.useRef(!1);return Qn(()=>{o.current=!0}),g.useCallback(function(h,d){if(d===void 0&&(d={}),!o.current)return;if(typeof h=="number"){n.go(h);return}let f=Vn(h,JSON.parse(i),l,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Ke([t,f.pathname])),(d.replace?n.replace:n.push)(f,d.state,d)},[t,n,i,l,e])}function Wt(){let{matches:e}=g.useContext(qe),t=e[e.length-1];return t?t.params:{}}function Zn(e,t){let{relative:s}=t===void 0?{}:t,{future:n}=g.useContext(st),{matches:a}=g.useContext(qe),{pathname:l}=pt(),i=JSON.stringify(qn(a,n.v7_relativeSplatPath));return g.useMemo(()=>Vn(e,JSON.parse(i),l,s==="path"),[e,i,l,s])}function Gi(e,t){return qi(e,t)}function qi(e,t,s,n){Bt()||oe(!1);let{navigator:a}=g.useContext(st),{matches:l}=g.useContext(qe),i=l[l.length-1],o=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let h=pt(),d;if(t){var f;let y=typeof t=="string"?xt(t):t;c==="/"||(f=y.pathname)!=null&&f.startsWith(c)||oe(!1),d=y}else d=h;let b=d.pathname||"/",m=b;if(c!=="/"){let y=c.replace(/^\//,"").split("/");m="/"+b.replace(/^\//,"").split("/").slice(y.length).join("/")}let j=ji(e,{pathname:m}),R=Zi(j&&j.map(y=>Object.assign({},y,{params:Object.assign({},o,y.params),pathname:Ke([c,a.encodeLocation?a.encodeLocation(y.pathname).pathname:y.pathname]),pathnameBase:y.pathnameBase==="/"?c:Ke([c,a.encodeLocation?a.encodeLocation(y.pathnameBase).pathname:y.pathnameBase])})),l,s,n);return t&&R?g.createElement(Br.Provider,{value:{location:_t({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Be.Pop}},R):R}function Vi(){let e=rl(),t=Hi(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),s=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},l=null;return g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),s?g.createElement("pre",{style:a},s):null,l)}const Xi=g.createElement(Vi,null);class Ji extends g.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,s){return s.location!==t.location||s.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:s.error,location:s.location,revalidation:t.revalidation||s.revalidation}}componentDidCatch(t,s){console.error("React Router caught the following error during render",t,s)}render(){return this.state.error!==void 0?g.createElement(qe.Provider,{value:this.props.routeContext},g.createElement(Jn.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Qi(e){let{routeContext:t,match:s,children:n}=e,a=g.useContext(Ns);return a&&a.static&&a.staticContext&&(s.route.errorElement||s.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=s.route.id),g.createElement(qe.Provider,{value:t},n)}function Zi(e,t,s,n){var a;if(t===void 0&&(t=[]),s===void 0&&(s=null),n===void 0&&(n=null),e==null){var l;if(!s)return null;if(s.errors)e=s.matches;else if((l=n)!=null&&l.v7_partialHydration&&t.length===0&&!s.initialized&&s.matches.length>0)e=s.matches;else return null}let i=e,o=(a=s)==null?void 0:a.errors;if(o!=null){let d=i.findIndex(f=>f.route.id&&(o==null?void 0:o[f.route.id])!==void 0);d>=0||oe(!1),i=i.slice(0,Math.min(i.length,d+1))}let c=!1,h=-1;if(s&&n&&n.v7_partialHydration)for(let d=0;d<i.length;d++){let f=i[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(h=d),f.route.id){let{loaderData:b,errors:m}=s,j=f.route.loader&&b[f.route.id]===void 0&&(!m||m[f.route.id]===void 0);if(f.route.lazy||j){c=!0,h>=0?i=i.slice(0,h+1):i=[i[0]];break}}}return i.reduceRight((d,f,b)=>{let m,j=!1,R=null,y=null;s&&(m=o&&f.route.id?o[f.route.id]:void 0,R=f.route.errorElement||Xi,c&&(h<0&&b===0?(nl("route-fallback",!1),j=!0,y=null):h===b&&(j=!0,y=f.route.hydrateFallbackElement||null)));let k=t.concat(i.slice(0,b+1)),w=()=>{let L;return m?L=R:j?L=y:f.route.Component?L=g.createElement(f.route.Component,null):f.route.element?L=f.route.element:L=d,g.createElement(Qi,{match:f,routeContext:{outlet:d,matches:k,isDataRoute:s!=null},children:L})};return s&&(f.route.ErrorBoundary||f.route.errorElement||b===0)?g.createElement(Ji,{location:s.location,revalidation:s.revalidation,component:R,error:m,children:w(),routeContext:{outlet:null,matches:k,isDataRoute:!0}}):w()},null)}var Yn=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Yn||{}),ar=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ar||{});function Yi(e){let t=g.useContext(Ns);return t||oe(!1),t}function el(e){let t=g.useContext(Fi);return t||oe(!1),t}function tl(e){let t=g.useContext(qe);return t||oe(!1),t}function ea(e){let t=tl(),s=t.matches[t.matches.length-1];return s.route.id||oe(!1),s.route.id}function rl(){var e;let t=g.useContext(Jn),s=el(ar.UseRouteError),n=ea(ar.UseRouteError);return t!==void 0?t:(e=s.errors)==null?void 0:e[n]}function sl(){let{router:e}=Yi(Yn.UseNavigateStable),t=ea(ar.UseNavigateStable),s=g.useRef(!1);return Qn(()=>{s.current=!0}),g.useCallback(function(a,l){l===void 0&&(l={}),s.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,_t({fromRouteId:t},l)))},[e,t])}const sn={};function nl(e,t,s){!t&&!sn[e]&&(sn[e]=!0)}function al(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Se(e){oe(!1)}function il(e){let{basename:t="/",children:s=null,location:n,navigationType:a=Be.Pop,navigator:l,static:i=!1,future:o}=e;Bt()&&oe(!1);let c=t.replace(/^\/*/,"/"),h=g.useMemo(()=>({basename:c,navigator:l,static:i,future:_t({v7_relativeSplatPath:!1},o)}),[c,o,l,i]);typeof n=="string"&&(n=xt(n));let{pathname:d="/",search:f="",hash:b="",state:m=null,key:j="default"}=n,R=g.useMemo(()=>{let y=ws(d,c);return y==null?null:{location:{pathname:y,search:f,hash:b,state:m,key:j},navigationType:a}},[c,d,f,b,m,j,a]);return R==null?null:g.createElement(st.Provider,{value:h},g.createElement(Br.Provider,{children:s,value:R}))}function ll(e){let{children:t,location:s}=e;return Gi(ls(t),s)}new Promise(()=>{});function ls(e,t){t===void 0&&(t=[]);let s=[];return g.Children.forEach(e,(n,a)=>{if(!g.isValidElement(n))return;let l=[...t,a];if(n.type===g.Fragment){s.push.apply(s,ls(n.props.children,l));return}n.type!==Se&&oe(!1),!n.props.index||!n.props.children||oe(!1);let i={id:n.props.id||l.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(i.children=ls(n.props.children,l)),s.push(i)}),s}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function os(){return os=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},os.apply(this,arguments)}function ol(e,t){if(e==null)return{};var s={},n=Object.keys(e),a,l;for(l=0;l<n.length;l++)a=n[l],!(t.indexOf(a)>=0)&&(s[a]=e[a]);return s}function cl(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function dl(e,t){return e.button===0&&(!t||t==="_self")&&!cl(e)}function cs(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,s)=>{let n=e[s];return t.concat(Array.isArray(n)?n.map(a=>[s,a]):[[s,n]])},[]))}function ul(e,t){let s=cs(e);return t&&t.forEach((n,a)=>{s.has(a)||t.getAll(a).forEach(l=>{s.append(a,l)})}),s}const hl=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],gl="6";try{window.__reactRouterVersion=gl}catch{}const fl="startTransition",nn=Va[fl];function xl(e){let{basename:t,children:s,future:n,window:a}=e,l=g.useRef();l.current==null&&(l.current=yi({window:a,v5Compat:!0}));let i=l.current,[o,c]=g.useState({action:i.action,location:i.location}),{v7_startTransition:h}=n||{},d=g.useCallback(f=>{h&&nn?nn(()=>c(f)):c(f)},[c,h]);return g.useLayoutEffect(()=>i.listen(d),[i,d]),g.useEffect(()=>al(n),[n]),g.createElement(il,{basename:t,children:s,location:o.location,navigationType:o.action,navigator:i,future:n})}const pl=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",ml=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,J=g.forwardRef(function(t,s){let{onClick:n,relative:a,reloadDocument:l,replace:i,state:o,target:c,to:h,preventScrollReset:d,viewTransition:f}=t,b=ol(t,hl),{basename:m}=g.useContext(st),j,R=!1;if(typeof h=="string"&&ml.test(h)&&(j=h,pl))try{let L=new URL(window.location.href),G=h.startsWith("//")?new URL(L.protocol+h):new URL(h),O=ws(G.pathname,m);G.origin===L.origin&&O!=null?h=O+G.search+G.hash:R=!0}catch{}let y=Wi(h,{relative:a}),k=yl(h,{replace:i,state:o,target:c,preventScrollReset:d,relative:a,viewTransition:f});function w(L){n&&n(L),L.defaultPrevented||k(L)}return g.createElement("a",os({},b,{href:j||y,onClick:R||l?n:w,ref:s,target:c}))});var an;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(an||(an={}));var ln;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ln||(ln={}));function yl(e,t){let{target:s,replace:n,state:a,preventScrollReset:l,relative:i,viewTransition:o}=t===void 0?{}:t,c=Ft(),h=pt(),d=Zn(e,{relative:i});return g.useCallback(f=>{if(dl(f,s)){f.preventDefault();let b=n!==void 0?n:nr(h)===nr(d);c(e,{replace:b,state:a,preventScrollReset:l,relative:i,viewTransition:o})}},[h,c,d,n,a,s,e,l,i,o])}function bl(e){let t=g.useRef(cs(e)),s=g.useRef(!1),n=pt(),a=g.useMemo(()=>ul(n.search,s.current?null:t.current),[n.search]),l=Ft(),i=g.useCallback((o,c)=>{const h=cs(typeof o=="function"?o(a):o);s.current=!0,l("?"+h,c)},[l,a]);return[a,i]}let vl={data:""},jl=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||vl,kl=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,wl=/\/\*[^]*?\*\/|  +/g,on=/\n+/g,Ue=(e,t)=>{let s="",n="",a="";for(let l in e){let i=e[l];l[0]=="@"?l[1]=="i"?s=l+" "+i+";":n+=l[1]=="f"?Ue(i,l):l+"{"+Ue(i,l[1]=="k"?"":t)+"}":typeof i=="object"?n+=Ue(i,t?t.replace(/([^,])+/g,o=>l.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,o):o?o+" "+c:c)):l):i!=null&&(l=/^--/.test(l)?l:l.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=Ue.p?Ue.p(l,i):l+":"+i+";")}return s+(t&&a?t+"{"+a+"}":a)+n},Te={},ta=e=>{if(typeof e=="object"){let t="";for(let s in e)t+=s+ta(e[s]);return t}return e},Nl=(e,t,s,n,a)=>{let l=ta(e),i=Te[l]||(Te[l]=(c=>{let h=0,d=11;for(;h<c.length;)d=101*d+c.charCodeAt(h++)>>>0;return"go"+d})(l));if(!Te[i]){let c=l!==e?e:(h=>{let d,f,b=[{}];for(;d=kl.exec(h.replace(wl,""));)d[4]?b.shift():d[3]?(f=d[3].replace(on," ").trim(),b.unshift(b[0][f]=b[0][f]||{})):b[0][d[1]]=d[2].replace(on," ").trim();return b[0]})(e);Te[i]=Ue(a?{["@keyframes "+i]:c}:c,s?"":"."+i)}let o=s&&Te.g?Te.g:null;return s&&(Te.g=Te[i]),((c,h,d,f)=>{f?h.data=h.data.replace(f,c):h.data.indexOf(c)===-1&&(h.data=d?c+h.data:h.data+c)})(Te[i],t,n,o),i},El=(e,t,s)=>e.reduce((n,a,l)=>{let i=t[l];if(i&&i.call){let o=i(s),c=o&&o.props&&o.props.className||/^go/.test(o)&&o;i=c?"."+c:o&&typeof o=="object"?o.props?"":Ue(o,""):o===!1?"":o}return n+a+(i??"")},"");function Fr(e){let t=this||{},s=e.call?e(t.p):e;return Nl(s.unshift?s.raw?El(s,[].slice.call(arguments,1),t.p):s.reduce((n,a)=>Object.assign(n,a&&a.call?a(t.p):a),{}):s,jl(t.target),t.g,t.o,t.k)}let ra,ds,us;Fr.bind({g:1});let Ie=Fr.bind({k:1});function Sl(e,t,s,n){Ue.p=t,ra=e,ds=s,us=n}function Ve(e,t){let s=this||{};return function(){let n=arguments;function a(l,i){let o=Object.assign({},l),c=o.className||a.className;s.p=Object.assign({theme:ds&&ds()},o),s.o=/ *go\d+/.test(c),o.className=Fr.apply(s,n)+(c?" "+c:""),t&&(o.ref=i);let h=e;return e[0]&&(h=o.as||e,delete o.as),us&&h[0]&&us(o),ra(h,o)}return t?t(a):a}}var Cl=e=>typeof e=="function",ir=(e,t)=>Cl(e)?e(t):e,Al=(()=>{let e=0;return()=>(++e).toString()})(),sa=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),Rl=20,na=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,Rl)};case 1:return{...e,toasts:e.toasts.map(l=>l.id===t.toast.id?{...l,...t.toast}:l)};case 2:let{toast:s}=t;return na(e,{type:e.toasts.find(l=>l.id===s.id)?1:0,toast:s});case 3:let{toastId:n}=t;return{...e,toasts:e.toasts.map(l=>l.id===n||n===void 0?{...l,dismissed:!0,visible:!1}:l)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(l=>l.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(l=>({...l,pauseDuration:l.pauseDuration+a}))}}},er=[],Ze={toasts:[],pausedAt:void 0},nt=e=>{Ze=na(Ze,e),er.forEach(t=>{t(Ze)})},Ml={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Tl=(e={})=>{let[t,s]=g.useState(Ze),n=g.useRef(Ze);g.useEffect(()=>(n.current!==Ze&&s(Ze),er.push(s),()=>{let l=er.indexOf(s);l>-1&&er.splice(l,1)}),[]);let a=t.toasts.map(l=>{var i,o,c;return{...e,...e[l.type],...l,removeDelay:l.removeDelay||((i=e[l.type])==null?void 0:i.removeDelay)||(e==null?void 0:e.removeDelay),duration:l.duration||((o=e[l.type])==null?void 0:o.duration)||(e==null?void 0:e.duration)||Ml[l.type],style:{...e.style,...(c=e[l.type])==null?void 0:c.style,...l.style}}});return{...t,toasts:a}},Ll=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(s==null?void 0:s.id)||Al()}),Kt=e=>(t,s)=>{let n=Ll(t,e,s);return nt({type:2,toast:n}),n.id},be=(e,t)=>Kt("blank")(e,t);be.error=Kt("error");be.success=Kt("success");be.loading=Kt("loading");be.custom=Kt("custom");be.dismiss=e=>{nt({type:3,toastId:e})};be.remove=e=>nt({type:4,toastId:e});be.promise=(e,t,s)=>{let n=be.loading(t.loading,{...s,...s==null?void 0:s.loading});return typeof e=="function"&&(e=e()),e.then(a=>{let l=t.success?ir(t.success,a):void 0;return l?be.success(l,{id:n,...s,...s==null?void 0:s.success}):be.dismiss(n),a}).catch(a=>{let l=t.error?ir(t.error,a):void 0;l?be.error(l,{id:n,...s,...s==null?void 0:s.error}):be.dismiss(n)}),e};var Ol=(e,t)=>{nt({type:1,toast:{id:e,height:t}})},_l=()=>{nt({type:5,time:Date.now()})},Et=new Map,Il=1e3,Pl=(e,t=Il)=>{if(Et.has(e))return;let s=setTimeout(()=>{Et.delete(e),nt({type:4,toastId:e})},t);Et.set(e,s)},$l=e=>{let{toasts:t,pausedAt:s}=Tl(e);g.useEffect(()=>{if(s)return;let l=Date.now(),i=t.map(o=>{if(o.duration===1/0)return;let c=(o.duration||0)+o.pauseDuration-(l-o.createdAt);if(c<0){o.visible&&be.dismiss(o.id);return}return setTimeout(()=>be.dismiss(o.id),c)});return()=>{i.forEach(o=>o&&clearTimeout(o))}},[t,s]);let n=g.useCallback(()=>{s&&nt({type:6,time:Date.now()})},[s]),a=g.useCallback((l,i)=>{let{reverseOrder:o=!1,gutter:c=8,defaultPosition:h}=i||{},d=t.filter(m=>(m.position||h)===(l.position||h)&&m.height),f=d.findIndex(m=>m.id===l.id),b=d.filter((m,j)=>j<f&&m.visible).length;return d.filter(m=>m.visible).slice(...o?[b+1]:[0,b]).reduce((m,j)=>m+(j.height||0)+c,0)},[t]);return g.useEffect(()=>{t.forEach(l=>{if(l.dismissed)Pl(l.id,l.removeDelay);else{let i=Et.get(l.id);i&&(clearTimeout(i),Et.delete(l.id))}})},[t]),{toasts:t,handlers:{updateHeight:Ol,startPause:_l,endPause:n,calculateOffset:a}}},Dl=Ie`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,zl=Ie`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Ul=Ie`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Hl=Ve("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Dl} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${zl} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Ul} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Bl=Ie`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Fl=Ve("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Bl} 1s linear infinite;
`,Wl=Ie`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,Kl=Ie`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Gl=Ve("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Wl} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${Kl} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,ql=Ve("div")`
  position: absolute;
`,Vl=Ve("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Xl=Ie`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Jl=Ve("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Xl} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Ql=({toast:e})=>{let{icon:t,type:s,iconTheme:n}=e;return t!==void 0?typeof t=="string"?g.createElement(Jl,null,t):t:s==="blank"?null:g.createElement(Vl,null,g.createElement(Fl,{...n}),s!=="loading"&&g.createElement(ql,null,s==="error"?g.createElement(Hl,{...n}):g.createElement(Gl,{...n})))},Zl=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Yl=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,eo="0%{opacity:0;} 100%{opacity:1;}",to="0%{opacity:1;} 100%{opacity:0;}",ro=Ve("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,so=Ve("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,no=(e,t)=>{let s=e.includes("top")?1:-1,[n,a]=sa()?[eo,to]:[Zl(s),Yl(s)];return{animation:t?`${Ie(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Ie(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},ao=g.memo(({toast:e,position:t,style:s,children:n})=>{let a=e.height?no(e.position||t||"top-center",e.visible):{opacity:0},l=g.createElement(Ql,{toast:e}),i=g.createElement(so,{...e.ariaProps},ir(e.message,e));return g.createElement(ro,{className:e.className,style:{...a,...s,...e.style}},typeof n=="function"?n({icon:l,message:i}):g.createElement(g.Fragment,null,l,i))});Sl(g.createElement);var io=({id:e,className:t,style:s,onHeightUpdate:n,children:a})=>{let l=g.useCallback(i=>{if(i){let o=()=>{let c=i.getBoundingClientRect().height;n(e,c)};o(),new MutationObserver(o).observe(i,{subtree:!0,childList:!0,characterData:!0})}},[e,n]);return g.createElement("div",{ref:l,className:t,style:s},a)},lo=(e,t)=>{let s=e.includes("top"),n=s?{top:0}:{bottom:0},a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:sa()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...n,...a}},oo=Fr`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,qt=16,co=({reverseOrder:e,position:t="top-center",toastOptions:s,gutter:n,children:a,containerStyle:l,containerClassName:i})=>{let{toasts:o,handlers:c}=$l(s);return g.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:qt,left:qt,right:qt,bottom:qt,pointerEvents:"none",...l},className:i,onMouseEnter:c.startPause,onMouseLeave:c.endPause},o.map(h=>{let d=h.position||t,f=c.calculateOffset(h,{reverseOrder:e,gutter:n,defaultPosition:t}),b=lo(d,f);return g.createElement(io,{id:h.id,key:h.id,onHeightUpdate:c.updateHeight,className:h.visible?oo:"",style:b},h.type==="custom"?ir(h.message,h):a?a(h):g.createElement(ao,{toast:h,position:d}))}))};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var uo={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ho=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),D=(e,t)=>{const s=g.forwardRef(({color:n="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:c,...h},d)=>g.createElement("svg",{ref:d,...uo,width:a,height:a,stroke:n,strokeWidth:i?Number(l)*24/Number(a):l,className:["lucide",`lucide-${ho(e)}`,o].join(" "),...h},[...t.map(([f,b])=>g.createElement(f,b)),...Array.isArray(c)?c:[c]]));return s.displayName=`${e}`,s};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lr=D("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const go=D("Bold",[["path",{d:"M14 12a4 4 0 0 0 0-8H6v8",key:"v2sylx"}],["path",{d:"M15 20a4 4 0 0 0 0-8H6v8Z",key:"1ef5ya"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=D("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tr=D("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fo=D("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rt=D("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xo=D("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const po=D("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mo=D("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const aa=D("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ia=D("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yo=D("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vt=D("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bo=D("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vo=D("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jo=D("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ko=D("Heading2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wo=D("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const No=D("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cn=D("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jt=D("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eo=D("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dn=D("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const So=D("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Co=D("ListOrdered",[["line",{x1:"10",x2:"21",y1:"6",y2:"6",key:"76qw6h"}],["line",{x1:"10",x2:"21",y1:"12",y2:"12",key:"16nom4"}],["line",{x1:"10",x2:"21",y1:"18",y2:"18",key:"u3jurt"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ao=D("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ro=D("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Mo=D("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const la=D("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const un=D("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const To=D("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vr=D("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Lo=D("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oo=D("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wt=D("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _o=D("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hs=D("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Io=D("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Po=D("Quote",[["path",{d:"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z",key:"4rm80e"}],["path",{d:"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z",key:"10za9r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $o=D("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Do=D("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const It=D("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zo=D("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ut=D("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Uo=D("SkipBack",[["polygon",{points:"19 20 9 12 19 4 19 20",key:"o2sva"}],["line",{x1:"5",x2:"5",y1:"19",y2:"5",key:"1ocqjk"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ho=D("SkipForward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hn=D("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bo=D("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fo=D("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const St=D("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wo=D("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gs=D("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ko=D("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Go=D("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mt=D("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qo=D("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vo=D("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const or=D("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function Xo(){const[e,t]=g.useState(()=>{const n=localStorage.getItem("theme");return n||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")});return g.useEffect(()=>{const n=document.documentElement;n.classList.remove("light","dark"),n.classList.add(e),localStorage.setItem("theme",e)},[e]),{theme:e,toggleTheme:()=>{t(n=>n==="light"?"dark":"light")},setTheme:t,isDark:e==="dark"}}const Jo={weather:"https://blog.fddfffff.site",aiImage:"https://blog.fddfffff.site",blog:"https://blog.fddfffff.site",music:"https://blog.fddfffff.site",r2Storage:"https://pub-02490a32db742d596d4d7c00aec.r2.dev"},ce=Jo,ae={weather:`${ce.weather}/weather`,aiImage:`${ce.aiImage}/ai/generate`,posts:`${ce.blog}/api/posts`,post:e=>`${ce.blog}/api/posts/${e}`,comments:e=>`${ce.blog}/api/posts/${e}/comments`,comment:(e,t)=>`${ce.blog}/api/posts/${e}/comments/${t}`,uploadImage:`${ce.blog}/api/upload-image`,auth:{verify:`${ce.blog}/api/auth/verify`},music:{search:`${ce.music}/music/search`,songUrl:`${ce.music}/music/url`,lyric:`${ce.music}/music/lyric`,songDetail:`${ce.music}/music/song/detail`,playlist:`${ce.music}/music/playlist/detail`},r2Images:`${ce.r2Storage}`,categories:`${ce.blog}/api/categories`,tags:`${ce.blog}/api/tags`,search:`${ce.blog}/api/search`},oa={"Content-Type":"application/json",Accept:"application/json"},ca=6e4;function Qo(){return localStorage.getItem("admin_api_key")}const Ee=(e,t={})=>{const s=new AbortController,n=setTimeout(()=>s.abort(),ca);return fetch(e,{headers:{...oa,...t.headers},signal:s.signal,...t}).finally(()=>{clearTimeout(n)})},rr=(e,t={})=>{const s=Qo(),n={...oa,...t.headers};s&&(n["X-API-Key"]=s);const a=new AbortController,l=setTimeout(()=>a.abort(),ca);return fetch(e,{headers:n,signal:a.signal,...t}).finally(()=>{clearTimeout(l)})};class cr extends Error{constructor(t,s,n){super(n||`API Error: ${t} ${s}`),this.status=t,this.statusText=s,this.name="ApiError"}}const de=async e=>{if(!e.ok){let t;try{const s=await e.json();t=s.error||s.message||e.statusText}catch{t=e.statusText}throw new cr(e.status,e.statusText,t)}return e.json()},da=g.createContext(void 0);function Gt(){const e=g.useContext(da);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e}function Zo({children:e}){const[t,s]=g.useState(!1),[n,a]=g.useState(null),[l,i]=g.useState(!0);g.useEffect(()=>{const f=localStorage.getItem("admin_api_key");f?o(f):i(!1)},[]);const o=async f=>{try{return(await(await fetch(ae.auth.verify,{method:"POST",headers:{"Content-Type":"application/json","X-API-Key":f}})).json()).isAdmin?(s(!0),a(f),localStorage.setItem("admin_api_key",f),!0):(s(!1),a(null),localStorage.removeItem("admin_api_key"),!1)}catch(b){return console.error("API密钥验证失败:",b),s(!1),a(null),localStorage.removeItem("admin_api_key"),!1}finally{i(!1)}},d={isAdmin:t,apiKey:n,login:async f=>(i(!0),await o(f)),logout:()=>{s(!1),a(null),localStorage.removeItem("admin_api_key")},isLoading:l};return r.jsx(da.Provider,{value:d,children:e})}function ua({isOpen:e,onClose:t}){const[s,n]=g.useState(""),[a,l]=g.useState(!1),[i,o]=g.useState(""),[c,h]=g.useState(!1),{login:d}=Gt();if(!e)return null;const f=async m=>{if(m.preventDefault(),!s.trim()){o("请输入API密钥");return}h(!0),o("");try{await d(s.trim())?(n(""),t()):o("API密钥无效，请检查后重试")}catch{o("登录失败，请稍后重试")}finally{h(!1)}},b=()=>{n(""),o(""),l(!1),t()};return r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:[r.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(ut,{className:"text-white",size:20})}),r.jsxs("div",{children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"管理员登录"}),r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"输入API密钥以获取管理权限"})]})]}),r.jsx("button",{onClick:b,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"关闭",children:r.jsx(or,{size:20})})]}),r.jsxs("form",{onSubmit:f,className:"p-6",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r.jsx(dn,{size:16,className:"inline mr-2"}),"API密钥"]}),r.jsxs("div",{className:"relative",children:[r.jsx("input",{type:a?"text":"password",value:s,onChange:m=>n(m.target.value),placeholder:"请输入管理员API密钥",className:"w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",disabled:c}),r.jsx("button",{type:"button",onClick:()=>l(!a),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",disabled:c,title:a?"隐藏密钥":"显示密钥",children:a?r.jsx(aa,{size:16}):r.jsx(ia,{size:16})})]})]}),i&&r.jsx("div",{className:"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:r.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[r.jsx(Ro,{size:16,className:"mr-2"}),i]})})]}),r.jsxs("div",{className:"flex space-x-3 mt-6",children:[r.jsx("button",{type:"button",onClick:b,className:"flex-1 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",disabled:c,children:"取消"}),r.jsx("button",{type:"submit",disabled:c||!s.trim(),className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:c?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"验证中..."})]}):r.jsxs(r.Fragment,{children:[r.jsx(dn,{size:16}),r.jsx("span",{children:"登录"})]})})]})]})]})})}class pe{static async getPosts(t=1,s=10,n,a){var o,c,h;const l=new URLSearchParams({page:t.toString(),pageSize:s.toString()});n&&l.append("category",n),a&&l.append("tag",a);const i=`${ae.posts}?${l.toString()}`;try{const d=await Ee(i),f=await de(d);return{posts:f.posts||[],total:((o=f.pagination)==null?void 0:o.totalPosts)||0,page:((c=f.pagination)==null?void 0:c.page)||1,pageSize:((h=f.pagination)==null?void 0:h.pageSize)||10}}catch(d){throw console.error("获取文章列表失败:",d),d}}static async getPost(t){try{const s=await Ee(ae.post(t));return await de(s)}catch(s){throw console.error("获取文章失败:",s),s}}static async createPost(t){try{const s=await rr(ae.posts,{method:"POST",body:JSON.stringify(t)});return await de(s)}catch(s){throw console.error("创建文章失败:",s),s}}static async updatePost(t,s){try{const n=await rr(ae.post(t),{method:"PUT",body:JSON.stringify(s)});return await de(n)}catch(n){throw console.error("更新文章失败:",n),n}}static async deletePost(t){try{const s=await rr(ae.post(t),{method:"DELETE"});await de(s)}catch(s){throw console.error("删除文章失败:",s),s}}static async searchPosts(t){const s=new URLSearchParams({q:t}),n=`${ae.search}?${s.toString()}`;try{const a=await Ee(n);return(await de(a)).posts}catch(a){throw console.error("搜索文章失败:",a),a}}}function Yo({onMusicToggle:e}){const[t,s]=g.useState(!1),[n,a]=g.useState(!1),[l,i]=g.useState(!1),[o,c]=g.useState(""),[h,d]=g.useState([]),[f,b]=g.useState(!1),{theme:m,toggleTheme:j}=Xo(),{isAdmin:R,logout:y}=Gt(),k=Ft(),w=g.useRef(null),L=g.useRef(null),G=[{name:"首页",href:"/"},{name:"分类",href:"/categories"},{name:"标签",href:"/tags"},{name:"关于",href:"/about"}],O=async T=>{if(!T.trim()){d([]);return}b(!0);try{const Z=await pe.searchPosts(T.trim());d(Z.slice(0,5))}catch(Z){console.error("搜索失败:",Z),d([])}finally{b(!1)}};return g.useEffect(()=>{const T=Z=>{(Z.ctrlKey||Z.metaKey)&&Z.key==="k"&&(Z.preventDefault(),i(ee=>{const te=!ee;return te?setTimeout(()=>{var $;return($=L.current)==null?void 0:$.focus()},100):(c(""),d([])),te})),Z.key==="Escape"&&(i(!1),c(""),d([]))};return document.addEventListener("keydown",T),()=>document.removeEventListener("keydown",T)},[]),g.useEffect(()=>{const T=Z=>{w.current&&!w.current.contains(Z.target)&&(i(!1),c(""),d([]))};if(l)return document.addEventListener("mousedown",T),()=>document.removeEventListener("mousedown",T)},[l]),g.useEffect(()=>{const T=setTimeout(()=>{O(o)},300);return()=>clearTimeout(T)},[o]),r.jsxs("header",{className:"sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700",children:[r.jsxs("div",{className:"container mx-auto px-4",children:[r.jsxs("div",{className:"flex items-center justify-between h-16",children:[r.jsxs(J,{to:"/",className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-sm",children:"B"})}),r.jsx("span",{className:"font-semibold text-xl text-gray-900 dark:text-white",children:"个人博客"})]}),r.jsx("nav",{className:"hidden md:flex items-center space-x-8",children:G.map(T=>r.jsx(J,{to:T.href,className:"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:T.name},T.name))}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("div",{className:"relative",ref:w,children:[r.jsx("button",{onClick:()=>i(!l),title:"快捷搜索 (Ctrl+K)","aria-label":"快捷搜索",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(It,{size:20})}),l&&r.jsx("div",{className:"absolute top-full mt-2 right-0 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50",children:r.jsxs("div",{className:"p-4",children:[r.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[r.jsx(It,{size:16,className:"text-gray-400"}),r.jsx("input",{ref:L,type:"text",placeholder:"搜索文章...",value:o,onChange:T=>c(T.target.value),className:"flex-1 outline-none bg-transparent text-sm",onKeyDown:T=>{T.key==="Enter"&&o.trim()&&(k(`/search?q=${encodeURIComponent(o.trim())}`),i(!1),c(""))}}),r.jsxs("kbd",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded text-gray-500 dark:text-gray-400",children:[r.jsx(mo,{size:12,className:"inline mr-1"}),"K"]})]}),f&&r.jsx("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:"搜索中..."}),h.length>0&&!f&&r.jsxs("div",{className:"space-y-2",children:[r.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:"搜索结果"}),h.map(T=>r.jsxs(J,{to:`/post/${T.slug}`,onClick:()=>{i(!1),c("")},className:"block p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-sm",children:[r.jsx("div",{className:"font-medium text-gray-900 dark:text-white truncate",children:T.title}),r.jsx("div",{className:"text-gray-500 dark:text-gray-400 text-xs truncate mt-1",children:T.excerpt})]},T.slug)),o.trim()&&r.jsx(J,{to:`/search?q=${encodeURIComponent(o.trim())}`,onClick:()=>{i(!1),c("")},className:"block p-2 text-center text-blue-600 dark:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-sm",children:"查看所有结果 →"})]}),o.trim()&&h.length===0&&!f&&r.jsxs("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:["未找到相关文章",r.jsx(J,{to:`/search?q=${encodeURIComponent(o.trim())}`,onClick:()=>{i(!1),c("")},className:"block mt-2 text-blue-600 dark:text-blue-400 hover:underline",children:"在搜索页面查看 →"})]})]})})]}),r.jsx("button",{onClick:e,title:"音乐播放器","aria-label":"打开音乐播放器",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors",children:r.jsx(wt,{size:20})}),R?r.jsx("button",{onClick:y,title:"管理员已登录 - 点击退出","aria-label":"管理员退出",className:"p-2 text-green-600 dark:text-green-400 hover:text-red-600 dark:hover:text-red-400 transition-colors",children:r.jsx(ut,{size:20})}):r.jsx("button",{onClick:()=>a(!0),title:"管理员登录","aria-label":"管理员登录",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(ut,{size:20})}),R&&r.jsxs(J,{to:"/write",title:"写文章",className:"hidden md:flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[r.jsx(hs,{size:16}),r.jsx("span",{children:"写文章"})]}),r.jsx("button",{onClick:j,title:m==="dark"?"切换到浅色模式":"切换到深色模式","aria-label":m==="dark"?"切换到浅色模式":"切换到深色模式",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:m==="dark"?r.jsx(Fo,{size:20}):r.jsx(Lo,{size:20})}),r.jsx("button",{onClick:()=>s(!t),title:t?"关闭菜单":"打开菜单","aria-label":t?"关闭菜单":"打开菜单","aria-expanded":t,className:"md:hidden p-2 text-gray-600 dark:text-gray-400",children:t?r.jsx(or,{size:20}):r.jsx(To,{size:20})})]})]}),t&&r.jsx("div",{className:"md:hidden py-4 border-t border-gray-200 dark:border-gray-700",children:r.jsxs("nav",{className:"space-y-2",children:[G.map(T=>r.jsx(J,{to:T.href,className:"block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors",onClick:()=>s(!1),children:T.name},T.name)),R?r.jsxs(r.Fragment,{children:[r.jsxs(J,{to:"/write",className:"flex items-center space-x-2 px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors",onClick:()=>s(!1),children:[r.jsx(hs,{size:16}),r.jsx("span",{children:"写文章"})]}),r.jsxs("button",{onClick:()=>{y(),s(!1)},className:"flex items-center space-x-2 px-4 py-2 text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left",children:[r.jsx(Mo,{size:16}),r.jsx("span",{children:"管理员退出"})]})]}):r.jsxs("button",{onClick:()=>{a(!0),s(!1)},className:"flex items-center space-x-2 px-4 py-2 text-green-600 dark:text-green-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left",children:[r.jsx(ut,{size:16}),r.jsx("span",{children:"管理员登录"})]})]})})]}),r.jsx(ua,{isOpen:n,onClose:()=>a(!1)})]})}function ec(){return r.jsx("footer",{className:"bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700",children:r.jsxs("div",{className:"container mx-auto px-4 py-8",children:[r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"个人博客"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"基于 Cloudflare Workers 构建的现代个人博客系统，集成 AI 配图、天气显示等功能。"})]}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"快速链接"}),r.jsxs("ul",{className:"space-y-2 text-sm",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"首页"})}),r.jsx("li",{children:r.jsx("a",{href:"/about",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"关于我"})}),r.jsx("li",{children:r.jsx("a",{href:"/write",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"写文章"})})]})]}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"联系方式"}),r.jsxs("div",{className:"flex space-x-4",children:[r.jsx("a",{href:"https://github.com/ajkdfe2e2e",target:"_blank",rel:"noopener noreferrer",title:"GitHub","aria-label":"访问 GitHub 主页",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(bo,{size:20})}),r.jsx("a",{href:"https://x.com/x2a1HRjxs552213",target:"_blank",rel:"noopener noreferrer",title:"Twitter","aria-label":"访问 Twitter 主页",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(Ko,{size:20})}),r.jsx("a",{href:"mailto:<EMAIL>",title:"邮箱","aria-label":"发送邮件联系我",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(la,{size:20})})]})]})]}),r.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center",children:r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"© 2025 个人博客. 使用 Cloudflare Workers 强力驱动"})})]})})}class gn{static async getCurrentWeather(){try{let t;try{t=await this.getCurrentPosition()}catch(l){console.warn("无法获取当前位置，使用默认位置（北京）:",l),t={latitude:39.9042,longitude:116.4074}}const s=new URLSearchParams({lat:t.latitude.toString(),lon:t.longitude.toString()}),n=`${ae.weather}?${s.toString()}`,a=await Ee(n);return await de(a)}catch(t){throw console.error("获取天气信息失败:",t),t}}static async getWeatherByLocation(t,s){try{const n=new URLSearchParams({lat:t.toString(),lon:s.toString()}),a=`${ae.weather}?${n.toString()}`,l=await Ee(a);return await de(l)}catch(n){throw console.error("获取天气信息失败:",n),n}}static getCurrentPosition(){return new Promise((t,s)=>{if(!navigator.geolocation){s(new Error("该浏览器不支持地理定位"));return}const n={enableHighAccuracy:!0,timeout:5e3,maximumAge:3e5};navigator.geolocation.getCurrentPosition(a=>{t({latitude:a.coords.latitude,longitude:a.coords.longitude})},a=>{let l="获取位置失败";switch(a.code){case a.PERMISSION_DENIED:l="用户拒绝了位置请求";break;case a.POSITION_UNAVAILABLE:l="位置信息不可用";break;case a.TIMEOUT:l="获取位置超时";break}s(new Error(l))},n)})}static isGeolocationSupported(){return"geolocation"in navigator}}function tc(){const[e,t]=g.useState(null),[s,n]=g.useState(!0),[a,l]=g.useState(null);return g.useEffect(()=>{(async()=>{try{if(n(!0),l(null),!gn.isGeolocationSupported())throw new Error("浏览器不支持地理定位");const o=await gn.getCurrentWeather();t(o)}catch(o){console.error("获取天气信息失败:",o),l(o instanceof Error?o.message:"获取天气信息失败")}finally{n(!1)}})()},[]),s?r.jsx("div",{className:"card p-4",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"animate-spin",children:r.jsx(xo,{size:20,className:"text-gray-400"})}),r.jsxs("div",{children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-1"}),r.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"})]})]})}):a||!e?r.jsx("div",{className:"card p-4",children:r.jsxs("div",{className:"flex items-center space-x-3 text-gray-500 dark:text-gray-400",children:[r.jsx(un,{size:20}),r.jsxs("div",{children:[r.jsx("p",{className:"text-sm",children:"位置信息"}),r.jsx("p",{className:"text-xs",children:"获取失败"})]})]})}):r.jsx("div",{className:"card p-4",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(Wo,{size:20,className:"text-white"})})}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(un,{size:14,className:"text-gray-500 dark:text-gray-400"}),r.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.location})]}),r.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[r.jsx("span",{className:"text-lg font-semibold text-blue-600 dark:text-blue-400",children:e.temperature}),r.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.weather})]})]})]})})}function rc(){const[e,t]=g.useState([]),[s,n]=g.useState([]),[a,l]=g.useState(!0);return g.useEffect(()=>{(async()=>{try{l(!0);const o=await pe.getPosts(1,5);t(o.posts);const c=await pe.getPosts(1,100),h=new Set;c.posts.forEach(d=>{d.tags.forEach(f=>h.add(f))}),n(Array.from(h))}catch(o){console.error("加载侧边栏数据失败:",o),t([]),n([])}finally{l(!1)}})()},[]),r.jsxs("div",{className:"sticky top-8 space-y-6",children:[r.jsx(tc,{}),r.jsxs("div",{className:"card p-4",children:[r.jsx("h3",{className:"font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"最新文章"}),r.jsx("div",{className:"space-y-2",children:a?Array.from({length:3}).map((i,o)=>r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"},o)):e.length>0?e.map(i=>r.jsx(J,{to:`/post/${i.slug}`,className:"block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors line-clamp-2",title:i.title,children:i.title},i.id)):r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"暂无文章"})})]}),r.jsxs("div",{className:"card p-4",children:[r.jsx("h3",{className:"font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"标签"}),r.jsx("div",{className:"flex flex-wrap gap-2",children:a?Array.from({length:6}).map((i,o)=>r.jsx("div",{className:"h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"},o)):s.length>0?s.map(i=>r.jsx(J,{to:`/tag/${encodeURIComponent(i)}`,className:"px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-blue-600 hover:text-white transition-colors cursor-pointer",children:i},i)):r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"暂无标签"})})]})]})}function sc(){const e=pt(),t=Wt(),[s,n]=g.useState([]),[a,l]=g.useState(null);return g.useEffect(()=>{(async()=>{if(e.pathname.startsWith("/post/")&&t.slug)try{const o=await pe.getPost(t.slug);l({title:o.title,category:o.category})}catch(o){console.error("获取文章详情失败:",o),l({title:"文章详情",category:void 0})}})()},[e.pathname,t.slug]),g.useEffect(()=>{n((()=>{const o=e.pathname,c=[{label:"首页",href:"/"}];return o==="/"?[{label:"首页"}]:(o==="/categories"?c.push({label:"分类"}):o.startsWith("/category/")?(c.push({label:"分类",href:"/categories"}),c.push({label:decodeURIComponent(t.category||"")})):o==="/tags"?c.push({label:"标签"}):o.startsWith("/tag/")?(c.push({label:"标签",href:"/tags"}),c.push({label:decodeURIComponent(t.tag||"")})):o==="/search"?c.push({label:"搜索"}):o==="/about"?c.push({label:"关于"}):o==="/write"?c.push({label:"写文章"}):o.startsWith("/write/")?(c.push({label:"写文章",href:"/write"}),c.push({label:"编辑文章"})):o.startsWith("/post/")&&(a?(a.category&&(c.push({label:"分类",href:"/categories"}),c.push({label:a.category,href:`/category/${encodeURIComponent(a.category)}`})),c.push({label:a.title})):c.push({label:"文章详情"})),c)})())},[e.pathname,t,a]),e.pathname==="/"?null:r.jsx("nav",{"aria-label":"面包屑导航",className:"mb-6",children:r.jsx("ol",{className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400",children:s.map((i,o)=>r.jsxs("li",{className:"flex items-center",children:[o>0&&r.jsx(fo,{size:16,className:"mr-2 text-gray-400"}),i.href?r.jsxs(J,{to:i.href,className:"flex items-center hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[o===0&&r.jsx(cn,{size:16,className:"mr-1"}),r.jsx("span",{className:"truncate max-w-[200px]",title:i.label,children:i.label})]}):r.jsxs("span",{className:"flex items-center text-gray-900 dark:text-gray-100",children:[o===0&&r.jsx(cn,{size:16,className:"mr-1"}),r.jsx("span",{className:"truncate max-w-[200px]",title:i.label,children:i.label})]})]},o))})})}function nc({children:e,onMusicToggle:t}){return r.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors",children:[r.jsx(Yo,{onMusicToggle:t}),r.jsx("main",{className:"container mx-auto px-4 py-8 max-w-6xl",children:r.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[r.jsxs("div",{className:"lg:col-span-3",children:[r.jsx(sc,{}),e]}),r.jsx("aside",{className:"lg:col-span-1",children:r.jsx(rc,{})})]})}),r.jsx(ec,{})]})}const fn=(e,t,s)=>{if(console.log(`[Music Player] 处理图片URL: ${e} (歌曲: ${t})`),!e)return console.log("[Music Player] 图片URL为空，使用默认图片"),"/default-album.jpg";if(s!=null&&s.has(e))return console.log(`[Music Player] URL之前加载失败过，使用默认图片: ${e}`),"/default-album.jpg";let n=e;return n.startsWith("http:")&&(console.log(`[Music Player] 转换HTTP到HTTPS: ${n}`),n.match(/http:\/\/p\d+\.music\.126\.net/)?n=n.replace(/^http:\/\/p(\d+)\.music\.126\.net/,"https://p$1.music.126.net"):n.match(/http:\/\/y\.gtimg\.cn/)?n=n.replace(/^http:\/\//,"https://"):n=n.replace("http:","https:"),console.log(`[Music Player] 转换后URL: ${n}`)),!n||n==="null"||n==="undefined"?(console.log(`[Music Player] URL无效，使用默认图片: ${n}`),"/default-album.jpg"):(console.log(`[Music Player] 最终图片URL: ${n}`),n)},ac=g.forwardRef((e,t)=>{var Ae,M;const[s,n]=g.useState(!1),[a,l]=g.useState(null),[i,o]=g.useState([]),[c,h]=g.useState(0),[d,f]=g.useState(!1),[b,m]=g.useState(!1),[j,R]=g.useState(0),[y,k]=g.useState(0),[w,L]=g.useState(.7),[G,O]=g.useState(!1),[T,Z]=g.useState(""),[ee,te]=g.useState([]),[$,ke]=g.useState(!1),[ie,je]=g.useState([]),[le,ue]=g.useState(-1),[N,x]=g.useState([]),[E,_]=g.useState("auto"),[C,P]=g.useState(!1),[H,I]=g.useState(""),z=g.useRef(null),q=g.useRef(null),[X,re]=g.useState(new Set);g.useImperativeHandle(t,()=>({toggleVisibility:()=>{f(!d),m(!0)}})),g.useEffect(()=>{(async()=>{try{const v=await(await fetch(`${ae.music.search.replace("/search","/sources")}`)).json();if(v&&v.code===200&&Array.isArray(v.sources))x(v.sources);else throw new Error("Invalid response format")}catch(p){console.error("Error fetching music sources:",p),x([{type:"wyy",name:"网易云音乐",enabled:!0,priority:1},{type:"qq",name:"QQ音乐",enabled:!0,priority:2},{type:"kg",name:"酷狗音乐",enabled:!0,priority:3},{type:"kw",name:"酷我音乐",enabled:!0,priority:4},{type:"xmla",name:"喜马拉雅",enabled:!0,priority:5},{type:"qishui",name:"汽水音乐",enabled:!0,priority:6},{type:"qt",name:"蜻蜓FM",enabled:!0,priority:7}])}})()},[]),g.useEffect(()=>{let u=window.innerWidth;const p=()=>{const v=window.innerWidth;if(Math.abs(v-u)<50)return;const A=v<640;u<640!==A&&(A&&!a&&d&&!b?f(!1):!A&&!d&&f(!0)),u=v};return window.addEventListener("resize",p),()=>window.removeEventListener("resize",p)},[a,d]),g.useEffect(()=>{const u=z.current;if(!u)return;const p=()=>R(u.currentTime),v=()=>k(u.duration),A=()=>Ce();return u.addEventListener("timeupdate",p),u.addEventListener("durationchange",v),u.addEventListener("ended",A),()=>{u.removeEventListener("timeupdate",p),u.removeEventListener("durationchange",v),u.removeEventListener("ended",A)}},[]),g.useEffect(()=>{z.current&&(z.current.volume=G?0:w)},[w,G]),g.useEffect(()=>{var p;if(!ie.length)return;const u=ie.findIndex((v,A)=>{const U=ie[A+1];return j>=v.time&&(!U||j<U.time)});if(u!==-1&&u!==le){ue(u);const v=(p=q.current)==null?void 0:p.children[u];v&&v.scrollIntoView({behavior:"smooth",block:"center"})}},[j,ie,le]);const he=async u=>{if(u.trim()){ke(!0);try{let p=`${ae.music.search}?keywords=${encodeURIComponent(u)}`;E!=="auto"&&(p+=`&source=${E}`);const v=await fetch(p);if(!v.ok)throw new Error(`HTTP error! status: ${v.status}`);const A=await v.json();if(console.log("[Music Player] 后端返回的原始数据:",A),A.code===200&&A.result&&Array.isArray(A.result.songs)){const U=A.result.songs.map(F=>{var Je,Us,Hs,Bs,Fs,Ws,Ks,Gs,qs,Vs,Xs,Js;console.log("[Music Player] 处理单首歌曲数据:",F);const xe={id:((Je=F.album)==null?void 0:Je.id)||((Us=F.al)==null?void 0:Us.id)||0,name:((Hs=F.album)==null?void 0:Hs.name)||((Bs=F.al)==null?void 0:Bs.name)||"未知专辑",picUrl:((Fs=F.album)==null?void 0:Fs.picUrl)||((Ws=F.al)==null?void 0:Ws.picUrl)||"/default-album.jpg"};return console.log("[Music Player] 提取的专辑信息:",xe),{id:F.id,name:F.name||"未知歌曲",artists:Array.isArray(F.artists)?F.artists:Array.isArray(F.ar)?F.ar:[{id:0,name:"未知艺术家"}],album:xe,duration:F.duration||F.dt||0,sourceType:F.sourceType||A.source,sourceName:F.sourceName||A.sourceName,mid:((Ks=F.originalData)==null?void 0:Ks.mid)||F.mid,media_mid:((Gs=F.originalData)==null?void 0:Gs.media_mid)||F.media_mid,vid:((qs=F.originalData)==null?void 0:qs.vid)||F.vid,hash:((Vs=F.originalData)==null?void 0:Vs.hash)||F.hash,album_id:((Xs=F.originalData)==null?void 0:Xs.album_id)||F.album_id,album_audio_id:((Js=F.originalData)==null?void 0:Js.album_audio_id)||F.album_audio_id}});if(te(U),A.sourceName)I(`搜索来源：${A.sourceName}`);else if(E!=="auto"){const F=N.find(xe=>xe.type===E);I(`搜索来源：${(F==null?void 0:F.name)||E}`)}else I("多源智能搜索")}else te([]),I("搜索无结果")}catch(p){console.error("Error searching music:",p),te([]),I("搜索失败，请检查网络连接")}finally{ke(!1)}}},lt=async u=>{var p,v;try{let A=`${ae.music.songUrl}?`;const U=new URLSearchParams;switch(u.sourceType&&U.append("source",u.sourceType),u.sourceType){case"qq":if(u.mid&&u.media_mid&&u.vid)U.append("mid",u.mid),U.append("media_mid",u.media_mid),U.append("vid",u.vid);else return console.error("QQ音乐缺少必要参数:",u),null;break;case"kg":if(u.hash&&u.album_id&&u.album_audio_id)U.append("hash",u.hash),U.append("album_id",u.album_id),U.append("album_audio_id",u.album_audio_id);else return console.error("酷狗音乐缺少必要参数:",u),null;break;default:U.append("id",u.id.toString());break}A+=U.toString(),console.log("调用URL获取接口:",A);const xe=await(await fetch(A)).json();return console.log("URL API响应:",xe),xe.code===200&&((v=(p=xe.data)==null?void 0:p[0])!=null&&v.url)?xe.data[0].url:null}catch(A){return console.error("Error fetching song URL:",A),null}},yt=async u=>{try{let p=`${ae.music.lyric}?`;const v=new URLSearchParams;switch(u.sourceType&&v.append("source",u.sourceType),u.sourceType){case"qq":v.append("id",u.id.toString());break;case"kg":if(u.hash)v.append("hash",u.hash);else{console.error("酷狗音乐歌词缺少hash参数:",u),je([]);return}break;default:v.append("id",u.id.toString());break}p+=v.toString(),console.log("调用歌词获取接口:",p);const U=await(await fetch(p)).json();if(console.log("歌词API响应:",U),U.code===200&&U.lrc&&U.lrc.lyric){const F=bt(U.lrc.lyric);je(F)}else je([])}catch(p){console.error("Error fetching lyrics:",p),je([])}},bt=u=>u.split(`
`).map(p=>{const v=p.match(/\[(\d{2}):(\d{2})\.(\d{2,3})\](.*)/);if(!v)return null;const A=parseInt(v[1]),U=parseInt(v[2]),F=parseInt(v[3].padEnd(3,"0")),xe=A*60+U+F/1e3,Je=v[4].trim();return Je?{time:xe,text:Je}:null}).filter(Boolean),Xe=async(u,p)=>{if(console.log("Playing song:",u),p!==void 0)h(p),o(ee);else{const v=ee.findIndex(A=>A.id===u.id&&A.sourceType===u.sourceType);v!==-1?(h(v),o(ee)):(o([u]),h(0))}l(u);try{const v=await lt(u);if(v){if(z.current){let A=v;v.startsWith("http://")&&(v.match(/http:\/\/m\d+\.music\.126\.net/)?A=v.replace(/^http:\/\/m(\d+)\.music\.126\.net/,"https://m$1.music.126.net"):v.match(/http:\/\/isure\.stream\.qqmusic\.qq\.com/)?A=v.replace(/^http:\/\//,"https://"):A="https://"+v.substring(7)),z.current.src=A,z.current.play(),n(!0),yt(u),u.sourceName&&I(`播放来源：${u.sourceName}`)}}else console.error("Failed to get song URL"),I("播放链接获取失败")}catch(v){console.error("Error playing song:",v),I("播放失败")}},Pe=()=>{z.current&&(s?z.current.pause():z.current.play(),n(!s))},Ce=()=>{if(i.length===0)return;const u=(c+1)%i.length;Xe(i[u],u)},ge=()=>{if(i.length===0)return;const u=(c-1+i.length)%i.length;Xe(i[u],u)},me=u=>{const p=parseFloat(u.target.value);R(p),z.current&&(z.current.currentTime=p)},$e=()=>{z.current&&z.current.removeEventListener("timeupdate",()=>R(z.current.currentTime))},B=()=>{z.current&&z.current.addEventListener("timeupdate",()=>R(z.current.currentTime))},W=u=>{const p=parseFloat(u.target.value);L(p),O(!1)},se=()=>{O(!G)},K=u=>{const p=Math.floor(u/60),v=Math.floor(u%60);return`${p}:${v.toString().padStart(2,"0")}`},Q=()=>{P(!C)},fe=u=>{if(_(u),P(!1),u==="auto")I("多源智能搜索");else{const p=N.find(v=>v.type===u);I(`当前音源：${(p==null?void 0:p.name)||u}`)}},ye=(u,p)=>{const v=u.currentTarget,A=v.src;console.error("[Music Player] 图片加载失败:",{songName:p.name,originalUrl:p.album.picUrl,currentSrc:A,sourceName:p.sourceName}),v.onerror=null,re(U=>new Set(U).add(A)),A.includes("default-album.jpg")?(console.error(`[Music Player] 连默认图片都加载失败了: ${p.name}`),v.style.backgroundColor="#4b5563",v.style.display="flex",v.style.alignItems="center",v.style.justifyContent="center"):(console.log(`[Music Player] 回退到默认图片: ${p.name}`),v.src="/default-album.jpg")};return r.jsxs(r.Fragment,{children:[r.jsx("audio",{ref:z}),r.jsx("div",{className:`fixed bottom-0 right-0 z-50 transition-transform duration-300 ${d?"translate-y-0":"translate-y-full"}`,children:r.jsxs("div",{className:"w-screen sm:w-[450px] sm:right-4 sm:bottom-4 sm:mb-0 bg-gray-800/95 backdrop-blur-md text-white sm:rounded-lg shadow-lg flex flex-col h-[60vh] sm:h-[500px] sm:max-h-[80vh]",children:[r.jsxs("div",{className:"p-3 sm:p-4 flex justify-between items-center border-b border-gray-700",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsxs("h2",{className:"text-base sm:text-lg font-bold flex items-center",children:[r.jsx(wt,{className:"mr-2",size:20})," 音乐播放器"]}),H&&r.jsx("span",{className:"text-xs text-purple-400 bg-purple-500/20 px-2 py-1 rounded-full",children:H})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsxs("div",{className:"relative",children:[r.jsx("button",{onClick:Q,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"音源设置",children:r.jsx(wo,{size:18})}),C&&r.jsx("div",{className:"absolute top-full right-0 mt-1 bg-gray-700 rounded-lg shadow-lg border border-gray-600 z-10 min-w-48",children:r.jsxs("div",{className:"p-2",children:[r.jsx("div",{className:"text-xs text-gray-400 mb-2",children:"选择音源"}),r.jsx("button",{onClick:()=>fe("auto"),className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${E==="auto"?"bg-purple-600 text-white":"hover:bg-gray-600"}`,children:"🔄 智能多源 (推荐)"}),r.jsx("div",{className:"border-t border-gray-600 my-2"}),N.map(u=>r.jsxs("button",{onClick:()=>fe(u.type),disabled:!u.enabled,className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${E===u.type?"bg-purple-600 text-white":u.enabled?"hover:bg-gray-600":"text-gray-500 cursor-not-allowed"}`,children:[u.name,!u.enabled&&" (禁用)"]},u.type))]})})]}),r.jsx("button",{onClick:()=>{f(!1),m(!0)},className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"收起播放器",children:r.jsx(tr,{size:20})})]})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row flex-grow overflow-hidden",children:[r.jsxs("div",{className:"w-full sm:w-1/2 flex flex-col border-b sm:border-b-0 sm:border-r border-gray-700",children:[r.jsxs("div",{className:"p-3 flex-shrink-0",children:[r.jsxs("form",{onSubmit:u=>{u.preventDefault(),he(T)},className:"flex",children:[r.jsx("input",{type:"text",value:T,onChange:u=>Z(u.target.value),placeholder:"搜索歌曲、歌手...",className:"flex-grow bg-gray-700/80 border border-gray-600 rounded-l-md px-3 py-2.5 sm:py-1.5 focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm touch-target"}),r.jsx("button",{type:"submit",className:"bg-purple-600 hover:bg-purple-700 px-4 sm:px-3 py-2.5 sm:py-1.5 rounded-r-md touch-target",title:"搜索",children:r.jsx(It,{size:18})})]}),r.jsx("div",{className:"mt-2 text-xs text-gray-400",children:E==="auto"?r.jsx("span",{children:"🔄 多源智能搜索"}):r.jsxs("span",{children:["🎵 ",((Ae=N.find(u=>u.type===E))==null?void 0:Ae.name)||E]})})]}),r.jsx("div",{className:"flex-grow overflow-y-auto px-1 mb-2",style:{maxHeight:"calc(60vh - 200px)"},children:$?r.jsx("p",{className:"p-4 text-center text-gray-400",children:"正在搜索..."}):ee.length>0?ee.map((u,p)=>r.jsxs("div",{className:"p-2 flex items-center gap-3 hover:bg-white/10 rounded-lg cursor-pointer transition-colors",onClick:()=>Xe(u,p),children:[r.jsx("img",{src:fn(u.album.picUrl,u.name,X),alt:u.album.name||"专辑封面",className:"w-12 h-12 rounded-md object-cover bg-gray-700",onError:v=>ye(v,u)}),r.jsxs("div",{className:"flex-grow overflow-hidden",children:[r.jsx("p",{className:"font-semibold text-sm truncate",children:u.name}),r.jsxs("p",{className:"text-xs text-gray-400 truncate",children:[Array.isArray(u.artists)?u.artists.map(v=>v.name).join(" / "):"未知艺术家",r.jsxs("span",{className:"text-purple-400 ml-2",children:["• ",u.sourceName]})]})]})]},`${u.id}-${u.sourceType}-${p}`)):T?r.jsx("p",{className:"p-4 text-center text-gray-400",children:"没有找到相关歌曲"}):r.jsx("div",{className:"p-4 text-center text-gray-500",children:r.jsx("p",{className:"text-sm",children:"开始搜索音乐"})})})]}),r.jsx("div",{className:"w-full sm:w-1/2 flex flex-col p-3 min-h-0",children:a?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"text-center pb-3 border-b border-gray-700 mb-3 flex-shrink-0",children:r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsx("img",{src:fn(a.album.picUrl,a.name,X),alt:a.album.name,className:"w-16 h-16 rounded-lg shadow-lg",onError:u=>ye(u,a)}),r.jsxs("div",{className:"flex flex-col",children:[r.jsx("h3",{className:"text-lg font-bold",children:a.name||"未知歌曲"}),r.jsx("p",{className:"text-sm text-gray-400",children:Array.isArray(a.artists)&&a.artists.length>0?a.artists.map(u=>u.name).join(" / "):"未知艺术家"})]})]})}),r.jsx("div",{ref:q,className:"flex-grow overflow-y-auto text-center space-y-2 text-gray-300 px-2",style:{maxHeight:"calc(60vh - 240px)"},children:ie.length>0?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"sm:hidden",children:r.jsx("p",{className:"text-sm text-purple-400 font-medium text-center py-2",children:le>=0&&ie[le]?ie[le].text:((M=ie[0])==null?void 0:M.text)||"♪ ♪"})}),r.jsx("div",{className:"hidden sm:block",children:ie.map((u,p)=>r.jsx("p",{className:`transition-all duration-300 text-sm leading-relaxed ${p===le?"text-purple-400 font-bold scale-105":"hover:text-gray-200"}`,children:u.text},p))})]}):r.jsx("p",{className:"text-gray-500 text-sm",children:a?"暂无歌词":"♪ ♪"})})]}):r.jsxs("div",{className:"flex-grow flex flex-col items-center justify-center text-gray-500 px-4",children:[r.jsx(wt,{size:48,className:"mb-4 opacity-50 hidden sm:block"}),r.jsx(wt,{size:32,className:"mb-2 opacity-50 sm:hidden"}),r.jsx("p",{className:"text-center text-sm sm:text-base",children:"选择音乐开始播放"}),r.jsx("p",{className:"text-xs mt-2 text-gray-600 text-center hidden sm:block",children:"歌词和音乐信息将在此显示"}),r.jsx("p",{className:"text-xs mt-1 text-gray-600 text-center sm:hidden",children:"歌词将在此显示"})]})})]}),r.jsxs("div",{className:"p-3 sm:p-3 border-t border-gray-700 bg-gray-800/95",children:[r.jsxs("div",{className:"mb-3",children:[r.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400 mb-1",children:[r.jsx("span",{children:K(j)}),r.jsx("span",{children:K(y)})]}),r.jsx("input",{type:"range",value:j,max:y||0,onChange:me,onTouchStart:$e,onMouseDown:$e,onTouchEnd:B,onMouseUp:B,className:"w-full music-slider progress-slider",title:"播放进度",style:{"--progress":`${j/(y||1)*100}%`}})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:se,className:"p-2 hover:bg-white/10 rounded-full transition-colors",children:G||w===0?r.jsx(Vo,{size:20}):r.jsx(qo,{size:20})}),r.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:G?0:w,onChange:W,className:"volume-slider w-24","aria-label":"音量控制"})]}),r.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[r.jsx("button",{onClick:ge,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"上一首",children:r.jsx(Uo,{size:20})}),r.jsx("button",{onClick:Pe,className:"bg-purple-600 hover:bg-purple-700 rounded-full p-3 text-white touch-target transition-colors",title:s?"暂停":"播放",children:s?r.jsx(_o,{size:24}):r.jsx(Io,{size:24})}),r.jsx("button",{onClick:Ce,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"下一首",children:r.jsx(Ho,{size:20})})]}),r.jsx("div",{className:"w-24 sm:w-20"})]})]})]})}),!d&&r.jsx("div",{className:"fixed bottom-4 right-4 z-40",children:r.jsx("button",{onClick:()=>{f(!0),m(!0)},className:"bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110 touch-target",title:"打开音乐播放器",children:r.jsx(wt,{size:24})})})]})});function fs({slug:e,title:t,onDeleted:s,showEdit:n=!0,showDelete:a=!0}){const{isAdmin:l}=Gt(),[i,o]=g.useState(!1),[c,h]=g.useState(!1),[d,f]=g.useState(!1),b=Ft(),m=()=>{b(`/write/${e}`),o(!1)},j=()=>{f(!0),o(!1)},R=async()=>{if(!c)try{h(!0),await pe.deletePost(e),s==null||s(),window.location.pathname.includes(`/post/${e}`)&&b("/")}catch(k){console.error("删除文章失败:",k),alert("删除文章失败，请稍后重试")}finally{h(!1),f(!1)}},y=()=>{f(!1)};return!l||!n&&!a?null:r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"relative",children:[r.jsx("button",{onClick:()=>o(!i),className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",title:"文章操作",children:r.jsx(Oo,{size:18})}),i&&r.jsxs("div",{className:"absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50",children:[n&&r.jsxs("button",{onClick:m,className:"w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 first:rounded-t-lg",children:[r.jsx(hs,{size:16}),r.jsx("span",{children:"编辑文章"})]}),a&&r.jsxs("button",{onClick:j,className:"w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2 last:rounded-b-lg",children:[r.jsx(gs,{size:16}),r.jsx("span",{children:"删除文章"})]})]}),i&&r.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>o(!1)})]}),d&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"确认删除文章"}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:["你确定要删除文章 ",r.jsxs("strong",{children:['"',t,'"']})," 吗？此操作无法撤销。"]}),r.jsxs("div",{className:"flex space-x-3 justify-end",children:[r.jsx("button",{onClick:y,disabled:c,className:"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50",children:"取消"}),r.jsx("button",{onClick:R,disabled:c,className:"px-4 py-2 text-sm text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors disabled:opacity-50 flex items-center space-x-2",children:c?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"删除中..."})]}):r.jsxs(r.Fragment,{children:[r.jsx(gs,{size:16}),r.jsx("span",{children:"确认删除"})]})})]})]})})]})}function ic({onFilterChange:e,availableCategories:t,availableTags:s}){var y;const[n,a]=g.useState({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),[l,i]=g.useState(!1),[o,c]=g.useState(!1),[h,d]=g.useState(!1),f=[{value:"date",label:"发布时间",icon:Ge},{value:"readTime",label:"阅读时长",icon:rt},{value:"featured",label:"推荐程度",icon:No}];g.useEffect(()=>{e(n)},[n,e]);const b=(k,w)=>{a(L=>({...L,[k]:w}))},m=k=>{const w=k==="sortBy"?"date":k==="sortOrder"?"desc":"";a(L=>({...L,[k]:w}))},j=()=>{a({category:"",tag:"",sortBy:"date",sortOrder:"desc"})},R=n.category||n.tag||n.sortBy!=="date"||n.sortOrder!=="desc";return r.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-6",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(yo,{size:20,className:"text-gray-500 dark:text-gray-400"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"筛选文章"})]}),R&&r.jsx("button",{onClick:j,className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:"清除所有筛选"})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"分类"}),r.jsxs("button",{onClick:()=>i(!l),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[r.jsx("span",{className:n.category?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:n.category||"选择分类"}),r.jsx(tr,{size:16})]}),l&&r.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:r.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[r.jsx("button",{onClick:()=>{b("category",""),i(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"全部分类"}),t.map(k=>r.jsx("button",{onClick:()=>{b("category",k),i(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:k},k))]})}),n.category&&r.jsx("button",{onClick:()=>m("category"),"aria-label":"清除分类筛选",className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:r.jsx(or,{size:14})})]}),r.jsxs("div",{className:"relative",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标签"}),r.jsxs("button",{onClick:()=>c(!o),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[r.jsx("span",{className:n.tag?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:n.tag||"选择标签"}),r.jsx(tr,{size:16})]}),o&&r.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:r.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[r.jsx("button",{onClick:()=>{b("tag",""),c(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"全部标签"}),s.map(k=>r.jsx("button",{onClick:()=>{b("tag",k),c(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:k},k))]})}),n.tag&&r.jsx("button",{onClick:()=>m("tag"),"aria-label":"清除标签筛选",className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:r.jsx(or,{size:14})})]}),r.jsxs("div",{className:"relative",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"排序方式"}),r.jsxs("button",{onClick:()=>d(!h),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[r.jsx("span",{className:"text-gray-900 dark:text-white",children:(y=f.find(k=>k.value===n.sortBy))==null?void 0:y.label}),r.jsx(tr,{size:16})]}),h&&r.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:r.jsx("div",{className:"py-1",children:f.map(k=>{const w=k.icon;return r.jsxs("button",{onClick:()=>{b("sortBy",k.value),d(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2",children:[r.jsx(w,{size:16}),r.jsx("span",{children:k.label})]},k.value)})})})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"排序顺序"}),r.jsxs("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>b("sortOrder","desc"),className:`flex-1 px-3 py-2 text-sm rounded-md border ${n.sortOrder==="desc"?"bg-blue-600 text-white border-blue-600":"bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"}`,children:"倒序"}),r.jsx("button",{onClick:()=>b("sortOrder","asc"),className:`flex-1 px-3 py-2 text-sm rounded-md border ${n.sortOrder==="asc"?"bg-blue-600 text-white border-blue-600":"bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"}`,children:"正序"})]})]})]})]})}function lc(){const[e,t]=g.useState([]),[s,n]=g.useState(!0),[a,l]=g.useState(null),[i,o]=g.useState({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),c=async()=>{try{n(!0),l(null),console.log("开始加载文章列表...");const y=await pe.getPosts(1,10);console.log("获取文章响应:",y),t(y.posts||[]),(!y.posts||y.posts.length===0)&&console.warn("API返回空文章列表")}catch(y){console.error("加载文章失败详情:",y),y instanceof cr?l(`API错误 (${y.status}): ${y.message}`):y instanceof Error?l(`网络错误: ${y.message}`):l("未知错误，请检查网络连接"),t([])}finally{n(!1)}};g.useEffect(()=>{c()},[]);const h=()=>{c()},d=g.useCallback(y=>{o(y)},[]),f=g.useMemo(()=>{const y=e.filter(k=>k.category).map(k=>k.category);return Array.from(new Set(y))},[e]),b=g.useMemo(()=>{const y=e.flatMap(k=>k.tags);return Array.from(new Set(y))},[e]),m=g.useMemo(()=>{let y=[...e];return i.category&&(y=y.filter(k=>k.category===i.category)),i.tag&&(y=y.filter(k=>k.tags.includes(i.tag))),y.sort((k,w)=>{let L=0;switch(i.sortBy){case"date":L=new Date(k.date).getTime()-new Date(w.date).getTime();break;case"readTime":L=k.readTime-w.readTime;break;case"featured":L=(k.featured?1:0)-(w.featured?1:0);break}return i.sortOrder==="desc"?-L:L}),y},[e,i]),j=m.find(y=>y.featured),R=m.filter(y=>!y.featured);return s?r.jsxs("div",{className:"space-y-8",children:[r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto mb-4 animate-pulse"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"})]}),r.jsxs("div",{className:"card p-8 animate-pulse",children:[r.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]})]}):r.jsxs("div",{className:"space-y-12",children:[r.jsxs("div",{className:"text-center py-12",children:[r.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4",children:"欢迎来到我的博客"}),r.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"分享技术见解、开发经验和生活感悟的个人空间"})]}),a&&r.jsx("div",{className:"card p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800",children:r.jsxs("div",{className:"flex items-start justify-between",children:[r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"text-red-800 dark:text-red-200 font-medium mb-2",children:"文章加载失败"}),r.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm mb-4",children:a}),r.jsx("div",{className:"text-xs text-red-600 dark:text-red-400 mb-4",children:"请打开浏览器开发者工具的控制台查看详细错误信息"})]}),r.jsx("button",{onClick:c,className:"ml-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm",disabled:s,children:s?"重试中...":"重试"})]})}),e.length>0&&r.jsx(ic,{onFilterChange:d,availableCategories:f,availableTags:b}),!s&&e.length===0&&r.jsx("div",{className:"text-center py-16",children:r.jsxs("div",{className:"card p-8 max-w-md mx-auto",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"还没有文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"开始写作，分享你的想法和经验吧！"}),r.jsx(J,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})}),!s&&e.length>0&&m.length===0&&r.jsx("div",{className:"text-center py-16",children:r.jsxs("div",{className:"card p-8 max-w-md mx-auto",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"没有找到匹配的文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"试试调整筛选条件，或者浏览全部文章"}),r.jsx("button",{onClick:()=>o({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"清除筛选条件"})]})}),j&&r.jsxs("div",{children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"✨ 特色文章"}),r.jsxs("div",{className:"card p-8 hover:shadow-lg transition-all duration-300 relative group",children:[r.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity",children:r.jsx(fs,{slug:j.slug,title:j.title,onDeleted:h})}),r.jsxs(J,{to:`/post/${j.slug}`,className:"block",children:[j.imageUrl&&r.jsx("div",{className:"mb-6 rounded-xl overflow-hidden shadow-lg bg-gray-100 dark:bg-gray-800",children:r.jsx("img",{src:j.imageUrl,alt:j.title,className:"w-full h-auto max-h-96 object-contain group-hover:scale-105 transition-transform duration-500",loading:"lazy",onError:y=>{console.warn("特色文章封面加载失败:",j.imageUrl);const w=y.target.parentElement;w&&(w.style.display="none")}})}),r.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 pr-12 leading-tight",children:j.title}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed mb-4",children:j.excerpt}),r.jsxs("div",{className:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(mt,{size:16}),r.jsx("span",{children:j.author})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:16}),r.jsx("span",{children:j.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(rt,{size:16}),r.jsxs("span",{children:[j.readTime," 分钟阅读"]})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2 mt-4",children:j.tags.map(y=>r.jsx("span",{className:"px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full",children:y},y))})]})]})]}),R.length>0&&r.jsxs("div",{children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"📚 最新文章"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:R.map(y=>r.jsxs("div",{className:"card p-0 hover:shadow-lg transition-all duration-300 group h-full relative overflow-hidden rounded-lg",children:[r.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10",children:r.jsx(fs,{slug:y.slug,title:y.title,onDeleted:h})}),r.jsxs(J,{to:`/post/${y.slug}`,className:"block h-full",children:[y.imageUrl&&r.jsx("div",{className:"w-full max-h-64 overflow-hidden bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:r.jsx("img",{src:y.imageUrl,alt:y.title,className:"w-full h-auto max-h-64 object-contain group-hover:scale-105 transition-transform duration-500",loading:"lazy",onError:k=>{console.warn("文章封面加载失败:",y.imageUrl);const L=k.target.parentElement;L&&(L.style.display="none")}})}),r.jsxs("div",{className:"p-6 flex flex-col h-full",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 line-clamp-2",children:y.title}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3 flex-grow",children:y.excerpt}),r.jsxs("div",{className:"mt-auto",children:[r.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[y.tags.slice(0,3).map(k=>r.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full",children:k},k)),y.tags.length>3&&r.jsxs("span",{className:"px-2 py-1 text-xs text-gray-400 dark:text-gray-500",children:["+",y.tags.length-3]})]}),r.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:12}),r.jsx("span",{children:y.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(rt,{size:12}),r.jsxs("span",{children:[y.readTime," 分钟"]})]})]})]})]})]})]},y.id))})]}),e.length>0&&!j&&R.length===0&&r.jsx("div",{className:"text-center py-8",children:r.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"文章加载中..."})})]})}class Vt{static async getComments(t){try{const s=await Ee(ae.comments(t));return(await de(s)).comments||[]}catch(s){return console.error("获取评论失败:",s),[]}}static async createComment(t,s){try{const n=await Ee(ae.comments(t),{method:"POST",body:JSON.stringify(s)});return await de(n)}catch(n){throw console.error("创建评论失败:",n),n}}static async deleteComment(t,s){try{const n=await rr(ae.comment(t,s),{method:"DELETE"});await de(n)}catch(n){throw console.error("删除评论失败:",n),n}}static validateComment(t){var s,n;return(s=t.content)!=null&&s.trim()?t.content.length>1e3?"评论内容过长，最多1000个字符":(n=t.author)!=null&&n.trim()?t.author.length>50?"姓名过长，最多50个字符":t.email&&!this.isValidEmail(t.email)?"请输入有效的邮箱地址":t.website&&!this.isValidUrl(t.website)?"请输入有效的网站地址":null:"请输入您的姓名":"评论内容不能为空"}static isValidEmail(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)}static isValidUrl(t){try{return new URL(t.startsWith("http")?t:`https://${t}`),!0}catch{return!1}}}function oc({slug:e}){const{isAdmin:t}=Gt(),[s,n]=g.useState([]),[a,l]=g.useState(!0),[i,o]=g.useState(!1),[c,h]=g.useState(null),[d,f]=g.useState(!1),[b,m]=g.useState({content:"",author:"",email:"",website:""}),j=async()=>{try{l(!0),h(null);const w=await Vt.getComments(e);n(Array.isArray(w)?w:[])}catch(w){console.error("加载评论失败:",w),h("加载评论失败，请稍后重试"),n([])}finally{l(!1)}},R=async w=>{w.preventDefault();const L=Vt.validateComment(b);if(L){h(L);return}try{o(!0),h(null);const G=await Vt.createComment(e,b);n(O=>[G,...Array.isArray(O)?O:[]]),m({content:"",author:"",email:"",website:""}),f(!1)}catch(G){console.error("提交评论失败:",G),h(G.message||"提交评论失败，请稍后重试")}finally{o(!1)}},y=async w=>{if(confirm("确定要删除这条评论吗？"))try{await Vt.deleteComment(e,w),n(L=>Array.isArray(L)?L.filter(G=>G.id!==w):[])}catch(L){console.error("删除评论失败:",L),h(L.message||"删除评论失败，请稍后重试")}},k=w=>{const L=new Date(w),O=new Date().getTime()-L.getTime(),T=Math.floor(O/(1e3*60*60*24));return T===0?"今天":T===1?"昨天":T<7?`${T}天前`:L.toLocaleDateString("zh-CN")};return g.useEffect(()=>{j()},[e]),r.jsx("div",{className:"mt-12",children:r.jsxs("div",{className:"card p-8",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center space-x-2",children:[r.jsx(Vr,{size:20}),r.jsxs("span",{children:["评论讨论 (",(s==null?void 0:s.length)||0,")"]})]}),!d&&r.jsxs("button",{onClick:()=>f(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2",children:[r.jsx(Vr,{size:16}),r.jsx("span",{children:"发表评论"})]})]}),c&&r.jsx("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:r.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm",children:c})}),d&&r.jsxs("form",{onSubmit:R,className:"mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("textarea",{value:b.content,onChange:w=>m(L=>({...L,content:w.target.value})),placeholder:"写下您的评论...",rows:4,className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0}),r.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[b.content.length,"/1000 字符"]})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx(mt,{className:"absolute left-3 top-3 text-gray-400",size:16}),r.jsx("input",{type:"text",value:b.author,onChange:w=>m(L=>({...L,author:w.target.value})),placeholder:"您的姓名 *",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),r.jsxs("div",{className:"relative",children:[r.jsx(la,{className:"absolute left-3 top-3 text-gray-400",size:16}),r.jsx("input",{type:"email",value:b.email,onChange:w=>m(L=>({...L,email:w.target.value})),placeholder:"邮箱 (可选)",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),r.jsxs("div",{className:"relative",children:[r.jsx(vo,{className:"absolute left-3 top-3 text-gray-400",size:16}),r.jsx("input",{type:"url",value:b.website,onChange:w=>m(L=>({...L,website:w.target.value})),placeholder:"网站 (可选)",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),r.jsxs("div",{className:"flex items-center justify-between mt-4",children:[r.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"评论将会被公开显示，请文明发言"}),r.jsxs("div",{className:"flex space-x-3",children:[r.jsx("button",{type:"button",onClick:()=>f(!1),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:"取消"}),r.jsxs("button",{type:"submit",disabled:i,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:[r.jsx(zo,{size:16}),r.jsx("span",{children:i?"发表中...":"发表评论"})]})]})]})]}),r.jsx("div",{className:"space-y-6",children:a?r.jsx("div",{className:"space-y-4",children:[...Array(3)].map((w,L)=>r.jsx("div",{className:"animate-pulse",children:r.jsxs("div",{className:"flex space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]})]})},L))}):((s==null?void 0:s.length)||0)===0?r.jsxs("div",{className:"text-center py-8",children:[r.jsx(Vr,{className:"mx-auto mb-4 text-gray-400",size:48}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"还没有评论，来发表第一条吧！"})]}):(s||[]).map(w=>r.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0 last:pb-0",children:r.jsxs("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold",children:w.author.charAt(0).toUpperCase()}),r.jsxs("div",{className:"flex-1",children:[r.jsxs("div",{className:"flex items-center justify-between mb-2",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:w.website?r.jsx("a",{href:w.website.startsWith("http")?w.website:`https://${w.website}`,target:"_blank",rel:"noopener noreferrer",className:"hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:w.author}):w.author}),r.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400",children:[r.jsx(Ge,{size:14}),r.jsx("span",{children:k(w.createdAt)})]})]}),t&&r.jsx("button",{onClick:()=>y(w.id),className:"text-gray-400 hover:text-red-500 transition-colors p-1 rounded",title:"删除评论",children:r.jsx(gs,{size:14})})]}),r.jsx("div",{className:"prose prose-sm dark:prose-dark max-w-none",children:r.jsx("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:w.content})})]})]})},w.id))})]})})}function cc(){const{slug:e}=Wt(),[t,s]=g.useState(null),[n,a]=g.useState(!0),[l,i]=g.useState(null);if(g.useEffect(()=>{(async()=>{if(e)try{a(!0),i(null);const h=await pe.getPost(e);s(h)}catch(h){console.error("获取文章详情失败:",h),i("文章加载失败，请稍后重试")}finally{a(!1)}})()},[e]),n)return r.jsx("div",{className:"max-w-4xl mx-auto",children:r.jsxs("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-8"}),r.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"}),r.jsxs("div",{className:"space-y-4",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"})]})]})});if(l||!t)return r.jsx("div",{className:"max-w-4xl mx-auto text-center",children:r.jsxs("div",{className:"card p-8",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:l||"文章未找到"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:l||"请检查链接是否正确，或返回首页查看其他文章"}),r.jsxs(J,{to:"/",className:"inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:underline",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"返回首页"})]})]})});const o=()=>{};return r.jsxs("article",{className:"max-w-4xl mx-auto",children:[r.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[r.jsxs(J,{to:"/",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"返回首页"})]}),r.jsx(fs,{slug:t.slug,title:t.title,onDeleted:o})]}),r.jsxs("header",{className:"mb-8",children:[t.imageUrl&&r.jsx("div",{className:"mb-8 rounded-xl overflow-hidden shadow-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center",children:r.jsx("img",{src:t.imageUrl,alt:t.title,className:"w-full h-auto max-h-[500px] object-contain hover:scale-105 transition-transform duration-500",loading:"lazy",onError:c=>{console.warn("封面图片加载失败:",t.imageUrl);const d=c.target.parentElement;d&&(d.style.display="none")},onLoad:()=>{console.log("封面图片加载成功:",t.imageUrl)}})}),r.jsx("div",{className:"mb-6",children:r.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white leading-tight",children:t.title})}),r.jsxs("div",{className:"flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-6",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(mt,{size:16}),r.jsx("span",{children:t.author})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(Ge,{size:16}),r.jsx("span",{children:t.date})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(rt,{size:16}),r.jsxs("span",{children:[t.readTime," 分钟阅读"]})]}),t.category&&r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(St,{size:16}),r.jsx("span",{children:t.category})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:t.tags.map(c=>r.jsx(J,{to:`/tag/${c}`,className:"px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors",children:c},c))})]}),r.jsx("div",{className:"card p-8 mb-8",children:t.content&&t.content.trim()?r.jsx("div",{className:"prose prose-gray dark:prose-dark max-w-none prose-img:rounded-lg prose-img:shadow-md",dangerouslySetInnerHTML:{__html:t.content}}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"text-gray-400 mb-4",children:r.jsx("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),r.jsx("h3",{className:"text-lg font-medium text-gray-500 dark:text-gray-400 mb-2",children:"文章内容为空"}),r.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-sm",children:"该文章可能正在编辑中，或者内容尚未加载完成"})]})}),r.jsx("footer",{className:"mt-12 pt-8 border-t border-gray-200 dark:border-gray-700",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:r.jsx("p",{children:"感谢阅读！如果您觉得这篇文章有用，请分享给更多人。"})}),r.jsxs("div",{className:"flex space-x-4",children:[r.jsx(J,{to:"/",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"更多文章"}),r.jsx(J,{to:"/about",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"关于作者"})]})]})}),r.jsx(oc,{slug:t.slug})]})}const dc=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,uc=Qa.create({name:"image",addOptions(){return{inline:!1,allowBase64:!1,HTMLAttributes:{}}},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes(){return{src:{default:null},alt:{default:null},title:{default:null}}},parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:e}){return["img",ss(this.options.HTMLAttributes,e)]},addCommands(){return{setImage:e=>({commands:t})=>t.insertContent({type:this.name,attrs:e})}},addInputRules(){return[Za({find:dc,type:this.type,getAttributes:e=>{const[,,t,s,n]=e;return{src:s,alt:t,title:n}}})]}}),hc="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",gc="ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2",ht=(e,t)=>{for(const s in t)e[s]=t[s];return e},xs="numeric",ps="ascii",ms="alpha",Ct="asciinumeric",Nt="alphanumeric",ys="domain",ha="emoji",fc="scheme",xc="slashscheme",Xr="whitespace";function pc(e,t){return e in t||(t[e]=[]),t[e]}function Ye(e,t,s){t[xs]&&(t[Ct]=!0,t[Nt]=!0),t[ps]&&(t[Ct]=!0,t[ms]=!0),t[Ct]&&(t[Nt]=!0),t[ms]&&(t[Nt]=!0),t[Nt]&&(t[ys]=!0),t[ha]&&(t[ys]=!0);for(const n in t){const a=pc(n,s);a.indexOf(e)<0&&a.push(e)}}function mc(e,t){const s={};for(const n in t)t[n].indexOf(e)>=0&&(s[n]=!0);return s}function ve(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}ve.groups={};ve.prototype={accepts(){return!!this.t},go(e){const t=this,s=t.j[e];if(s)return s;for(let n=0;n<t.jr.length;n++){const a=t.jr[n][0],l=t.jr[n][1];if(l&&a.test(e))return l}return t.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,s,n){for(let a=0;a<e.length;a++)this.tt(e[a],t,s,n)},tr(e,t,s,n){n=n||ve.groups;let a;return t&&t.j?a=t:(a=new ve(t),s&&n&&Ye(t,s,n)),this.jr.push([e,a]),a},ts(e,t,s,n){let a=this;const l=e.length;if(!l)return a;for(let i=0;i<l-1;i++)a=a.tt(e[i]);return a.tt(e[l-1],t,s,n)},tt(e,t,s,n){n=n||ve.groups;const a=this;if(t&&t.j)return a.j[e]=t,t;const l=t;let i,o=a.go(e);if(o?(i=new ve,ht(i.j,o.j),i.jr.push.apply(i.jr,o.jr),i.jd=o.jd,i.t=o.t):i=new ve,l){if(n)if(i.t&&typeof i.t=="string"){const c=ht(mc(i.t,n),s);Ye(l,c,n)}else s&&Ye(l,s,n);i.t=l}return a.j[e]=i,i}};const V=(e,t,s,n,a)=>e.ta(t,s,n,a),ne=(e,t,s,n,a)=>e.tr(t,s,n,a),xn=(e,t,s,n,a)=>e.ts(t,s,n,a),S=(e,t,s,n,a)=>e.tt(t,s,n,a),_e="WORD",bs="UWORD",ga="ASCIINUMERICAL",fa="ALPHANUMERICAL",Pt="LOCALHOST",vs="TLD",js="UTLD",sr="SCHEME",dt="SLASH_SCHEME",Es="NUM",ks="WS",Ss="NL",At="OPENBRACE",Rt="CLOSEBRACE",dr="OPENBRACKET",ur="CLOSEBRACKET",hr="OPENPAREN",gr="CLOSEPAREN",fr="OPENANGLEBRACKET",xr="CLOSEANGLEBRACKET",pr="FULLWIDTHLEFTPAREN",mr="FULLWIDTHRIGHTPAREN",yr="LEFTCORNERBRACKET",br="RIGHTCORNERBRACKET",vr="LEFTWHITECORNERBRACKET",jr="RIGHTWHITECORNERBRACKET",kr="FULLWIDTHLESSTHAN",wr="FULLWIDTHGREATERTHAN",Nr="AMPERSAND",Er="APOSTROPHE",Sr="ASTERISK",ze="AT",Cr="BACKSLASH",Ar="BACKTICK",Rr="CARET",He="COLON",Cs="COMMA",Mr="DOLLAR",Re="DOT",Tr="EQUALS",As="EXCLAMATION",Ne="HYPHEN",Mt="PERCENT",Lr="PIPE",Or="PLUS",_r="POUND",Tt="QUERY",Rs="QUOTE",xa="FULLWIDTHMIDDLEDOT",Ms="SEMI",Me="SLASH",Lt="TILDE",Ir="UNDERSCORE",pa="EMOJI",Pr="SYM";var ma=Object.freeze({__proto__:null,ALPHANUMERICAL:fa,AMPERSAND:Nr,APOSTROPHE:Er,ASCIINUMERICAL:ga,ASTERISK:Sr,AT:ze,BACKSLASH:Cr,BACKTICK:Ar,CARET:Rr,CLOSEANGLEBRACKET:xr,CLOSEBRACE:Rt,CLOSEBRACKET:ur,CLOSEPAREN:gr,COLON:He,COMMA:Cs,DOLLAR:Mr,DOT:Re,EMOJI:pa,EQUALS:Tr,EXCLAMATION:As,FULLWIDTHGREATERTHAN:wr,FULLWIDTHLEFTPAREN:pr,FULLWIDTHLESSTHAN:kr,FULLWIDTHMIDDLEDOT:xa,FULLWIDTHRIGHTPAREN:mr,HYPHEN:Ne,LEFTCORNERBRACKET:yr,LEFTWHITECORNERBRACKET:vr,LOCALHOST:Pt,NL:Ss,NUM:Es,OPENANGLEBRACKET:fr,OPENBRACE:At,OPENBRACKET:dr,OPENPAREN:hr,PERCENT:Mt,PIPE:Lr,PLUS:Or,POUND:_r,QUERY:Tt,QUOTE:Rs,RIGHTCORNERBRACKET:br,RIGHTWHITECORNERBRACKET:jr,SCHEME:sr,SEMI:Ms,SLASH:Me,SLASH_SCHEME:dt,SYM:Pr,TILDE:Lt,TLD:vs,UNDERSCORE:Ir,UTLD:js,UWORD:bs,WORD:_e,WS:ks});const Le=/[a-z]/,kt=/\p{L}/u,Jr=/\p{Emoji}/u,Oe=/\d/,Qr=/\s/,pn="\r",Zr=`
`,yc="️",bc="‍",Yr="￼";let Xt=null,Jt=null;function vc(e=[]){const t={};ve.groups=t;const s=new ve;Xt==null&&(Xt=mn(hc)),Jt==null&&(Jt=mn(gc)),S(s,"'",Er),S(s,"{",At),S(s,"}",Rt),S(s,"[",dr),S(s,"]",ur),S(s,"(",hr),S(s,")",gr),S(s,"<",fr),S(s,">",xr),S(s,"（",pr),S(s,"）",mr),S(s,"「",yr),S(s,"」",br),S(s,"『",vr),S(s,"』",jr),S(s,"＜",kr),S(s,"＞",wr),S(s,"&",Nr),S(s,"*",Sr),S(s,"@",ze),S(s,"`",Ar),S(s,"^",Rr),S(s,":",He),S(s,",",Cs),S(s,"$",Mr),S(s,".",Re),S(s,"=",Tr),S(s,"!",As),S(s,"-",Ne),S(s,"%",Mt),S(s,"|",Lr),S(s,"+",Or),S(s,"#",_r),S(s,"?",Tt),S(s,'"',Rs),S(s,"/",Me),S(s,";",Ms),S(s,"~",Lt),S(s,"_",Ir),S(s,"\\",Cr),S(s,"・",xa);const n=ne(s,Oe,Es,{[xs]:!0});ne(n,Oe,n);const a=ne(n,Le,ga,{[Ct]:!0}),l=ne(n,kt,fa,{[Nt]:!0}),i=ne(s,Le,_e,{[ps]:!0});ne(i,Oe,a),ne(i,Le,i),ne(a,Oe,a),ne(a,Le,a);const o=ne(s,kt,bs,{[ms]:!0});ne(o,Le),ne(o,Oe,l),ne(o,kt,o),ne(l,Oe,l),ne(l,Le),ne(l,kt,l);const c=S(s,Zr,Ss,{[Xr]:!0}),h=S(s,pn,ks,{[Xr]:!0}),d=ne(s,Qr,ks,{[Xr]:!0});S(s,Yr,d),S(h,Zr,c),S(h,Yr,d),ne(h,Qr,d),S(d,pn),S(d,Zr),ne(d,Qr,d),S(d,Yr,d);const f=ne(s,Jr,pa,{[ha]:!0});S(f,"#"),ne(f,Jr,f),S(f,yc,f);const b=S(f,bc);S(b,"#"),ne(b,Jr,f);const m=[[Le,i],[Oe,a]],j=[[Le,null],[kt,o],[Oe,l]];for(let R=0;R<Xt.length;R++)De(s,Xt[R],vs,_e,m);for(let R=0;R<Jt.length;R++)De(s,Jt[R],js,bs,j);Ye(vs,{tld:!0,ascii:!0},t),Ye(js,{utld:!0,alpha:!0},t),De(s,"file",sr,_e,m),De(s,"mailto",sr,_e,m),De(s,"http",dt,_e,m),De(s,"https",dt,_e,m),De(s,"ftp",dt,_e,m),De(s,"ftps",dt,_e,m),Ye(sr,{scheme:!0,ascii:!0},t),Ye(dt,{slashscheme:!0,ascii:!0},t),e=e.sort((R,y)=>R[0]>y[0]?1:-1);for(let R=0;R<e.length;R++){const y=e[R][0],w=e[R][1]?{[fc]:!0}:{[xc]:!0};y.indexOf("-")>=0?w[ys]=!0:Le.test(y)?Oe.test(y)?w[Ct]=!0:w[ps]=!0:w[xs]=!0,xn(s,y,y,w)}return xn(s,"localhost",Pt,{ascii:!0}),s.jd=new ve(Pr),{start:s,tokens:ht({groups:t},ma)}}function ya(e,t){const s=jc(t.replace(/[A-Z]/g,o=>o.toLowerCase())),n=s.length,a=[];let l=0,i=0;for(;i<n;){let o=e,c=null,h=0,d=null,f=-1,b=-1;for(;i<n&&(c=o.go(s[i]));)o=c,o.accepts()?(f=0,b=0,d=o):f>=0&&(f+=s[i].length,b++),h+=s[i].length,l+=s[i].length,i++;l-=f,i-=b,h-=f,a.push({t:d.t,v:t.slice(l-h,l),s:l-h,e:l})}return a}function jc(e){const t=[],s=e.length;let n=0;for(;n<s;){let a=e.charCodeAt(n),l,i=a<55296||a>56319||n+1===s||(l=e.charCodeAt(n+1))<56320||l>57343?e[n]:e.slice(n,n+2);t.push(i),n+=i.length}return t}function De(e,t,s,n,a){let l;const i=t.length;for(let o=0;o<i-1;o++){const c=t[o];e.j[c]?l=e.j[c]:(l=new ve(n),l.jr=a.slice(),e.j[c]=l),e=l}return l=new ve(s),l.jr=a.slice(),e.j[t[i-1]]=l,l}function mn(e){const t=[],s=[];let n=0,a="0123456789";for(;n<e.length;){let l=0;for(;a.indexOf(e[n+l])>=0;)l++;if(l>0){t.push(s.join(""));for(let i=parseInt(e.substring(n,n+l),10);i>0;i--)s.pop();n+=l}else s.push(e[n]),n++}return t}const $t={defaultProtocol:"http",events:null,format:yn,formatHref:yn,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Ts(e,t=null){let s=ht({},$t);e&&(s=ht(s,e instanceof Ts?e.o:e));const n=s.ignoreTags,a=[];for(let l=0;l<n.length;l++)a.push(n[l].toUpperCase());this.o=s,t&&(this.defaultRender=t),this.ignoreTags=a}Ts.prototype={o:$t,ignoreTags:[],defaultRender(e){return e},check(e){return this.get("validate",e.toString(),e)},get(e,t,s){const n=t!=null;let a=this.o[e];return a&&(typeof a=="object"?(a=s.t in a?a[s.t]:$t[e],typeof a=="function"&&n&&(a=a(t,s))):typeof a=="function"&&n&&(a=a(t,s.t,s)),a)},getObj(e,t,s){let n=this.o[e];return typeof n=="function"&&t!=null&&(n=n(t,s.t,s)),n},render(e){const t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}};function yn(e){return e}function ba(e,t){this.t="token",this.v=e,this.tk=t}ba.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){const t=this.toString(),s=e.get("truncate",t,this),n=e.get("format",t,this);return s&&n.length>s?n.substring(0,s)+"…":n},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=$t.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){const t=this,s=this.toHref(e.get("defaultProtocol")),n=e.get("formatHref",s,this),a=e.get("tagName",s,t),l=this.toFormattedString(e),i={},o=e.get("className",s,t),c=e.get("target",s,t),h=e.get("rel",s,t),d=e.getObj("attributes",s,t),f=e.getObj("events",s,t);return i.href=n,o&&(i.class=o),c&&(i.target=c),h&&(i.rel=h),d&&ht(i,d),{tagName:a,attributes:i,content:l,eventListeners:f}}};function Wr(e,t){class s extends ba{constructor(a,l){super(a,l),this.t=e}}for(const n in t)s.prototype[n]=t[n];return s.t=e,s}const bn=Wr("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),vn=Wr("text"),kc=Wr("nl"),Qt=Wr("url",{isLink:!0,toHref(e=$t.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){const e=this.tk;return e.length>=2&&e[0].t!==Pt&&e[1].t===He}}),we=e=>new ve(e);function wc({groups:e}){const t=e.domain.concat([Nr,Sr,ze,Cr,Ar,Rr,Mr,Tr,Ne,Es,Mt,Lr,Or,_r,Me,Pr,Lt,Ir]),s=[Er,He,Cs,Re,As,Mt,Tt,Rs,Ms,fr,xr,At,Rt,ur,dr,hr,gr,pr,mr,yr,br,vr,jr,kr,wr],n=[Nr,Er,Sr,Cr,Ar,Rr,Mr,Tr,Ne,At,Rt,Mt,Lr,Or,_r,Tt,Me,Pr,Lt,Ir],a=we(),l=S(a,Lt);V(l,n,l),V(l,e.domain,l);const i=we(),o=we(),c=we();V(a,e.domain,i),V(a,e.scheme,o),V(a,e.slashscheme,c),V(i,n,l),V(i,e.domain,i);const h=S(i,ze);S(l,ze,h),S(o,ze,h),S(c,ze,h);const d=S(l,Re);V(d,n,l),V(d,e.domain,l);const f=we();V(h,e.domain,f),V(f,e.domain,f);const b=S(f,Re);V(b,e.domain,f);const m=we(bn);V(b,e.tld,m),V(b,e.utld,m),S(h,Pt,m);const j=S(f,Ne);S(j,Ne,j),V(j,e.domain,f),V(m,e.domain,f),S(m,Re,b),S(m,Ne,j);const R=S(m,He);V(R,e.numeric,bn);const y=S(i,Ne),k=S(i,Re);S(y,Ne,y),V(y,e.domain,i),V(k,n,l),V(k,e.domain,i);const w=we(Qt);V(k,e.tld,w),V(k,e.utld,w),V(w,e.domain,i),V(w,n,l),S(w,Re,k),S(w,Ne,y),S(w,ze,h);const L=S(w,He),G=we(Qt);V(L,e.numeric,G);const O=we(Qt),T=we();V(O,t,O),V(O,s,T),V(T,t,O),V(T,s,T),S(w,Me,O),S(G,Me,O);const Z=S(o,He),ee=S(c,He),te=S(ee,Me),$=S(te,Me);V(o,e.domain,i),S(o,Re,k),S(o,Ne,y),V(c,e.domain,i),S(c,Re,k),S(c,Ne,y),V(Z,e.domain,O),S(Z,Me,O),S(Z,Tt,O),V($,e.domain,O),V($,t,O),S($,Me,O);const ke=[[At,Rt],[dr,ur],[hr,gr],[fr,xr],[pr,mr],[yr,br],[vr,jr],[kr,wr]];for(let ie=0;ie<ke.length;ie++){const[je,le]=ke[ie],ue=S(O,je);S(T,je,ue),S(ue,le,O);const N=we(Qt);V(ue,t,N);const x=we();V(ue,s),V(N,t,N),V(N,s,x),V(x,t,N),V(x,s,x),S(N,le,O),S(x,le,O)}return S(a,Pt,w),S(a,Ss,kc),{start:a,tokens:ma}}function Nc(e,t,s){let n=s.length,a=0,l=[],i=[];for(;a<n;){let o=e,c=null,h=null,d=0,f=null,b=-1;for(;a<n&&!(c=o.go(s[a].t));)i.push(s[a++]);for(;a<n&&(h=c||o.go(s[a].t));)c=null,o=h,o.accepts()?(b=0,f=o):b>=0&&b++,a++,d++;if(b<0)a-=d,a<n&&(i.push(s[a]),a++);else{i.length>0&&(l.push(es(vn,t,i)),i=[]),a-=b,d-=b;const m=f.t,j=s.slice(a-d,a);l.push(es(m,t,j))}}return i.length>0&&l.push(es(vn,t,i)),l}function es(e,t,s){const n=s[0].s,a=s[s.length-1].e,l=t.slice(n,a);return new e(l,s)}const Ec=typeof console<"u"&&console&&console.warn||(()=>{}),Sc="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",Y={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function Cc(){return ve.groups={},Y.scanner=null,Y.parser=null,Y.tokenQueue=[],Y.pluginQueue=[],Y.customSchemes=[],Y.initialized=!1,Y}function jn(e,t=!1){if(Y.initialized&&Ec(`linkifyjs: already initialized - will not register custom scheme "${e}" ${Sc}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw new Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);Y.customSchemes.push([e,t])}function Ac(){Y.scanner=vc(Y.customSchemes);for(let e=0;e<Y.tokenQueue.length;e++)Y.tokenQueue[e][1]({scanner:Y.scanner});Y.parser=wc(Y.scanner.tokens);for(let e=0;e<Y.pluginQueue.length;e++)Y.pluginQueue[e][1]({scanner:Y.scanner,parser:Y.parser});return Y.initialized=!0,Y}function Ls(e){return Y.initialized||Ac(),Nc(Y.parser.start,e,ya(Y.scanner.start,e))}Ls.scan=ya;function va(e,t=null,s=null){if(t&&typeof t=="object"){if(s)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);s=t,t=null}const n=new Ts(s),a=Ls(e),l=[];for(let i=0;i<a.length;i++){const o=a[i];o.isLink&&(!t||o.t===t)&&n.check(o)&&l.push(o.toFormattedObject(n))}return l}function Rc(e){return e.length===1?e[0].isLink:e.length===3&&e[1].isLink?["()","[]"].includes(e[0].value+e[2].value):!1}function Mc(e){return new zr({key:new Ur("autolink"),appendTransaction:(t,s,n)=>{const a=t.some(h=>h.docChanged)&&!s.doc.eq(n.doc),l=t.some(h=>h.getMeta("preventAutolink"));if(!a||l)return;const{tr:i}=n,o=ti(s.doc,[...t]);if(ri(o).forEach(({newRange:h})=>{const d=si(n.doc,h,m=>m.isTextblock);let f,b;if(d.length>1?(f=d[0],b=n.doc.textBetween(f.pos,f.pos+f.node.nodeSize,void 0," ")):d.length&&n.doc.textBetween(h.from,h.to," "," ").endsWith(" ")&&(f=d[0],b=n.doc.textBetween(f.pos,h.to,void 0," ")),f&&b){const m=b.split(" ").filter(k=>k!=="");if(m.length<=0)return!1;const j=m[m.length-1],R=f.pos+b.lastIndexOf(j);if(!j)return!1;const y=Ls(j).map(k=>k.toObject(e.defaultProtocol));if(!Rc(y))return!1;y.filter(k=>k.isLink).map(k=>({...k,from:R+k.start+1,to:R+k.end+1})).filter(k=>n.schema.marks.code?!n.doc.rangeHasMark(k.from,k.to,n.schema.marks.code):!0).filter(k=>e.validate(k.value)).filter(k=>e.shouldAutoLink(k.value)).forEach(k=>{ni(k.from,k.to,n.doc).some(w=>w.mark.type===e.type)||i.addMark(k.from,k.to,e.type.create({href:k.href}))})}}),!!i.steps.length)return i}})}function Tc(e){return new zr({key:new Ur("handleClickLink"),props:{handleClick:(t,s,n)=>{var a,l;if(n.button!==0||!t.editable)return!1;let i=n.target;const o=[];for(;i.nodeName!=="DIV";)o.push(i),i=i.parentNode;if(!o.find(b=>b.nodeName==="A"))return!1;const c=ai(t.state,e.type.name),h=n.target,d=(a=h==null?void 0:h.href)!==null&&a!==void 0?a:c.href,f=(l=h==null?void 0:h.target)!==null&&l!==void 0?l:c.target;return h&&d?(window.open(d,f),!0):!1}}})}function Lc(e){return new zr({key:new Ur("handlePasteLink"),props:{handlePaste:(t,s,n)=>{const{state:a}=t,{selection:l}=a,{empty:i}=l;if(i)return!1;let o="";n.content.forEach(h=>{o+=h.textContent});const c=va(o,{defaultProtocol:e.defaultProtocol}).find(h=>h.isLink&&h.value===o);return!o||!c?!1:e.editor.commands.setMark(e.type,{href:c.href})}}})}const Oc=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function Qe(e,t){const s=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach(n=>{const a=typeof n=="string"?n:n.scheme;a&&s.push(a)}),!e||e.replace(Oc,"").match(new RegExp(`^(?:(?:${s.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}const _c=Ya.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if(typeof e=="string"){jn(e);return}jn(e.scheme,e.optionalSlashes)})},onDestroy(){Cc()},inclusive(){return this.options.autolink},addOptions(){return{openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!Qe(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}},addAttributes(){return{href:{default:null,parseHTML(e){return e.getAttribute("href")}},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{const t=e.getAttribute("href");return!t||!this.options.isAllowedUri(t,{defaultValidate:s=>!!Qe(s,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:t=>!!Qe(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",ss(this.options.HTMLAttributes,e),0]:["a",ss(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{const{href:s}=e;return this.options.isAllowedUri(s,{defaultValidate:n=>!!Qe(n,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?t().setMark(this.name,e).setMeta("preventAutolink",!0).run():!1},toggleLink:e=>({chain:t})=>{const{href:s}=e;return this.options.isAllowedUri(s,{defaultValidate:n=>!!Qe(n,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run():!1},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[ei({find:e=>{const t=[];if(e){const{protocols:s,defaultProtocol:n}=this.options,a=va(e).filter(l=>l.isLink&&this.options.isAllowedUri(l.value,{defaultValidate:i=>!!Qe(i,s),protocols:s,defaultProtocol:n}));a.length&&a.forEach(l=>t.push({text:l.value,data:{href:l.href},index:l.start}))}return t},type:this.type,getAttributes:e=>{var t;return{href:(t=e.data)===null||t===void 0?void 0:t.href}}})]},addProseMirrorPlugins(){const e=[],{protocols:t,defaultProtocol:s}=this.options;return this.options.autolink&&e.push(Mc({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:n=>this.options.isAllowedUri(n,{defaultValidate:a=>!!Qe(a,t),protocols:t,defaultProtocol:s}),shouldAutoLink:this.options.shouldAutoLink})),this.options.openOnClick===!0&&e.push(Tc({type:this.type})),this.options.linkOnPaste&&e.push(Lc({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),e}});function Ic(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function ja(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(t=>{const s=e[t],n=typeof s;(n==="object"||n==="function")&&!Object.isFrozen(s)&&ja(s)}),e}let kn=class{constructor(t){t.data===void 0&&(t.data={}),this.data=t.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}};function ka(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function Fe(e,...t){const s=Object.create(null);for(const n in e)s[n]=e[n];return t.forEach(function(n){for(const a in n)s[a]=n[a]}),s}const Pc="</span>",wn=e=>!!e.scope,$c=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const s=e.split(".");return[`${t}${s.shift()}`,...s.map((n,a)=>`${n}${"_".repeat(a+1)}`)].join(" ")}return`${t}${e}`};let Dc=class{constructor(t,s){this.buffer="",this.classPrefix=s.classPrefix,t.walk(this)}addText(t){this.buffer+=ka(t)}openNode(t){if(!wn(t))return;const s=$c(t.scope,{prefix:this.classPrefix});this.span(s)}closeNode(t){wn(t)&&(this.buffer+=Pc)}value(){return this.buffer}span(t){this.buffer+=`<span class="${t}">`}};const Nn=(e={})=>{const t={children:[]};return Object.assign(t,e),t};let zc=class wa{constructor(){this.rootNode=Nn(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(t){this.top.children.push(t)}openNode(t){const s=Nn({scope:t});this.add(s),this.stack.push(s)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(t){return this.constructor._walk(t,this.rootNode)}static _walk(t,s){return typeof s=="string"?t.addText(s):s.children&&(t.openNode(s),s.children.forEach(n=>this._walk(t,n)),t.closeNode(s)),t}static _collapse(t){typeof t!="string"&&t.children&&(t.children.every(s=>typeof s=="string")?t.children=[t.children.join("")]:t.children.forEach(s=>{wa._collapse(s)}))}},Uc=class extends zc{constructor(t){super(),this.options=t}addText(t){t!==""&&this.add(t)}startScope(t){this.openNode(t)}endScope(){this.closeNode()}__addSublanguage(t,s){const n=t.root;s&&(n.scope=`language:${s}`),this.add(n)}toHTML(){return new Dc(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}};function Dt(e){return e?typeof e=="string"?e:e.source:null}function Na(e){return at("(?=",e,")")}function Hc(e){return at("(?:",e,")*")}function Bc(e){return at("(?:",e,")?")}function at(...e){return e.map(s=>Dt(s)).join("")}function Fc(e){const t=e[e.length-1];return typeof t=="object"&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}function Os(...e){return"("+(Fc(e).capture?"":"?:")+e.map(n=>Dt(n)).join("|")+")"}function Ea(e){return new RegExp(e.toString()+"|").exec("").length-1}function Wc(e,t){const s=e&&e.exec(t);return s&&s.index===0}const Kc=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function _s(e,{joinWith:t}){let s=0;return e.map(n=>{s+=1;const a=s;let l=Dt(n),i="";for(;l.length>0;){const o=Kc.exec(l);if(!o){i+=l;break}i+=l.substring(0,o.index),l=l.substring(o.index+o[0].length),o[0][0]==="\\"&&o[1]?i+="\\"+String(Number(o[1])+a):(i+=o[0],o[0]==="("&&s++)}return i}).map(n=>`(${n})`).join(t)}const Gc=/\b\B/,Sa="[a-zA-Z]\\w*",Is="[a-zA-Z_]\\w*",Ca="\\b\\d+(\\.\\d+)?",Aa="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Ra="\\b(0b[01]+)",qc="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",Vc=(e={})=>{const t=/^#![ ]*\//;return e.binary&&(e.begin=at(t,/.*\b/,e.binary,/\b.*/)),Fe({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(s,n)=>{s.index!==0&&n.ignoreMatch()}},e)},zt={begin:"\\\\[\\s\\S]",relevance:0},Xc={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[zt]},Jc={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[zt]},Qc={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Kr=function(e,t,s={}){const n=Fe({scope:"comment",begin:e,end:t,contains:[]},s);n.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const a=Os("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return n.contains.push({begin:at(/[ ]+/,"(",a,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),n},Zc=Kr("//","$"),Yc=Kr("/\\*","\\*/"),ed=Kr("#","$"),td={scope:"number",begin:Ca,relevance:0},rd={scope:"number",begin:Aa,relevance:0},sd={scope:"number",begin:Ra,relevance:0},nd={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[zt,{begin:/\[/,end:/\]/,relevance:0,contains:[zt]}]},ad={scope:"title",begin:Sa,relevance:0},id={scope:"title",begin:Is,relevance:0},ld={begin:"\\.\\s*"+Is,relevance:0},od=function(e){return Object.assign(e,{"on:begin":(t,s)=>{s.data._beginMatch=t[1]},"on:end":(t,s)=>{s.data._beginMatch!==t[1]&&s.ignoreMatch()}})};var Zt=Object.freeze({__proto__:null,APOS_STRING_MODE:Xc,BACKSLASH_ESCAPE:zt,BINARY_NUMBER_MODE:sd,BINARY_NUMBER_RE:Ra,COMMENT:Kr,C_BLOCK_COMMENT_MODE:Yc,C_LINE_COMMENT_MODE:Zc,C_NUMBER_MODE:rd,C_NUMBER_RE:Aa,END_SAME_AS_BEGIN:od,HASH_COMMENT_MODE:ed,IDENT_RE:Sa,MATCH_NOTHING_RE:Gc,METHOD_GUARD:ld,NUMBER_MODE:td,NUMBER_RE:Ca,PHRASAL_WORDS_MODE:Qc,QUOTE_STRING_MODE:Jc,REGEXP_MODE:nd,RE_STARTERS_RE:qc,SHEBANG:Vc,TITLE_MODE:ad,UNDERSCORE_IDENT_RE:Is,UNDERSCORE_TITLE_MODE:id});function cd(e,t){e.input[e.index-1]==="."&&t.ignoreMatch()}function dd(e,t){e.className!==void 0&&(e.scope=e.className,delete e.className)}function ud(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=cd,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function hd(e,t){Array.isArray(e.illegal)&&(e.illegal=Os(...e.illegal))}function gd(e,t){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function fd(e,t){e.relevance===void 0&&(e.relevance=1)}const xd=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const s=Object.assign({},e);Object.keys(e).forEach(n=>{delete e[n]}),e.keywords=s.keywords,e.begin=at(s.beforeMatch,Na(s.begin)),e.starts={relevance:0,contains:[Object.assign(s,{endsParent:!0})]},e.relevance=0,delete s.beforeMatch},pd=["of","and","for","in","not","or","if","then","parent","list","value"],md="keyword";function Ma(e,t,s=md){const n=Object.create(null);return typeof e=="string"?a(s,e.split(" ")):Array.isArray(e)?a(s,e):Object.keys(e).forEach(function(l){Object.assign(n,Ma(e[l],t,l))}),n;function a(l,i){t&&(i=i.map(o=>o.toLowerCase())),i.forEach(function(o){const c=o.split("|");n[c[0]]=[l,yd(c[0],c[1])]})}}function yd(e,t){return t?Number(t):bd(e)?0:1}function bd(e){return pd.includes(e.toLowerCase())}const En={},et=e=>{console.error(e)},Sn=(e,...t)=>{console.log(`WARN: ${e}`,...t)},ot=(e,t)=>{En[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),En[`${e}/${t}`]=!0)},$r=new Error;function Ta(e,t,{key:s}){let n=0;const a=e[s],l={},i={};for(let o=1;o<=t.length;o++)i[o+n]=a[o],l[o+n]=!0,n+=Ea(t[o-1]);e[s]=i,e[s]._emit=l,e[s]._multi=!0}function vd(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw et("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),$r;if(typeof e.beginScope!="object"||e.beginScope===null)throw et("beginScope must be object"),$r;Ta(e,e.begin,{key:"beginScope"}),e.begin=_s(e.begin,{joinWith:""})}}function jd(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw et("skip, excludeEnd, returnEnd not compatible with endScope: {}"),$r;if(typeof e.endScope!="object"||e.endScope===null)throw et("endScope must be object"),$r;Ta(e,e.end,{key:"endScope"}),e.end=_s(e.end,{joinWith:""})}}function kd(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function wd(e){kd(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),vd(e),jd(e)}function Nd(e){function t(i,o){return new RegExp(Dt(i),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(o?"g":""))}class s{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(o,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,o]),this.matchAt+=Ea(o)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const o=this.regexes.map(c=>c[1]);this.matcherRe=t(_s(o,{joinWith:"|"}),!0),this.lastIndex=0}exec(o){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(o);if(!c)return null;const h=c.findIndex((f,b)=>b>0&&f!==void 0),d=this.matchIndexes[h];return c.splice(0,h),Object.assign(c,d)}}class n{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(o){if(this.multiRegexes[o])return this.multiRegexes[o];const c=new s;return this.rules.slice(o).forEach(([h,d])=>c.addRule(h,d)),c.compile(),this.multiRegexes[o]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(o,c){this.rules.push([o,c]),c.type==="begin"&&this.count++}exec(o){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let h=c.exec(o);if(this.resumingScanAtSamePosition()&&!(h&&h.index===this.lastIndex)){const d=this.getMatcher(0);d.lastIndex=this.lastIndex+1,h=d.exec(o)}return h&&(this.regexIndex+=h.position+1,this.regexIndex===this.count&&this.considerAll()),h}}function a(i){const o=new n;return i.contains.forEach(c=>o.addRule(c.begin,{rule:c,type:"begin"})),i.terminatorEnd&&o.addRule(i.terminatorEnd,{type:"end"}),i.illegal&&o.addRule(i.illegal,{type:"illegal"}),o}function l(i,o){const c=i;if(i.isCompiled)return c;[dd,gd,wd,xd].forEach(d=>d(i,o)),e.compilerExtensions.forEach(d=>d(i,o)),i.__beforeBegin=null,[ud,hd,fd].forEach(d=>d(i,o)),i.isCompiled=!0;let h=null;return typeof i.keywords=="object"&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),h=i.keywords.$pattern,delete i.keywords.$pattern),h=h||/\w+/,i.keywords&&(i.keywords=Ma(i.keywords,e.case_insensitive)),c.keywordPatternRe=t(h,!0),o&&(i.begin||(i.begin=/\B|\b/),c.beginRe=t(c.begin),!i.end&&!i.endsWithParent&&(i.end=/\B|\b/),i.end&&(c.endRe=t(c.end)),c.terminatorEnd=Dt(c.end)||"",i.endsWithParent&&o.terminatorEnd&&(c.terminatorEnd+=(i.end?"|":"")+o.terminatorEnd)),i.illegal&&(c.illegalRe=t(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(d){return Ed(d==="self"?i:d)})),i.contains.forEach(function(d){l(d,c)}),i.starts&&l(i.starts,o),c.matcher=a(c),c}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=Fe(e.classNameAliases||{}),l(e)}function La(e){return e?e.endsWithParent||La(e.starts):!1}function Ed(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(t){return Fe(e,{variants:null},t)})),e.cachedVariants?e.cachedVariants:La(e)?Fe(e,{starts:e.starts?Fe(e.starts):null}):Object.isFrozen(e)?Fe(e):e}var Sd="11.10.0";let Cd=class extends Error{constructor(t,s){super(t),this.name="HTMLInjectionError",this.html=s}};const ts=ka,Cn=Fe,An=Symbol("nomatch"),Ad=7,Oa=function(e){const t=Object.create(null),s=Object.create(null),n=[];let a=!0;const l="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let o={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:Uc};function c(x){return o.noHighlightRe.test(x)}function h(x){let E=x.className+" ";E+=x.parentNode?x.parentNode.className:"";const _=o.languageDetectRe.exec(E);if(_){const C=te(_[1]);return C||(Sn(l.replace("{}",_[1])),Sn("Falling back to no-highlight mode for this block.",x)),C?_[1]:"no-highlight"}return E.split(/\s+/).find(C=>c(C)||te(C))}function d(x,E,_){let C="",P="";typeof E=="object"?(C=x,_=E.ignoreIllegals,P=E.language):(ot("10.7.0","highlight(lang, code, ...args) has been deprecated."),ot("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),P=x,C=E),_===void 0&&(_=!0);const H={code:C,language:P};ue("before:highlight",H);const I=H.result?H.result:f(H.language,H.code,_);return I.code=H.code,ue("after:highlight",I),I}function f(x,E,_,C){const P=Object.create(null);function H(u,p){return u.keywords[p]}function I(){if(!W.keywords){K.addText(Q);return}let u=0;W.keywordPatternRe.lastIndex=0;let p=W.keywordPatternRe.exec(Q),v="";for(;p;){v+=Q.substring(u,p.index);const A=me.case_insensitive?p[0].toLowerCase():p[0],U=H(W,A);if(U){const[F,xe]=U;if(K.addText(v),v="",P[A]=(P[A]||0)+1,P[A]<=Ad&&(fe+=xe),F.startsWith("_"))v+=p[0];else{const Je=me.classNameAliases[F]||F;X(p[0],Je)}}else v+=p[0];u=W.keywordPatternRe.lastIndex,p=W.keywordPatternRe.exec(Q)}v+=Q.substring(u),K.addText(v)}function z(){if(Q==="")return;let u=null;if(typeof W.subLanguage=="string"){if(!t[W.subLanguage]){K.addText(Q);return}u=f(W.subLanguage,Q,!0,se[W.subLanguage]),se[W.subLanguage]=u._top}else u=m(Q,W.subLanguage.length?W.subLanguage:null);W.relevance>0&&(fe+=u.relevance),K.__addSublanguage(u._emitter,u.language)}function q(){W.subLanguage!=null?z():I(),Q=""}function X(u,p){u!==""&&(K.startScope(p),K.addText(u),K.endScope())}function re(u,p){let v=1;const A=p.length-1;for(;v<=A;){if(!u._emit[v]){v++;continue}const U=me.classNameAliases[u[v]]||u[v],F=p[v];U?X(F,U):(Q=F,I(),Q=""),v++}}function he(u,p){return u.scope&&typeof u.scope=="string"&&K.openNode(me.classNameAliases[u.scope]||u.scope),u.beginScope&&(u.beginScope._wrap?(X(Q,me.classNameAliases[u.beginScope._wrap]||u.beginScope._wrap),Q=""):u.beginScope._multi&&(re(u.beginScope,p),Q="")),W=Object.create(u,{parent:{value:W}}),W}function lt(u,p,v){let A=Wc(u.endRe,v);if(A){if(u["on:end"]){const U=new kn(u);u["on:end"](p,U),U.isMatchIgnored&&(A=!1)}if(A){for(;u.endsParent&&u.parent;)u=u.parent;return u}}if(u.endsWithParent)return lt(u.parent,p,v)}function yt(u){return W.matcher.regexIndex===0?(Q+=u[0],1):(M=!0,0)}function bt(u){const p=u[0],v=u.rule,A=new kn(v),U=[v.__beforeBegin,v["on:begin"]];for(const F of U)if(F&&(F(u,A),A.isMatchIgnored))return yt(p);return v.skip?Q+=p:(v.excludeBegin&&(Q+=p),q(),!v.returnBegin&&!v.excludeBegin&&(Q=p)),he(v,u),v.returnBegin?0:p.length}function Xe(u){const p=u[0],v=E.substring(u.index),A=lt(W,u,v);if(!A)return An;const U=W;W.endScope&&W.endScope._wrap?(q(),X(p,W.endScope._wrap)):W.endScope&&W.endScope._multi?(q(),re(W.endScope,u)):U.skip?Q+=p:(U.returnEnd||U.excludeEnd||(Q+=p),q(),U.excludeEnd&&(Q=p));do W.scope&&K.closeNode(),!W.skip&&!W.subLanguage&&(fe+=W.relevance),W=W.parent;while(W!==A.parent);return A.starts&&he(A.starts,u),U.returnEnd?0:p.length}function Pe(){const u=[];for(let p=W;p!==me;p=p.parent)p.scope&&u.unshift(p.scope);u.forEach(p=>K.openNode(p))}let Ce={};function ge(u,p){const v=p&&p[0];if(Q+=u,v==null)return q(),0;if(Ce.type==="begin"&&p.type==="end"&&Ce.index===p.index&&v===""){if(Q+=E.slice(p.index,p.index+1),!a){const A=new Error(`0 width match regex (${x})`);throw A.languageName=x,A.badRule=Ce.rule,A}return 1}if(Ce=p,p.type==="begin")return bt(p);if(p.type==="illegal"&&!_){const A=new Error('Illegal lexeme "'+v+'" for mode "'+(W.scope||"<unnamed>")+'"');throw A.mode=W,A}else if(p.type==="end"){const A=Xe(p);if(A!==An)return A}if(p.type==="illegal"&&v==="")return 1;if(Ae>1e5&&Ae>p.index*3)throw new Error("potential infinite loop, way more iterations than matches");return Q+=v,v.length}const me=te(x);if(!me)throw et(l.replace("{}",x)),new Error('Unknown language: "'+x+'"');const $e=Nd(me);let B="",W=C||$e;const se={},K=new o.__emitter(o);Pe();let Q="",fe=0,ye=0,Ae=0,M=!1;try{if(me.__emitTokens)me.__emitTokens(E,K);else{for(W.matcher.considerAll();;){Ae++,M?M=!1:W.matcher.considerAll(),W.matcher.lastIndex=ye;const u=W.matcher.exec(E);if(!u)break;const p=E.substring(ye,u.index),v=ge(p,u);ye=u.index+v}ge(E.substring(ye))}return K.finalize(),B=K.toHTML(),{language:x,value:B,relevance:fe,illegal:!1,_emitter:K,_top:W}}catch(u){if(u.message&&u.message.includes("Illegal"))return{language:x,value:ts(E),illegal:!0,relevance:0,_illegalBy:{message:u.message,index:ye,context:E.slice(ye-100,ye+100),mode:u.mode,resultSoFar:B},_emitter:K};if(a)return{language:x,value:ts(E),illegal:!1,relevance:0,errorRaised:u,_emitter:K,_top:W};throw u}}function b(x){const E={value:ts(x),illegal:!1,relevance:0,_top:i,_emitter:new o.__emitter(o)};return E._emitter.addText(x),E}function m(x,E){E=E||o.languages||Object.keys(t);const _=b(x),C=E.filter(te).filter(ke).map(q=>f(q,x,!1));C.unshift(_);const P=C.sort((q,X)=>{if(q.relevance!==X.relevance)return X.relevance-q.relevance;if(q.language&&X.language){if(te(q.language).supersetOf===X.language)return 1;if(te(X.language).supersetOf===q.language)return-1}return 0}),[H,I]=P,z=H;return z.secondBest=I,z}function j(x,E,_){const C=E&&s[E]||_;x.classList.add("hljs"),x.classList.add(`language-${C}`)}function R(x){let E=null;const _=h(x);if(c(_))return;if(ue("before:highlightElement",{el:x,language:_}),x.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",x);return}if(x.children.length>0&&(o.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(x)),o.throwUnescapedHTML))throw new Cd("One of your code blocks includes unescaped HTML.",x.innerHTML);E=x;const C=E.textContent,P=_?d(C,{language:_,ignoreIllegals:!0}):m(C);x.innerHTML=P.value,x.dataset.highlighted="yes",j(x,_,P.language),x.result={language:P.language,re:P.relevance,relevance:P.relevance},P.secondBest&&(x.secondBest={language:P.secondBest.language,relevance:P.secondBest.relevance}),ue("after:highlightElement",{el:x,result:P,text:C})}function y(x){o=Cn(o,x)}const k=()=>{G(),ot("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function w(){G(),ot("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let L=!1;function G(){if(document.readyState==="loading"){L=!0;return}document.querySelectorAll(o.cssSelector).forEach(R)}function O(){L&&G()}typeof window<"u"&&window.addEventListener&&window.addEventListener("DOMContentLoaded",O,!1);function T(x,E){let _=null;try{_=E(e)}catch(C){if(et("Language definition for '{}' could not be registered.".replace("{}",x)),a)et(C);else throw C;_=i}_.name||(_.name=x),t[x]=_,_.rawDefinition=E.bind(null,e),_.aliases&&$(_.aliases,{languageName:x})}function Z(x){delete t[x];for(const E of Object.keys(s))s[E]===x&&delete s[E]}function ee(){return Object.keys(t)}function te(x){return x=(x||"").toLowerCase(),t[x]||t[s[x]]}function $(x,{languageName:E}){typeof x=="string"&&(x=[x]),x.forEach(_=>{s[_.toLowerCase()]=E})}function ke(x){const E=te(x);return E&&!E.disableAutodetect}function ie(x){x["before:highlightBlock"]&&!x["before:highlightElement"]&&(x["before:highlightElement"]=E=>{x["before:highlightBlock"](Object.assign({block:E.el},E))}),x["after:highlightBlock"]&&!x["after:highlightElement"]&&(x["after:highlightElement"]=E=>{x["after:highlightBlock"](Object.assign({block:E.el},E))})}function je(x){ie(x),n.push(x)}function le(x){const E=n.indexOf(x);E!==-1&&n.splice(E,1)}function ue(x,E){const _=x;n.forEach(function(C){C[_]&&C[_](E)})}function N(x){return ot("10.7.0","highlightBlock will be removed entirely in v12.0"),ot("10.7.0","Please use highlightElement now."),R(x)}Object.assign(e,{highlight:d,highlightAuto:m,highlightAll:G,highlightElement:R,highlightBlock:N,configure:y,initHighlighting:k,initHighlightingOnLoad:w,registerLanguage:T,unregisterLanguage:Z,listLanguages:ee,getLanguage:te,registerAliases:$,autoDetection:ke,inherit:Cn,addPlugin:je,removePlugin:le}),e.debugMode=function(){a=!1},e.safeMode=function(){a=!0},e.versionString=Sd,e.regex={concat:at,lookahead:Na,either:Os,optional:Bc,anyNumberOfTimes:Hc};for(const x in Zt)typeof Zt[x]=="object"&&ja(Zt[x]);return Object.assign(e,Zt),e},gt=Oa({});gt.newInstance=()=>Oa({});var Rd=gt;gt.HighlightJS=gt;gt.default=gt;var Md=Ic(Rd);function _a(e,t=[]){return e.map(s=>{const n=[...t,...s.properties?s.properties.className:[]];return s.children?_a(s.children,n):{text:s.value,classes:n}}).flat()}function Rn(e){return e.value||e.children||[]}function Td(e){return!!Md.getLanguage(e)}function Mn({doc:e,name:t,lowlight:s,defaultLanguage:n}){const a=[];return ns(e,l=>l.type.name===t).forEach(l=>{var i;let o=l.pos+1;const c=l.node.attrs.language||n,h=s.listLanguages(),d=c&&(h.includes(c)||Td(c)||!((i=s.registered)===null||i===void 0)&&i.call(s,c))?Rn(s.highlight(c,l.node.textContent)):Rn(s.highlightAuto(l.node.textContent));_a(d).forEach(f=>{const b=o+f.text.length;if(f.classes.length){const m=li.inline(o,b,{class:f.classes.join(" ")});a.push(m)}o=b})}),oi.create(e,a)}function Ld(e){return typeof e=="function"}function Od({name:e,lowlight:t,defaultLanguage:s}){if(!["highlight","highlightAuto","listLanguages"].every(a=>Ld(t[a])))throw Error("You should provide an instance of lowlight to use the code-block-lowlight extension");const n=new zr({key:new Ur("lowlight"),state:{init:(a,{doc:l})=>Mn({doc:l,name:e,lowlight:t,defaultLanguage:s}),apply:(a,l,i,o)=>{const c=i.selection.$head.parent.type.name,h=o.selection.$head.parent.type.name,d=ns(i.doc,b=>b.type.name===e),f=ns(o.doc,b=>b.type.name===e);return a.docChanged&&([c,h].includes(e)||f.length!==d.length||a.steps.some(b=>b.from!==void 0&&b.to!==void 0&&d.some(m=>m.pos>=b.from&&m.pos+m.node.nodeSize<=b.to)))?Mn({doc:a.doc,name:e,lowlight:t,defaultLanguage:s}):l.map(a.mapping,a.doc)}},props:{decorations(a){return n.getState(a)}}});return n}const _d=ii.extend({addOptions(){var e;return{...(e=this.parent)===null||e===void 0?void 0:e.call(this),lowlight:{},languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},addProseMirrorPlugins(){var e;return[...((e=this.parent)===null||e===void 0?void 0:e.call(this))||[],Od({name:this.name,lowlight:this.options.lowlight,defaultLanguage:this.options.defaultLanguage})]}});function Ia(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(t=>{const s=e[t],n=typeof s;(n==="object"||n==="function")&&!Object.isFrozen(s)&&Ia(s)}),e}class Tn{constructor(t){t.data===void 0&&(t.data={}),this.data=t.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function Pa(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function We(e,...t){const s=Object.create(null);for(const n in e)s[n]=e[n];return t.forEach(function(n){for(const a in n)s[a]=n[a]}),s}const Id="</span>",Ln=e=>!!e.scope,Pd=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const s=e.split(".");return[`${t}${s.shift()}`,...s.map((n,a)=>`${n}${"_".repeat(a+1)}`)].join(" ")}return`${t}${e}`};class $d{constructor(t,s){this.buffer="",this.classPrefix=s.classPrefix,t.walk(this)}addText(t){this.buffer+=Pa(t)}openNode(t){if(!Ln(t))return;const s=Pd(t.scope,{prefix:this.classPrefix});this.span(s)}closeNode(t){Ln(t)&&(this.buffer+=Id)}value(){return this.buffer}span(t){this.buffer+=`<span class="${t}">`}}const On=(e={})=>{const t={children:[]};return Object.assign(t,e),t};class Ps{constructor(){this.rootNode=On(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(t){this.top.children.push(t)}openNode(t){const s=On({scope:t});this.add(s),this.stack.push(s)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(t){return this.constructor._walk(t,this.rootNode)}static _walk(t,s){return typeof s=="string"?t.addText(s):s.children&&(t.openNode(s),s.children.forEach(n=>this._walk(t,n)),t.closeNode(s)),t}static _collapse(t){typeof t!="string"&&t.children&&(t.children.every(s=>typeof s=="string")?t.children=[t.children.join("")]:t.children.forEach(s=>{Ps._collapse(s)}))}}class Dd extends Ps{constructor(t){super(),this.options=t}addText(t){t!==""&&this.add(t)}startScope(t){this.openNode(t)}endScope(){this.closeNode()}__addSublanguage(t,s){const n=t.root;s&&(n.scope=`language:${s}`),this.add(n)}toHTML(){return new $d(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function Ut(e){return e?typeof e=="string"?e:e.source:null}function $a(e){return it("(?=",e,")")}function zd(e){return it("(?:",e,")*")}function Ud(e){return it("(?:",e,")?")}function it(...e){return e.map(s=>Ut(s)).join("")}function Hd(e){const t=e[e.length-1];return typeof t=="object"&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}function $s(...e){return"("+(Hd(e).capture?"":"?:")+e.map(n=>Ut(n)).join("|")+")"}function Da(e){return new RegExp(e.toString()+"|").exec("").length-1}function Bd(e,t){const s=e&&e.exec(t);return s&&s.index===0}const Fd=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function Ds(e,{joinWith:t}){let s=0;return e.map(n=>{s+=1;const a=s;let l=Ut(n),i="";for(;l.length>0;){const o=Fd.exec(l);if(!o){i+=l;break}i+=l.substring(0,o.index),l=l.substring(o.index+o[0].length),o[0][0]==="\\"&&o[1]?i+="\\"+String(Number(o[1])+a):(i+=o[0],o[0]==="("&&s++)}return i}).map(n=>`(${n})`).join(t)}const Wd=/\b\B/,za="[a-zA-Z]\\w*",zs="[a-zA-Z_]\\w*",Ua="\\b\\d+(\\.\\d+)?",Ha="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Ba="\\b(0b[01]+)",Kd="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",Gd=(e={})=>{const t=/^#![ ]*\//;return e.binary&&(e.begin=it(t,/.*\b/,e.binary,/\b.*/)),We({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(s,n)=>{s.index!==0&&n.ignoreMatch()}},e)},Ht={begin:"\\\\[\\s\\S]",relevance:0},qd={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[Ht]},Vd={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[Ht]},Xd={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Gr=function(e,t,s={}){const n=We({scope:"comment",begin:e,end:t,contains:[]},s);n.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const a=$s("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return n.contains.push({begin:it(/[ ]+/,"(",a,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),n},Jd=Gr("//","$"),Qd=Gr("/\\*","\\*/"),Zd=Gr("#","$"),Yd={scope:"number",begin:Ua,relevance:0},eu={scope:"number",begin:Ha,relevance:0},tu={scope:"number",begin:Ba,relevance:0},ru={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[Ht,{begin:/\[/,end:/\]/,relevance:0,contains:[Ht]}]},su={scope:"title",begin:za,relevance:0},nu={scope:"title",begin:zs,relevance:0},au={begin:"\\.\\s*"+zs,relevance:0},iu=function(e){return Object.assign(e,{"on:begin":(t,s)=>{s.data._beginMatch=t[1]},"on:end":(t,s)=>{s.data._beginMatch!==t[1]&&s.ignoreMatch()}})};var Yt=Object.freeze({__proto__:null,APOS_STRING_MODE:qd,BACKSLASH_ESCAPE:Ht,BINARY_NUMBER_MODE:tu,BINARY_NUMBER_RE:Ba,COMMENT:Gr,C_BLOCK_COMMENT_MODE:Qd,C_LINE_COMMENT_MODE:Jd,C_NUMBER_MODE:eu,C_NUMBER_RE:Ha,END_SAME_AS_BEGIN:iu,HASH_COMMENT_MODE:Zd,IDENT_RE:za,MATCH_NOTHING_RE:Wd,METHOD_GUARD:au,NUMBER_MODE:Yd,NUMBER_RE:Ua,PHRASAL_WORDS_MODE:Xd,QUOTE_STRING_MODE:Vd,REGEXP_MODE:ru,RE_STARTERS_RE:Kd,SHEBANG:Gd,TITLE_MODE:su,UNDERSCORE_IDENT_RE:zs,UNDERSCORE_TITLE_MODE:nu});function lu(e,t){e.input[e.index-1]==="."&&t.ignoreMatch()}function ou(e,t){e.className!==void 0&&(e.scope=e.className,delete e.className)}function cu(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=lu,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function du(e,t){Array.isArray(e.illegal)&&(e.illegal=$s(...e.illegal))}function uu(e,t){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function hu(e,t){e.relevance===void 0&&(e.relevance=1)}const gu=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const s=Object.assign({},e);Object.keys(e).forEach(n=>{delete e[n]}),e.keywords=s.keywords,e.begin=it(s.beforeMatch,$a(s.begin)),e.starts={relevance:0,contains:[Object.assign(s,{endsParent:!0})]},e.relevance=0,delete s.beforeMatch},fu=["of","and","for","in","not","or","if","then","parent","list","value"],xu="keyword";function Fa(e,t,s=xu){const n=Object.create(null);return typeof e=="string"?a(s,e.split(" ")):Array.isArray(e)?a(s,e):Object.keys(e).forEach(function(l){Object.assign(n,Fa(e[l],t,l))}),n;function a(l,i){t&&(i=i.map(o=>o.toLowerCase())),i.forEach(function(o){const c=o.split("|");n[c[0]]=[l,pu(c[0],c[1])]})}}function pu(e,t){return t?Number(t):mu(e)?0:1}function mu(e){return fu.includes(e.toLowerCase())}const _n={},tt=e=>{console.error(e)},In=(e,...t)=>{console.log(`WARN: ${e}`,...t)},ct=(e,t)=>{_n[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),_n[`${e}/${t}`]=!0)},Dr=new Error;function Wa(e,t,{key:s}){let n=0;const a=e[s],l={},i={};for(let o=1;o<=t.length;o++)i[o+n]=a[o],l[o+n]=!0,n+=Da(t[o-1]);e[s]=i,e[s]._emit=l,e[s]._multi=!0}function yu(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw tt("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),Dr;if(typeof e.beginScope!="object"||e.beginScope===null)throw tt("beginScope must be object"),Dr;Wa(e,e.begin,{key:"beginScope"}),e.begin=Ds(e.begin,{joinWith:""})}}function bu(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw tt("skip, excludeEnd, returnEnd not compatible with endScope: {}"),Dr;if(typeof e.endScope!="object"||e.endScope===null)throw tt("endScope must be object"),Dr;Wa(e,e.end,{key:"endScope"}),e.end=Ds(e.end,{joinWith:""})}}function vu(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function ju(e){vu(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),yu(e),bu(e)}function ku(e){function t(i,o){return new RegExp(Ut(i),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(o?"g":""))}class s{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(o,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,o]),this.matchAt+=Da(o)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const o=this.regexes.map(c=>c[1]);this.matcherRe=t(Ds(o,{joinWith:"|"}),!0),this.lastIndex=0}exec(o){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(o);if(!c)return null;const h=c.findIndex((f,b)=>b>0&&f!==void 0),d=this.matchIndexes[h];return c.splice(0,h),Object.assign(c,d)}}class n{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(o){if(this.multiRegexes[o])return this.multiRegexes[o];const c=new s;return this.rules.slice(o).forEach(([h,d])=>c.addRule(h,d)),c.compile(),this.multiRegexes[o]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(o,c){this.rules.push([o,c]),c.type==="begin"&&this.count++}exec(o){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let h=c.exec(o);if(this.resumingScanAtSamePosition()&&!(h&&h.index===this.lastIndex)){const d=this.getMatcher(0);d.lastIndex=this.lastIndex+1,h=d.exec(o)}return h&&(this.regexIndex+=h.position+1,this.regexIndex===this.count&&this.considerAll()),h}}function a(i){const o=new n;return i.contains.forEach(c=>o.addRule(c.begin,{rule:c,type:"begin"})),i.terminatorEnd&&o.addRule(i.terminatorEnd,{type:"end"}),i.illegal&&o.addRule(i.illegal,{type:"illegal"}),o}function l(i,o){const c=i;if(i.isCompiled)return c;[ou,uu,ju,gu].forEach(d=>d(i,o)),e.compilerExtensions.forEach(d=>d(i,o)),i.__beforeBegin=null,[cu,du,hu].forEach(d=>d(i,o)),i.isCompiled=!0;let h=null;return typeof i.keywords=="object"&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),h=i.keywords.$pattern,delete i.keywords.$pattern),h=h||/\w+/,i.keywords&&(i.keywords=Fa(i.keywords,e.case_insensitive)),c.keywordPatternRe=t(h,!0),o&&(i.begin||(i.begin=/\B|\b/),c.beginRe=t(c.begin),!i.end&&!i.endsWithParent&&(i.end=/\B|\b/),i.end&&(c.endRe=t(c.end)),c.terminatorEnd=Ut(c.end)||"",i.endsWithParent&&o.terminatorEnd&&(c.terminatorEnd+=(i.end?"|":"")+o.terminatorEnd)),i.illegal&&(c.illegalRe=t(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(d){return wu(d==="self"?i:d)})),i.contains.forEach(function(d){l(d,c)}),i.starts&&l(i.starts,o),c.matcher=a(c),c}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=We(e.classNameAliases||{}),l(e)}function Ka(e){return e?e.endsWithParent||Ka(e.starts):!1}function wu(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(t){return We(e,{variants:null},t)})),e.cachedVariants?e.cachedVariants:Ka(e)?We(e,{starts:e.starts?We(e.starts):null}):Object.isFrozen(e)?We(e):e}var Nu="11.11.1";class Eu extends Error{constructor(t,s){super(t),this.name="HTMLInjectionError",this.html=s}}const rs=Pa,Pn=We,$n=Symbol("nomatch"),Su=7,Ga=function(e){const t=Object.create(null),s=Object.create(null),n=[];let a=!0;const l="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let o={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:Dd};function c(N){return o.noHighlightRe.test(N)}function h(N){let x=N.className+" ";x+=N.parentNode?N.parentNode.className:"";const E=o.languageDetectRe.exec(x);if(E){const _=ee(E[1]);return _||(In(l.replace("{}",E[1])),In("Falling back to no-highlight mode for this block.",N)),_?E[1]:"no-highlight"}return x.split(/\s+/).find(_=>c(_)||ee(_))}function d(N,x,E){let _="",C="";typeof x=="object"?(_=N,E=x.ignoreIllegals,C=x.language):(ct("10.7.0","highlight(lang, code, ...args) has been deprecated."),ct("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),C=N,_=x),E===void 0&&(E=!0);const P={code:_,language:C};le("before:highlight",P);const H=P.result?P.result:f(P.language,P.code,E);return H.code=P.code,le("after:highlight",H),H}function f(N,x,E,_){const C=Object.create(null);function P(M,u){return M.keywords[u]}function H(){if(!B.keywords){se.addText(K);return}let M=0;B.keywordPatternRe.lastIndex=0;let u=B.keywordPatternRe.exec(K),p="";for(;u;){p+=K.substring(M,u.index);const v=ge.case_insensitive?u[0].toLowerCase():u[0],A=P(B,v);if(A){const[U,F]=A;if(se.addText(p),p="",C[v]=(C[v]||0)+1,C[v]<=Su&&(Q+=F),U.startsWith("_"))p+=u[0];else{const xe=ge.classNameAliases[U]||U;q(u[0],xe)}}else p+=u[0];M=B.keywordPatternRe.lastIndex,u=B.keywordPatternRe.exec(K)}p+=K.substring(M),se.addText(p)}function I(){if(K==="")return;let M=null;if(typeof B.subLanguage=="string"){if(!t[B.subLanguage]){se.addText(K);return}M=f(B.subLanguage,K,!0,W[B.subLanguage]),W[B.subLanguage]=M._top}else M=m(K,B.subLanguage.length?B.subLanguage:null);B.relevance>0&&(Q+=M.relevance),se.__addSublanguage(M._emitter,M.language)}function z(){B.subLanguage!=null?I():H(),K=""}function q(M,u){M!==""&&(se.startScope(u),se.addText(M),se.endScope())}function X(M,u){let p=1;const v=u.length-1;for(;p<=v;){if(!M._emit[p]){p++;continue}const A=ge.classNameAliases[M[p]]||M[p],U=u[p];A?q(U,A):(K=U,H(),K=""),p++}}function re(M,u){return M.scope&&typeof M.scope=="string"&&se.openNode(ge.classNameAliases[M.scope]||M.scope),M.beginScope&&(M.beginScope._wrap?(q(K,ge.classNameAliases[M.beginScope._wrap]||M.beginScope._wrap),K=""):M.beginScope._multi&&(X(M.beginScope,u),K="")),B=Object.create(M,{parent:{value:B}}),B}function he(M,u,p){let v=Bd(M.endRe,p);if(v){if(M["on:end"]){const A=new Tn(M);M["on:end"](u,A),A.isMatchIgnored&&(v=!1)}if(v){for(;M.endsParent&&M.parent;)M=M.parent;return M}}if(M.endsWithParent)return he(M.parent,u,p)}function lt(M){return B.matcher.regexIndex===0?(K+=M[0],1):(Ae=!0,0)}function yt(M){const u=M[0],p=M.rule,v=new Tn(p),A=[p.__beforeBegin,p["on:begin"]];for(const U of A)if(U&&(U(M,v),v.isMatchIgnored))return lt(u);return p.skip?K+=u:(p.excludeBegin&&(K+=u),z(),!p.returnBegin&&!p.excludeBegin&&(K=u)),re(p,M),p.returnBegin?0:u.length}function bt(M){const u=M[0],p=x.substring(M.index),v=he(B,M,p);if(!v)return $n;const A=B;B.endScope&&B.endScope._wrap?(z(),q(u,B.endScope._wrap)):B.endScope&&B.endScope._multi?(z(),X(B.endScope,M)):A.skip?K+=u:(A.returnEnd||A.excludeEnd||(K+=u),z(),A.excludeEnd&&(K=u));do B.scope&&se.closeNode(),!B.skip&&!B.subLanguage&&(Q+=B.relevance),B=B.parent;while(B!==v.parent);return v.starts&&re(v.starts,M),A.returnEnd?0:u.length}function Xe(){const M=[];for(let u=B;u!==ge;u=u.parent)u.scope&&M.unshift(u.scope);M.forEach(u=>se.openNode(u))}let Pe={};function Ce(M,u){const p=u&&u[0];if(K+=M,p==null)return z(),0;if(Pe.type==="begin"&&u.type==="end"&&Pe.index===u.index&&p===""){if(K+=x.slice(u.index,u.index+1),!a){const v=new Error(`0 width match regex (${N})`);throw v.languageName=N,v.badRule=Pe.rule,v}return 1}if(Pe=u,u.type==="begin")return yt(u);if(u.type==="illegal"&&!E){const v=new Error('Illegal lexeme "'+p+'" for mode "'+(B.scope||"<unnamed>")+'"');throw v.mode=B,v}else if(u.type==="end"){const v=bt(u);if(v!==$n)return v}if(u.type==="illegal"&&p==="")return K+=`
`,1;if(ye>1e5&&ye>u.index*3)throw new Error("potential infinite loop, way more iterations than matches");return K+=p,p.length}const ge=ee(N);if(!ge)throw tt(l.replace("{}",N)),new Error('Unknown language: "'+N+'"');const me=ku(ge);let $e="",B=_||me;const W={},se=new o.__emitter(o);Xe();let K="",Q=0,fe=0,ye=0,Ae=!1;try{if(ge.__emitTokens)ge.__emitTokens(x,se);else{for(B.matcher.considerAll();;){ye++,Ae?Ae=!1:B.matcher.considerAll(),B.matcher.lastIndex=fe;const M=B.matcher.exec(x);if(!M)break;const u=x.substring(fe,M.index),p=Ce(u,M);fe=M.index+p}Ce(x.substring(fe))}return se.finalize(),$e=se.toHTML(),{language:N,value:$e,relevance:Q,illegal:!1,_emitter:se,_top:B}}catch(M){if(M.message&&M.message.includes("Illegal"))return{language:N,value:rs(x),illegal:!0,relevance:0,_illegalBy:{message:M.message,index:fe,context:x.slice(fe-100,fe+100),mode:M.mode,resultSoFar:$e},_emitter:se};if(a)return{language:N,value:rs(x),illegal:!1,relevance:0,errorRaised:M,_emitter:se,_top:B};throw M}}function b(N){const x={value:rs(N),illegal:!1,relevance:0,_top:i,_emitter:new o.__emitter(o)};return x._emitter.addText(N),x}function m(N,x){x=x||o.languages||Object.keys(t);const E=b(N),_=x.filter(ee).filter($).map(z=>f(z,N,!1));_.unshift(E);const C=_.sort((z,q)=>{if(z.relevance!==q.relevance)return q.relevance-z.relevance;if(z.language&&q.language){if(ee(z.language).supersetOf===q.language)return 1;if(ee(q.language).supersetOf===z.language)return-1}return 0}),[P,H]=C,I=P;return I.secondBest=H,I}function j(N,x,E){const _=x&&s[x]||E;N.classList.add("hljs"),N.classList.add(`language-${_}`)}function R(N){let x=null;const E=h(N);if(c(E))return;if(le("before:highlightElement",{el:N,language:E}),N.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",N);return}if(N.children.length>0&&(o.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(N)),o.throwUnescapedHTML))throw new Eu("One of your code blocks includes unescaped HTML.",N.innerHTML);x=N;const _=x.textContent,C=E?d(_,{language:E,ignoreIllegals:!0}):m(_);N.innerHTML=C.value,N.dataset.highlighted="yes",j(N,E,C.language),N.result={language:C.language,re:C.relevance,relevance:C.relevance},C.secondBest&&(N.secondBest={language:C.secondBest.language,relevance:C.secondBest.relevance}),le("after:highlightElement",{el:N,result:C,text:_})}function y(N){o=Pn(o,N)}const k=()=>{G(),ct("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function w(){G(),ct("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let L=!1;function G(){function N(){G()}if(document.readyState==="loading"){L||window.addEventListener("DOMContentLoaded",N,!1),L=!0;return}document.querySelectorAll(o.cssSelector).forEach(R)}function O(N,x){let E=null;try{E=x(e)}catch(_){if(tt("Language definition for '{}' could not be registered.".replace("{}",N)),a)tt(_);else throw _;E=i}E.name||(E.name=N),t[N]=E,E.rawDefinition=x.bind(null,e),E.aliases&&te(E.aliases,{languageName:N})}function T(N){delete t[N];for(const x of Object.keys(s))s[x]===N&&delete s[x]}function Z(){return Object.keys(t)}function ee(N){return N=(N||"").toLowerCase(),t[N]||t[s[N]]}function te(N,{languageName:x}){typeof N=="string"&&(N=[N]),N.forEach(E=>{s[E.toLowerCase()]=x})}function $(N){const x=ee(N);return x&&!x.disableAutodetect}function ke(N){N["before:highlightBlock"]&&!N["before:highlightElement"]&&(N["before:highlightElement"]=x=>{N["before:highlightBlock"](Object.assign({block:x.el},x))}),N["after:highlightBlock"]&&!N["after:highlightElement"]&&(N["after:highlightElement"]=x=>{N["after:highlightBlock"](Object.assign({block:x.el},x))})}function ie(N){ke(N),n.push(N)}function je(N){const x=n.indexOf(N);x!==-1&&n.splice(x,1)}function le(N,x){const E=N;n.forEach(function(_){_[E]&&_[E](x)})}function ue(N){return ct("10.7.0","highlightBlock will be removed entirely in v12.0"),ct("10.7.0","Please use highlightElement now."),R(N)}Object.assign(e,{highlight:d,highlightAuto:m,highlightAll:G,highlightElement:R,highlightBlock:ue,configure:y,initHighlighting:k,initHighlightingOnLoad:w,registerLanguage:O,unregisterLanguage:T,listLanguages:Z,getLanguage:ee,registerAliases:te,autoDetection:$,inherit:Pn,addPlugin:ie,removePlugin:je}),e.debugMode=function(){a=!1},e.safeMode=function(){a=!0},e.versionString=Nu,e.regex={concat:it,lookahead:$a,either:$s,optional:Ud,anyNumberOfTimes:zd};for(const N in Yt)typeof Yt[N]=="object"&&Ia(Yt[N]);return Object.assign(e,Yt),e},ft=Ga({});ft.newInstance=()=>Ga({});var Cu=ft;ft.HighlightJS=ft;ft.default=ft;const Au=Xa(Cu),Dn={},Ru="hljs-";function Mu(e){const t=Au.newInstance();return e&&l(e),{highlight:s,highlightAuto:n,listLanguages:a,register:l,registerAlias:i,registered:o};function s(c,h,d){const f=d||Dn,b=typeof f.prefix=="string"?f.prefix:Ru;if(!t.getLanguage(c))throw new Error("Unknown language: `"+c+"` is not registered");t.configure({__emitter:Tu,classPrefix:b});const m=t.highlight(h,{ignoreIllegals:!0,language:c});if(m.errorRaised)throw new Error("Could not highlight with `Highlight.js`",{cause:m.errorRaised});const j=m._emitter.root,R=j.data;return R.language=m.language,R.relevance=m.relevance,j}function n(c,h){const f=(h||Dn).subset||a();let b=-1,m=0,j;for(;++b<f.length;){const R=f[b];if(!t.getLanguage(R))continue;const y=s(R,c,h);y.data&&y.data.relevance!==void 0&&y.data.relevance>m&&(m=y.data.relevance,j=y)}return j||{type:"root",children:[],data:{language:void 0,relevance:m}}}function a(){return t.listLanguages()}function l(c,h){if(typeof c=="string")t.registerLanguage(c,h);else{let d;for(d in c)Object.hasOwn(c,d)&&t.registerLanguage(d,c[d])}}function i(c,h){if(typeof c=="string")t.registerAliases(typeof h=="string"?h:[...h],{languageName:c});else{let d;for(d in c)if(Object.hasOwn(c,d)){const f=c[d];t.registerAliases(typeof f=="string"?f:[...f],{languageName:d})}}}function o(c){return!!t.getLanguage(c)}}class Tu{constructor(t){this.options=t,this.root={type:"root",children:[],data:{language:void 0,relevance:0}},this.stack=[this.root]}addText(t){if(t==="")return;const s=this.stack[this.stack.length-1],n=s.children[s.children.length-1];n&&n.type==="text"?n.value+=t:s.children.push({type:"text",value:t})}startScope(t){this.openNode(String(t))}endScope(){this.closeNode()}__addSublanguage(t,s){const n=this.stack[this.stack.length-1],a=t.root.children;s?n.children.push({type:"element",tagName:"span",properties:{className:[s]},children:a}):n.children.push(...a)}openNode(t){const s=this,n=t.split(".").map(function(i,o){return o?i+"_".repeat(o):s.options.classPrefix+i}),a=this.stack[this.stack.length-1],l={type:"element",tagName:"span",properties:{className:n},children:[]};a.children.push(l),this.stack.push(l)}closeNode(){this.stack.pop()}finalize(){}toHTML(){return""}}const Lu=Mu();function Ou({content:e,onChange:t,placeholder:s="开始写作...",coverImage:n,onCoverImageChange:a}){const[l,i]=g.useState(!1),[o,c]=g.useState(!1),[h,d]=g.useState(!1),[f,b]=g.useState(!1),[m,j]=g.useState(!1),[R,y]=g.useState([]),[k,w]=g.useState(!1),[L,G]=g.useState(!1),[O,T]=g.useState(!1),[Z,ee]=g.useState(!1),te=async C=>{const P=new FormData;P.append("file",C);try{const H=await Ee(ae.uploadImage,{method:"POST",body:P,headers:{}});return(await de(H)).url}catch(H){throw console.error("图片上传失败:",H),new Error("图片上传失败")}},$=ci({extensions:[di,uc.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),_c.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-600 dark:text-blue-400 underline"}}),_d.configure({lowlight:Lu,HTMLAttributes:{class:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"}})],content:e,onUpdate:({editor:C})=>{t(C.getHTML())},editorProps:{attributes:{class:"prose prose-gray dark:prose-dark max-w-none focus:outline-none min-h-[400px] p-4","data-placeholder":s},handlePaste:(C,P,H)=>{var q;const z=Array.from(((q=P.clipboardData)==null?void 0:q.items)||[]).filter(X=>X.type.indexOf("image")===0);return z.length>0?(P.preventDefault(),z.forEach(async X=>{const re=X.getAsFile();if(re)try{d(!0);const he=await te(re);$==null||$.chain().focus().setImage({src:he}).run()}catch(he){console.error("图片上传失败:",he),alert("图片上传失败，请重试")}finally{d(!1)}}),!0):!1},handleDrop:(C,P,H,I)=>{var X;const q=Array.from(((X=P.dataTransfer)==null?void 0:X.files)||[]).filter(re=>re.type.indexOf("image")===0);return q.length>0?(P.preventDefault(),q.forEach(async re=>{try{d(!0);const he=await te(re);$==null||$.chain().focus().setImage({src:he}).run()}catch(he){console.error("图片上传失败:",he),alert("图片上传失败，请重试")}finally{d(!1)}}),!0):!1}}}),ke=async()=>{if(!$)return;const C=$.state.selection.empty?"现代科技风格的文章配图":$.state.doc.textBetween($.state.selection.from,$.state.selection.to),P=window.prompt(`AI智能配图 - 请描述您想要的配图风格和内容：

提示：可以描述场景、颜色、风格等
例如：蓝色科技风格、自然风光、商务办公等`,C);if(P===null)return;const H=P.trim()||C;i(!0);try{console.log("开始生成AI配图，提示词:",H),console.log("API端点:",ae.aiImage);const I=new AbortController,z=setTimeout(()=>I.abort(),6e4),q=await Ee(ae.aiImage,{method:"POST",body:JSON.stringify({prompt:H,style:"tech"}),signal:I.signal});clearTimeout(z),console.log("API响应状态:",q.status),console.log("API响应头:",Object.fromEntries(q.headers.entries()));const X=await de(q);if(console.log("API响应数据:",X),X.success&&X.images&&X.images.length>0){const re=X.images[0].url;console.log("插入AI生成图片URL:",re),$.chain().focus().setImage({src:re}).run(),alert("AI配图生成成功！"),b(!1)}else throw console.error("API响应数据格式错误:",X),new Error("AI配图生成失败：没有返回图片")}catch(I){console.error("AI配图生成错误:",I),console.error("错误详情:",{name:I==null?void 0:I.name,message:I==null?void 0:I.message,stack:I==null?void 0:I.stack}),(I==null?void 0:I.name)==="AbortError"?alert("生成超时（60秒），AI图片生成通常需要15-45秒，请稍后重试"):I instanceof cr?alert(`AI配图生成失败: ${I.message}`):alert(`AI配图生成失败: ${(I==null?void 0:I.message)||"未知错误"}`)}finally{i(!1)}},ie=async()=>{if(!$)return;const C=window.confirm(`选择图片类型：

确定 = 一般图片（适合所有场景）
取消 = R18图片（仅限成人内容）

注意：所有图片均来自Pixiv艺术创作平台`);if(C===null)return;const P=C?"safe":"r18";c(!0);try{console.log("开始获取Mossia API图片，类型:",P);const H=await Ee(ae.aiImage,{method:"POST",body:JSON.stringify({type:"mossia",source:P,num:9})}),I=await de(H);if(console.log("Mossia API响应数据:",I),I.success&&I.images&&I.images.length>0){const z=I.images.filter(re=>re.url&&re.url.trim()!=="");if(z.length===0)throw new Error("没有可用的图片");y(z),j(!0),b(!1);const q=I.cached?"（来自缓存，快速加载）":"（实时获取）",X=P==="r18"?"R18":"一般";console.log(`获取到 ${z.length} 张${X}图片${q}`)}else throw new Error("Mossia API没有返回图片")}catch(H){console.error("Mossia图片获取错误:",H),alert(`配图获取失败: ${(H==null?void 0:H.message)||"未知错误"}`)}finally{c(!1)}},je=C=>{console.log("选择图片URL:",C),Z?(a==null||a(C),j(!1),y([]),w(!1),ee(!1),alert("封面图片设置成功！")):$&&($.chain().focus().setImage({src:C}).run(),j(!1),alert("配图添加成功！"))},le=()=>{j(!1),y([]),Z&&(ee(!1),w(!1))},ue=()=>{if(!$)return;const C=window.prompt("请输入链接地址:");C&&$.chain().focus().setLink({href:C}).run()},N=()=>{if(!$)return;const C=window.prompt("请输入图片地址:");C&&$.chain().focus().setImage({src:C}).run()},x=async()=>{const C=window.confirm(`选择封面图片类型：

确定 = 一般图片（适合所有场景）
取消 = R18图片（仅限成人内容）

注意：所有图片均来自Pixiv艺术创作平台`);if(C===null)return;const P=C?"safe":"r18";try{G(!0),console.log("开始获取封面图片，类型:",P);const H=await Ee(ae.aiImage,{method:"POST",body:JSON.stringify({type:"mossia",source:P,num:9})}),I=await de(H);if(console.log("封面图片API响应数据:",I),I.success&&I.images&&I.images.length>0){const z=I.images.filter(re=>re.url&&re.url.trim()!=="");if(console.log("有效图片数量:",z.length),z.length===0)throw new Error("没有可用的图片");y(z),ee(!0),j(!0);const q=I.cached?"（来自缓存）":"（实时获取）",X=P==="r18"?"R18":"一般";console.log(`获取到${z.length}张${X}封面图片${q}`)}else throw console.error("API响应格式错误:",I),new Error("API没有返回可用的图片")}catch(H){console.error("获取精美封面失败:",H),alert(`获取精美封面失败: ${(H==null?void 0:H.message)||"未知错误"}`)}finally{G(!1)}},E=async()=>{const C=window.prompt(`生成文章封面图 - 请描述封面图的风格和内容：

提示：可以描述主题、颜色、风格等
例如：现代科技、蓝色调、商务风格`,"现代简约风格的文章封面");if(C!==null)try{T(!0);const P=new AbortController,H=setTimeout(()=>P.abort(),6e4),I=await Ee(ae.aiImage,{method:"POST",body:JSON.stringify({prompt:C.trim()||"现代简约风格的文章封面",style:"tech"}),signal:P.signal});clearTimeout(H);const z=await de(I);z.url&&(a==null||a(z.url),w(!1),alert("AI封面生成成功！"))}catch(P){console.error("AI生成封面失败:",P),P instanceof cr?alert(`AI生成封面失败: ${P.message}`):alert("AI生成封面失败，请重试")}finally{T(!1)}},_=()=>{a==null||a("")};return $?r.jsxs("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[r.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"文章封面图"}),n&&r.jsx("button",{onClick:_,className:"text-red-600 hover:text-red-700 text-sm",title:"删除封面图",children:"删除封面"})]}),n?r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:n,alt:"文章封面",className:"w-full h-32 object-cover rounded-lg",onError:C=>{const P=C.target;P.style.display="none"}}),r.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center",children:r.jsx("div",{className:"opacity-0 hover:opacity-100 flex space-x-2",children:r.jsx("button",{onClick:()=>w(!k),className:"px-3 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"更换封面"})})})]}):r.jsxs("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center",children:[r.jsx(jt,{size:32,className:"mx-auto text-gray-400 mb-2"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4",children:"添加文章封面图，让您的文章更吸引人"}),r.jsx("button",{onClick:()=>w(!k),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"添加封面图"})]}),k&&r.jsx("div",{className:"mt-4 p-4 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600",children:r.jsxs("div",{className:"flex flex-wrap gap-3 items-center",children:[r.jsxs("button",{onClick:x,disabled:L,className:"flex items-center space-x-2 px-3 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 disabled:opacity-50 transition-colors",children:[L?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(jt,{size:16}),r.jsx("span",{children:L?"获取中":"精美图片"})]}),r.jsxs("button",{onClick:E,disabled:O||L,className:"flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 transition-colors",children:[O?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(hn,{size:16}),r.jsx("span",{children:O?"AI生成中":"AI生成"})]}),r.jsx("button",{onClick:()=>w(!1),className:"px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors",children:"取消"})]})})]}),r.jsxs("div",{className:"flex flex-wrap items-center gap-1 p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:[r.jsx("button",{onClick:()=>$.chain().focus().toggleBold().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("bold")?"bg-gray-200 dark:bg-gray-700":""}`,title:"粗体",children:r.jsx(go,{size:16})}),r.jsx("button",{onClick:()=>$.chain().focus().toggleItalic().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("italic")?"bg-gray-200 dark:bg-gray-700":""}`,title:"斜体",children:r.jsx(Eo,{size:16})}),r.jsx("button",{onClick:()=>$.chain().focus().toggleStrike().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("strike")?"bg-gray-200 dark:bg-gray-700":""}`,title:"删除线",children:r.jsx(Bo,{size:16})}),r.jsx("button",{onClick:()=>$.chain().focus().toggleCode().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("code")?"bg-gray-200 dark:bg-gray-700":""}`,title:"行内代码",children:r.jsx(po,{size:16})}),r.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),r.jsx("button",{onClick:()=>$.chain().focus().toggleHeading({level:1}).run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("heading",{level:1})?"bg-gray-200 dark:bg-gray-700":""}`,title:"标题1",children:r.jsx(jo,{size:16})}),r.jsx("button",{onClick:()=>$.chain().focus().toggleHeading({level:2}).run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("heading",{level:2})?"bg-gray-200 dark:bg-gray-700":""}`,title:"标题2",children:r.jsx(ko,{size:16})}),r.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),r.jsx("button",{onClick:()=>$.chain().focus().toggleBulletList().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("bulletList")?"bg-gray-200 dark:bg-gray-700":""}`,title:"无序列表",children:r.jsx(Ao,{size:16})}),r.jsx("button",{onClick:()=>$.chain().focus().toggleOrderedList().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("orderedList")?"bg-gray-200 dark:bg-gray-700":""}`,title:"有序列表",children:r.jsx(Co,{size:16})}),r.jsx("button",{onClick:()=>$.chain().focus().toggleBlockquote().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${$.isActive("blockquote")?"bg-gray-200 dark:bg-gray-700":""}`,title:"引用",children:r.jsx(Po,{size:16})}),r.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),r.jsx("button",{onClick:ue,className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors",title:"插入链接",children:r.jsx(So,{size:16})}),r.jsx("button",{onClick:N,className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors",title:"插入图片",children:r.jsx(jt,{size:16})}),f?r.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-lg",children:[r.jsxs("button",{onClick:ke,disabled:l||o,className:"flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 transition-colors",title:l?"AI生成中，通常需要15-45秒...":"AI智能生成配图",children:[l?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(hn,{size:16}),r.jsx("span",{children:l?"AI生成中":"AI生成"})]}),r.jsxs("button",{onClick:ie,disabled:l||o,className:"flex items-center space-x-1 px-3 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 disabled:opacity-50 transition-colors",title:o?"获取精美图片中...":"精美图片 - 来自Pixiv等优质图源",children:[o?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(jt,{size:16}),r.jsx("span",{children:o?"获取中":"精美图片"})]}),r.jsx("button",{onClick:()=>b(!1),className:"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors",title:"关闭配图选项",children:"×"})]}):r.jsxs("button",{onClick:()=>b(!0),disabled:l||o,className:"flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 transition-all",title:"文章配图 - 选择AI生成或精美图片",children:[r.jsx(jt,{size:16}),r.jsx("span",{children:"文章配图"})]}),h&&r.jsxs("div",{className:"flex items-center space-x-2 px-3 py-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded",children:[r.jsx("div",{className:"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"}),r.jsx("span",{className:"text-sm",children:"图片上传中..."})]}),r.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),r.jsx("button",{onClick:()=>$.chain().focus().undo().run(),disabled:!$.can().undo(),className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors",title:"撤销",children:r.jsx(Go,{size:16})}),r.jsx("button",{onClick:()=>$.chain().focus().redo().run(),disabled:!$.can().redo(),className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors",title:"重做",children:r.jsx($o,{size:16})})]}),r.jsx(ui,{editor:$}),m&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto",children:[r.jsxs("div",{className:"flex justify-between items-center mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:Z?`选择封面图片 (${R.length} 张)`:`选择文章配图 (${R.length} 张)`}),r.jsx("button",{onClick:le,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",title:"关闭画廊",children:"✕"})]}),r.jsx("div",{className:"grid grid-cols-3 gap-4",children:R.map((C,P)=>r.jsxs("div",{className:"relative group cursor-pointer border-2 border-transparent hover:border-blue-500 rounded-lg overflow-hidden",onClick:()=>je(C.url),children:[r.jsx("img",{src:C.url,alt:C.title||"配图选项",className:"w-full h-32 object-cover",loading:"lazy",onError:H=>{console.error("图片加载失败:",C.url),H.currentTarget.style.display="none"},onLoad:()=>{console.log("图片加载成功:",C.url)}}),r.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center",children:r.jsx("span",{className:"text-white opacity-0 group-hover:opacity-100 font-medium",children:"选择"})}),C.title&&r.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-2",children:[r.jsx("p",{className:"truncate",title:C.title,children:C.title}),C.author&&r.jsxs("p",{className:"truncate text-gray-300",title:`作者: ${C.author}`,children:["作者: ",C.author]})]})]},`${C.url}-${P}`))}),r.jsxs("div",{className:"mt-4 flex justify-between",children:[r.jsx("button",{onClick:()=>{j(!1),Z?x():ie()},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",children:"换一批图片"}),r.jsx("button",{onClick:le,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors",children:"取消选择"})]})]})})]}):r.jsx("div",{children:"编辑器加载中..."})}function zn(){const e=Ft(),{slug:t}=Wt(),{isAdmin:s,isLoading:n}=Gt(),a=!!t,[l,i]=g.useState(""),[o,c]=g.useState({title:"",excerpt:"",tags:[],category:"",featured:!1,imageUrl:""}),[h,d]=g.useState(!1),[f,b]=g.useState(!1),[m,j]=g.useState(!1),[R,y]=g.useState(!1);g.useEffect(()=>{a&&t&&(async()=>{j(!0);try{const T=await pe.getPost(t);i(T.content||""),c({title:T.title,excerpt:T.excerpt,tags:T.tags||[],category:T.category||"",featured:T.featured||!1,imageUrl:T.imageUrl||""})}catch(T){console.error("加载文章失败:",T),alert("加载文章失败，请检查文章是否存在"),e("/")}finally{j(!1)}})()},[a,t,e]);const k=async()=>{if(!o.title.trim()){alert("请输入文章标题");return}b(!0);try{const O={...o,content:l};if(a)await pe.updatePost(t,O),e(`/post/${t}`);else{const T=await pe.createPost(O);e(`/post/${T.slug}`)}}catch(O){console.error("保存失败:",O),alert("保存失败，请重试")}finally{b(!1)}},w=O=>{O&&!o.tags.includes(O)&&c(T=>({...T,tags:[...T.tags,O]}))},L=O=>{c(T=>({...T,tags:T.tags.filter(Z=>Z!==O)}))},G=O=>{c(T=>({...T,imageUrl:O}))};return n?r.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"检查权限中..."})]}):s?m?r.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载文章中..."})]}):r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsxs("div",{className:"flex items-center justify-between mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:a?"编辑文章":"写新文章"}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("button",{onClick:()=>d(!h),title:h?"切换到编辑模式":"切换到预览模式","aria-label":h?"切换到编辑模式":"切换到预览模式",className:"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[h?r.jsx(aa,{size:16}):r.jsx(ia,{size:16}),r.jsx("span",{children:h?"编辑":"预览"})]}),r.jsxs("button",{onClick:k,disabled:f,title:f?"正在保存文章":"保存文章","aria-label":f?"正在保存文章":"保存文章",className:"flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:[r.jsx(Do,{size:16}),r.jsx("span",{children:f?"保存中...":"保存"})]})]})]}),r.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[r.jsx("div",{className:"lg:col-span-2",children:r.jsx("div",{className:"card p-6",children:h?r.jsxs("div",{className:"prose prose-gray dark:prose-dark max-w-none",children:[r.jsx("h1",{children:o.title}),r.jsx("div",{dangerouslySetInnerHTML:{__html:l}})]}):r.jsx(Ou,{content:l,onChange:i,placeholder:"开始写作...",coverImage:o.imageUrl,onCoverImageChange:G})})}),r.jsx("div",{className:"lg:col-span-1",children:r.jsxs("div",{className:"card p-6",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"文章设置"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标题"}),r.jsx("input",{type:"text",value:o.title,onChange:O=>c(T=>({...T,title:O.target.value})),className:"input w-full",placeholder:"输入文章标题"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"摘要"}),r.jsx("textarea",{value:o.excerpt,onChange:O=>c(T=>({...T,excerpt:O.target.value})),className:"input w-full h-20 resize-none",placeholder:"输入文章摘要"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"分类"}),r.jsx("input",{type:"text",value:o.category,onChange:O=>c(T=>({...T,category:O.target.value})),className:"input w-full",placeholder:"输入分类"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标签"}),r.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:o.tags.map(O=>r.jsxs("span",{className:"inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full",children:[O,r.jsx("button",{onClick:()=>L(O),title:`移除标签: ${O}`,"aria-label":`移除标签: ${O}`,className:"ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200",children:"×"})]},O))}),r.jsx("input",{type:"text",className:"input w-full",placeholder:"输入标签后按回车",onKeyPress:O=>{O.key==="Enter"&&(w(O.currentTarget.value.trim()),O.currentTarget.value="")}})]}),r.jsx("div",{children:r.jsxs("label",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"checkbox",checked:o.featured,onChange:O=>c(T=>({...T,featured:O.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),r.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"设为特色文章"})]})})]})]})})]})]}):r.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[r.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(ut,{className:"text-red-600 dark:text-red-400",size:32})}),r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"需要管理员权限"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"只有管理员才能创建和编辑文章"}),r.jsxs("button",{onClick:()=>y(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[r.jsx(ut,{size:20}),r.jsx("span",{children:"管理员登录"})]}),r.jsx(ua,{isOpen:R,onClose:()=>y(!1)})]})}function Un(){const{category:e}=Wt(),[t,s]=g.useState([]),[n,a]=g.useState([]),[l,i]=g.useState(!0),[o,c]=g.useState(null);g.useEffect(()=>{h()},[e]);const h=async()=>{i(!0),c(null);try{if(e){const d=await pe.getPosts(1,20,e);s(d.posts)}else{const d=await pe.getPosts(1,100);s(d.posts);const f=new Map;d.posts.forEach(m=>{m.category&&f.set(m.category,(f.get(m.category)||0)+1)}),f.set("General",d.posts.length);const b=Array.from(f.entries()).map(([m,j])=>({name:m,count:j})).sort((m,j)=>m.name==="General"?-1:j.name==="General"?1:j.count-m.count);a(b)}}catch(d){console.error("加载分类数据失败:",d),c("加载失败，请重试")}finally{i(!1)}};return l?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载中..."})]}):o?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("p",{className:"text-red-600 dark:text-red-400",children:o}),r.jsx("button",{onClick:h,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]}):e?r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsx("div",{className:"mb-6",children:r.jsxs(J,{to:"/categories",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"返回所有分类"})]})}),r.jsxs("div",{className:"mb-8",children:[r.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[r.jsx(vt,{size:24,className:"text-blue-600"}),r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e})]}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共找到 ",t.length," 篇文章"]})]}),t.length>0?r.jsx("div",{className:"space-y-6",children:t.map(d=>r.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[r.jsx("h2",{className:"text-xl font-semibold mb-3",children:r.jsx(J,{to:`/post/${d.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:d.title})}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:d.excerpt}),r.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(mt,{size:14}),r.jsx("span",{children:d.author})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:14}),r.jsx("span",{children:d.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(rt,{size:14}),r.jsxs("span",{children:[d.readTime," 分钟阅读"]})]}),d.category&&r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(vt,{size:14}),r.jsx("span",{className:"px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded",children:d.category})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:d.tags.map(f=>r.jsxs(J,{to:`/tag/${f}`,className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:["#",f]},f))})]},d.id))}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx(vt,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无相关文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"该分类下还没有文章"}),r.jsx(J,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]}):r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsxs("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"所有分类"}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共 ",n.length," 个分类"]})]}),n.length>0?r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(({name:d,count:f})=>r.jsxs(J,{to:`/category/${d}`,className:"group p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 hover:shadow-lg transition-all",children:[r.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[r.jsx(vt,{size:20,className:"text-blue-600"}),r.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:d})]}),r.jsxs("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:[f," 篇文章"]})]},d))}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx(vt,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无分类"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"还没有任何分类"}),r.jsx(J,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})]})}function Hn(){const{tag:e}=Wt(),[t,s]=g.useState([]),[n,a]=g.useState([]),[l,i]=g.useState(!0),[o,c]=g.useState(null);g.useEffect(()=>{h()},[e]);const h=async()=>{i(!0),c(null);try{if(e){const d=await pe.getPosts(1,20,void 0,e);s(d.posts)}else{const d=await pe.getPosts(1,100);s(d.posts);const f=new Map;d.posts.forEach(m=>{m.tags.forEach(j=>{f.set(j,(f.get(j)||0)+1)})});const b=Array.from(f.entries()).map(([m,j])=>({name:m,count:j})).sort((m,j)=>j.count-m.count);a(b)}}catch(d){console.error("加载标签数据失败:",d),c("加载失败，请重试")}finally{i(!1)}};return l?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载中..."})]}):o?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("p",{className:"text-red-600 dark:text-red-400",children:o}),r.jsx("button",{onClick:h,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]}):e?r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsx("div",{className:"mb-6",children:r.jsxs(J,{to:"/tags",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"返回所有标签"})]})}),r.jsxs("div",{className:"mb-8",children:[r.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[r.jsx(St,{size:24,className:"text-blue-600"}),r.jsxs("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["#",e]})]}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共找到 ",t.length," 篇文章"]})]}),t.length>0?r.jsx("div",{className:"space-y-6",children:t.map(d=>r.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[r.jsx("h2",{className:"text-xl font-semibold mb-3",children:r.jsx(J,{to:`/post/${d.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:d.title})}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:d.excerpt}),r.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(mt,{size:14}),r.jsx("span",{children:d.author})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:14}),r.jsx("span",{children:d.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(rt,{size:14}),r.jsxs("span",{children:[d.readTime," 分钟阅读"]})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:d.tags.map(f=>r.jsxs(J,{to:`/tag/${f}`,className:`px-2 py-1 text-xs rounded transition-colors ${f===e?"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:["#",f]},f))})]},d.id))}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx(St,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无相关文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"该标签下还没有文章"}),r.jsx(J,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]}):r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsxs("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"所有标签"}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共 ",n.length," 个标签"]})]}),n.length>0?r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:n.map(({name:d,count:f})=>r.jsx(J,{to:`/tag/${d}`,className:"group p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 hover:shadow-lg transition-all",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("span",{className:"font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:["#",d]}),r.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:f})]})},d))}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx(St,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无标签"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"还没有任何标签"}),r.jsx(J,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})]})}function _u(){const[e,t]=bl(),[s,n]=g.useState(e.get("q")||""),[a,l]=g.useState([]),[i,o]=g.useState(!1),[c,h]=g.useState(!1);g.useEffect(()=>{const m=e.get("q");m&&(n(m),d(m))},[e]);const d=async m=>{if(m.trim()){o(!0),h(!0);try{const j=await pe.searchPosts(m);l(j)}catch(j){console.error("搜索失败:",j),l([])}finally{o(!1)}}},f=m=>{m.preventDefault(),s.trim()&&t({q:s.trim()})},b=m=>{n(m.target.value)};return r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsx("div",{className:"mb-8",children:r.jsxs("form",{onSubmit:f,className:"relative",children:[r.jsxs("div",{className:"relative",children:[r.jsx(It,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),r.jsx("input",{type:"text",value:s,onChange:b,placeholder:"搜索文章...",className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),r.jsx("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-4 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"搜索"})]})}),r.jsxs("div",{children:[i&&r.jsxs("div",{className:"text-center py-8",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"搜索中..."})]}),!i&&c&&r.jsx("div",{className:"mb-6",children:r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:[a.length>0?`找到 ${a.length} 篇文章`:"没有找到相关文章",s&&` 关于 "${s}"`]})}),!i&&a.length>0&&r.jsx("div",{className:"space-y-6",children:a.map(m=>r.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[r.jsx("h2",{className:"text-xl font-semibold mb-3",children:r.jsx(J,{to:`/post/${m.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:m.title})}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:m.excerpt}),r.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(mt,{size:14}),r.jsx("span",{children:m.author})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:14}),r.jsx("span",{children:m.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(rt,{size:14}),r.jsxs("span",{children:[m.readTime," 分钟阅读"]})]}),m.category&&r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(St,{size:14}),r.jsx("span",{children:m.category})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:m.tags.map(j=>r.jsxs(J,{to:`/tag/${j}`,className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:["#",j]},j))})]},m.id))}),!i&&c&&a.length===0&&r.jsxs("div",{className:"text-center py-12",children:[r.jsx(It,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"没有找到相关文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"尝试使用不同的关键词或查看所有文章"}),r.jsx(J,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]})]})}function Iu(){return r.jsx("div",{className:"max-w-4xl mx-auto",children:r.jsxs("div",{className:"card p-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-6",children:"关于我"}),r.jsxs("div",{className:"prose prose-gray dark:prose-dark max-w-none",children:[r.jsx("p",{children:"欢迎来到我的个人博客！这里是我分享技术见解、开发经验和生活感悟的地方。"}),r.jsx("h2",{children:"技术栈"}),r.jsxs("ul",{children:[r.jsx("li",{children:"前端：React, TypeScript, Tailwind CSS"}),r.jsx("li",{children:"后端：Cloudflare Workers, Node.js"}),r.jsx("li",{children:"数据库：Cloudflare KV, MongoDB"}),r.jsx("li",{children:"部署：Cloudflare Pages, Vercel"})]}),r.jsx("h2",{children:"联系方式"}),r.jsx("p",{children:"如果您想与我交流或有任何问题，请通过以下方式联系我："}),r.jsxs("ul",{children:[r.jsx("li",{children:"<EMAIL>"}),r.jsx("li",{children:"https://github.com/ajkdfe2e2e"}),r.jsx("li",{children:"https://x.com/x2a1HRjxs552213"})]})]})]})})}function Pu(){const e=g.useRef(null),t=()=>{var s;(s=e.current)==null||s.toggleVisibility()};return r.jsxs(Zo,{children:[r.jsx(nc,{onMusicToggle:t,children:r.jsxs(ll,{children:[r.jsx(Se,{path:"/",element:r.jsx(lc,{})}),r.jsx(Se,{path:"/post/:slug",element:r.jsx(cc,{})}),r.jsx(Se,{path:"/write",element:r.jsx(zn,{})}),r.jsx(Se,{path:"/write/:slug",element:r.jsx(zn,{})}),r.jsx(Se,{path:"/categories",element:r.jsx(Un,{})}),r.jsx(Se,{path:"/category/:category",element:r.jsx(Un,{})}),r.jsx(Se,{path:"/tags",element:r.jsx(Hn,{})}),r.jsx(Se,{path:"/tag/:tag",element:r.jsx(Hn,{})}),r.jsx(Se,{path:"/search",element:r.jsx(_u,{})}),r.jsx(Se,{path:"/about",element:r.jsx(Iu,{})})]})}),r.jsx(ac,{ref:e})]})}as.createRoot(document.getElementById("root")).render(r.jsx(Ja.StrictMode,{children:r.jsxs(xl,{children:[r.jsx(Pu,{}),r.jsx(co,{position:"top-center",toastOptions:{duration:3e3,style:{background:"#1f2937",color:"#f9fafb"}}})]})}));
//# sourceMappingURL=index-0a1c9d84.js.map
