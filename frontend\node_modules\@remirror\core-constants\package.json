{"name": "@remirror/core-constants", "version": "3.0.0", "description": "The core constants used throughout the remirror codebase", "homepage": "https://github.com/remirror/remirror/tree/HEAD/packages/remirror__core-constants", "repository": {"type": "git", "url": "https://github.com/remirror/remirror.git", "directory": "packages/remirror__core-constants"}, "license": "MIT", "contributors": ["Ifiok Jr. <<EMAIL>>"], "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/remirror-core-constants.d.ts", "import": "./dist/remirror-core-constants.js", "require": "./dist/remirror-core-constants.cjs"}, "./package.json": "./package.json"}, "main": "./dist/remirror-core-constants.cjs", "module": "./dist/remirror-core-constants.js", "types": "./dist/remirror-core-constants.d.ts", "files": ["dist", "dist-types"], "dependencies": {}, "devDependencies": {"@remirror/cli": "1.1.0"}, "publishConfig": {"access": "public"}, "@remirror": {"sizeLimit": "2 KB"}, "scripts": {"build": "remirror-cli build"}}