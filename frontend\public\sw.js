// Service Worker for image caching and optimization
const CACHE_NAME = 'blog-images-v1'
const IMAGE_CACHE_NAME = 'blog-images-cache-v1'
const CACHE_EXPIRY = 30 * 24 * 60 * 60 * 1000 // 30 days

// Install event
self.addEventListener('install', (event) => {
  console.log('[SW] Installing service worker')
  self.skipWaiting()
})

// Activate event
self.addEventListener('activate', (event) => {
  console.log('[SW] Activating service worker')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME && cacheName !== IMAGE_CACHE_NAME) {
            console.log('[SW] Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      return self.clients.claim()
    })
  )
})

// Fetch event - handle image requests
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url)
  
  // Only handle image requests
  if (isImageRequest(event.request)) {
    event.respondWith(handleImageRequest(event.request))
  }
})

// Check if request is for an image
function isImageRequest(request) {
  const url = new URL(request.url)
  const pathname = url.pathname.toLowerCase()
  
  // Check for image file extensions
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.avif', '.svg']
  const hasImageExtension = imageExtensions.some(ext => pathname.endsWith(ext))
  
  // Check for image proxy requests
  const isImageProxy = pathname.includes('/api/image-proxy/')
  
  // Check for R2 image URLs
  const isR2Image = url.hostname === 'pub-a1a2.r2.dev'
  
  // Check Accept header
  const acceptHeader = request.headers.get('Accept') || ''
  const acceptsImage = acceptHeader.includes('image/')
  
  return hasImageExtension || isImageProxy || isR2Image || acceptsImage
}

// Handle image requests with caching
async function handleImageRequest(request) {
  const cache = await caches.open(IMAGE_CACHE_NAME)
  const url = new URL(request.url)
  
  try {
    // Try to get from cache first
    const cachedResponse = await cache.match(request)
    
    if (cachedResponse) {
      // Check if cache is still valid
      const cacheDate = cachedResponse.headers.get('sw-cache-date')
      if (cacheDate) {
        const cacheTime = new Date(cacheDate).getTime()
        const now = Date.now()
        
        if (now - cacheTime < CACHE_EXPIRY) {
          console.log('[SW] Serving image from cache:', url.pathname)
          return cachedResponse
        } else {
          console.log('[SW] Cache expired for:', url.pathname)
          await cache.delete(request)
        }
      }
    }
    
    // Fetch from network
    console.log('[SW] Fetching image from network:', url.pathname)
    const networkResponse = await fetch(request)
    
    if (networkResponse.ok) {
      // Clone response for caching
      const responseToCache = networkResponse.clone()
      
      // Add cache timestamp
      const headers = new Headers(responseToCache.headers)
      headers.set('sw-cache-date', new Date().toISOString())
      
      const cachedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      })
      
      // Cache the response
      await cache.put(request, cachedResponse)
      console.log('[SW] Cached image:', url.pathname)
    }
    
    return networkResponse
    
  } catch (error) {
    console.error('[SW] Error handling image request:', error)
    
    // Try to serve from cache as fallback
    const cachedResponse = await cache.match(request)
    if (cachedResponse) {
      console.log('[SW] Serving stale cache as fallback:', url.pathname)
      return cachedResponse
    }
    
    // Return a placeholder image or error response
    return new Response('Image not available', { 
      status: 404,
      headers: { 'Content-Type': 'text/plain' }
    })
  }
}

// Message handling for cache management
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEAR_IMAGE_CACHE') {
    event.waitUntil(
      caches.delete(IMAGE_CACHE_NAME).then(() => {
        console.log('[SW] Image cache cleared')
        event.ports[0].postMessage({ success: true })
      }).catch((error) => {
        console.error('[SW] Error clearing cache:', error)
        event.ports[0].postMessage({ success: false, error: error.message })
      })
    )
  }
  
  if (event.data && event.data.type === 'PRELOAD_IMAGES') {
    event.waitUntil(
      preloadImages(event.data.urls).then(() => {
        console.log('[SW] Images preloaded')
        event.ports[0].postMessage({ success: true })
      }).catch((error) => {
        console.error('[SW] Error preloading images:', error)
        event.ports[0].postMessage({ success: false, error: error.message })
      })
    )
  }
})

// Preload images
async function preloadImages(urls) {
  const cache = await caches.open(IMAGE_CACHE_NAME)
  
  const preloadPromises = urls.map(async (url) => {
    try {
      const request = new Request(url)
      const cachedResponse = await cache.match(request)
      
      if (!cachedResponse) {
        console.log('[SW] Preloading image:', url)
        const response = await fetch(request)
        if (response.ok) {
          const headers = new Headers(response.headers)
          headers.set('sw-cache-date', new Date().toISOString())
          
          const cachedResponse = new Response(response.body, {
            status: response.status,
            statusText: response.statusText,
            headers: headers
          })
          
          await cache.put(request, cachedResponse)
        }
      }
    } catch (error) {
      console.warn('[SW] Failed to preload image:', url, error)
    }
  })
  
  await Promise.allSettled(preloadPromises)
}

// Periodic cache cleanup
self.addEventListener('periodicsync', (event) => {
  if (event.tag === 'image-cache-cleanup') {
    event.waitUntil(cleanupExpiredCache())
  }
})

// Clean up expired cache entries
async function cleanupExpiredCache() {
  try {
    const cache = await caches.open(IMAGE_CACHE_NAME)
    const requests = await cache.keys()
    const now = Date.now()
    
    const cleanupPromises = requests.map(async (request) => {
      const response = await cache.match(request)
      if (response) {
        const cacheDate = response.headers.get('sw-cache-date')
        if (cacheDate) {
          const cacheTime = new Date(cacheDate).getTime()
          if (now - cacheTime > CACHE_EXPIRY) {
            console.log('[SW] Cleaning up expired cache:', request.url)
            await cache.delete(request)
          }
        }
      }
    })
    
    await Promise.allSettled(cleanupPromises)
    console.log('[SW] Cache cleanup completed')
  } catch (error) {
    console.error('[SW] Error during cache cleanup:', error)
  }
}
