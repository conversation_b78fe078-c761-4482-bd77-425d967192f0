var Gn=Object.defineProperty;var Vn=(e,s,r)=>s in e?Gn(e,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[s]=r;var Ks=(e,s,r)=>(Vn(e,typeof s!="symbol"?s+"":s,r),r);import{r as d,b as vr,c as Jn,g as Xn,R as Qn}from"./vendor-5f6cd04d.js";import{N as Zn,m as tr,n as Yn,M as ei,a as ti,P as $s,b as Ds,c as si,g as ri,f as ai,d as ni,e as ii,C as li,h as sr,D as oi,i as ci,u as di,S as ui,E as hi}from"./editor-cda9e262.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))a(n);new MutationObserver(n=>{for(const l of n)if(l.type==="childList")for(const i of l.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&a(i)}).observe(document,{childList:!0,subtree:!0});function r(n){const l={};return n.integrity&&(l.integrity=n.integrity),n.referrerPolicy&&(l.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?l.credentials="include":n.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function a(n){if(n.ep)return;n.ep=!0;const l=r(n);fetch(n.href,l)}})();var Ba={exports:{}},Us={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gi=d,xi=Symbol.for("react.element"),fi=Symbol.for("react.fragment"),mi=Object.prototype.hasOwnProperty,pi=gi.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,yi={key:!0,ref:!0,__self:!0,__source:!0};function Wa(e,s,r){var a,n={},l=null,i=null;r!==void 0&&(l=""+r),s.key!==void 0&&(l=""+s.key),s.ref!==void 0&&(i=s.ref);for(a in s)mi.call(s,a)&&!yi.hasOwnProperty(a)&&(n[a]=s[a]);if(e&&e.defaultProps)for(a in s=e.defaultProps,s)n[a]===void 0&&(n[a]=s[a]);return{$$typeof:xi,type:e,key:l,ref:i,props:n,_owner:pi.current}}Us.Fragment=fi;Us.jsx=Wa;Us.jsxs=Wa;Ba.exports=Us;var t=Ba.exports,rr={},Zr=vr;rr.createRoot=Zr.createRoot,rr.hydrateRoot=Zr.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ot(){return Ot=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},Ot.apply(this,arguments)}var Ge;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Ge||(Ge={}));const Yr="popstate";function bi(e){e===void 0&&(e={});function s(a,n){let{pathname:l,search:i,hash:o}=a.location;return ar("",{pathname:l,search:i,hash:o},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(a,n){return typeof n=="string"?n:as(n)}return ji(s,r,null,e)}function pe(e,s){if(e===!1||e===null||typeof e>"u")throw new Error(s)}function Fa(e,s){if(!e){typeof console<"u"&&console.warn(s);try{throw new Error(s)}catch{}}}function vi(){return Math.random().toString(36).substr(2,8)}function ea(e,s){return{usr:e.state,key:e.key,idx:s}}function ar(e,s,r,a){return r===void 0&&(r=null),Ot({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof s=="string"?vt(s):s,{state:r,key:s&&s.key||a||vi()})}function as(e){let{pathname:s="/",search:r="",hash:a=""}=e;return r&&r!=="?"&&(s+=r.charAt(0)==="?"?r:"?"+r),a&&a!=="#"&&(s+=a.charAt(0)==="#"?a:"#"+a),s}function vt(e){let s={};if(e){let r=e.indexOf("#");r>=0&&(s.hash=e.substr(r),e=e.substr(0,r));let a=e.indexOf("?");a>=0&&(s.search=e.substr(a),e=e.substr(0,a)),e&&(s.pathname=e)}return s}function ji(e,s,r,a){a===void 0&&(a={});let{window:n=document.defaultView,v5Compat:l=!1}=a,i=n.history,o=Ge.Pop,c=null,x=u();x==null&&(x=0,i.replaceState(Ot({},i.state,{idx:x}),""));function u(){return(i.state||{idx:null}).idx}function h(){o=Ge.Pop;let N=u(),E=N==null?null:N-x;x=N,c&&c({action:o,location:A.location,delta:E})}function p(N,E){o=Ge.Push;let _=ar(A.location,N,E);r&&r(_,N),x=u()+1;let K=ea(_,x),T=A.createHref(_);try{i.pushState(K,"",T)}catch(H){if(H instanceof DOMException&&H.name==="DataCloneError")throw H;n.location.assign(T)}l&&c&&c({action:o,location:A.location,delta:1})}function m(N,E){o=Ge.Replace;let _=ar(A.location,N,E);r&&r(_,N),x=u();let K=ea(_,x),T=A.createHref(_);i.replaceState(K,"",T),l&&c&&c({action:o,location:A.location,delta:0})}function k(N){let E=n.location.origin!=="null"?n.location.origin:n.location.href,_=typeof N=="string"?N:as(N);return _=_.replace(/ $/,"%20"),pe(E,"No window.location.(origin|href) available to create URL for href: "+_),new URL(_,E)}let A={get action(){return o},get location(){return e(n,i)},listen(N){if(c)throw new Error("A history only accepts one active listener");return n.addEventListener(Yr,h),c=N,()=>{n.removeEventListener(Yr,h),c=null}},createHref(N){return s(n,N)},createURL:k,encodeLocation(N){let E=k(N);return{pathname:E.pathname,search:E.search,hash:E.hash}},push:p,replace:m,go(N){return i.go(N)}};return A}var ta;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(ta||(ta={}));function ki(e,s,r){return r===void 0&&(r="/"),wi(e,s,r,!1)}function wi(e,s,r,a){let n=typeof s=="string"?vt(s):s,l=jr(n.pathname||"/",r);if(l==null)return null;let i=Ka(e);Ni(i);let o=null;for(let c=0;o==null&&c<i.length;++c){let x=Pi(l);o=Ii(i[c],x,a)}return o}function Ka(e,s,r,a){s===void 0&&(s=[]),r===void 0&&(r=[]),a===void 0&&(a="");let n=(l,i,o)=>{let c={relativePath:o===void 0?l.path||"":o,caseSensitive:l.caseSensitive===!0,childrenIndex:i,route:l};c.relativePath.startsWith("/")&&(pe(c.relativePath.startsWith(a),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+a+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(a.length));let x=Xe([a,c.relativePath]),u=r.concat(c);l.children&&l.children.length>0&&(pe(l.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+x+'".')),Ka(l.children,s,u,x)),!(l.path==null&&!l.index)&&s.push({path:x,score:Ti(x,l.index),routesMeta:u})};return e.forEach((l,i)=>{var o;if(l.path===""||!((o=l.path)!=null&&o.includes("?")))n(l,i);else for(let c of qa(l.path))n(l,i,c)}),s}function qa(e){let s=e.split("/");if(s.length===0)return[];let[r,...a]=s,n=r.endsWith("?"),l=r.replace(/\?$/,"");if(a.length===0)return n?[l,""]:[l];let i=qa(a.join("/")),o=[];return o.push(...i.map(c=>c===""?l:[l,c].join("/"))),n&&o.push(...i),o.map(c=>e.startsWith("/")&&c===""?"/":c)}function Ni(e){e.sort((s,r)=>s.score!==r.score?r.score-s.score:Li(s.routesMeta.map(a=>a.childrenIndex),r.routesMeta.map(a=>a.childrenIndex)))}const Ei=/^:[\w-]+$/,Si=3,Ci=2,Ai=1,Ri=10,Mi=-2,sa=e=>e==="*";function Ti(e,s){let r=e.split("/"),a=r.length;return r.some(sa)&&(a+=Mi),s&&(a+=Ci),r.filter(n=>!sa(n)).reduce((n,l)=>n+(Ei.test(l)?Si:l===""?Ai:Ri),a)}function Li(e,s){return e.length===s.length&&e.slice(0,-1).every((a,n)=>a===s[n])?e[e.length-1]-s[s.length-1]:0}function Ii(e,s,r){r===void 0&&(r=!1);let{routesMeta:a}=e,n={},l="/",i=[];for(let o=0;o<a.length;++o){let c=a[o],x=o===a.length-1,u=l==="/"?s:s.slice(l.length)||"/",h=ra({path:c.relativePath,caseSensitive:c.caseSensitive,end:x},u),p=c.route;if(!h&&x&&r&&!a[a.length-1].route.index&&(h=ra({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},u)),!h)return null;Object.assign(n,h.params),i.push({params:n,pathname:Xe([l,h.pathname]),pathnameBase:Di(Xe([l,h.pathnameBase])),route:p}),h.pathnameBase!=="/"&&(l=Xe([l,h.pathnameBase]))}return i}function ra(e,s){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=Oi(e.path,e.caseSensitive,e.end),n=s.match(r);if(!n)return null;let l=n[0],i=l.replace(/(.)\/+$/,"$1"),o=n.slice(1);return{params:a.reduce((x,u,h)=>{let{paramName:p,isOptional:m}=u;if(p==="*"){let A=o[h]||"";i=l.slice(0,l.length-A.length).replace(/(.)\/+$/,"$1")}const k=o[h];return m&&!k?x[p]=void 0:x[p]=(k||"").replace(/%2F/g,"/"),x},{}),pathname:l,pathnameBase:i,pattern:e}}function Oi(e,s,r){s===void 0&&(s=!1),r===void 0&&(r=!0),Fa(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,o,c)=>(a.push({paramName:o,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,s?void 0:"i"),a]}function Pi(e){try{return e.split("/").map(s=>decodeURIComponent(s).replace(/\//g,"%2F")).join("/")}catch(s){return Fa(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+s+").")),e}}function jr(e,s){if(s==="/")return e;if(!e.toLowerCase().startsWith(s.toLowerCase()))return null;let r=s.endsWith("/")?s.length-1:s.length,a=e.charAt(r);return a&&a!=="/"?null:e.slice(r)||"/"}function _i(e,s){s===void 0&&(s="/");let{pathname:r,search:a="",hash:n=""}=typeof e=="string"?vt(e):e;return{pathname:r?r.startsWith("/")?r:zi(r,s):s,search:Ui(a),hash:Hi(n)}}function zi(e,s){let r=s.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function qs(e,s,r,a){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+s+"` field ["+JSON.stringify(a)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function $i(e){return e.filter((s,r)=>r===0||s.route.path&&s.route.path.length>0)}function Ga(e,s){let r=$i(e);return s?r.map((a,n)=>n===r.length-1?a.pathname:a.pathnameBase):r.map(a=>a.pathnameBase)}function Va(e,s,r,a){a===void 0&&(a=!1);let n;typeof e=="string"?n=vt(e):(n=Ot({},e),pe(!n.pathname||!n.pathname.includes("?"),qs("?","pathname","search",n)),pe(!n.pathname||!n.pathname.includes("#"),qs("#","pathname","hash",n)),pe(!n.search||!n.search.includes("#"),qs("#","search","hash",n)));let l=e===""||n.pathname==="",i=l?"/":n.pathname,o;if(i==null)o=r;else{let h=s.length-1;if(!a&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),h-=1;n.pathname=p.join("/")}o=h>=0?s[h]:"/"}let c=_i(n,o),x=i&&i!=="/"&&i.endsWith("/"),u=(l||i===".")&&r.endsWith("/");return!c.pathname.endsWith("/")&&(x||u)&&(c.pathname+="/"),c}const Xe=e=>e.join("/").replace(/\/\/+/g,"/"),Di=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ui=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Hi=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Bi(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Ja=["post","put","patch","delete"];new Set(Ja);const Wi=["get",...Ja];new Set(Wi);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Pt(){return Pt=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},Pt.apply(this,arguments)}const kr=d.createContext(null),Fi=d.createContext(null),it=d.createContext(null),Hs=d.createContext(null),Ye=d.createContext({outlet:null,matches:[],isDataRoute:!1}),Xa=d.createContext(null);function Ki(e,s){let{relative:r}=s===void 0?{}:s;Ft()||pe(!1);let{basename:a,navigator:n}=d.useContext(it),{hash:l,pathname:i,search:o}=Za(e,{relative:r}),c=i;return a!=="/"&&(c=i==="/"?a:Xe([a,i])),n.createHref({pathname:c,search:o,hash:l})}function Ft(){return d.useContext(Hs)!=null}function jt(){return Ft()||pe(!1),d.useContext(Hs).location}function Qa(e){d.useContext(it).static||d.useLayoutEffect(e)}function Kt(){let{isDataRoute:e}=d.useContext(Ye);return e?al():qi()}function qi(){Ft()||pe(!1);let e=d.useContext(kr),{basename:s,future:r,navigator:a}=d.useContext(it),{matches:n}=d.useContext(Ye),{pathname:l}=jt(),i=JSON.stringify(Ga(n,r.v7_relativeSplatPath)),o=d.useRef(!1);return Qa(()=>{o.current=!0}),d.useCallback(function(x,u){if(u===void 0&&(u={}),!o.current)return;if(typeof x=="number"){a.go(x);return}let h=Va(x,JSON.parse(i),l,u.relative==="path");e==null&&s!=="/"&&(h.pathname=h.pathname==="/"?s:Xe([s,h.pathname])),(u.replace?a.replace:a.push)(h,u.state,u)},[s,a,i,l,e])}function qt(){let{matches:e}=d.useContext(Ye),s=e[e.length-1];return s?s.params:{}}function Za(e,s){let{relative:r}=s===void 0?{}:s,{future:a}=d.useContext(it),{matches:n}=d.useContext(Ye),{pathname:l}=jt(),i=JSON.stringify(Ga(n,a.v7_relativeSplatPath));return d.useMemo(()=>Va(e,JSON.parse(i),l,r==="path"),[e,i,l,r])}function Gi(e,s){return Vi(e,s)}function Vi(e,s,r,a){Ft()||pe(!1);let{navigator:n}=d.useContext(it),{matches:l}=d.useContext(Ye),i=l[l.length-1],o=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let x=jt(),u;if(s){var h;let N=typeof s=="string"?vt(s):s;c==="/"||(h=N.pathname)!=null&&h.startsWith(c)||pe(!1),u=N}else u=x;let p=u.pathname||"/",m=p;if(c!=="/"){let N=c.replace(/^\//,"").split("/");m="/"+p.replace(/^\//,"").split("/").slice(N.length).join("/")}let k=ki(e,{pathname:m}),A=Yi(k&&k.map(N=>Object.assign({},N,{params:Object.assign({},o,N.params),pathname:Xe([c,n.encodeLocation?n.encodeLocation(N.pathname).pathname:N.pathname]),pathnameBase:N.pathnameBase==="/"?c:Xe([c,n.encodeLocation?n.encodeLocation(N.pathnameBase).pathname:N.pathnameBase])})),l,r,a);return s&&A?d.createElement(Hs.Provider,{value:{location:Pt({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:Ge.Pop}},A):A}function Ji(){let e=rl(),s=Bi(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},l=null;return d.createElement(d.Fragment,null,d.createElement("h2",null,"Unexpected Application Error!"),d.createElement("h3",{style:{fontStyle:"italic"}},s),r?d.createElement("pre",{style:n},r):null,l)}const Xi=d.createElement(Ji,null);class Qi extends d.Component{constructor(s){super(s),this.state={location:s.location,revalidation:s.revalidation,error:s.error}}static getDerivedStateFromError(s){return{error:s}}static getDerivedStateFromProps(s,r){return r.location!==s.location||r.revalidation!=="idle"&&s.revalidation==="idle"?{error:s.error,location:s.location,revalidation:s.revalidation}:{error:s.error!==void 0?s.error:r.error,location:r.location,revalidation:s.revalidation||r.revalidation}}componentDidCatch(s,r){console.error("React Router caught the following error during render",s,r)}render(){return this.state.error!==void 0?d.createElement(Ye.Provider,{value:this.props.routeContext},d.createElement(Xa.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Zi(e){let{routeContext:s,match:r,children:a}=e,n=d.useContext(kr);return n&&n.static&&n.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=r.route.id),d.createElement(Ye.Provider,{value:s},a)}function Yi(e,s,r,a){var n;if(s===void 0&&(s=[]),r===void 0&&(r=null),a===void 0&&(a=null),e==null){var l;if(!r)return null;if(r.errors)e=r.matches;else if((l=a)!=null&&l.v7_partialHydration&&s.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let i=e,o=(n=r)==null?void 0:n.errors;if(o!=null){let u=i.findIndex(h=>h.route.id&&(o==null?void 0:o[h.route.id])!==void 0);u>=0||pe(!1),i=i.slice(0,Math.min(i.length,u+1))}let c=!1,x=-1;if(r&&a&&a.v7_partialHydration)for(let u=0;u<i.length;u++){let h=i[u];if((h.route.HydrateFallback||h.route.hydrateFallbackElement)&&(x=u),h.route.id){let{loaderData:p,errors:m}=r,k=h.route.loader&&p[h.route.id]===void 0&&(!m||m[h.route.id]===void 0);if(h.route.lazy||k){c=!0,x>=0?i=i.slice(0,x+1):i=[i[0]];break}}}return i.reduceRight((u,h,p)=>{let m,k=!1,A=null,N=null;r&&(m=o&&h.route.id?o[h.route.id]:void 0,A=h.route.errorElement||Xi,c&&(x<0&&p===0?(nl("route-fallback",!1),k=!0,N=null):x===p&&(k=!0,N=h.route.hydrateFallbackElement||null)));let E=s.concat(i.slice(0,p+1)),_=()=>{let K;return m?K=A:k?K=N:h.route.Component?K=d.createElement(h.route.Component,null):h.route.element?K=h.route.element:K=u,d.createElement(Zi,{match:h,routeContext:{outlet:u,matches:E,isDataRoute:r!=null},children:K})};return r&&(h.route.ErrorBoundary||h.route.errorElement||p===0)?d.createElement(Qi,{location:r.location,revalidation:r.revalidation,component:A,error:m,children:_(),routeContext:{outlet:null,matches:E,isDataRoute:!0}}):_()},null)}var Ya=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Ya||{}),ns=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ns||{});function el(e){let s=d.useContext(kr);return s||pe(!1),s}function tl(e){let s=d.useContext(Fi);return s||pe(!1),s}function sl(e){let s=d.useContext(Ye);return s||pe(!1),s}function en(e){let s=sl(),r=s.matches[s.matches.length-1];return r.route.id||pe(!1),r.route.id}function rl(){var e;let s=d.useContext(Xa),r=tl(ns.UseRouteError),a=en(ns.UseRouteError);return s!==void 0?s:(e=r.errors)==null?void 0:e[a]}function al(){let{router:e}=el(Ya.UseNavigateStable),s=en(ns.UseNavigateStable),r=d.useRef(!1);return Qa(()=>{r.current=!0}),d.useCallback(function(n,l){l===void 0&&(l={}),r.current&&(typeof n=="number"?e.navigate(n):e.navigate(n,Pt({fromRouteId:s},l)))},[e,s])}const aa={};function nl(e,s,r){!s&&!aa[e]&&(aa[e]=!0)}function il(e,s){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!s||s.v7_relativeSplatPath),s&&(s.v7_fetcherPersist,s.v7_normalizeFormMethod,s.v7_partialHydration,s.v7_skipActionErrorRevalidation)}function Le(e){pe(!1)}function ll(e){let{basename:s="/",children:r=null,location:a,navigationType:n=Ge.Pop,navigator:l,static:i=!1,future:o}=e;Ft()&&pe(!1);let c=s.replace(/^\/*/,"/"),x=d.useMemo(()=>({basename:c,navigator:l,static:i,future:Pt({v7_relativeSplatPath:!1},o)}),[c,o,l,i]);typeof a=="string"&&(a=vt(a));let{pathname:u="/",search:h="",hash:p="",state:m=null,key:k="default"}=a,A=d.useMemo(()=>{let N=jr(u,c);return N==null?null:{location:{pathname:N,search:h,hash:p,state:m,key:k},navigationType:n}},[c,u,h,p,m,k,n]);return A==null?null:d.createElement(it.Provider,{value:x},d.createElement(Hs.Provider,{children:r,value:A}))}function ol(e){let{children:s,location:r}=e;return Gi(nr(s),r)}new Promise(()=>{});function nr(e,s){s===void 0&&(s=[]);let r=[];return d.Children.forEach(e,(a,n)=>{if(!d.isValidElement(a))return;let l=[...s,n];if(a.type===d.Fragment){r.push.apply(r,nr(a.props.children,l));return}a.type!==Le&&pe(!1),!a.props.index||!a.props.children||pe(!1);let i={id:a.props.id||l.join("-"),caseSensitive:a.props.caseSensitive,element:a.props.element,Component:a.props.Component,index:a.props.index,path:a.props.path,loader:a.props.loader,action:a.props.action,errorElement:a.props.errorElement,ErrorBoundary:a.props.ErrorBoundary,hasErrorBoundary:a.props.ErrorBoundary!=null||a.props.errorElement!=null,shouldRevalidate:a.props.shouldRevalidate,handle:a.props.handle,lazy:a.props.lazy};a.props.children&&(i.children=nr(a.props.children,l)),r.push(i)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ir(){return ir=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},ir.apply(this,arguments)}function cl(e,s){if(e==null)return{};var r={},a=Object.keys(e),n,l;for(l=0;l<a.length;l++)n=a[l],!(s.indexOf(n)>=0)&&(r[n]=e[n]);return r}function dl(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ul(e,s){return e.button===0&&(!s||s==="_self")&&!dl(e)}function lr(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((s,r)=>{let a=e[r];return s.concat(Array.isArray(a)?a.map(n=>[r,n]):[[r,a]])},[]))}function hl(e,s){let r=lr(e);return s&&s.forEach((a,n)=>{r.has(n)||s.getAll(n).forEach(l=>{r.append(n,l)})}),r}const gl=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],xl="6";try{window.__reactRouterVersion=xl}catch{}const fl="startTransition",na=Jn[fl];function ml(e){let{basename:s,children:r,future:a,window:n}=e,l=d.useRef();l.current==null&&(l.current=bi({window:n,v5Compat:!0}));let i=l.current,[o,c]=d.useState({action:i.action,location:i.location}),{v7_startTransition:x}=a||{},u=d.useCallback(h=>{x&&na?na(()=>c(h)):c(h)},[c,x]);return d.useLayoutEffect(()=>i.listen(u),[i,u]),d.useEffect(()=>il(a),[a]),d.createElement(ll,{basename:s,children:r,location:o.location,navigationType:o.action,navigator:i,future:a})}const pl=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",yl=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ae=d.forwardRef(function(s,r){let{onClick:a,relative:n,reloadDocument:l,replace:i,state:o,target:c,to:x,preventScrollReset:u,viewTransition:h}=s,p=cl(s,gl),{basename:m}=d.useContext(it),k,A=!1;if(typeof x=="string"&&yl.test(x)&&(k=x,pl))try{let K=new URL(window.location.href),T=x.startsWith("//")?new URL(K.protocol+x):new URL(x),H=jr(T.pathname,m);T.origin===K.origin&&H!=null?x=H+T.search+T.hash:A=!0}catch{}let N=Ki(x,{relative:n}),E=bl(x,{replace:i,state:o,target:c,preventScrollReset:u,relative:n,viewTransition:h});function _(K){a&&a(K),K.defaultPrevented||E(K)}return d.createElement("a",ir({},p,{href:k||N,onClick:A||l?a:_,ref:r,target:c}))});var ia;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(ia||(ia={}));var la;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(la||(la={}));function bl(e,s){let{target:r,replace:a,state:n,preventScrollReset:l,relative:i,viewTransition:o}=s===void 0?{}:s,c=Kt(),x=jt(),u=Za(e,{relative:i});return d.useCallback(h=>{if(ul(h,r)){h.preventDefault();let p=a!==void 0?a:as(x)===as(u);c(e,{replace:p,state:n,preventScrollReset:l,relative:i,viewTransition:o})}},[x,c,u,a,n,r,e,l,i,o])}function vl(e){let s=d.useRef(lr(e)),r=d.useRef(!1),a=jt(),n=d.useMemo(()=>hl(a.search,r.current?null:s.current),[a.search]),l=Kt(),i=d.useCallback((o,c)=>{const x=lr(typeof o=="function"?o(n):o);r.current=!0,l("?"+x,c)},[l,n]);return[n,i]}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var jl={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kl=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),D=(e,s)=>{const r=d.forwardRef(({color:a="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:c,...x},u)=>d.createElement("svg",{ref:u,...jl,width:n,height:n,stroke:a,strokeWidth:i?Number(l)*24/Number(n):l,className:["lucide",`lucide-${kl(e)}`,o].join(" "),...x},[...s.map(([h,p])=>d.createElement(h,p)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=D("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=D("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wl=D("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nl=D("Bold",[["path",{d:"M14 12a4 4 0 0 0 0-8H6v8",key:"v2sylx"}],["path",{d:"M15 20a4 4 0 0 0 0-8H6v8Z",key:"1ef5ya"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ae=D("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const or=D("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gt=D("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const El=D("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tn=D("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=D("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sl=D("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cl=D("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Al=D("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=D("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wr=D("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qe=D("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ml=D("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=D("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tl=D("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=D("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Il=D("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=D("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pl=D("Heading2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=D("Heading3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=D("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ls=D("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oa=D("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=D("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ca=D("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=D("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const da=D("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=D("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=D("ListOrdered",[["line",{x1:"10",x2:"21",y1:"6",y2:"6",key:"76qw6h"}],["line",{x1:"10",x2:"21",y1:"12",y2:"12",key:"16nom4"}],["line",{x1:"10",x2:"21",y1:"18",y2:"18",key:"u3jurt"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sn=D("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mt=D("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hl=D("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bl=D("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rn=D("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ua=D("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const an=D("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wl=D("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ts=D("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fl=D("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kl=D("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ql=D("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gl=D("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nt=D("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vl=D("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jl=D("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=D("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xl=D("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ql=D("Quote",[["path",{d:"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z",key:"4rm80e"}],["path",{d:"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z",key:"10za9r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zl=D("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yl=D("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eo=D("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const to=D("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=D("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const so=D("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xt=D("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ro=D("SkipBack",[["polygon",{points:"19 20 9 12 19 4 19 20",key:"o2sva"}],["line",{x1:"5",x2:"5",y1:"19",y2:"5",key:"1ocqjk"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ao=D("SkipForward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ct=D("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nn=D("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const no=D("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const io=D("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ft=D("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lo=D("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zt=D("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oo=D("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const co=D("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uo=D("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ze=D("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ho=D("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const go=D("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const We=D("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xo=D("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fo=D("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);function mo(){const[e,s]=d.useState(()=>{const a=localStorage.getItem("theme");return a||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")});return d.useEffect(()=>{const a=document.documentElement;a.classList.remove("light","dark"),a.classList.add(e),localStorage.setItem("theme",e)},[e]),{theme:e,toggleTheme:()=>{s(a=>a==="light"?"dark":"light")},setTheme:s,isDark:e==="dark"}}const po={weather:"https://blog.fddfffff.site",aiImage:"https://blog.fddfffff.site",blog:"https://blog.fddfffff.site",music:"https://blog.fddfffff.site",r2Storage:"https://pub-02490a32db742d596d4d7c00aec.r2.dev"},ye=po,fe={weather:`${ye.weather}/weather`,aiImage:`${ye.aiImage}/ai/generate`,posts:`${ye.blog}/api/posts`,post:e=>`${ye.blog}/api/posts/${e}`,comments:e=>`${ye.blog}/api/posts/${e}/comments`,comment:(e,s)=>`${ye.blog}/api/posts/${e}/comments/${s}`,uploadImage:`${ye.blog}/api/upload-image`,auth:{verify:`${ye.blog}/api/auth/verify`},music:{search:`${ye.music}/music/search`,songUrl:`${ye.music}/music/url`,lyric:`${ye.music}/music/lyric`,songDetail:`${ye.music}/music/song/detail`,playlist:`${ye.music}/music/playlist/detail`},r2Images:`${ye.r2Storage}`,categories:`${ye.blog}/api/categories`,tags:`${ye.blog}/api/tags`,search:`${ye.blog}/api/search`},ln={"Content-Type":"application/json",Accept:"application/json"},on=6e4;function yo(){return localStorage.getItem("admin_api_key")}const Te=(e,s={})=>{const r=new AbortController,a=setTimeout(()=>r.abort(),on);return fetch(e,{headers:{...ln,...s.headers},signal:r.signal,...s}).finally(()=>{clearTimeout(a)})},ss=(e,s={})=>{const r=yo(),a={...ln,...s.headers};r&&(a["X-API-Key"]=r);const n=new AbortController,l=setTimeout(()=>n.abort(),on);return fetch(e,{headers:a,signal:n.signal,...s}).finally(()=>{clearTimeout(l)})};class os extends Error{constructor(s,r,a){super(a||`API Error: ${s} ${r}`),this.status=s,this.statusText=r,this.name="ApiError"}}const ve=async e=>{if(!e.ok){let s;try{const r=await e.json();s=r.error||r.message||e.statusText}catch{s=e.statusText}throw new os(e.status,e.statusText,s)}return e.json()},cn=d.createContext(void 0);function Gt(){const e=d.useContext(cn);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e}function bo({children:e}){const[s,r]=d.useState(!1),[a,n]=d.useState(null),[l,i]=d.useState(!0);d.useEffect(()=>{const h=localStorage.getItem("admin_api_key");h?o(h):i(!1)},[]);const o=async h=>{try{return(await(await fetch(fe.auth.verify,{method:"POST",headers:{"Content-Type":"application/json","X-API-Key":h}})).json()).isAdmin?(r(!0),n(h),localStorage.setItem("admin_api_key",h),!0):(r(!1),n(null),localStorage.removeItem("admin_api_key"),!1)}catch(p){return console.error("API密钥验证失败:",p),r(!1),n(null),localStorage.removeItem("admin_api_key"),!1}finally{i(!1)}},u={isAdmin:s,apiKey:a,login:async h=>(i(!0),await o(h)),logout:()=>{r(!1),n(null),localStorage.removeItem("admin_api_key")},isLoading:l};return t.jsx(cn.Provider,{value:u,children:e})}function dn({isOpen:e,onClose:s}){const[r,a]=d.useState(""),[n,l]=d.useState(!1),[i,o]=d.useState(""),[c,x]=d.useState(!1),{login:u}=Gt();if(!e)return null;const h=async m=>{if(m.preventDefault(),!r.trim()){o("请输入API密钥");return}x(!0),o("");try{await u(r.trim())?(a(""),s()):o("API密钥无效，请检查后重试")}catch{o("登录失败，请稍后重试")}finally{x(!1)}},p=()=>{a(""),o(""),l(!1),s()};return t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:t.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:[t.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:t.jsx(xt,{className:"text-white",size:20})}),t.jsxs("div",{children:[t.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"管理员登录"}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"输入API密钥以获取管理权限"})]})]}),t.jsx("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"关闭",children:t.jsx(We,{size:20})})]}),t.jsxs("form",{onSubmit:h,className:"p-6",children:[t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[t.jsx(da,{size:16,className:"inline mr-2"}),"API密钥"]}),t.jsxs("div",{className:"relative",children:[t.jsx("input",{type:n?"text":"password",value:r,onChange:m=>a(m.target.value),placeholder:"请输入管理员API密钥",className:"w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",disabled:c}),t.jsx("button",{type:"button",onClick:()=>l(!n),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",disabled:c,title:n?"隐藏密钥":"显示密钥",children:n?t.jsx(wr,{size:16}):t.jsx(Qe,{size:16})})]})]}),i&&t.jsx("div",{className:"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:t.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[t.jsx(Hl,{size:16,className:"mr-2"}),i]})})]}),t.jsxs("div",{className:"flex space-x-3 mt-6",children:[t.jsx("button",{type:"button",onClick:p,className:"flex-1 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",disabled:c,children:"取消"}),t.jsx("button",{type:"submit",disabled:c||!r.trim(),className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:c?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),t.jsx("span",{children:"验证中..."})]}):t.jsxs(t.Fragment,{children:[t.jsx(da,{size:16}),t.jsx("span",{children:"登录"})]})})]})]})]})})}class je{static async getPosts(s=1,r=10,a,n){var o,c,x;const l=new URLSearchParams({page:s.toString(),pageSize:r.toString()});a&&l.append("category",a),n&&l.append("tag",n);const i=`${fe.posts}?${l.toString()}`;try{const u=await Te(i),h=await ve(u);return{posts:h.posts||[],total:((o=h.pagination)==null?void 0:o.totalPosts)||0,page:((c=h.pagination)==null?void 0:c.page)||1,pageSize:((x=h.pagination)==null?void 0:x.pageSize)||10}}catch(u){throw console.error("获取文章列表失败:",u),u}}static async getPost(s){try{const r=await Te(fe.post(s));return await ve(r)}catch(r){throw console.error("获取文章失败:",r),r}}static async createPost(s){try{const r=await ss(fe.posts,{method:"POST",body:JSON.stringify(s)});return await ve(r)}catch(r){throw console.error("创建文章失败:",r),r}}static async updatePost(s,r){try{const a=await ss(fe.post(s),{method:"PUT",body:JSON.stringify(r)});return await ve(a)}catch(a){throw console.error("更新文章失败:",a),a}}static async deletePost(s){try{const r=await ss(fe.post(s),{method:"DELETE"});await ve(r)}catch(r){throw console.error("删除文章失败:",r),r}}static async searchPosts(s){const r=new URLSearchParams({q:s}),a=`${fe.search}?${r.toString()}`;try{const n=await Te(a);return(await ve(n)).posts}catch(n){throw console.error("搜索文章失败:",n),n}}}function vo({onMusicToggle:e}){const[s,r]=d.useState(!1),[a,n]=d.useState(!1),[l,i]=d.useState(!1),[o,c]=d.useState(""),[x,u]=d.useState([]),[h,p]=d.useState(!1),{theme:m,toggleTheme:k}=mo(),{isAdmin:A,logout:N}=Gt(),E=Kt(),_=d.useRef(null),K=d.useRef(null),T=[{name:"首页",href:"/"},{name:"分类",href:"/categories"},{name:"标签",href:"/tags"},{name:"关于",href:"/about"}],H=async $=>{if(!$.trim()){u([]);return}p(!0);try{const G=await je.searchPosts($.trim());u(G.slice(0,5))}catch(G){console.error("搜索失败:",G),u([])}finally{p(!1)}};return d.useEffect(()=>{const $=G=>{(G.ctrlKey||G.metaKey)&&G.key==="k"&&(G.preventDefault(),i(z=>{const O=!z;return O?setTimeout(()=>{var X;return(X=K.current)==null?void 0:X.focus()},100):(c(""),u([])),O})),G.key==="Escape"&&(i(!1),c(""),u([]))};return document.addEventListener("keydown",$),()=>document.removeEventListener("keydown",$)},[]),d.useEffect(()=>{const $=G=>{_.current&&!_.current.contains(G.target)&&(i(!1),c(""),u([]))};if(l)return document.addEventListener("mousedown",$),()=>document.removeEventListener("mousedown",$)},[l]),d.useEffect(()=>{const $=setTimeout(()=>{H(o)},300);return()=>clearTimeout($)},[o]),t.jsxs("header",{className:"sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700",children:[t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex items-center justify-between h-16",children:[t.jsxs(ae,{to:"/",className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:t.jsx("span",{className:"text-white font-bold text-sm",children:"B"})}),t.jsx("span",{className:"font-semibold text-xl text-gray-900 dark:text-white",children:"个人博客"})]}),t.jsx("nav",{className:"hidden md:flex items-center space-x-8",children:T.map($=>t.jsx(ae,{to:$.href,className:"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:$.name},$.name))}),t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("div",{className:"relative",ref:_,children:[t.jsx("button",{onClick:()=>i(!l),title:"快捷搜索 (Ctrl+K)","aria-label":"快捷搜索",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(De,{size:20})}),l&&t.jsx("div",{className:"absolute top-full mt-2 right-0 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50",children:t.jsxs("div",{className:"p-4",children:[t.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[t.jsx(De,{size:16,className:"text-gray-400"}),t.jsx("input",{ref:K,type:"text",placeholder:"搜索文章...",value:o,onChange:$=>c($.target.value),className:"flex-1 outline-none bg-transparent text-sm",onKeyDown:$=>{$.key==="Enter"&&o.trim()&&(E(`/search?q=${encodeURIComponent(o.trim())}`),i(!1),c(""))}}),t.jsxs("kbd",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded text-gray-500 dark:text-gray-400",children:[t.jsx(Al,{size:12,className:"inline mr-1"}),"K"]})]}),h&&t.jsx("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:"搜索中..."}),x.length>0&&!h&&t.jsxs("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:"搜索结果"}),x.map($=>t.jsxs(ae,{to:`/post/${$.slug}`,onClick:()=>{i(!1),c("")},className:"block p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-sm",children:[t.jsx("div",{className:"font-medium text-gray-900 dark:text-white truncate",children:$.title}),t.jsx("div",{className:"text-gray-500 dark:text-gray-400 text-xs truncate mt-1",children:$.excerpt})]},$.slug)),o.trim()&&t.jsx(ae,{to:`/search?q=${encodeURIComponent(o.trim())}`,onClick:()=>{i(!1),c("")},className:"block p-2 text-center text-blue-600 dark:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-sm",children:"查看所有结果 →"})]}),o.trim()&&x.length===0&&!h&&t.jsxs("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:["未找到相关文章",t.jsx(ae,{to:`/search?q=${encodeURIComponent(o.trim())}`,onClick:()=>{i(!1),c("")},className:"block mt-2 text-blue-600 dark:text-blue-400 hover:underline",children:"在搜索页面查看 →"})]})]})})]}),t.jsx("button",{onClick:e,title:"音乐播放器","aria-label":"打开音乐播放器",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors",children:t.jsx(Nt,{size:20})}),A?t.jsx("button",{onClick:N,title:"管理员已登录 - 点击退出","aria-label":"管理员退出",className:"p-2 text-green-600 dark:text-green-400 hover:text-red-600 dark:hover:text-red-400 transition-colors",children:t.jsx(xt,{size:20})}):t.jsx("button",{onClick:()=>n(!0),title:"管理员登录","aria-label":"管理员登录",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(xt,{size:20})}),A&&t.jsxs(ae,{to:"/write",title:"写文章",className:"hidden md:flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[t.jsx(cr,{size:16}),t.jsx("span",{children:"写文章"})]}),t.jsx("button",{onClick:k,title:m==="dark"?"切换到浅色模式":"切换到深色模式","aria-label":m==="dark"?"切换到浅色模式":"切换到深色模式",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:m==="dark"?t.jsx(io,{size:20}):t.jsx(Kl,{size:20})}),t.jsx("button",{onClick:()=>r(!s),title:s?"关闭菜单":"打开菜单","aria-label":s?"关闭菜单":"打开菜单","aria-expanded":s,className:"md:hidden p-2 text-gray-600 dark:text-gray-400",children:s?t.jsx(We,{size:20}):t.jsx(Wl,{size:20})})]})]}),s&&t.jsx("div",{className:"md:hidden py-4 border-t border-gray-200 dark:border-gray-700",children:t.jsxs("nav",{className:"space-y-2",children:[T.map($=>t.jsx(ae,{to:$.href,className:"block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors",onClick:()=>r(!1),children:$.name},$.name)),A?t.jsxs(t.Fragment,{children:[t.jsxs(ae,{to:"/write",className:"flex items-center space-x-2 px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors",onClick:()=>r(!1),children:[t.jsx(cr,{size:16}),t.jsx("span",{children:"写文章"})]}),t.jsxs("button",{onClick:()=>{N(),r(!1)},className:"flex items-center space-x-2 px-4 py-2 text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left",children:[t.jsx(Bl,{size:16}),t.jsx("span",{children:"管理员退出"})]})]}):t.jsxs("button",{onClick:()=>{n(!0),r(!1)},className:"flex items-center space-x-2 px-4 py-2 text-green-600 dark:text-green-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left",children:[t.jsx(xt,{size:16}),t.jsx("span",{children:"管理员登录"})]})]})})]}),t.jsx(dn,{isOpen:a,onClose:()=>n(!1)})]})}function jo(){return t.jsx("footer",{className:"bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700",children:t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"个人博客"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"基于 Cloudflare Workers 构建的现代个人博客系统，集成 AI 配图、天气显示等功能。"})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"快速链接"}),t.jsxs("ul",{className:"space-y-2 text-sm",children:[t.jsx("li",{children:t.jsx("a",{href:"/",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"首页"})}),t.jsx("li",{children:t.jsx("a",{href:"/about",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"关于我"})}),t.jsx("li",{children:t.jsx("a",{href:"/write",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"写文章"})})]})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"联系方式"}),t.jsxs("div",{className:"flex space-x-4",children:[t.jsx("a",{href:"https://github.com/ajkdfe2e2e",target:"_blank",rel:"noopener noreferrer",title:"GitHub","aria-label":"访问 GitHub 主页",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(Tl,{size:20})}),t.jsx("a",{href:"https://x.com/x2a1HRjxs552213",target:"_blank",rel:"noopener noreferrer",title:"Twitter","aria-label":"访问 Twitter 主页",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(oo,{size:20})}),t.jsx("a",{href:"mailto:<EMAIL>",title:"邮箱","aria-label":"发送邮件联系我",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(rn,{size:20})})]})]})]}),t.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center",children:t.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"© 2025 个人博客. 使用 Cloudflare Workers 强力驱动"})})]})})}class ha{static async getCurrentWeather(){try{let s;try{s=await this.getCurrentPosition()}catch(l){console.warn("无法获取当前位置，使用默认位置（北京）:",l),s={latitude:39.9042,longitude:116.4074}}const r=new URLSearchParams({lat:s.latitude.toString(),lon:s.longitude.toString()}),a=`${fe.weather}?${r.toString()}`,n=await Te(a);return await ve(n)}catch(s){throw console.error("获取天气信息失败:",s),s}}static async getWeatherByLocation(s,r){try{const a=new URLSearchParams({lat:s.toString(),lon:r.toString()}),n=`${fe.weather}?${a.toString()}`,l=await Te(n);return await ve(l)}catch(a){throw console.error("获取天气信息失败:",a),a}}static getCurrentPosition(){return new Promise((s,r)=>{if(!navigator.geolocation){r(new Error("该浏览器不支持地理定位"));return}const a={enableHighAccuracy:!0,timeout:5e3,maximumAge:3e5};navigator.geolocation.getCurrentPosition(n=>{s({latitude:n.coords.latitude,longitude:n.coords.longitude})},n=>{let l="获取位置失败";switch(n.code){case n.PERMISSION_DENIED:l="用户拒绝了位置请求";break;case n.POSITION_UNAVAILABLE:l="位置信息不可用";break;case n.TIMEOUT:l="获取位置超时";break}r(new Error(l))},a)})}static isGeolocationSupported(){return"geolocation"in navigator}}function ko(){const[e,s]=d.useState(null),[r,a]=d.useState(!0),[n,l]=d.useState(null);return d.useEffect(()=>{(async()=>{try{if(a(!0),l(null),!ha.isGeolocationSupported())throw new Error("浏览器不支持地理定位");const o=await ha.getCurrentWeather();s(o)}catch(o){console.error("获取天气信息失败:",o),l(o instanceof Error?o.message:"获取天气信息失败")}finally{a(!1)}})()},[]),r?t.jsx("div",{className:"card p-4",children:t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"animate-spin",children:t.jsx(Sl,{size:20,className:"text-gray-400"})}),t.jsxs("div",{children:[t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-1"}),t.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"})]})]})}):n||!e?t.jsx("div",{className:"card p-4",children:t.jsxs("div",{className:"flex items-center space-x-3 text-gray-500 dark:text-gray-400",children:[t.jsx(ua,{size:20}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm",children:"位置信息"}),t.jsx("p",{className:"text-xs",children:"获取失败"})]})]})}):t.jsx("div",{className:"card p-4",children:t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center",children:t.jsx(lo,{size:20,className:"text-white"})})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(ua,{size:14,className:"text-gray-500 dark:text-gray-400"}),t.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.location})]}),t.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[t.jsx("span",{className:"text-lg font-semibold text-blue-600 dark:text-blue-400",children:e.temperature}),t.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.weather})]})]})]})})}function wo(){const[e,s]=d.useState([]),[r,a]=d.useState([]),[n,l]=d.useState(!0);return d.useEffect(()=>{(async()=>{try{l(!0);const o=await je.getPosts(1,5);s(o.posts);const c=await je.getPosts(1,100),x=new Set;c.posts.forEach(u=>{u.tags.forEach(h=>x.add(h))}),a(Array.from(x))}catch(o){console.error("加载侧边栏数据失败:",o),s([]),a([])}finally{l(!1)}})()},[]),t.jsxs("div",{className:"sticky top-8 space-y-6",children:[t.jsx(ko,{}),t.jsxs("div",{className:"card p-4",children:[t.jsx("h3",{className:"font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"最新文章"}),t.jsx("div",{className:"space-y-2",children:n?Array.from({length:3}).map((i,o)=>t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"},o)):e.length>0?e.map(i=>t.jsx(ae,{to:`/post/${i.slug}`,className:"block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors line-clamp-2",title:i.title,children:i.title},i.id)):t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"暂无文章"})})]}),t.jsxs("div",{className:"card p-4",children:[t.jsx("h3",{className:"font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"标签"}),t.jsx("div",{className:"flex flex-wrap gap-2",children:n?Array.from({length:6}).map((i,o)=>t.jsx("div",{className:"h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"},o)):r.length>0?r.map(i=>t.jsx(ae,{to:`/tag/${encodeURIComponent(i)}`,className:"px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-blue-600 hover:text-white transition-colors cursor-pointer",children:i},i)):t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"暂无标签"})})]})]})}function No(){const e=jt(),s=qt(),[r,a]=d.useState([]),[n,l]=d.useState(null);return d.useEffect(()=>{(async()=>{if(e.pathname.startsWith("/post/")&&s.slug)try{const o=await je.getPost(s.slug);l({title:o.title,category:o.category})}catch(o){console.error("获取文章详情失败:",o),l({title:"文章详情",category:void 0})}})()},[e.pathname,s.slug]),d.useEffect(()=>{a((()=>{const o=e.pathname,c=[{label:"首页",href:"/"}];return o==="/"?[{label:"首页"}]:(o==="/categories"?c.push({label:"分类"}):o.startsWith("/category/")?(c.push({label:"分类",href:"/categories"}),c.push({label:decodeURIComponent(s.category||"")})):o==="/tags"?c.push({label:"标签"}):o.startsWith("/tag/")?(c.push({label:"标签",href:"/tags"}),c.push({label:decodeURIComponent(s.tag||"")})):o==="/search"?c.push({label:"搜索"}):o==="/about"?c.push({label:"关于"}):o==="/write"?c.push({label:"写文章"}):o.startsWith("/write/")?(c.push({label:"写文章",href:"/write"}),c.push({label:"编辑文章"})):o.startsWith("/post/")&&(n?(n.category&&(c.push({label:"分类",href:"/categories"}),c.push({label:n.category,href:`/category/${encodeURIComponent(n.category)}`})),c.push({label:n.title})):c.push({label:"文章详情"})),c)})())},[e.pathname,s,n]),e.pathname==="/"?null:t.jsx("nav",{"aria-label":"面包屑导航",className:"mb-6",children:t.jsx("ol",{className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400",children:r.map((i,o)=>t.jsxs("li",{className:"flex items-center",children:[o>0&&t.jsx(tn,{size:16,className:"mr-2 text-gray-400"}),i.href?t.jsxs(ae,{to:i.href,className:"flex items-center hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[o===0&&t.jsx(oa,{size:16,className:"mr-1"}),t.jsx("span",{className:"truncate max-w-[200px]",title:i.label,children:i.label})]}):t.jsxs("span",{className:"flex items-center text-gray-900 dark:text-gray-100",children:[o===0&&t.jsx(oa,{size:16,className:"mr-1"}),t.jsx("span",{className:"truncate max-w-[200px]",title:i.label,children:i.label})]})]},o))})})}function Eo({children:e,onMusicToggle:s}){return t.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors",children:[t.jsx(vo,{onMusicToggle:s}),t.jsx("main",{className:"container mx-auto px-4 py-8 max-w-6xl",children:t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[t.jsxs("div",{className:"lg:col-span-3",children:[t.jsx(No,{}),e]}),t.jsx("aside",{className:"lg:col-span-1",children:t.jsx(wo,{})})]})}),t.jsx(jo,{})]})}const ga=(e,s,r)=>{if(console.log(`[Music Player] 处理图片URL: ${e} (歌曲: ${s})`),!e)return console.log("[Music Player] 图片URL为空，使用默认图片"),"/default-album.jpg";if(r!=null&&r.has(e))return console.log(`[Music Player] URL之前加载失败过，使用默认图片: ${e}`),"/default-album.jpg";let a=e;return a.startsWith("http:")&&(console.log(`[Music Player] 转换HTTP到HTTPS: ${a}`),a.match(/http:\/\/p\d+\.music\.126\.net/)?a=a.replace(/^http:\/\/p(\d+)\.music\.126\.net/,"https://p$1.music.126.net"):a.match(/http:\/\/y\.gtimg\.cn/)?a=a.replace(/^http:\/\//,"https://"):a=a.replace("http:","https:"),console.log(`[Music Player] 转换后URL: ${a}`)),!a||a==="null"||a==="undefined"?(console.log(`[Music Player] URL无效，使用默认图片: ${a}`),"/default-album.jpg"):(console.log(`[Music Player] 最终图片URL: ${a}`),a)},So=d.forwardRef((e,s)=>{var _e,P;const[r,a]=d.useState(!1),[n,l]=d.useState(null),[i,o]=d.useState([]),[c,x]=d.useState(0),[u,h]=d.useState(!1),[p,m]=d.useState(!1),[k,A]=d.useState(0),[N,E]=d.useState(0),[_,K]=d.useState(.7),[T,H]=d.useState(!1),[$,G]=d.useState(""),[z,O]=d.useState([]),[X,Z]=d.useState(!1),[Y,ue]=d.useState([]),[le,ce]=d.useState(-1),[w,f]=d.useState([]),[j,b]=d.useState("auto"),[S,L]=d.useState(!1),[q,Q]=d.useState(""),C=d.useRef(null),ne=d.useRef(null),[oe,ke]=d.useState(new Set);d.useImperativeHandle(s,()=>({toggleVisibility:()=>{h(!u),m(!0)}})),d.useEffect(()=>{(async()=>{try{const v=await(await fetch(`${fe.music.search.replace("/search","/sources")}`)).json();if(v&&v.code===200&&Array.isArray(v.sources))f(v.sources);else throw new Error("Invalid response format")}catch(y){console.error("Error fetching music sources:",y),f([{type:"wyy",name:"网易云音乐",enabled:!0,priority:1},{type:"qq",name:"QQ音乐",enabled:!0,priority:2},{type:"kg",name:"酷狗音乐",enabled:!0,priority:3},{type:"kw",name:"酷我音乐",enabled:!0,priority:4},{type:"xmla",name:"喜马拉雅",enabled:!0,priority:5},{type:"qishui",name:"汽水音乐",enabled:!0,priority:6},{type:"qt",name:"蜻蜓FM",enabled:!0,priority:7}])}})()},[]),d.useEffect(()=>{let g=window.innerWidth;const y=()=>{const v=window.innerWidth;if(Math.abs(v-g)<50)return;const I=v<640;g<640!==I&&(I&&!n&&u&&!p?h(!1):!I&&!u&&h(!0)),g=v};return window.addEventListener("resize",y),()=>window.removeEventListener("resize",y)},[n,u]),d.useEffect(()=>{const g=C.current;if(!g)return;const y=()=>A(g.currentTime),v=()=>E(g.duration),I=()=>B();return g.addEventListener("timeupdate",y),g.addEventListener("durationchange",v),g.addEventListener("ended",I),()=>{g.removeEventListener("timeupdate",y),g.removeEventListener("durationchange",v),g.removeEventListener("ended",I)}},[]),d.useEffect(()=>{C.current&&(C.current.volume=T?0:_)},[_,T]),d.useEffect(()=>{var y;if(!Y.length)return;const g=Y.findIndex((v,I)=>{const V=Y[I+1];return k>=v.time&&(!V||k<V.time)});if(g!==-1&&g!==le){ce(g);const v=(y=ne.current)==null?void 0:y.children[g];v&&v.scrollIntoView({behavior:"smooth",block:"center"})}},[k,Y,le]);const de=async g=>{if(g.trim()){Z(!0);try{let y=`${fe.music.search}?keywords=${encodeURIComponent(g)}`;j!=="auto"&&(y+=`&source=${j}`);const v=await fetch(y);if(!v.ok)throw new Error(`HTTP error! status: ${v.status}`);const I=await v.json();if(console.log("[Music Player] 后端返回的原始数据:",I),I.code===200&&I.result&&Array.isArray(I.result.songs)){const V=I.result.songs.map(J=>{var et,Hr,Br,Wr,Fr,Kr,qr,Gr,Vr,Jr,Xr,Qr;console.log("[Music Player] 处理单首歌曲数据:",J);const Ee={id:((et=J.album)==null?void 0:et.id)||((Hr=J.al)==null?void 0:Hr.id)||0,name:((Br=J.album)==null?void 0:Br.name)||((Wr=J.al)==null?void 0:Wr.name)||"未知专辑",picUrl:((Fr=J.album)==null?void 0:Fr.picUrl)||((Kr=J.al)==null?void 0:Kr.picUrl)||"/default-album.jpg"};return console.log("[Music Player] 提取的专辑信息:",Ee),{id:J.id,name:J.name||"未知歌曲",artists:Array.isArray(J.artists)?J.artists:Array.isArray(J.ar)?J.ar:[{id:0,name:"未知艺术家"}],album:Ee,duration:J.duration||J.dt||0,sourceType:J.sourceType||I.source,sourceName:J.sourceName||I.sourceName,mid:((qr=J.originalData)==null?void 0:qr.mid)||J.mid,media_mid:((Gr=J.originalData)==null?void 0:Gr.media_mid)||J.media_mid,vid:((Vr=J.originalData)==null?void 0:Vr.vid)||J.vid,hash:((Jr=J.originalData)==null?void 0:Jr.hash)||J.hash,album_id:((Xr=J.originalData)==null?void 0:Xr.album_id)||J.album_id,album_audio_id:((Qr=J.originalData)==null?void 0:Qr.album_audio_id)||J.album_audio_id}});if(O(V),I.sourceName)Q(`搜索来源：${I.sourceName}`);else if(j!=="auto"){const J=w.find(Ee=>Ee.type===j);Q(`搜索来源：${(J==null?void 0:J.name)||j}`)}else Q("多源智能搜索")}else O([]),Q("搜索无结果")}catch(y){console.error("Error searching music:",y),O([]),Q("搜索失败，请检查网络连接")}finally{Z(!1)}}},ge=async g=>{var y,v;try{let I=`${fe.music.songUrl}?`;const V=new URLSearchParams;switch(g.sourceType&&V.append("source",g.sourceType),g.sourceType){case"qq":if(g.mid&&g.media_mid&&g.vid)V.append("mid",g.mid),V.append("media_mid",g.media_mid),V.append("vid",g.vid);else return console.error("QQ音乐缺少必要参数:",g),null;break;case"kg":if(g.hash&&g.album_id&&g.album_audio_id)V.append("hash",g.hash),V.append("album_id",g.album_id),V.append("album_audio_id",g.album_audio_id);else return console.error("酷狗音乐缺少必要参数:",g),null;break;default:V.append("id",g.id.toString());break}I+=V.toString(),console.log("调用URL获取接口:",I);const Ee=await(await fetch(I)).json();return console.log("URL API响应:",Ee),Ee.code===200&&((v=(y=Ee.data)==null?void 0:y[0])!=null&&v.url)?Ee.data[0].url:null}catch(I){return console.error("Error fetching song URL:",I),null}},we=async g=>{try{let y=`${fe.music.lyric}?`;const v=new URLSearchParams;switch(g.sourceType&&v.append("source",g.sourceType),g.sourceType){case"qq":v.append("id",g.id.toString());break;case"kg":if(g.hash)v.append("hash",g.hash);else{console.error("酷狗音乐歌词缺少hash参数:",g),ue([]);return}break;default:v.append("id",g.id.toString());break}y+=v.toString(),console.log("调用歌词获取接口:",y);const V=await(await fetch(y)).json();if(console.log("歌词API响应:",V),V.code===200&&V.lrc&&V.lrc.lyric){const J=be(V.lrc.lyric);ue(J)}else ue([])}catch(y){console.error("Error fetching lyrics:",y),ue([])}},be=g=>g.split(`
`).map(y=>{const v=y.match(/\[(\d{2}):(\d{2})\.(\d{2,3})\](.*)/);if(!v)return null;const I=parseInt(v[1]),V=parseInt(v[2]),J=parseInt(v[3].padEnd(3,"0")),Ee=I*60+V+J/1e3,et=v[4].trim();return et?{time:Ee,text:et}:null}).filter(Boolean),Oe=async(g,y)=>{if(console.log("Playing song:",g),y!==void 0)x(y),o(z);else{const v=z.findIndex(I=>I.id===g.id&&I.sourceType===g.sourceType);v!==-1?(x(v),o(z)):(o([g]),x(0))}l(g);try{const v=await ge(g);if(v){if(C.current){let I=v;v.startsWith("http://")&&(v.match(/http:\/\/m\d+\.music\.126\.net/)?I=v.replace(/^http:\/\/m(\d+)\.music\.126\.net/,"https://m$1.music.126.net"):v.match(/http:\/\/isure\.stream\.qqmusic\.qq\.com/)?I=v.replace(/^http:\/\//,"https://"):I="https://"+v.substring(7)),C.current.src=I,C.current.play(),a(!0),we(g),g.sourceName&&Q(`播放来源：${g.sourceName}`)}}else console.error("Failed to get song URL"),Q("播放链接获取失败")}catch(v){console.error("Error playing song:",v),Q("播放失败")}},Pe=()=>{C.current&&(r?C.current.pause():C.current.play(),a(!r))},B=()=>{if(i.length===0)return;const g=(c+1)%i.length;Oe(i[g],g)},W=()=>{if(i.length===0)return;const g=(c-1+i.length)%i.length;Oe(i[g],g)},ee=g=>{const y=parseFloat(g.target.value);A(y),C.current&&(C.current.currentTime=y)},ie=()=>{C.current&&C.current.removeEventListener("timeupdate",()=>A(C.current.currentTime))},M=()=>{C.current&&C.current.addEventListener("timeupdate",()=>A(C.current.currentTime))},F=g=>{const y=parseFloat(g.target.value);K(y),H(!1)},re=()=>{H(!T)},U=g=>{const y=Math.floor(g/60),v=Math.floor(g%60);return`${y}:${v.toString().padStart(2,"0")}`},te=()=>{L(!S)},Ne=g=>{if(b(g),L(!1),g==="auto")Q("多源智能搜索");else{const y=w.find(v=>v.type===g);Q(`当前音源：${(y==null?void 0:y.name)||g}`)}},Se=(g,y)=>{const v=g.currentTarget,I=v.src;console.error("[Music Player] 图片加载失败:",{songName:y.name,originalUrl:y.album.picUrl,currentSrc:I,sourceName:y.sourceName}),v.onerror=null,ke(V=>new Set(V).add(I)),I.includes("default-album.jpg")?(console.error(`[Music Player] 连默认图片都加载失败了: ${y.name}`),v.style.backgroundColor="#4b5563",v.style.display="flex",v.style.alignItems="center",v.style.justifyContent="center"):(console.log(`[Music Player] 回退到默认图片: ${y.name}`),v.src="/default-album.jpg")};return t.jsxs(t.Fragment,{children:[t.jsx("audio",{ref:C}),t.jsx("div",{className:`fixed bottom-0 right-0 z-50 transition-transform duration-300 ${u?"translate-y-0":"translate-y-full"}`,children:t.jsxs("div",{className:"w-screen sm:w-[450px] sm:right-4 sm:bottom-4 sm:mb-0 bg-gray-800/95 backdrop-blur-md text-white sm:rounded-lg shadow-lg flex flex-col h-[60vh] sm:h-[500px] sm:max-h-[80vh]",children:[t.jsxs("div",{className:"p-3 sm:p-4 flex justify-between items-center border-b border-gray-700",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsxs("h2",{className:"text-base sm:text-lg font-bold flex items-center",children:[t.jsx(Nt,{className:"mr-2",size:20})," 音乐播放器"]}),q&&t.jsx("span",{className:"text-xs text-purple-400 bg-purple-500/20 px-2 py-1 rounded-full",children:q})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsxs("div",{className:"relative",children:[t.jsx("button",{onClick:te,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"音源设置",children:t.jsx(zl,{size:18})}),S&&t.jsx("div",{className:"absolute top-full right-0 mt-1 bg-gray-700 rounded-lg shadow-lg border border-gray-600 z-10 min-w-48",children:t.jsxs("div",{className:"p-2",children:[t.jsx("div",{className:"text-xs text-gray-400 mb-2",children:"选择音源"}),t.jsx("button",{onClick:()=>Ne("auto"),className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${j==="auto"?"bg-purple-600 text-white":"hover:bg-gray-600"}`,children:"🔄 智能多源 (推荐)"}),t.jsx("div",{className:"border-t border-gray-600 my-2"}),w.map(g=>t.jsxs("button",{onClick:()=>Ne(g.type),disabled:!g.enabled,className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${j===g.type?"bg-purple-600 text-white":g.enabled?"hover:bg-gray-600":"text-gray-500 cursor-not-allowed"}`,children:[g.name,!g.enabled&&" (禁用)"]},g.type))]})})]}),t.jsx("button",{onClick:()=>{h(!1),m(!0)},className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"收起播放器",children:t.jsx(gt,{size:20})})]})]}),t.jsxs("div",{className:"flex flex-col sm:flex-row flex-grow overflow-hidden",children:[t.jsxs("div",{className:"w-full sm:w-1/2 flex flex-col border-b sm:border-b-0 sm:border-r border-gray-700",children:[t.jsxs("div",{className:"p-3 flex-shrink-0",children:[t.jsxs("form",{onSubmit:g=>{g.preventDefault(),de($)},className:"flex",children:[t.jsx("input",{type:"text",value:$,onChange:g=>G(g.target.value),placeholder:"搜索歌曲、歌手...",className:"flex-grow bg-gray-700/80 border border-gray-600 rounded-l-md px-3 py-2.5 sm:py-1.5 focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm touch-target"}),t.jsx("button",{type:"submit",className:"bg-purple-600 hover:bg-purple-700 px-4 sm:px-3 py-2.5 sm:py-1.5 rounded-r-md touch-target",title:"搜索",children:t.jsx(De,{size:18})})]}),t.jsx("div",{className:"mt-2 text-xs text-gray-400",children:j==="auto"?t.jsx("span",{children:"🔄 多源智能搜索"}):t.jsxs("span",{children:["🎵 ",((_e=w.find(g=>g.type===j))==null?void 0:_e.name)||j]})})]}),t.jsx("div",{className:"flex-grow overflow-y-auto px-1 mb-2",style:{maxHeight:"calc(60vh - 200px)"},children:X?t.jsx("p",{className:"p-4 text-center text-gray-400",children:"正在搜索..."}):z.length>0?z.map((g,y)=>t.jsxs("div",{className:"p-2 flex items-center gap-3 hover:bg-white/10 rounded-lg cursor-pointer transition-colors",onClick:()=>Oe(g,y),children:[t.jsx("img",{src:ga(g.album.picUrl,g.name,oe),alt:g.album.name||"专辑封面",className:"w-12 h-12 rounded-md object-cover bg-gray-700",onError:v=>Se(v,g)}),t.jsxs("div",{className:"flex-grow overflow-hidden",children:[t.jsx("p",{className:"font-semibold text-sm truncate",children:g.name}),t.jsxs("p",{className:"text-xs text-gray-400 truncate",children:[Array.isArray(g.artists)?g.artists.map(v=>v.name).join(" / "):"未知艺术家",t.jsxs("span",{className:"text-purple-400 ml-2",children:["• ",g.sourceName]})]})]})]},`${g.id}-${g.sourceType}-${y}`)):$?t.jsx("p",{className:"p-4 text-center text-gray-400",children:"没有找到相关歌曲"}):t.jsx("div",{className:"p-4 text-center text-gray-500",children:t.jsx("p",{className:"text-sm",children:"开始搜索音乐"})})})]}),t.jsx("div",{className:"w-full sm:w-1/2 flex flex-col p-3 min-h-0",children:n?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"text-center pb-3 border-b border-gray-700 mb-3 flex-shrink-0",children:t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx("img",{src:ga(n.album.picUrl,n.name,oe),alt:n.album.name,className:"w-16 h-16 rounded-lg shadow-lg",onError:g=>Se(g,n)}),t.jsxs("div",{className:"flex flex-col",children:[t.jsx("h3",{className:"text-lg font-bold",children:n.name||"未知歌曲"}),t.jsx("p",{className:"text-sm text-gray-400",children:Array.isArray(n.artists)&&n.artists.length>0?n.artists.map(g=>g.name).join(" / "):"未知艺术家"})]})]})}),t.jsx("div",{ref:ne,className:"flex-grow overflow-y-auto text-center space-y-2 text-gray-300 px-2",style:{maxHeight:"calc(60vh - 240px)"},children:Y.length>0?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"sm:hidden",children:t.jsx("p",{className:"text-sm text-purple-400 font-medium text-center py-2",children:le>=0&&Y[le]?Y[le].text:((P=Y[0])==null?void 0:P.text)||"♪ ♪"})}),t.jsx("div",{className:"hidden sm:block",children:Y.map((g,y)=>t.jsx("p",{className:`transition-all duration-300 text-sm leading-relaxed ${y===le?"text-purple-400 font-bold scale-105":"hover:text-gray-200"}`,children:g.text},y))})]}):t.jsx("p",{className:"text-gray-500 text-sm",children:n?"暂无歌词":"♪ ♪"})})]}):t.jsxs("div",{className:"flex-grow flex flex-col items-center justify-center text-gray-500 px-4",children:[t.jsx(Nt,{size:48,className:"mb-4 opacity-50 hidden sm:block"}),t.jsx(Nt,{size:32,className:"mb-2 opacity-50 sm:hidden"}),t.jsx("p",{className:"text-center text-sm sm:text-base",children:"选择音乐开始播放"}),t.jsx("p",{className:"text-xs mt-2 text-gray-600 text-center hidden sm:block",children:"歌词和音乐信息将在此显示"}),t.jsx("p",{className:"text-xs mt-1 text-gray-600 text-center sm:hidden",children:"歌词将在此显示"})]})})]}),t.jsxs("div",{className:"p-3 sm:p-3 border-t border-gray-700 bg-gray-800/95",children:[t.jsxs("div",{className:"mb-3",children:[t.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400 mb-1",children:[t.jsx("span",{children:U(k)}),t.jsx("span",{children:U(N)})]}),t.jsx("input",{type:"range",value:k,max:N||0,onChange:ee,onTouchStart:ie,onMouseDown:ie,onTouchEnd:M,onMouseUp:M,className:"w-full music-slider progress-slider",title:"播放进度",style:{"--progress":`${k/(N||1)*100}%`}})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("button",{onClick:re,className:"p-2 hover:bg-white/10 rounded-full transition-colors",children:T||_===0?t.jsx(go,{size:20}):t.jsx(ho,{size:20})}),t.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:T?0:_,onChange:F,className:"volume-slider w-24","aria-label":"音量控制"})]}),t.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[t.jsx("button",{onClick:W,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"上一首",children:t.jsx(ro,{size:20})}),t.jsx("button",{onClick:Pe,className:"bg-purple-600 hover:bg-purple-700 rounded-full p-3 text-white touch-target transition-colors",title:r?"暂停":"播放",children:r?t.jsx(Jl,{size:24}):t.jsx(Xl,{size:24})}),t.jsx("button",{onClick:B,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"下一首",children:t.jsx(ao,{size:20})})]}),t.jsx("div",{className:"w-24 sm:w-20"})]})]})]})}),!u&&t.jsx("div",{className:"fixed bottom-4 right-4 z-40",children:t.jsx("button",{onClick:()=>{h(!0),m(!0)},className:"bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110 touch-target",title:"打开音乐播放器",children:t.jsx(Nt,{size:24})})})]})});function Co({onFilterChange:e,availableCategories:s,availableTags:r}){var N;const[a,n]=d.useState({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),[l,i]=d.useState(!1),[o,c]=d.useState(!1),[x,u]=d.useState(!1),h=[{value:"date",label:"发布时间",icon:Ae},{value:"readTime",label:"阅读时长",icon:Ie},{value:"featured",label:"推荐程度",icon:ls}];d.useEffect(()=>{e(a)},[a,e]);const p=(E,_)=>{n(K=>({...K,[E]:_}))},m=E=>{const _=E==="sortBy"?"date":E==="sortOrder"?"desc":"";n(K=>({...K,[E]:_}))},k=()=>{n({category:"",tag:"",sortBy:"date",sortOrder:"desc"})},A=a.category||a.tag||a.sortBy!=="date"||a.sortOrder!=="desc";return t.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-6",children:[t.jsxs("div",{className:"flex items-center justify-between mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Ml,{size:20,className:"text-gray-500 dark:text-gray-400"}),t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"筛选文章"})]}),A&&t.jsx("button",{onClick:k,className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:"清除所有筛选"})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[t.jsxs("div",{className:"relative",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"分类"}),t.jsxs("button",{onClick:()=>i(!l),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[t.jsx("span",{className:a.category?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:a.category||"选择分类"}),t.jsx(gt,{size:16})]}),l&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[t.jsx("button",{onClick:()=>{p("category",""),i(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"全部分类"}),s.map(E=>t.jsx("button",{onClick:()=>{p("category",E),i(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:E},E))]})}),a.category&&t.jsx("button",{onClick:()=>m("category"),"aria-label":"清除分类筛选",className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:t.jsx(We,{size:14})})]}),t.jsxs("div",{className:"relative",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标签"}),t.jsxs("button",{onClick:()=>c(!o),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[t.jsx("span",{className:a.tag?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:a.tag||"选择标签"}),t.jsx(gt,{size:16})]}),o&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[t.jsx("button",{onClick:()=>{p("tag",""),c(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"全部标签"}),r.map(E=>t.jsx("button",{onClick:()=>{p("tag",E),c(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:E},E))]})}),a.tag&&t.jsx("button",{onClick:()=>m("tag"),"aria-label":"清除标签筛选",className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:t.jsx(We,{size:14})})]}),t.jsxs("div",{className:"relative",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"排序方式"}),t.jsxs("button",{onClick:()=>u(!x),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[t.jsx("span",{className:"text-gray-900 dark:text-white",children:(N=h.find(E=>E.value===a.sortBy))==null?void 0:N.label}),t.jsx(gt,{size:16})]}),x&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsx("div",{className:"py-1",children:h.map(E=>{const _=E.icon;return t.jsxs("button",{onClick:()=>{p("sortBy",E.value),u(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2",children:[t.jsx(_,{size:16}),t.jsx("span",{children:E.label})]},E.value)})})})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"排序顺序"}),t.jsxs("div",{className:"flex space-x-2",children:[t.jsx("button",{onClick:()=>p("sortOrder","desc"),className:`flex-1 px-3 py-2 text-sm rounded-md border ${a.sortOrder==="desc"?"bg-blue-600 text-white border-blue-600":"bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"}`,children:"倒序"}),t.jsx("button",{onClick:()=>p("sortOrder","asc"),className:`flex-1 px-3 py-2 text-sm rounded-md border ${a.sortOrder==="asc"?"bg-blue-600 text-white border-blue-600":"bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"}`,children:"正序"})]})]})]})]})}const st=class st{constructor(){Ks(this,"serviceWorkerRegistration",null);this.initServiceWorker()}static getInstance(){return st.instance||(st.instance=new st),st.instance}async initServiceWorker(){if("serviceWorker"in navigator)try{this.serviceWorkerRegistration=await navigator.serviceWorker.register("/sw.js"),console.log("[缓存管理] Service Worker 注册成功"),this.serviceWorkerRegistration.addEventListener("updatefound",()=>{console.log("[缓存管理] Service Worker 更新可用")})}catch(s){console.error("[缓存管理] Service Worker 注册失败:",s)}}async preloadImages(s){if(!this.serviceWorkerRegistration||!this.serviceWorkerRegistration.active)return console.warn("[缓存管理] Service Worker 未激活，使用浏览器预加载"),this.browserPreloadImages(s);try{const r=new MessageChannel,a=new Promise(n=>{r.port1.onmessage=l=>{n(l.data.success)}});return this.serviceWorkerRegistration.active.postMessage({type:"PRELOAD_IMAGES",urls:s},[r.port2]),await a}catch(r){return console.error("[缓存管理] 预加载失败:",r),!1}}async browserPreloadImages(s){try{const r=s.map(a=>new Promise((n,l)=>{const i=new Image;i.onload=()=>n(),i.onerror=()=>l(new Error(`Failed to load ${a}`)),i.src=a}));return await Promise.allSettled(r),console.log("[缓存管理] 浏览器预加载完成"),!0}catch(r){return console.error("[缓存管理] 浏览器预加载失败:",r),!1}}async clearImageCache(){if(!this.serviceWorkerRegistration||!this.serviceWorkerRegistration.active)return console.warn("[缓存管理] Service Worker 未激活，无法清理缓存"),!1;try{const s=new MessageChannel,r=new Promise(a=>{s.port1.onmessage=n=>{a(n.data.success)}});return this.serviceWorkerRegistration.active.postMessage({type:"CLEAR_IMAGE_CACHE"},[s.port2]),await r}catch(s){return console.error("[缓存管理] 清理缓存失败:",s),!1}}async getCacheSize(){if("storage"in navigator&&"estimate"in navigator.storage)try{return(await navigator.storage.estimate()).usage||0}catch(s){return console.error("[缓存管理] 获取缓存大小失败:",s),0}return 0}async checkCacheQuota(){if("storage"in navigator&&"estimate"in navigator.storage)try{const s=await navigator.storage.estimate(),r=s.usage||0,a=s.quota||0,n=a-r,l=a>0?r/a*100:0;return{used:r,available:n,percentage:l}}catch(s){console.error("[缓存管理] 检查缓存配额失败:",s)}return{used:0,available:0,percentage:0}}async smartPreload(s){try{const r=[],a=this.getFeaturedImages();r.push(...a),r.length>0&&(console.log("[缓存管理] 开始智能预加载:",r.length,"张图片"),await this.preloadImages(r))}catch(r){console.error("[缓存管理] 智能预加载失败:",r)}}getFeaturedImages(){try{const s=localStorage.getItem("featured_images");return s?JSON.parse(s):[]}catch{return[]}}cacheFeaturedImages(s){try{localStorage.setItem("featured_images",JSON.stringify(s))}catch(r){console.error("[缓存管理] 缓存特色图片列表失败:",r)}}adaptToNetworkCondition(){if("connection"in navigator){const s=navigator.connection;if(s){const{effectiveType:r,downlink:a}=s;if(r==="slow-2g"||r==="2g"||a<.5)return console.log("[缓存管理] 检测到慢速网络，减少预加载"),"conservative";if(r==="4g"&&a>2)return console.log("[缓存管理] 检测到快速网络，增加预加载"),"aggressive"}}return"normal"}setupNetworkListener(){if("connection"in navigator){const s=navigator.connection;s&&s.addEventListener("change",()=>{const r=this.adaptToNetworkCondition();console.log("[缓存管理] 网络状态变化，调整策略为:",r)})}window.addEventListener("online",()=>{console.log("[缓存管理] 网络已连接")}),window.addEventListener("offline",()=>{console.log("[缓存管理] 网络已断开")})}};Ks(st,"instance");let dr=st;const ur=dr.getInstance();function Ao({posts:e,onSearchResults:s,placeholder:r="搜索文章...",className:a=""}){const[n,l]=d.useState(""),[i,o]=d.useState(!1),[c,x]=d.useState([]),[u,h]=d.useState(-1),p=d.useRef(null),m=d.useRef(null),k=T=>{if(!T.trim())return[];const H=T.toLowerCase(),$=[];return e.forEach(G=>{if(G.title.toLowerCase().includes(H)){$.push({...G,matchType:"title",matchText:G.title});return}if(G.excerpt.toLowerCase().includes(H)){$.push({...G,matchType:"content",matchText:G.excerpt});return}const z=G.tags.find(O=>O.toLowerCase().includes(H));if(z){$.push({...G,matchType:"tag",matchText:z});return}G.category&&G.category.toLowerCase().includes(H)&&$.push({...G,matchType:"category",matchText:G.category})}),$.slice(0,8)};d.useEffect(()=>{const T=k(n);x(T),h(-1),n.trim()?s(T):s(e)},[n,e,s]);const A=T=>{var H;if(i)switch(T.key){case"ArrowDown":T.preventDefault(),h($=>$<c.length-1?$+1:$);break;case"ArrowUp":T.preventDefault(),h($=>$>0?$-1:-1);break;case"Enter":T.preventDefault(),u>=0&&c[u]&&N(c[u]);break;case"Escape":o(!1),(H=m.current)==null||H.blur();break}},N=T=>{l(T.title),o(!1),s([T])},E=()=>{l(""),o(!1),s(e)};d.useEffect(()=>{const T=H=>{p.current&&!p.current.contains(H.target)&&o(!1)};return document.addEventListener("mousedown",T),()=>document.removeEventListener("mousedown",T)},[]);const _=T=>{switch(T){case"title":return t.jsx(De,{size:14,className:"text-blue-500"});case"content":return t.jsx(De,{size:14,className:"text-green-500"});case"tag":return t.jsx(ft,{size:14,className:"text-purple-500"});case"category":return t.jsx(Ae,{size:14,className:"text-orange-500"});default:return t.jsx(De,{size:14,className:"text-gray-500"})}},K=(T,H)=>{if(!H.trim())return T;const $=new RegExp(`(${H})`,"gi");return T.split($).map((z,O)=>$.test(z)?t.jsx("mark",{className:"bg-yellow-200 dark:bg-yellow-800 px-1 rounded",children:z},O):z)};return t.jsxs("div",{ref:p,className:`relative ${a}`,children:[t.jsxs("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(De,{size:20,className:"text-gray-400"})}),t.jsx("input",{ref:m,type:"text",value:n,onChange:T=>l(T.target.value),onFocus:()=>o(!0),onKeyDown:A,placeholder:r,className:`w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                   focus:ring-2 focus:ring-blue-500 focus:border-transparent
                   placeholder-gray-500 dark:placeholder-gray-400
                   transition-all duration-200`}),n&&t.jsx("button",{onClick:E,className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:t.jsx(We,{size:20})})]}),i&&n.trim()&&t.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 
                      border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto`,children:c.length>0?t.jsxs("div",{className:"py-2",children:[t.jsxs("div",{className:"px-4 py-2 text-xs text-gray-500 dark:text-gray-400 border-b border-gray-100 dark:border-gray-700",children:["找到 ",c.length," 个结果"]}),c.map((T,H)=>t.jsx("button",{onClick:()=>N(T),className:`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 
                           transition-colors border-b border-gray-50 dark:border-gray-700 last:border-b-0
                           ${H===u?"bg-blue-50 dark:bg-blue-900/20":""}`,children:t.jsxs("div",{className:"flex items-start space-x-3",children:[t.jsx("div",{className:"flex-shrink-0 mt-1",children:_(T.matchType)}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:K(T.title,n)}),t.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[T.matchType==="title"&&"标题匹配",T.matchType==="content"&&"内容匹配",T.matchType==="tag"&&`标签: ${T.matchText}`,T.matchType==="category"&&`分类: ${T.matchText}`]}),t.jsxs("div",{className:"flex items-center space-x-3 text-xs text-gray-400 mt-1",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:10}),t.jsx("span",{children:T.date})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:10}),t.jsxs("span",{children:[T.readTime,"分钟"]})]})]})]})]})},T.id))]}):t.jsxs("div",{className:"px-4 py-8 text-center text-gray-500 dark:text-gray-400",children:[t.jsx(De,{size:32,className:"mx-auto mb-2 opacity-50"}),t.jsx("p",{children:"没有找到匹配的文章"}),t.jsx("p",{className:"text-xs mt-1",children:"试试其他关键词"})]})})]})}function Ro({currentPage:e,totalPages:s,totalItems:r,itemsPerPage:a,onPageChange:n,showInfo:l=!0,className:i=""}){if(s<=1)return null;const c=(()=>{const h=[];if(s<=7)for(let m=1;m<=s;m++)h.push(m);else if(e<=4){for(let m=1;m<=5;m++)h.push(m);h.push("ellipsis"),h.push(s)}else if(e>=s-3){h.push(1),h.push("ellipsis");for(let m=s-4;m<=s;m++)h.push(m)}else{h.push(1),h.push("ellipsis");for(let m=e-1;m<=e+1;m++)h.push(m);h.push("ellipsis"),h.push(s)}return h})(),x=(e-1)*a+1,u=Math.min(e*a,r);return t.jsxs("div",{className:`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 ${i}`,children:[l&&t.jsxs("div",{className:"text-sm text-gray-700 dark:text-gray-300",children:["显示第 ",t.jsx("span",{className:"font-medium",children:x})," 到"," ",t.jsx("span",{className:"font-medium",children:u})," 项，共"," ",t.jsx("span",{className:"font-medium",children:r})," 项"]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsxs("button",{onClick:()=>n(e-1),disabled:e===1,className:`flex items-center px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 
                   bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md 
                   hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300
                   disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-gray-800
                   transition-colors duration-200`,title:"上一页",children:[t.jsx(El,{size:16,className:"mr-1"}),"上一页"]}),t.jsx("div",{className:"flex items-center space-x-1",children:c.map((h,p)=>h==="ellipsis"?t.jsx("span",{className:"flex items-center justify-center w-10 h-10 text-gray-500 dark:text-gray-400",children:t.jsx(ql,{size:16})},`ellipsis-${p}`):t.jsx("button",{onClick:()=>n(h),className:`flex items-center justify-center w-10 h-10 text-sm font-medium rounded-md transition-colors duration-200 ${e===h?"bg-blue-600 text-white border border-blue-600 hover:bg-blue-700":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"}`,title:`第 ${h} 页`,children:h},h))}),t.jsxs("button",{onClick:()=>n(e+1),disabled:e===s,className:`flex items-center px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 
                   bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md 
                   hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300
                   disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-gray-800
                   transition-colors duration-200`,title:"下一页",children:["下一页",t.jsx(tn,{size:16,className:"ml-1"})]})]}),s>10&&t.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[t.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"跳转到"}),t.jsx("input",{type:"number",min:1,max:s,className:`w-16 px-2 py-1 text-center border border-gray-300 dark:border-gray-600 rounded 
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                     focus:ring-2 focus:ring-blue-500 focus:border-transparent`,onKeyPress:h=>{if(h.key==="Enter"){const p=parseInt(h.target.value);p>=1&&p<=s&&n(p)}},placeholder:e.toString()}),t.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"页"})]})]})}function Mo({pageSize:e,onPageSizeChange:s,options:r=[5,10,20,50],className:a=""}){return t.jsxs("div",{className:`flex items-center space-x-2 text-sm ${a}`,children:[t.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"每页显示"}),t.jsx("select",{value:e,onChange:n=>s(parseInt(n.target.value)),className:`px-2 py-1 border border-gray-300 dark:border-gray-600 rounded 
                 bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                 focus:ring-2 focus:ring-blue-500 focus:border-transparent`,children:r.map(n=>t.jsx("option",{value:n,children:n},n))}),t.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"项"})]})}function To({isOpen:e,title:s,isDeleting:r,onConfirm:a,onCancel:n}){const l=d.useRef(null),i=d.useRef(null);if(d.useEffect(()=>{if(!e)return;const c=h=>{h.key==="Escape"&&n()},x=h=>{if(h.key!=="Tab")return;const p=i.current;if(!p)return;const m=p.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),k=m[0],A=m[m.length-1];h.shiftKey?document.activeElement===k&&(h.preventDefault(),A.focus()):document.activeElement===A&&(h.preventDefault(),k.focus())};document.addEventListener("keydown",c),document.addEventListener("keydown",x);const u=document.body.style.overflow;return document.body.style.overflow="hidden",setTimeout(()=>{var h;(h=l.current)==null||h.focus()},100),()=>{document.removeEventListener("keydown",c),document.removeEventListener("keydown",x),document.body.style.overflow=u}},[e,n]),!e)return null;const o=t.jsxs("div",{className:"fixed inset-0 z-[100000] flex items-center justify-center p-4",style:{zIndex:1e5},children:[t.jsx("div",{className:"absolute inset-0 bg-black/50 backdrop-blur-sm",onClick:n,"aria-hidden":"true"}),t.jsxs("div",{ref:i,className:"relative bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full mx-4 transform transition-all",role:"dialog","aria-modal":"true","aria-labelledby":"modal-title","aria-describedby":"modal-description",onClick:c=>c.stopPropagation(),children:[t.jsx("button",{onClick:n,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":"关闭弹窗",children:t.jsx(We,{size:20})}),t.jsx("div",{className:"p-6 pb-4",children:t.jsxs("div",{className:"flex items-start space-x-4",children:[t.jsx("div",{className:"flex-shrink-0 w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center",children:t.jsx(zt,{className:"w-6 h-6 text-red-600 dark:text-red-400"})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("h3",{id:"modal-title",className:"text-lg font-semibold text-gray-900 dark:text-white mb-2",children:"确认删除文章"}),t.jsxs("p",{id:"modal-description",className:"text-sm text-gray-600 dark:text-gray-400 break-words",children:["你确定要删除文章 ",t.jsxs("span",{className:"font-medium text-gray-900 dark:text-white",children:['"',s,'"']})," 吗？"]}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500 mt-1",children:"此操作无法撤销。"})]})]})}),t.jsxs("div",{className:"px-6 py-4 bg-gray-50 dark:bg-gray-700/50 rounded-b-xl flex flex-col-reverse sm:flex-row sm:justify-end space-y-2 space-y-reverse sm:space-y-0 sm:space-x-3",children:[t.jsx("button",{type:"button",onClick:n,disabled:r,className:"w-full sm:w-auto px-4 py-2.5 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:"取消"}),t.jsx("button",{ref:l,type:"button",onClick:a,disabled:r,className:"w-full sm:w-auto px-4 py-2.5 text-sm font-medium text-white bg-red-600 border border-transparent rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center",children:r?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"删除中..."]}):t.jsxs(t.Fragment,{children:[t.jsx(zt,{size:16,className:"mr-2"}),"确认删除"]})})]})]})]});return vr.createPortal(o,document.body)}function ht({slug:e,title:s,onDeleted:r,showEdit:a=!0,showDelete:n=!0}){const{isAdmin:l}=Gt(),[i,o]=d.useState(!1),[c,x]=d.useState(!1),[u,h]=d.useState(!1),p=Kt(),m=()=>{p(`/write/${e}`),o(!1)},k=()=>{h(!0),o(!1)},A=async()=>{if(!c)try{x(!0),await je.deletePost(e),r==null||r(),window.location.pathname.includes(`/post/${e}`)&&p("/")}catch(E){console.error("删除文章失败:",E),alert("删除文章失败，请稍后重试")}finally{x(!1),h(!1)}},N=()=>{h(!1)};return!l||!a&&!n?null:t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"relative",children:[t.jsx("button",{onClick:()=>o(!i),className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",title:"文章操作",children:t.jsx(Gl,{size:18})}),i&&t.jsxs("div",{className:"absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-[9999]",children:[a&&t.jsxs("button",{onClick:m,className:"w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 first:rounded-t-lg",children:[t.jsx(cr,{size:16}),t.jsx("span",{children:"编辑文章"})]}),n&&t.jsxs("button",{onClick:k,className:"w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2 last:rounded-b-lg",children:[t.jsx(zt,{size:16}),t.jsx("span",{children:"删除文章"})]})]}),i&&t.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>o(!1)})]}),t.jsx(To,{isOpen:u,title:s,isDeleting:c,onConfirm:A,onCancel:N})]})}function Lo({className:e=""}){return t.jsx("div",{className:`animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 ${e}`,children:t.jsx("div",{className:"flex items-center justify-center h-full",children:t.jsx(es,{size:32,className:"text-gray-400 dark:text-gray-500"})})})}function Io({onRetry:e,showRetry:s=!0,className:r=""}){return t.jsxs("div",{className:`flex flex-col items-center justify-center h-full bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 ${r}`,children:[t.jsx(_t,{size:32,className:"mb-2"}),t.jsx("p",{className:"text-sm text-center mb-2",children:"图片加载失败"}),s&&e&&t.jsxs("button",{onClick:e,className:"flex items-center space-x-1 px-3 py-1 text-xs bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded transition-colors",children:[t.jsx(Yl,{size:12}),t.jsx("span",{children:"重试"})]})]})}function Et({src:e,alt:s,className:r="",containerClassName:a="",maxHeight:n=400,minHeight:l=200,priority:i="auto",lazy:o=!0,preload:c=!1,quality:x=85,width:u,height:h,aspectRatio:p,showSkeleton:m=!0,enablePreview:k=!1,fallbackSrc:A,retryCount:N=2,onError:E,onLoad:_,onPreview:K}){const[T,H]=d.useState("cover"),[$,G]=d.useState(!1),[z,O]=d.useState(!1),[X,Z]=d.useState(!1),[Y,ue]=d.useState(!o),[le,ce]=d.useState(""),[w,f]=d.useState(0),[j,b]=d.useState(null),S=d.useRef(null),L=d.useRef(null),q=d.useCallback(de=>{if(!de.includes("pub-a1a2.r2.dev")||de.includes("/api/image-proxy/"))return de;const ge=de.split("pub-a1a2.r2.dev/");if(ge.length<2)return de;const we=ge[1],be=new URLSearchParams;return u&&be.set("w",u.toString()),h&&be.set("h",h.toString()),x!==85&&be.set("q",x.toString()),be.set("f","auto"),`${window.location.origin.includes("localhost")?"http://localhost:8787":"https://blog.fddfffff.site"}/api/image-proxy/${we}?${be.toString()}`},[u,h,x]);d.useEffect(()=>{if(!o||Y)return;const de=new IntersectionObserver(ge=>{ge.forEach(we=>{we.isIntersecting&&(ue(!0),de.disconnect())})},{rootMargin:"50px",threshold:.1});return L.current&&de.observe(L.current),()=>de.disconnect()},[o,Y]),d.useEffect(()=>{if(Y&&e){O(!0),Z(!1),G(!1);const de=q(e);if(ce(de),c){const ge=new Image;ge.src=de,ge.onload=()=>{b({width:ge.naturalWidth,height:ge.naturalHeight})}}}},[Y,e,q,c]);const Q=d.useCallback(()=>{w<N&&(f(de=>de+1),Z(!1),O(!0),ce(w===0&&A?A:le!==e?e:e+"?retry="+Date.now()))},[w,N,A,le,e]),C=de=>{const ge=de.currentTarget;if(G(!0),O(!1),Z(!1),f(0),b({width:ge.naturalWidth,height:ge.naturalHeight}),i==="auto"){const we=ge.naturalWidth/ge.naturalHeight,be=p||(u&&h?u/h:16/9);Math.abs(we-be)>.5||we>2.5||we<.4?H("contain"):H("cover")}else H(i==="contain"?"contain":"cover");_==null||_()},ne=()=>{console.warn("图片加载失败:",le,"重试次数:",w),O(!1),G(!1),w<N?setTimeout(()=>{Q()},1e3*(w+1)):(Z(!0),E==null||E())},oe=()=>{k&&$&&le&&(K==null||K(le))},ke={minHeight:`${l}px`,maxHeight:`${n}px`,height:T==="contain"?"auto":`${n}px`,aspectRatio:p?p.toString():void 0};return t.jsxs("div",{ref:L,className:`relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-lg ${T==="contain"?"flex items-center justify-center":""} ${a}`,style:ke,children:[!Y&&m&&t.jsx(Lo,{className:"absolute inset-0"}),Y&&z&&!$&&t.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800",children:t.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"加载中..."})]})}),X&&t.jsx(Io,{onRetry:w<N?Q:void 0,showRetry:w<N,className:"absolute inset-0"}),Y&&le&&!X&&t.jsx("img",{ref:S,src:le,alt:s,className:`transition-all duration-700 ${T==="contain"?"max-w-full max-h-full object-contain":"w-full h-full object-cover"} ${r} ${$?"opacity-100 scale-100":"opacity-0 scale-105"} ${k?"cursor-pointer hover:scale-105":""}`,loading:o?"lazy":"eager",onLoad:C,onError:ne,onClick:oe,style:{maxHeight:`${n}px`,minHeight:T==="contain"?`${l}px`:"auto"}}),k&&$&&t.jsx("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:t.jsx("div",{className:"bg-black/50 text-white p-1 rounded",children:t.jsx(Qe,{size:16})})}),!1,T==="contain"&&$&&t.jsx("div",{className:"absolute inset-0 border border-gray-200/30 dark:border-gray-700/30 rounded-lg pointer-events-none"})]})}function Oo({src:e,alt:s,isOpen:r,onClose:a}){const[n,l]=d.useState(1),[i,o]=d.useState(0),[c,x]=d.useState({x:0,y:0}),[u,h]=d.useState(!1),[p,m]=d.useState({x:0,y:0}),[k,A]=d.useState(!0),N=d.useRef(null),E=d.useRef(null),_=()=>{l(1),o(0),x({x:0,y:0})},K=()=>{_(),a()};d.useEffect(()=>{if(!r)return;const O=X=>{switch(X.key){case"Escape":K();break;case"+":case"=":l(Z=>Math.min(Z*1.2,5));break;case"-":l(Z=>Math.max(Z/1.2,.1));break;case"r":case"R":o(Z=>Z+90);break;case"0":_();break}};return document.addEventListener("keydown",O),()=>document.removeEventListener("keydown",O)},[r]);const T=O=>{n<=1||(h(!0),m({x:O.clientX-c.x,y:O.clientY-c.y}))},H=O=>{u&&x({x:O.clientX-p.x,y:O.clientY-p.y})},$=()=>{h(!1)},G=O=>{O.preventDefault();const X=O.deltaY>0?.9:1.1;l(Z=>Math.max(.1,Math.min(5,Z*X)))},z=async()=>{try{const X=await(await fetch(e)).blob(),Z=window.URL.createObjectURL(X),Y=document.createElement("a");Y.href=Z,Y.download=s||"image",document.body.appendChild(Y),Y.click(),document.body.removeChild(Y),window.URL.revokeObjectURL(Z)}catch(O){console.error("下载图片失败:",O)}};return r?t.jsxs("div",{className:"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm",children:[t.jsx("div",{className:"absolute top-4 left-1/2 transform -translate-x-1/2 z-10",children:t.jsxs("div",{className:"flex items-center space-x-2 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2",children:[t.jsx("button",{onClick:()=>l(O=>Math.max(O/1.2,.1)),className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"缩小 (-)",children:t.jsx(fo,{size:16})}),t.jsxs("span",{className:"text-white text-sm min-w-[60px] text-center",children:[Math.round(n*100),"%"]}),t.jsx("button",{onClick:()=>l(O=>Math.min(O*1.2,5)),className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"放大 (+)",children:t.jsx(xo,{size:16})}),t.jsx("div",{className:"w-px h-6 bg-white/30"}),t.jsx("button",{onClick:()=>o(O=>O+90),className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"旋转 (R)",children:t.jsx(eo,{size:16})}),t.jsx("button",{onClick:_,className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"重置 (0)",children:t.jsx(an,{size:16})}),t.jsx("button",{onClick:z,className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"下载",children:t.jsx(Rl,{size:16})})]})}),t.jsx("button",{onClick:K,className:"absolute top-4 right-4 z-10 p-2 text-white hover:bg-white/20 rounded-full transition-colors",title:"关闭 (ESC)",children:t.jsx(We,{size:24})}),t.jsxs("div",{ref:E,className:"absolute inset-0 flex items-center justify-center cursor-move",onMouseDown:T,onMouseMove:H,onMouseUp:$,onMouseLeave:$,onWheel:G,onClick:O=>{O.target===O.currentTarget&&K()},children:[k&&t.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white"})}),t.jsx("img",{ref:N,src:e,alt:s,className:`max-w-none transition-all duration-300 ${u?"cursor-grabbing":n>1?"cursor-grab":"cursor-zoom-in"}`,style:{transform:`translate(${c.x}px, ${c.y}px) scale(${n}) rotate(${i}deg)`,maxHeight:n===1?"90vh":"none",maxWidth:n===1?"90vw":"none"},onLoad:()=>A(!1),onError:()=>A(!1),draggable:!1})]}),t.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2",children:t.jsx("div",{className:"bg-black/50 backdrop-blur-sm text-white text-xs px-4 py-2 rounded-lg",children:t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsx("span",{children:"滚轮: 缩放"}),t.jsx("span",{children:"拖拽: 移动"}),t.jsx("span",{children:"ESC: 关闭"}),t.jsx("span",{children:"R: 旋转"}),t.jsx("span",{children:"0: 重置"})]})})})]}):null}function Nr(){const[e,s]=d.useState(null),r=(l,i="")=>{s({src:l,alt:i})},a=()=>{s(null)},n=e?t.jsx(Oo,{src:e.src,alt:e.alt,isOpen:!!e,onClose:a}):null;return{openPreview:r,closePreview:a,PreviewComponent:n,isOpen:!!e}}function xa({post:e,variant:s="default",showActions:r=!1,showStats:a=!1,onDeleted:n,className:l=""}){const{openPreview:i,PreviewComponent:o}=Nr();return s==="featured"?t.jsxs("div",{className:`card p-8 hover:shadow-lg transition-all duration-300 relative group ${l}`,children:[t.jsx("div",{className:"absolute top-4 left-4 z-10",children:t.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",children:[t.jsx(nn,{size:12,className:"mr-1"}),"特色文章"]})}),r&&t.jsx("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-20 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-1",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:n})}),t.jsxs(ae,{to:`/post/${e.slug||e.id}`,className:"block",children:[e.imageUrl&&t.jsx("div",{className:"mb-6 rounded-xl overflow-hidden shadow-lg group",children:t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-105 transition-transform duration-500",containerClassName:"rounded-xl",maxHeight:400,minHeight:200,priority:"auto",lazy:!1,preload:!0,quality:90,width:800,height:400,aspectRatio:16/9,showSkeleton:!0,enablePreview:!0,retryCount:3,onPreview:c=>i(c,e.title),onError:()=>{console.warn("特色文章封面加载失败:",e.imageUrl)}})}),t.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 pr-12 leading-tight",children:e.title}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed mb-4 text-lg",children:e.excerpt}),t.jsxs("div",{className:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ze,{size:16}),t.jsx("span",{children:e.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:16}),t.jsx("span",{children:e.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:16}),t.jsxs("span",{children:[e.readTime," 分钟阅读"]})]}),a&&t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Qe,{size:16}),t.jsx("span",{children:e.views||0})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(ls,{size:16}),t.jsx("span",{children:e.likes||0})]})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:e.tags.map(c=>t.jsx("span",{className:"px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full",children:c},c))})]})]}):s==="compact"?t.jsxs("div",{className:`card p-4 hover:shadow-md transition-all duration-300 group relative ${l}`,children:[r&&t.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-20 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-1",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:n})}),t.jsx(ae,{to:`/post/${e.slug||e.id}`,className:"block",children:t.jsxs("div",{className:"flex space-x-4",children:[e.imageUrl&&t.jsx("div",{className:"flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden",children:t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-110 transition-transform duration-300",containerClassName:"rounded-lg",maxHeight:80,minHeight:80,priority:"cover",lazy:!0,quality:75,width:80,height:80,aspectRatio:1,showSkeleton:!0,enablePreview:!0,onPreview:c=>i(c,e.title)})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-2 line-clamp-2",children:e.title}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2",children:e.excerpt}),t.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:10}),t.jsx("span",{children:e.date})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:10}),t.jsxs("span",{children:[e.readTime,"分钟"]})]})]})]})]})})]}):s==="list"?t.jsxs("div",{className:`border-b border-gray-200 dark:border-gray-700 py-6 group relative ${l}`,children:[r&&t.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-20 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-1",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:n})}),t.jsx(ae,{to:`/post/${e.slug||e.id}`,className:"block",children:t.jsxs("div",{className:"flex space-x-6",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-2",children:e.title}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-3 line-clamp-2",children:e.excerpt}),t.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:e.tags.slice(0,3).map(c=>t.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded",children:c},c))}),t.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ze,{size:14}),t.jsx("span",{children:e.author})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:e.date})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:14}),t.jsxs("span",{children:[e.readTime," 分钟"]})]}),a&&t.jsxs(t.Fragment,{children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Qe,{size:14}),t.jsx("span",{children:e.views||0})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(ts,{size:14}),t.jsx("span",{children:e.comments||0})]})]})]})]}),e.imageUrl&&t.jsx("div",{className:"flex-shrink-0 w-32 h-24 rounded-lg overflow-hidden",children:t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-110 transition-transform duration-300",containerClassName:"rounded-lg",maxHeight:96,minHeight:96,priority:"cover",lazy:!0,quality:75,width:128,height:96,aspectRatio:4/3,showSkeleton:!0,enablePreview:!0,onPreview:c=>i(c,e.title)})})]})})]}):t.jsxs("div",{className:`card p-0 hover:shadow-lg transition-all duration-300 group h-full relative overflow-hidden rounded-lg ${l}`,children:[r&&t.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-20 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-1",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:n})}),t.jsxs(ae,{to:`/post/${e.slug||e.id}`,className:"block h-full",children:[e.imageUrl&&t.jsx("div",{className:"w-full h-48 overflow-hidden bg-gray-100 dark:bg-gray-800",children:t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-110 transition-transform duration-500",containerClassName:"rounded-t-lg w-full h-full",maxHeight:192,minHeight:192,priority:"contain",lazy:!0,quality:80,width:400,height:192,aspectRatio:25/12,showSkeleton:!0,enablePreview:!0,onPreview:c=>i(c,e.title),onError:()=>{console.warn("文章封面加载失败:",e.imageUrl)}})}),t.jsxs("div",{className:"p-6 flex flex-col h-full",children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 line-clamp-2",children:e.title}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3 flex-grow",children:e.excerpt}),t.jsxs("div",{className:"mt-auto",children:[t.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.tags.slice(0,3).map(c=>t.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full",children:c},c)),e.tags.length>3&&t.jsxs("span",{className:"px-2 py-1 text-xs text-gray-400 dark:text-gray-500",children:["+",e.tags.length-3]})]}),t.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:12}),t.jsx("span",{children:e.date})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:12}),t.jsxs("span",{children:[e.readTime," 分钟"]})]})]}),a&&t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Qe,{size:12}),t.jsx("span",{children:e.views||0})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(ls,{size:12}),t.jsx("span",{children:e.likes||0})]})]})]})]})]})]}),o]})}function Po({post:e,showActions:s=!1,onDeleted:r,className:a=""}){const{openPreview:n,PreviewComponent:l}=Nr();return t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:`relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 shadow-xl hover:shadow-2xl transition-all duration-500 group ${a}`,children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 dark:from-blue-400/10 dark:to-purple-400/10"}),t.jsx("div",{className:"absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-200/20 to-transparent dark:from-yellow-400/10 rounded-full -translate-y-32 translate-x-32"}),t.jsx("div",{className:"absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-pink-200/20 to-transparent dark:from-pink-400/10 rounded-full translate-y-24 -translate-x-24"}),t.jsx("div",{className:"absolute top-6 left-6 z-20",children:t.jsxs("div",{className:"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full shadow-lg",children:[t.jsx(nn,{size:16,className:"animate-pulse"}),t.jsx("span",{className:"text-sm font-semibold",children:"特色文章"}),t.jsx(Ct,{size:14,className:"animate-bounce"})]})}),s&&t.jsx("div",{className:"absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity z-20",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:r})}),t.jsx(ae,{to:`/post/${e.slug}`,className:"block relative z-10",children:t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 p-8",children:[t.jsxs("div",{className:"flex flex-col justify-center space-y-6 order-2 lg:order-1",children:[t.jsxs("div",{className:"space-y-4",children:[t.jsx("h2",{className:"text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300",children:e.title}),t.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 leading-relaxed line-clamp-3",children:e.excerpt})]}),t.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.tags.slice(0,4).map(i=>t.jsxs("span",{className:"px-3 py-1 text-sm bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-700 dark:text-gray-300 rounded-full border border-gray-200/50 dark:border-gray-700/50 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors",children:["#",i]},i)),e.tags.length>4&&t.jsxs("span",{className:"px-3 py-1 text-sm text-gray-500 dark:text-gray-400",children:["+",e.tags.length-4," 更多"]})]}),t.jsxs("div",{className:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:t.jsx(Ze,{size:14,className:"text-white"})}),t.jsx("span",{className:"font-medium",children:e.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:16}),t.jsx("span",{children:e.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:16}),t.jsxs("span",{children:[e.readTime," 分钟阅读"]})]})]}),t.jsxs("div",{className:"flex items-center space-x-6 text-sm",children:[t.jsxs("div",{className:"flex items-center space-x-1 text-gray-500 dark:text-gray-400",children:[t.jsx(Qe,{size:16}),t.jsxs("span",{children:[e.views||0," 次浏览"]})]}),t.jsxs("div",{className:"flex items-center space-x-1 text-red-500",children:[t.jsx(ls,{size:16}),t.jsxs("span",{children:[e.likes||0," 个赞"]})]})]}),t.jsx("div",{className:"pt-4",children:t.jsxs("div",{className:"inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl",children:[t.jsx("span",{className:"font-semibold",children:"阅读全文"}),t.jsx(wl,{size:16,className:"group-hover:translate-x-1 transition-transform"})]})})]}),t.jsx("div",{className:"order-1 lg:order-2",children:e.imageUrl?t.jsxs("div",{className:"relative h-80 lg:h-full rounded-xl overflow-hidden shadow-2xl group-hover:shadow-3xl transition-shadow duration-500",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent z-10"}),t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-110 transition-transform duration-700",containerClassName:"rounded-xl",maxHeight:400,minHeight:320,priority:"cover",lazy:!1,preload:!0,quality:95,width:600,height:400,aspectRatio:3/2,showSkeleton:!0,enablePreview:!0,retryCount:3,onPreview:i=>n(i,e.title),onError:()=>{console.warn("特色文章封面加载失败:",e.imageUrl)}}),t.jsx("div",{className:"absolute inset-0 rounded-xl border-2 border-white/20 dark:border-gray-700/30 pointer-events-none"}),t.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-tr from-blue-500/0 via-purple-500/0 to-pink-500/0 group-hover:from-blue-500/10 group-hover:via-purple-500/5 group-hover:to-pink-500/10 transition-all duration-700 pointer-events-none"})]}):t.jsx("div",{className:"h-80 lg:h-full rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center",children:t.jsxs("div",{className:"text-center text-gray-400 dark:text-gray-600",children:[t.jsx(Ct,{size:48,className:"mx-auto mb-4 opacity-50"}),t.jsx("p",{className:"text-lg font-medium",children:"精彩内容"}),t.jsx("p",{className:"text-sm",children:"等待您的发现"})]})})})]})}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"})]}),l]})}const un=d.createContext(void 0);function _o(){const e=d.useContext(un);if(!e)throw new Error("useToast must be used within a ToastProvider");return e}function zo({type:e}){const s={size:20};switch(e){case"success":return t.jsx(or,{...s,className:"text-green-500"});case"error":return t.jsx(_t,{...s,className:"text-red-500"});case"warning":return t.jsx(_t,{...s,className:"text-yellow-500"});case"info":return t.jsx(ca,{...s,className:"text-blue-500"});case"loading":return t.jsx(mt,{...s,className:"text-blue-500 animate-spin"});default:return t.jsx(ca,{...s,className:"text-gray-500"})}}function $o({toast:e,onRemove:s}){const[r,a]=d.useState(!1),[n,l]=d.useState(!1);d.useEffect(()=>{const x=setTimeout(()=>a(!0),10);return()=>clearTimeout(x)},[]),d.useEffect(()=>{if(e.type!=="loading"&&e.duration!==0){const x=e.duration||5e3,u=setTimeout(()=>{i()},x);return()=>clearTimeout(u)}},[e.duration,e.type]);const i=()=>{l(!0),setTimeout(()=>{s(e.id)},300)},o={success:"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800",error:"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800",warning:"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800",info:"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800",loading:"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"},c={success:"text-green-800 dark:text-green-200",error:"text-red-800 dark:text-red-200",warning:"text-yellow-800 dark:text-yellow-200",info:"text-blue-800 dark:text-blue-200",loading:"text-blue-800 dark:text-blue-200"};return t.jsx("div",{className:`
        relative max-w-sm w-full border rounded-lg shadow-lg p-4 mb-3
        transition-all duration-300 ease-in-out transform
        ${o[e.type]}
        ${r&&!n?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
        ${n?"scale-95":"scale-100"}
      `,children:t.jsxs("div",{className:"flex items-start",children:[t.jsx("div",{className:"flex-shrink-0 mr-3",children:t.jsx(zo,{type:e.type})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("h4",{className:`text-sm font-medium ${c[e.type]}`,children:e.title}),e.message&&t.jsx("p",{className:`mt-1 text-sm ${c[e.type]} opacity-90`,children:e.message}),e.action&&t.jsx("div",{className:"mt-3",children:t.jsx("button",{onClick:e.action.onClick,className:`text-sm font-medium underline hover:no-underline ${c[e.type]}`,children:e.action.label})})]}),e.type!=="loading"&&t.jsx("button",{onClick:i,className:`flex-shrink-0 ml-2 p-1 rounded-md hover:bg-black hover:bg-opacity-10 transition-colors ${c[e.type]}`,children:t.jsx(We,{size:16})})]})})}function Do({toasts:e,removeToast:s}){return t.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:e.map(r=>t.jsx($o,{toast:r,onRemove:s},r.id))})}function Uo({children:e}){const[s,r]=d.useState([]),a=i=>{const o=Math.random().toString(36).substr(2,9),c={...i,id:o,duration:i.duration??5e3};return r(x=>[...x,c]),o},n=i=>{r(o=>o.filter(c=>c.id!==i))},l=(i,o)=>{r(c=>c.map(x=>x.id===i?{...x,...o}:x))};return t.jsxs(un.Provider,{value:{toasts:s,addToast:a,removeToast:n,updateToast:l},children:[e,t.jsx(Do,{toasts:s,removeToast:n})]})}function Er(){const{addToast:e,updateToast:s,removeToast:r}=_o();return{success:(a,n,l)=>e({type:"success",title:a,message:n,...l}),error:(a,n,l)=>e({type:"error",title:a,message:n,...l}),warning:(a,n,l)=>e({type:"warning",title:a,message:n,...l}),info:(a,n,l)=>e({type:"info",title:a,message:n,...l}),loading:(a,n)=>e({type:"loading",title:a,message:n,duration:0}),update:s,remove:r}}function Ho(){const[e,s]=d.useState([]),[r,a]=d.useState([]),[n,l]=d.useState(!0),[i,o]=d.useState(null),[c,x]=d.useState({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),[u,h]=d.useState("grid"),[p,m]=d.useState(1),[k,A]=d.useState(12),N=Er(),{PreviewComponent:E}=Nr(),_=async()=>{try{l(!0),o(null),console.log("开始加载文章列表...");const f=await je.getPosts(1,50);console.log("获取文章响应:",f);const j=f.posts||[];if(s(j),a(j),j.length===0)console.warn("API返回空文章列表");else{const b=j.map(S=>S.imageUrl).filter(Boolean);b.length>0&&(ur.cacheFeaturedImages(b),ur.smartPreload().catch(S=>{console.warn("智能预加载失败:",S)})),console.log(`文章加载完成，共加载 ${j.length} 篇文章`)}}catch(f){console.error("加载文章失败详情:",f);let j="加载文章失败";f instanceof os?j=`API错误 (${f.status}): ${f.message}`:f instanceof Error?j=`网络错误: ${f.message}`:j="未知错误，请检查网络连接",o(j),N.error("加载失败",j),s([]),a([])}finally{l(!1)}};d.useEffect(()=>{_()},[]);const K=()=>{_()},T=d.useCallback(f=>{x(f),m(1)},[]),H=d.useCallback(f=>{a(f),m(1)},[]),$=d.useCallback(f=>{m(f),window.scrollTo({top:0,behavior:"smooth"})},[]),G=d.useCallback(f=>{A(f),m(1)},[]),z=d.useMemo(()=>{const f=e.filter(j=>j.category).map(j=>j.category);return Array.from(new Set(f))},[e]),O=d.useMemo(()=>{const f=e.flatMap(j=>j.tags);return Array.from(new Set(f))},[e]),X=d.useMemo(()=>{let f=[...r];return c.category&&(f=f.filter(j=>j.category===c.category)),c.tag&&(f=f.filter(j=>j.tags.includes(c.tag))),f.sort((j,b)=>{if(c.sortBy!=="date"){let C=0;switch(c.sortBy){case"readTime":C=j.readTime-b.readTime;break;case"featured":C=(j.featured?1:0)-(b.featured?1:0);break}return c.sortOrder==="desc"?-C:C}const S=C=>{const ne=[/^(.+?)-第(\d+)课/,/^(.+?)-课程(\d+)/,/^(.+?)\s+第(\d+)课/,/^(.+?)-(\d+)/,/^(.+?)\s+(\d+)/];for(const oe of ne){const ke=C.match(oe);if(ke)return{series:ke[1].trim(),number:parseInt(ke[2]),hasNumber:!0}}return{series:C,number:0,hasNumber:!1}},L=S(j.title),q=S(b.title);if(L.series===q.series&&L.hasNumber&&q.hasNumber)return c.sortOrder==="desc"?q.number-L.number:L.number-q.number;if(L.series===q.series){if(L.hasNumber&&!q.hasNumber)return c.sortOrder==="desc"?-1:1;if(!L.hasNumber&&q.hasNumber)return c.sortOrder==="desc"?1:-1}const Q=new Date(j.date).getTime()-new Date(b.date).getTime();return c.sortOrder==="desc"?-Q:Q}),f},[r,c]),Z=Math.ceil(X.length/k),Y=(p-1)*k,ue=Y+k,le=X.slice(Y,ue),ce=p===1?X.find(f=>f.featured):null,w=le.filter(f=>!f.featured||p>1);return n?t.jsxs("div",{className:"space-y-8",children:[t.jsxs("div",{className:"text-center py-12",children:[t.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto mb-4 animate-pulse"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"})]}),t.jsxs("div",{className:"card p-8 animate-pulse",children:[t.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]})]}):t.jsxs("div",{className:"space-y-12",children:[t.jsxs("div",{className:"text-center py-12",children:[t.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4",children:"欢迎来到杨博文的博客"}),t.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto mb-8",children:"分享技术见解、开发经验和生活感悟的个人空间"}),t.jsx("div",{className:"max-w-md mx-auto",children:t.jsx(Ao,{posts:e,onSearchResults:H,placeholder:"搜索文章、标签或分类...",className:"w-full"})})]}),i&&t.jsx("div",{className:"card p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800",children:t.jsxs("div",{className:"flex items-start justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("h3",{className:"text-red-800 dark:text-red-200 font-medium mb-2",children:"文章加载失败"}),t.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm mb-4",children:i}),t.jsx("div",{className:"text-xs text-red-600 dark:text-red-400 mb-4",children:"请打开浏览器开发者工具的控制台查看详细错误信息"})]}),t.jsx("button",{onClick:_,className:"ml-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm",disabled:n,children:n?"重试中...":"重试"})]})}),e.length>0&&t.jsxs("div",{className:"space-y-6",children:[t.jsx(Co,{onFilterChange:T,availableCategories:z,availableTags:O}),t.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:["共 ",X.length," 篇文章"]}),t.jsx(Mo,{pageSize:k,onPageSizeChange:G,options:[6,12,24,48]})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400 mr-2",children:"视图:"}),t.jsx("button",{onClick:()=>h("grid"),className:`p-2 rounded-md transition-colors ${u==="grid"?"bg-blue-600 text-white":"bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600"}`,title:"网格视图",children:t.jsx(Il,{size:16})}),t.jsx("button",{onClick:()=>h("list"),className:`p-2 rounded-md transition-colors ${u==="list"?"bg-blue-600 text-white":"bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600"}`,title:"列表视图",children:t.jsx(sn,{size:16})})]})]})]}),!n&&e.length===0&&t.jsx("div",{className:"text-center py-16",children:t.jsxs("div",{className:"card p-8 max-w-md mx-auto",children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"还没有文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"开始写作，分享你的想法和经验吧！"}),t.jsx(ae,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})}),!n&&e.length>0&&X.length===0&&t.jsx("div",{className:"text-center py-16",children:t.jsxs("div",{className:"card p-8 max-w-md mx-auto",children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"没有找到匹配的文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"试试调整筛选条件，或者浏览全部文章"}),t.jsx("button",{onClick:()=>x({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"清除筛选条件"})]})}),ce&&t.jsxs("div",{className:"mb-16",children:[t.jsxs("div",{className:"text-center mb-8",children:[t.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"✨ 精选推荐"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"为您精心挑选的优质内容"})]}),t.jsx(Po,{post:ce,showActions:!0,onDeleted:K})]}),w.length>0&&t.jsxs("div",{children:[t.jsxs("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:p===1&&ce?"📚 最新文章":"📚 文章列表"}),t.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["第 ",p," 页，共 ",Z," 页"]})]}),u==="grid"?t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:w.map(f=>t.jsx(xa,{post:f,variant:"default",showActions:!0,showStats:!1,onDeleted:K},f.id))}):t.jsx("div",{className:"space-y-0",children:w.map(f=>t.jsx(xa,{post:f,variant:"list",showActions:!0,showStats:!1,onDeleted:K},f.id))})]}),X.length>k&&t.jsx("div",{className:"mt-12",children:t.jsx(Ro,{currentPage:p,totalPages:Z,totalItems:X.length,itemsPerPage:k,onPageChange:$,showInfo:!0})}),e.length>0&&!ce&&w.length===0&&p===1&&t.jsx("div",{className:"text-center py-8",children:t.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"当前页面没有文章"})}),E]})}class Vt{static async getComments(s){try{const r=await Te(fe.comments(s));return(await ve(r)).comments||[]}catch(r){return console.error("获取评论失败:",r),[]}}static async createComment(s,r){try{const a=await Te(fe.comments(s),{method:"POST",body:JSON.stringify(r)});return await ve(a)}catch(a){throw console.error("创建评论失败:",a),a}}static async deleteComment(s,r){try{const a=await ss(fe.comment(s,r),{method:"DELETE"});await ve(a)}catch(a){throw console.error("删除评论失败:",a),a}}static validateComment(s){var r,a;return(r=s.content)!=null&&r.trim()?s.content.length>1e3?"评论内容过长，最多1000个字符":(a=s.author)!=null&&a.trim()?s.author.length>50?"姓名过长，最多50个字符":s.email&&!this.isValidEmail(s.email)?"请输入有效的邮箱地址":s.website&&!this.isValidUrl(s.website)?"请输入有效的网站地址":null:"请输入您的姓名":"评论内容不能为空"}static isValidEmail(s){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s)}static isValidUrl(s){try{return new URL(s.startsWith("http")?s:`https://${s}`),!0}catch{return!1}}}function Bo({slug:e}){const{isAdmin:s}=Gt(),[r,a]=d.useState([]),[n,l]=d.useState(!0),[i,o]=d.useState(!1),[c,x]=d.useState(null),[u,h]=d.useState(!1),[p,m]=d.useState(null),[k,A]=d.useState(null),[N,E]=d.useState({content:"",author:"",email:"",website:""}),_=async()=>{try{l(!0),x(null);const z=await Vt.getComments(e);a(Array.isArray(z)?z:[])}catch(z){console.error("加载评论失败:",z),x("加载评论失败，请稍后重试"),a([])}finally{l(!1)}},K=async z=>{z.preventDefault();const O=Vt.validateComment(N);if(O){x(O);return}try{o(!0),x(null);const X=await Vt.createComment(e,N);a(Z=>[X,...Array.isArray(Z)?Z:[]]),E({content:"",author:"",email:"",website:""}),h(!1)}catch(X){console.error("提交评论失败:",X),x(X.message||"提交评论失败，请稍后重试")}finally{o(!1)}},T=z=>{A(z)},H=async z=>{if(!p)try{m(z),await Vt.deleteComment(e,z),a(O=>Array.isArray(O)?O.filter(X=>X.id!==z):[]),A(null)}catch(O){console.error("删除评论失败:",O),x(O.message||"删除评论失败，请稍后重试")}finally{m(null)}},$=()=>{A(null)},G=z=>{const O=new Date(z),Z=new Date().getTime()-O.getTime(),Y=Math.floor(Z/(1e3*60*60*24));return Y===0?"今天":Y===1?"昨天":Y<7?`${Y}天前`:O.toLocaleDateString("zh-CN")};return d.useEffect(()=>{_()},[e]),t.jsxs("div",{className:"mt-12",children:[t.jsxs("div",{className:"card p-8",children:[t.jsxs("div",{className:"flex items-center justify-between mb-6",children:[t.jsxs("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center space-x-2",children:[t.jsx(ts,{size:20}),t.jsxs("span",{children:["评论讨论 (",(r==null?void 0:r.length)||0,")"]})]}),!u&&t.jsxs("button",{onClick:()=>h(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2",children:[t.jsx(ts,{size:16}),t.jsx("span",{children:"发表评论"})]})]}),c&&t.jsx("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:t.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm",children:c})}),u&&t.jsxs("form",{onSubmit:K,className:"mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("textarea",{value:N.content,onChange:z=>E(O=>({...O,content:z.target.value})),placeholder:"写下您的评论...",rows:4,className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0}),t.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[N.content.length,"/1000 字符"]})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[t.jsxs("div",{className:"relative",children:[t.jsx(Ze,{className:"absolute left-3 top-3 text-gray-400",size:16}),t.jsx("input",{type:"text",value:N.author,onChange:z=>E(O=>({...O,author:z.target.value})),placeholder:"您的姓名 *",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),t.jsxs("div",{className:"relative",children:[t.jsx(rn,{className:"absolute left-3 top-3 text-gray-400",size:16}),t.jsx("input",{type:"email",value:N.email,onChange:z=>E(O=>({...O,email:z.target.value})),placeholder:"邮箱 (可选)",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),t.jsxs("div",{className:"relative",children:[t.jsx(Ll,{className:"absolute left-3 top-3 text-gray-400",size:16}),t.jsx("input",{type:"url",value:N.website,onChange:z=>E(O=>({...O,website:z.target.value})),placeholder:"网站 (可选)",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),t.jsxs("div",{className:"flex items-center justify-between mt-4",children:[t.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"评论将会被公开显示，请文明发言"}),t.jsxs("div",{className:"flex space-x-3",children:[t.jsx("button",{type:"button",onClick:()=>h(!1),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:"取消"}),t.jsxs("button",{type:"submit",disabled:i,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:[t.jsx(so,{size:16}),t.jsx("span",{children:i?"发表中...":"发表评论"})]})]})]})]}),t.jsx("div",{className:"space-y-6",children:n?t.jsx("div",{className:"space-y-4",children:[...Array(3)].map((z,O)=>t.jsx("div",{className:"animate-pulse",children:t.jsxs("div",{className:"flex space-x-3",children:[t.jsx("div",{className:"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"}),t.jsxs("div",{className:"flex-1",children:[t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]})]})},O))}):((r==null?void 0:r.length)||0)===0?t.jsxs("div",{className:"text-center py-8",children:[t.jsx(ts,{className:"mx-auto mb-4 text-gray-400",size:48}),t.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"还没有评论，来发表第一条吧！"})]}):(r||[]).map(z=>t.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0 last:pb-0",children:t.jsxs("div",{className:"flex items-start space-x-3",children:[t.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold",children:z.author.charAt(0).toUpperCase()}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-center justify-between mb-2",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:z.website?t.jsx("a",{href:z.website.startsWith("http")?z.website:`https://${z.website}`,target:"_blank",rel:"noopener noreferrer",className:"hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:z.author}):z.author}),t.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:G(z.createdAt)})]})]}),s&&t.jsx("button",{onClick:()=>T(z.id),className:"text-gray-400 hover:text-red-500 transition-colors p-1 rounded",title:"删除评论",children:t.jsx(zt,{size:14})})]}),t.jsx("div",{className:"prose prose-sm dark:prose-dark max-w-none",children:t.jsx("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:z.content})})]})]})},z.id))})]}),k&&vr.createPortal(t.jsxs("div",{className:"fixed inset-0 z-[99999] overflow-y-auto",style:{zIndex:99999},children:[t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 transition-opacity",onClick:$,style:{zIndex:99999}}),t.jsx("div",{className:"flex min-h-full items-center justify-center p-4 text-center",style:{zIndex:1e5},children:t.jsxs("div",{className:"relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg",onClick:z=>z.stopPropagation(),style:{zIndex:100001},children:[t.jsx("div",{className:"bg-white dark:bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4",children:t.jsxs("div",{className:"sm:flex sm:items-start",children:[t.jsx("div",{className:"mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20 sm:mx-0 sm:h-10 sm:w-10",children:t.jsx(zt,{className:"h-6 w-6 text-red-600 dark:text-red-400"})}),t.jsxs("div",{className:"mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left",children:[t.jsx("h3",{className:"text-base font-semibold leading-6 text-gray-900 dark:text-white",children:"确认删除评论"}),t.jsx("div",{className:"mt-2",children:t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"你确定要删除这条评论吗？此操作无法撤销。"})})]})]})}),t.jsxs("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6",children:[t.jsx("button",{type:"button",onClick:()=>H(k),disabled:p===k,className:"inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed",children:p===k?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"删除中..."]}):"确认删除"}),t.jsx("button",{type:"button",onClick:$,disabled:p===k,className:"mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-600 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500 sm:mt-0 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed",children:"取消"})]})]})})]}),document.body)]})}function Wo(){const{slug:e}=qt(),[s,r]=d.useState(null),[a,n]=d.useState(!0),[l,i]=d.useState(null);if(d.useEffect(()=>{(async()=>{if(e)try{n(!0),i(null);const x=await je.getPost(e);r(x)}catch(x){console.error("获取文章详情失败:",x),i("文章加载失败，请稍后重试")}finally{n(!1)}})()},[e]),a)return t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsxs("div",{className:"animate-pulse",children:[t.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-8"}),t.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"}),t.jsxs("div",{className:"space-y-4",children:[t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"})]})]})});if(l||!s)return t.jsx("div",{className:"max-w-4xl mx-auto text-center",children:t.jsxs("div",{className:"card p-8",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:l||"文章未找到"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:l||"请检查链接是否正确，或返回首页查看其他文章"}),t.jsxs(ae,{to:"/",className:"inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:underline",children:[t.jsx(is,{size:16}),t.jsx("span",{children:"返回首页"})]})]})});const o=()=>{};return t.jsxs("article",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[t.jsxs(ae,{to:"/",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[t.jsx(is,{size:16}),t.jsx("span",{children:"返回首页"})]}),t.jsx(ht,{slug:s.slug,title:s.title,onDeleted:o})]}),t.jsxs("header",{className:"mb-8",children:[s.imageUrl&&t.jsx("div",{className:"mb-8 rounded-xl overflow-hidden shadow-lg bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900",children:t.jsx("img",{src:s.imageUrl,alt:s.title,className:"w-full h-auto min-h-[300px] max-h-[600px] object-cover hover:scale-105 transition-transform duration-500",style:{aspectRatio:"16/10"},loading:"lazy",onError:c=>{console.warn("封面图片加载失败:",s.imageUrl);const u=c.target.parentElement;u&&(u.style.display="none")},onLoad:()=>{console.log("封面图片加载成功:",s.imageUrl)}})}),t.jsx("div",{className:"mb-6",children:t.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white leading-tight",children:s.title})}),t.jsxs("div",{className:"flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-6",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Ze,{size:16}),t.jsx("span",{children:s.author})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Ae,{size:16}),t.jsx("span",{children:s.date})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Ie,{size:16}),t.jsxs("span",{children:[s.readTime," 分钟阅读"]})]}),s.category&&t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(ft,{size:16}),t.jsx("span",{children:s.category})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:s.tags.map(c=>t.jsx(ae,{to:`/tag/${c}`,className:"px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors",children:c},c))})]}),t.jsx("div",{className:"card p-8 mb-8",children:s.content&&s.content.trim()?t.jsx("div",{className:"prose prose-gray dark:prose-dark max-w-none prose-img:rounded-lg prose-img:shadow-md",dangerouslySetInnerHTML:{__html:s.content}}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx("div",{className:"text-gray-400 mb-4",children:t.jsx("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),t.jsx("h3",{className:"text-lg font-medium text-gray-500 dark:text-gray-400 mb-2",children:"文章内容为空"}),t.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-sm",children:"该文章可能正在编辑中，或者内容尚未加载完成"})]})}),t.jsx("footer",{className:"mt-12 pt-8 border-t border-gray-200 dark:border-gray-700",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.jsx("p",{children:"感谢阅读！如果您觉得这篇文章有用，请分享给更多人。"})}),t.jsxs("div",{className:"flex space-x-4",children:[t.jsx(ae,{to:"/",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"更多文章"}),t.jsx(ae,{to:"/about",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"关于作者"})]})]})}),t.jsx(Bo,{slug:s.slug})]})}const Fo=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,Ko=Zn.create({name:"image",addOptions(){return{inline:!1,allowBase64:!1,HTMLAttributes:{}}},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes(){return{src:{default:null},alt:{default:null},title:{default:null}}},parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:e}){return["img",tr(this.options.HTMLAttributes,e)]},addCommands(){return{setImage:e=>({commands:s})=>s.insertContent({type:this.name,attrs:e})}},addInputRules(){return[Yn({find:Fo,type:this.type,getAttributes:e=>{const[,,s,r,a]=e;return{src:r,alt:s,title:a}}})]}}),qo="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",Go="ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2",pt=(e,s)=>{for(const r in s)e[r]=s[r];return e},hr="numeric",gr="ascii",xr="alpha",At="asciinumeric",St="alphanumeric",fr="domain",hn="emoji",Vo="scheme",Jo="slashscheme",Gs="whitespace";function Xo(e,s){return e in s||(s[e]=[]),s[e]}function rt(e,s,r){s[hr]&&(s[At]=!0,s[St]=!0),s[gr]&&(s[At]=!0,s[xr]=!0),s[At]&&(s[St]=!0),s[xr]&&(s[St]=!0),s[St]&&(s[fr]=!0),s[hn]&&(s[fr]=!0);for(const a in s){const n=Xo(a,r);n.indexOf(e)<0&&n.push(e)}}function Qo(e,s){const r={};for(const a in s)s[a].indexOf(e)>=0&&(r[a]=!0);return r}function Ce(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}Ce.groups={};Ce.prototype={accepts(){return!!this.t},go(e){const s=this,r=s.j[e];if(r)return r;for(let a=0;a<s.jr.length;a++){const n=s.jr[a][0],l=s.jr[a][1];if(l&&n.test(e))return l}return s.jd},has(e,s=!1){return s?e in this.j:!!this.go(e)},ta(e,s,r,a){for(let n=0;n<e.length;n++)this.tt(e[n],s,r,a)},tr(e,s,r,a){a=a||Ce.groups;let n;return s&&s.j?n=s:(n=new Ce(s),r&&a&&rt(s,r,a)),this.jr.push([e,n]),n},ts(e,s,r,a){let n=this;const l=e.length;if(!l)return n;for(let i=0;i<l-1;i++)n=n.tt(e[i]);return n.tt(e[l-1],s,r,a)},tt(e,s,r,a){a=a||Ce.groups;const n=this;if(s&&s.j)return n.j[e]=s,s;const l=s;let i,o=n.go(e);if(o?(i=new Ce,pt(i.j,o.j),i.jr.push.apply(i.jr,o.jr),i.jd=o.jd,i.t=o.t):i=new Ce,l){if(a)if(i.t&&typeof i.t=="string"){const c=pt(Qo(i.t,a),r);rt(l,c,a)}else r&&rt(l,r,a);i.t=l}return n.j[e]=i,i}};const se=(e,s,r,a,n)=>e.ta(s,r,a,n),xe=(e,s,r,a,n)=>e.tr(s,r,a,n),fa=(e,s,r,a,n)=>e.ts(s,r,a,n),R=(e,s,r,a,n)=>e.tt(s,r,a,n),Be="WORD",mr="UWORD",gn="ASCIINUMERICAL",xn="ALPHANUMERICAL",$t="LOCALHOST",pr="TLD",yr="UTLD",rs="SCHEME",ut="SLASH_SCHEME",Sr="NUM",br="WS",Cr="NL",Rt="OPENBRACE",Mt="CLOSEBRACE",cs="OPENBRACKET",ds="CLOSEBRACKET",us="OPENPAREN",hs="CLOSEPAREN",gs="OPENANGLEBRACKET",xs="CLOSEANGLEBRACKET",fs="FULLWIDTHLEFTPAREN",ms="FULLWIDTHRIGHTPAREN",ps="LEFTCORNERBRACKET",ys="RIGHTCORNERBRACKET",bs="LEFTWHITECORNERBRACKET",vs="RIGHTWHITECORNERBRACKET",js="FULLWIDTHLESSTHAN",ks="FULLWIDTHGREATERTHAN",ws="AMPERSAND",Ns="APOSTROPHE",Es="ASTERISK",Ke="AT",Ss="BACKSLASH",Cs="BACKTICK",As="CARET",qe="COLON",Ar="COMMA",Rs="DOLLAR",ze="DOT",Ms="EQUALS",Rr="EXCLAMATION",Me="HYPHEN",Tt="PERCENT",Ts="PIPE",Ls="PLUS",Is="POUND",Lt="QUERY",Mr="QUOTE",fn="FULLWIDTHMIDDLEDOT",Tr="SEMI",$e="SLASH",It="TILDE",Os="UNDERSCORE",mn="EMOJI",Ps="SYM";var pn=Object.freeze({__proto__:null,ALPHANUMERICAL:xn,AMPERSAND:ws,APOSTROPHE:Ns,ASCIINUMERICAL:gn,ASTERISK:Es,AT:Ke,BACKSLASH:Ss,BACKTICK:Cs,CARET:As,CLOSEANGLEBRACKET:xs,CLOSEBRACE:Mt,CLOSEBRACKET:ds,CLOSEPAREN:hs,COLON:qe,COMMA:Ar,DOLLAR:Rs,DOT:ze,EMOJI:mn,EQUALS:Ms,EXCLAMATION:Rr,FULLWIDTHGREATERTHAN:ks,FULLWIDTHLEFTPAREN:fs,FULLWIDTHLESSTHAN:js,FULLWIDTHMIDDLEDOT:fn,FULLWIDTHRIGHTPAREN:ms,HYPHEN:Me,LEFTCORNERBRACKET:ps,LEFTWHITECORNERBRACKET:bs,LOCALHOST:$t,NL:Cr,NUM:Sr,OPENANGLEBRACKET:gs,OPENBRACE:Rt,OPENBRACKET:cs,OPENPAREN:us,PERCENT:Tt,PIPE:Ts,PLUS:Ls,POUND:Is,QUERY:Lt,QUOTE:Mr,RIGHTCORNERBRACKET:ys,RIGHTWHITECORNERBRACKET:vs,SCHEME:rs,SEMI:Tr,SLASH:$e,SLASH_SCHEME:ut,SYM:Ps,TILDE:It,TLD:pr,UNDERSCORE:Os,UTLD:yr,UWORD:mr,WORD:Be,WS:br});const Ue=/[a-z]/,wt=/\p{L}/u,Vs=/\p{Emoji}/u,He=/\d/,Js=/\s/,ma="\r",Xs=`
`,Zo="️",Yo="‍",Qs="￼";let Jt=null,Xt=null;function ec(e=[]){const s={};Ce.groups=s;const r=new Ce;Jt==null&&(Jt=pa(qo)),Xt==null&&(Xt=pa(Go)),R(r,"'",Ns),R(r,"{",Rt),R(r,"}",Mt),R(r,"[",cs),R(r,"]",ds),R(r,"(",us),R(r,")",hs),R(r,"<",gs),R(r,">",xs),R(r,"（",fs),R(r,"）",ms),R(r,"「",ps),R(r,"」",ys),R(r,"『",bs),R(r,"』",vs),R(r,"＜",js),R(r,"＞",ks),R(r,"&",ws),R(r,"*",Es),R(r,"@",Ke),R(r,"`",Cs),R(r,"^",As),R(r,":",qe),R(r,",",Ar),R(r,"$",Rs),R(r,".",ze),R(r,"=",Ms),R(r,"!",Rr),R(r,"-",Me),R(r,"%",Tt),R(r,"|",Ts),R(r,"+",Ls),R(r,"#",Is),R(r,"?",Lt),R(r,'"',Mr),R(r,"/",$e),R(r,";",Tr),R(r,"~",It),R(r,"_",Os),R(r,"\\",Ss),R(r,"・",fn);const a=xe(r,He,Sr,{[hr]:!0});xe(a,He,a);const n=xe(a,Ue,gn,{[At]:!0}),l=xe(a,wt,xn,{[St]:!0}),i=xe(r,Ue,Be,{[gr]:!0});xe(i,He,n),xe(i,Ue,i),xe(n,He,n),xe(n,Ue,n);const o=xe(r,wt,mr,{[xr]:!0});xe(o,Ue),xe(o,He,l),xe(o,wt,o),xe(l,He,l),xe(l,Ue),xe(l,wt,l);const c=R(r,Xs,Cr,{[Gs]:!0}),x=R(r,ma,br,{[Gs]:!0}),u=xe(r,Js,br,{[Gs]:!0});R(r,Qs,u),R(x,Xs,c),R(x,Qs,u),xe(x,Js,u),R(u,ma),R(u,Xs),xe(u,Js,u),R(u,Qs,u);const h=xe(r,Vs,mn,{[hn]:!0});R(h,"#"),xe(h,Vs,h),R(h,Zo,h);const p=R(h,Yo);R(p,"#"),xe(p,Vs,h);const m=[[Ue,i],[He,n]],k=[[Ue,null],[wt,o],[He,l]];for(let A=0;A<Jt.length;A++)Fe(r,Jt[A],pr,Be,m);for(let A=0;A<Xt.length;A++)Fe(r,Xt[A],yr,mr,k);rt(pr,{tld:!0,ascii:!0},s),rt(yr,{utld:!0,alpha:!0},s),Fe(r,"file",rs,Be,m),Fe(r,"mailto",rs,Be,m),Fe(r,"http",ut,Be,m),Fe(r,"https",ut,Be,m),Fe(r,"ftp",ut,Be,m),Fe(r,"ftps",ut,Be,m),rt(rs,{scheme:!0,ascii:!0},s),rt(ut,{slashscheme:!0,ascii:!0},s),e=e.sort((A,N)=>A[0]>N[0]?1:-1);for(let A=0;A<e.length;A++){const N=e[A][0],_=e[A][1]?{[Vo]:!0}:{[Jo]:!0};N.indexOf("-")>=0?_[fr]=!0:Ue.test(N)?He.test(N)?_[At]=!0:_[gr]=!0:_[hr]=!0,fa(r,N,N,_)}return fa(r,"localhost",$t,{ascii:!0}),r.jd=new Ce(Ps),{start:r,tokens:pt({groups:s},pn)}}function yn(e,s){const r=tc(s.replace(/[A-Z]/g,o=>o.toLowerCase())),a=r.length,n=[];let l=0,i=0;for(;i<a;){let o=e,c=null,x=0,u=null,h=-1,p=-1;for(;i<a&&(c=o.go(r[i]));)o=c,o.accepts()?(h=0,p=0,u=o):h>=0&&(h+=r[i].length,p++),x+=r[i].length,l+=r[i].length,i++;l-=h,i-=p,x-=h,n.push({t:u.t,v:s.slice(l-x,l),s:l-x,e:l})}return n}function tc(e){const s=[],r=e.length;let a=0;for(;a<r;){let n=e.charCodeAt(a),l,i=n<55296||n>56319||a+1===r||(l=e.charCodeAt(a+1))<56320||l>57343?e[a]:e.slice(a,a+2);s.push(i),a+=i.length}return s}function Fe(e,s,r,a,n){let l;const i=s.length;for(let o=0;o<i-1;o++){const c=s[o];e.j[c]?l=e.j[c]:(l=new Ce(a),l.jr=n.slice(),e.j[c]=l),e=l}return l=new Ce(r),l.jr=n.slice(),e.j[s[i-1]]=l,l}function pa(e){const s=[],r=[];let a=0,n="0123456789";for(;a<e.length;){let l=0;for(;n.indexOf(e[a+l])>=0;)l++;if(l>0){s.push(r.join(""));for(let i=parseInt(e.substring(a,a+l),10);i>0;i--)r.pop();a+=l}else r.push(e[a]),a++}return s}const Dt={defaultProtocol:"http",events:null,format:ya,formatHref:ya,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Lr(e,s=null){let r=pt({},Dt);e&&(r=pt(r,e instanceof Lr?e.o:e));const a=r.ignoreTags,n=[];for(let l=0;l<a.length;l++)n.push(a[l].toUpperCase());this.o=r,s&&(this.defaultRender=s),this.ignoreTags=n}Lr.prototype={o:Dt,ignoreTags:[],defaultRender(e){return e},check(e){return this.get("validate",e.toString(),e)},get(e,s,r){const a=s!=null;let n=this.o[e];return n&&(typeof n=="object"?(n=r.t in n?n[r.t]:Dt[e],typeof n=="function"&&a&&(n=n(s,r))):typeof n=="function"&&a&&(n=n(s,r.t,r)),n)},getObj(e,s,r){let a=this.o[e];return typeof a=="function"&&s!=null&&(a=a(s,r.t,r)),a},render(e){const s=e.render(this);return(this.get("render",null,e)||this.defaultRender)(s,e.t,e)}};function ya(e){return e}function bn(e,s){this.t="token",this.v=e,this.tk=s}bn.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){const s=this.toString(),r=e.get("truncate",s,this),a=e.get("format",s,this);return r&&a.length>r?a.substring(0,r)+"…":a},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=Dt.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){const s=this,r=this.toHref(e.get("defaultProtocol")),a=e.get("formatHref",r,this),n=e.get("tagName",r,s),l=this.toFormattedString(e),i={},o=e.get("className",r,s),c=e.get("target",r,s),x=e.get("rel",r,s),u=e.getObj("attributes",r,s),h=e.getObj("events",r,s);return i.href=a,o&&(i.class=o),c&&(i.target=c),x&&(i.rel=x),u&&pt(i,u),{tagName:n,attributes:i,content:l,eventListeners:h}}};function Bs(e,s){class r extends bn{constructor(n,l){super(n,l),this.t=e}}for(const a in s)r.prototype[a]=s[a];return r.t=e,r}const ba=Bs("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),va=Bs("text"),sc=Bs("nl"),Qt=Bs("url",{isLink:!0,toHref(e=Dt.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){const e=this.tk;return e.length>=2&&e[0].t!==$t&&e[1].t===qe}}),Re=e=>new Ce(e);function rc({groups:e}){const s=e.domain.concat([ws,Es,Ke,Ss,Cs,As,Rs,Ms,Me,Sr,Tt,Ts,Ls,Is,$e,Ps,It,Os]),r=[Ns,qe,Ar,ze,Rr,Tt,Lt,Mr,Tr,gs,xs,Rt,Mt,ds,cs,us,hs,fs,ms,ps,ys,bs,vs,js,ks],a=[ws,Ns,Es,Ss,Cs,As,Rs,Ms,Me,Rt,Mt,Tt,Ts,Ls,Is,Lt,$e,Ps,It,Os],n=Re(),l=R(n,It);se(l,a,l),se(l,e.domain,l);const i=Re(),o=Re(),c=Re();se(n,e.domain,i),se(n,e.scheme,o),se(n,e.slashscheme,c),se(i,a,l),se(i,e.domain,i);const x=R(i,Ke);R(l,Ke,x),R(o,Ke,x),R(c,Ke,x);const u=R(l,ze);se(u,a,l),se(u,e.domain,l);const h=Re();se(x,e.domain,h),se(h,e.domain,h);const p=R(h,ze);se(p,e.domain,h);const m=Re(ba);se(p,e.tld,m),se(p,e.utld,m),R(x,$t,m);const k=R(h,Me);R(k,Me,k),se(k,e.domain,h),se(m,e.domain,h),R(m,ze,p),R(m,Me,k);const A=R(m,qe);se(A,e.numeric,ba);const N=R(i,Me),E=R(i,ze);R(N,Me,N),se(N,e.domain,i),se(E,a,l),se(E,e.domain,i);const _=Re(Qt);se(E,e.tld,_),se(E,e.utld,_),se(_,e.domain,i),se(_,a,l),R(_,ze,E),R(_,Me,N),R(_,Ke,x);const K=R(_,qe),T=Re(Qt);se(K,e.numeric,T);const H=Re(Qt),$=Re();se(H,s,H),se(H,r,$),se($,s,H),se($,r,$),R(_,$e,H),R(T,$e,H);const G=R(o,qe),z=R(c,qe),O=R(z,$e),X=R(O,$e);se(o,e.domain,i),R(o,ze,E),R(o,Me,N),se(c,e.domain,i),R(c,ze,E),R(c,Me,N),se(G,e.domain,H),R(G,$e,H),R(G,Lt,H),se(X,e.domain,H),se(X,s,H),R(X,$e,H);const Z=[[Rt,Mt],[cs,ds],[us,hs],[gs,xs],[fs,ms],[ps,ys],[bs,vs],[js,ks]];for(let Y=0;Y<Z.length;Y++){const[ue,le]=Z[Y],ce=R(H,ue);R($,ue,ce),R(ce,le,H);const w=Re(Qt);se(ce,s,w);const f=Re();se(ce,r),se(w,s,w),se(w,r,f),se(f,s,w),se(f,r,f),R(w,le,H),R(f,le,H)}return R(n,$t,_),R(n,Cr,sc),{start:n,tokens:pn}}function ac(e,s,r){let a=r.length,n=0,l=[],i=[];for(;n<a;){let o=e,c=null,x=null,u=0,h=null,p=-1;for(;n<a&&!(c=o.go(r[n].t));)i.push(r[n++]);for(;n<a&&(x=c||o.go(r[n].t));)c=null,o=x,o.accepts()?(p=0,h=o):p>=0&&p++,n++,u++;if(p<0)n-=u,n<a&&(i.push(r[n]),n++);else{i.length>0&&(l.push(Zs(va,s,i)),i=[]),n-=p,u-=p;const m=h.t,k=r.slice(n-u,n);l.push(Zs(m,s,k))}}return i.length>0&&l.push(Zs(va,s,i)),l}function Zs(e,s,r){const a=r[0].s,n=r[r.length-1].e,l=s.slice(a,n);return new e(l,r)}const nc=typeof console<"u"&&console&&console.warn||(()=>{}),ic="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",he={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function lc(){return Ce.groups={},he.scanner=null,he.parser=null,he.tokenQueue=[],he.pluginQueue=[],he.customSchemes=[],he.initialized=!1,he}function ja(e,s=!1){if(he.initialized&&nc(`linkifyjs: already initialized - will not register custom scheme "${e}" ${ic}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw new Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);he.customSchemes.push([e,s])}function oc(){he.scanner=ec(he.customSchemes);for(let e=0;e<he.tokenQueue.length;e++)he.tokenQueue[e][1]({scanner:he.scanner});he.parser=rc(he.scanner.tokens);for(let e=0;e<he.pluginQueue.length;e++)he.pluginQueue[e][1]({scanner:he.scanner,parser:he.parser});return he.initialized=!0,he}function Ir(e){return he.initialized||oc(),ac(he.parser.start,e,yn(he.scanner.start,e))}Ir.scan=yn;function vn(e,s=null,r=null){if(s&&typeof s=="object"){if(r)throw Error(`linkifyjs: Invalid link type ${s}; must be a string`);r=s,s=null}const a=new Lr(r),n=Ir(e),l=[];for(let i=0;i<n.length;i++){const o=n[i];o.isLink&&(!s||o.t===s)&&a.check(o)&&l.push(o.toFormattedObject(a))}return l}function cc(e){return e.length===1?e[0].isLink:e.length===3&&e[1].isLink?["()","[]"].includes(e[0].value+e[2].value):!1}function dc(e){return new $s({key:new Ds("autolink"),appendTransaction:(s,r,a)=>{const n=s.some(x=>x.docChanged)&&!r.doc.eq(a.doc),l=s.some(x=>x.getMeta("preventAutolink"));if(!n||l)return;const{tr:i}=a,o=si(r.doc,[...s]);if(ri(o).forEach(({newRange:x})=>{const u=ai(a.doc,x,m=>m.isTextblock);let h,p;if(u.length>1?(h=u[0],p=a.doc.textBetween(h.pos,h.pos+h.node.nodeSize,void 0," ")):u.length&&a.doc.textBetween(x.from,x.to," "," ").endsWith(" ")&&(h=u[0],p=a.doc.textBetween(h.pos,x.to,void 0," ")),h&&p){const m=p.split(" ").filter(E=>E!=="");if(m.length<=0)return!1;const k=m[m.length-1],A=h.pos+p.lastIndexOf(k);if(!k)return!1;const N=Ir(k).map(E=>E.toObject(e.defaultProtocol));if(!cc(N))return!1;N.filter(E=>E.isLink).map(E=>({...E,from:A+E.start+1,to:A+E.end+1})).filter(E=>a.schema.marks.code?!a.doc.rangeHasMark(E.from,E.to,a.schema.marks.code):!0).filter(E=>e.validate(E.value)).filter(E=>e.shouldAutoLink(E.value)).forEach(E=>{ni(E.from,E.to,a.doc).some(_=>_.mark.type===e.type)||i.addMark(E.from,E.to,e.type.create({href:E.href}))})}}),!!i.steps.length)return i}})}function uc(e){return new $s({key:new Ds("handleClickLink"),props:{handleClick:(s,r,a)=>{var n,l;if(a.button!==0||!s.editable)return!1;let i=a.target;const o=[];for(;i.nodeName!=="DIV";)o.push(i),i=i.parentNode;if(!o.find(p=>p.nodeName==="A"))return!1;const c=ii(s.state,e.type.name),x=a.target,u=(n=x==null?void 0:x.href)!==null&&n!==void 0?n:c.href,h=(l=x==null?void 0:x.target)!==null&&l!==void 0?l:c.target;return x&&u?(window.open(u,h),!0):!1}}})}function hc(e){return new $s({key:new Ds("handlePasteLink"),props:{handlePaste:(s,r,a)=>{const{state:n}=s,{selection:l}=n,{empty:i}=l;if(i)return!1;let o="";a.content.forEach(x=>{o+=x.textContent});const c=vn(o,{defaultProtocol:e.defaultProtocol}).find(x=>x.isLink&&x.value===o);return!o||!c?!1:e.editor.commands.setMark(e.type,{href:c.href})}}})}const gc=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function tt(e,s){const r=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return s&&s.forEach(a=>{const n=typeof a=="string"?a:a.scheme;n&&r.push(n)}),!e||e.replace(gc,"").match(new RegExp(`^(?:(?:${r.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}const xc=ei.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if(typeof e=="string"){ja(e);return}ja(e.scheme,e.optionalSlashes)})},onDestroy(){lc()},inclusive(){return this.options.autolink},addOptions(){return{openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,s)=>!!tt(e,s.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}},addAttributes(){return{href:{default:null,parseHTML(e){return e.getAttribute("href")}},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{const s=e.getAttribute("href");return!s||!this.options.isAllowedUri(s,{defaultValidate:r=>!!tt(r,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:s=>!!tt(s,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",tr(this.options.HTMLAttributes,e),0]:["a",tr(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:s})=>{const{href:r}=e;return this.options.isAllowedUri(r,{defaultValidate:a=>!!tt(a,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?s().setMark(this.name,e).setMeta("preventAutolink",!0).run():!1},toggleLink:e=>({chain:s})=>{const{href:r}=e;return this.options.isAllowedUri(r,{defaultValidate:a=>!!tt(a,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?s().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run():!1},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[ti({find:e=>{const s=[];if(e){const{protocols:r,defaultProtocol:a}=this.options,n=vn(e).filter(l=>l.isLink&&this.options.isAllowedUri(l.value,{defaultValidate:i=>!!tt(i,r),protocols:r,defaultProtocol:a}));n.length&&n.forEach(l=>s.push({text:l.value,data:{href:l.href},index:l.start}))}return s},type:this.type,getAttributes:e=>{var s;return{href:(s=e.data)===null||s===void 0?void 0:s.href}}})]},addProseMirrorPlugins(){const e=[],{protocols:s,defaultProtocol:r}=this.options;return this.options.autolink&&e.push(dc({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:a=>this.options.isAllowedUri(a,{defaultValidate:n=>!!tt(n,s),protocols:s,defaultProtocol:r}),shouldAutoLink:this.options.shouldAutoLink})),this.options.openOnClick===!0&&e.push(uc({type:this.type})),this.options.linkOnPaste&&e.push(hc({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),e}});function fc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function jn(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(s=>{const r=e[s],a=typeof r;(a==="object"||a==="function")&&!Object.isFrozen(r)&&jn(r)}),e}let ka=class{constructor(s){s.data===void 0&&(s.data={}),this.data=s.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}};function kn(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function Ve(e,...s){const r=Object.create(null);for(const a in e)r[a]=e[a];return s.forEach(function(a){for(const n in a)r[n]=a[n]}),r}const mc="</span>",wa=e=>!!e.scope,pc=(e,{prefix:s})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const r=e.split(".");return[`${s}${r.shift()}`,...r.map((a,n)=>`${a}${"_".repeat(n+1)}`)].join(" ")}return`${s}${e}`};let yc=class{constructor(s,r){this.buffer="",this.classPrefix=r.classPrefix,s.walk(this)}addText(s){this.buffer+=kn(s)}openNode(s){if(!wa(s))return;const r=pc(s.scope,{prefix:this.classPrefix});this.span(r)}closeNode(s){wa(s)&&(this.buffer+=mc)}value(){return this.buffer}span(s){this.buffer+=`<span class="${s}">`}};const Na=(e={})=>{const s={children:[]};return Object.assign(s,e),s};let bc=class wn{constructor(){this.rootNode=Na(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(s){this.top.children.push(s)}openNode(s){const r=Na({scope:s});this.add(r),this.stack.push(r)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(s){return this.constructor._walk(s,this.rootNode)}static _walk(s,r){return typeof r=="string"?s.addText(r):r.children&&(s.openNode(r),r.children.forEach(a=>this._walk(s,a)),s.closeNode(r)),s}static _collapse(s){typeof s!="string"&&s.children&&(s.children.every(r=>typeof r=="string")?s.children=[s.children.join("")]:s.children.forEach(r=>{wn._collapse(r)}))}},vc=class extends bc{constructor(s){super(),this.options=s}addText(s){s!==""&&this.add(s)}startScope(s){this.openNode(s)}endScope(){this.closeNode()}__addSublanguage(s,r){const a=s.root;r&&(a.scope=`language:${r}`),this.add(a)}toHTML(){return new yc(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}};function Ut(e){return e?typeof e=="string"?e:e.source:null}function Nn(e){return lt("(?=",e,")")}function jc(e){return lt("(?:",e,")*")}function kc(e){return lt("(?:",e,")?")}function lt(...e){return e.map(r=>Ut(r)).join("")}function wc(e){const s=e[e.length-1];return typeof s=="object"&&s.constructor===Object?(e.splice(e.length-1,1),s):{}}function Or(...e){return"("+(wc(e).capture?"":"?:")+e.map(a=>Ut(a)).join("|")+")"}function En(e){return new RegExp(e.toString()+"|").exec("").length-1}function Nc(e,s){const r=e&&e.exec(s);return r&&r.index===0}const Ec=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function Pr(e,{joinWith:s}){let r=0;return e.map(a=>{r+=1;const n=r;let l=Ut(a),i="";for(;l.length>0;){const o=Ec.exec(l);if(!o){i+=l;break}i+=l.substring(0,o.index),l=l.substring(o.index+o[0].length),o[0][0]==="\\"&&o[1]?i+="\\"+String(Number(o[1])+n):(i+=o[0],o[0]==="("&&r++)}return i}).map(a=>`(${a})`).join(s)}const Sc=/\b\B/,Sn="[a-zA-Z]\\w*",_r="[a-zA-Z_]\\w*",Cn="\\b\\d+(\\.\\d+)?",An="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Rn="\\b(0b[01]+)",Cc="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",Ac=(e={})=>{const s=/^#![ ]*\//;return e.binary&&(e.begin=lt(s,/.*\b/,e.binary,/\b.*/)),Ve({scope:"meta",begin:s,end:/$/,relevance:0,"on:begin":(r,a)=>{r.index!==0&&a.ignoreMatch()}},e)},Ht={begin:"\\\\[\\s\\S]",relevance:0},Rc={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[Ht]},Mc={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[Ht]},Tc={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Ws=function(e,s,r={}){const a=Ve({scope:"comment",begin:e,end:s,contains:[]},r);a.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const n=Or("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return a.contains.push({begin:lt(/[ ]+/,"(",n,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),a},Lc=Ws("//","$"),Ic=Ws("/\\*","\\*/"),Oc=Ws("#","$"),Pc={scope:"number",begin:Cn,relevance:0},_c={scope:"number",begin:An,relevance:0},zc={scope:"number",begin:Rn,relevance:0},$c={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[Ht,{begin:/\[/,end:/\]/,relevance:0,contains:[Ht]}]},Dc={scope:"title",begin:Sn,relevance:0},Uc={scope:"title",begin:_r,relevance:0},Hc={begin:"\\.\\s*"+_r,relevance:0},Bc=function(e){return Object.assign(e,{"on:begin":(s,r)=>{r.data._beginMatch=s[1]},"on:end":(s,r)=>{r.data._beginMatch!==s[1]&&r.ignoreMatch()}})};var Zt=Object.freeze({__proto__:null,APOS_STRING_MODE:Rc,BACKSLASH_ESCAPE:Ht,BINARY_NUMBER_MODE:zc,BINARY_NUMBER_RE:Rn,COMMENT:Ws,C_BLOCK_COMMENT_MODE:Ic,C_LINE_COMMENT_MODE:Lc,C_NUMBER_MODE:_c,C_NUMBER_RE:An,END_SAME_AS_BEGIN:Bc,HASH_COMMENT_MODE:Oc,IDENT_RE:Sn,MATCH_NOTHING_RE:Sc,METHOD_GUARD:Hc,NUMBER_MODE:Pc,NUMBER_RE:Cn,PHRASAL_WORDS_MODE:Tc,QUOTE_STRING_MODE:Mc,REGEXP_MODE:$c,RE_STARTERS_RE:Cc,SHEBANG:Ac,TITLE_MODE:Dc,UNDERSCORE_IDENT_RE:_r,UNDERSCORE_TITLE_MODE:Uc});function Wc(e,s){e.input[e.index-1]==="."&&s.ignoreMatch()}function Fc(e,s){e.className!==void 0&&(e.scope=e.className,delete e.className)}function Kc(e,s){s&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=Wc,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function qc(e,s){Array.isArray(e.illegal)&&(e.illegal=Or(...e.illegal))}function Gc(e,s){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function Vc(e,s){e.relevance===void 0&&(e.relevance=1)}const Jc=(e,s)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const r=Object.assign({},e);Object.keys(e).forEach(a=>{delete e[a]}),e.keywords=r.keywords,e.begin=lt(r.beforeMatch,Nn(r.begin)),e.starts={relevance:0,contains:[Object.assign(r,{endsParent:!0})]},e.relevance=0,delete r.beforeMatch},Xc=["of","and","for","in","not","or","if","then","parent","list","value"],Qc="keyword";function Mn(e,s,r=Qc){const a=Object.create(null);return typeof e=="string"?n(r,e.split(" ")):Array.isArray(e)?n(r,e):Object.keys(e).forEach(function(l){Object.assign(a,Mn(e[l],s,l))}),a;function n(l,i){s&&(i=i.map(o=>o.toLowerCase())),i.forEach(function(o){const c=o.split("|");a[c[0]]=[l,Zc(c[0],c[1])]})}}function Zc(e,s){return s?Number(s):Yc(e)?0:1}function Yc(e){return Xc.includes(e.toLowerCase())}const Ea={},at=e=>{console.error(e)},Sa=(e,...s)=>{console.log(`WARN: ${e}`,...s)},ct=(e,s)=>{Ea[`${e}/${s}`]||(console.log(`Deprecated as of ${e}. ${s}`),Ea[`${e}/${s}`]=!0)},_s=new Error;function Tn(e,s,{key:r}){let a=0;const n=e[r],l={},i={};for(let o=1;o<=s.length;o++)i[o+a]=n[o],l[o+a]=!0,a+=En(s[o-1]);e[r]=i,e[r]._emit=l,e[r]._multi=!0}function ed(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw at("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),_s;if(typeof e.beginScope!="object"||e.beginScope===null)throw at("beginScope must be object"),_s;Tn(e,e.begin,{key:"beginScope"}),e.begin=Pr(e.begin,{joinWith:""})}}function td(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw at("skip, excludeEnd, returnEnd not compatible with endScope: {}"),_s;if(typeof e.endScope!="object"||e.endScope===null)throw at("endScope must be object"),_s;Tn(e,e.end,{key:"endScope"}),e.end=Pr(e.end,{joinWith:""})}}function sd(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function rd(e){sd(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),ed(e),td(e)}function ad(e){function s(i,o){return new RegExp(Ut(i),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(o?"g":""))}class r{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(o,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,o]),this.matchAt+=En(o)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const o=this.regexes.map(c=>c[1]);this.matcherRe=s(Pr(o,{joinWith:"|"}),!0),this.lastIndex=0}exec(o){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(o);if(!c)return null;const x=c.findIndex((h,p)=>p>0&&h!==void 0),u=this.matchIndexes[x];return c.splice(0,x),Object.assign(c,u)}}class a{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(o){if(this.multiRegexes[o])return this.multiRegexes[o];const c=new r;return this.rules.slice(o).forEach(([x,u])=>c.addRule(x,u)),c.compile(),this.multiRegexes[o]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(o,c){this.rules.push([o,c]),c.type==="begin"&&this.count++}exec(o){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let x=c.exec(o);if(this.resumingScanAtSamePosition()&&!(x&&x.index===this.lastIndex)){const u=this.getMatcher(0);u.lastIndex=this.lastIndex+1,x=u.exec(o)}return x&&(this.regexIndex+=x.position+1,this.regexIndex===this.count&&this.considerAll()),x}}function n(i){const o=new a;return i.contains.forEach(c=>o.addRule(c.begin,{rule:c,type:"begin"})),i.terminatorEnd&&o.addRule(i.terminatorEnd,{type:"end"}),i.illegal&&o.addRule(i.illegal,{type:"illegal"}),o}function l(i,o){const c=i;if(i.isCompiled)return c;[Fc,Gc,rd,Jc].forEach(u=>u(i,o)),e.compilerExtensions.forEach(u=>u(i,o)),i.__beforeBegin=null,[Kc,qc,Vc].forEach(u=>u(i,o)),i.isCompiled=!0;let x=null;return typeof i.keywords=="object"&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),x=i.keywords.$pattern,delete i.keywords.$pattern),x=x||/\w+/,i.keywords&&(i.keywords=Mn(i.keywords,e.case_insensitive)),c.keywordPatternRe=s(x,!0),o&&(i.begin||(i.begin=/\B|\b/),c.beginRe=s(c.begin),!i.end&&!i.endsWithParent&&(i.end=/\B|\b/),i.end&&(c.endRe=s(c.end)),c.terminatorEnd=Ut(c.end)||"",i.endsWithParent&&o.terminatorEnd&&(c.terminatorEnd+=(i.end?"|":"")+o.terminatorEnd)),i.illegal&&(c.illegalRe=s(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(u){return nd(u==="self"?i:u)})),i.contains.forEach(function(u){l(u,c)}),i.starts&&l(i.starts,o),c.matcher=n(c),c}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=Ve(e.classNameAliases||{}),l(e)}function Ln(e){return e?e.endsWithParent||Ln(e.starts):!1}function nd(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(s){return Ve(e,{variants:null},s)})),e.cachedVariants?e.cachedVariants:Ln(e)?Ve(e,{starts:e.starts?Ve(e.starts):null}):Object.isFrozen(e)?Ve(e):e}var id="11.10.0";let ld=class extends Error{constructor(s,r){super(s),this.name="HTMLInjectionError",this.html=r}};const Ys=kn,Ca=Ve,Aa=Symbol("nomatch"),od=7,In=function(e){const s=Object.create(null),r=Object.create(null),a=[];let n=!0;const l="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let o={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:vc};function c(f){return o.noHighlightRe.test(f)}function x(f){let j=f.className+" ";j+=f.parentNode?f.parentNode.className:"";const b=o.languageDetectRe.exec(j);if(b){const S=O(b[1]);return S||(Sa(l.replace("{}",b[1])),Sa("Falling back to no-highlight mode for this block.",f)),S?b[1]:"no-highlight"}return j.split(/\s+/).find(S=>c(S)||O(S))}function u(f,j,b){let S="",L="";typeof j=="object"?(S=f,b=j.ignoreIllegals,L=j.language):(ct("10.7.0","highlight(lang, code, ...args) has been deprecated."),ct("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),L=f,S=j),b===void 0&&(b=!0);const q={code:S,language:L};ce("before:highlight",q);const Q=q.result?q.result:h(q.language,q.code,b);return Q.code=q.code,ce("after:highlight",Q),Q}function h(f,j,b,S){const L=Object.create(null);function q(g,y){return g.keywords[y]}function Q(){if(!F.keywords){U.addText(te);return}let g=0;F.keywordPatternRe.lastIndex=0;let y=F.keywordPatternRe.exec(te),v="";for(;y;){v+=te.substring(g,y.index);const I=ee.case_insensitive?y[0].toLowerCase():y[0],V=q(F,I);if(V){const[J,Ee]=V;if(U.addText(v),v="",L[I]=(L[I]||0)+1,L[I]<=od&&(Ne+=Ee),J.startsWith("_"))v+=y[0];else{const et=ee.classNameAliases[J]||J;oe(y[0],et)}}else v+=y[0];g=F.keywordPatternRe.lastIndex,y=F.keywordPatternRe.exec(te)}v+=te.substring(g),U.addText(v)}function C(){if(te==="")return;let g=null;if(typeof F.subLanguage=="string"){if(!s[F.subLanguage]){U.addText(te);return}g=h(F.subLanguage,te,!0,re[F.subLanguage]),re[F.subLanguage]=g._top}else g=m(te,F.subLanguage.length?F.subLanguage:null);F.relevance>0&&(Ne+=g.relevance),U.__addSublanguage(g._emitter,g.language)}function ne(){F.subLanguage!=null?C():Q(),te=""}function oe(g,y){g!==""&&(U.startScope(y),U.addText(g),U.endScope())}function ke(g,y){let v=1;const I=y.length-1;for(;v<=I;){if(!g._emit[v]){v++;continue}const V=ee.classNameAliases[g[v]]||g[v],J=y[v];V?oe(J,V):(te=J,Q(),te=""),v++}}function de(g,y){return g.scope&&typeof g.scope=="string"&&U.openNode(ee.classNameAliases[g.scope]||g.scope),g.beginScope&&(g.beginScope._wrap?(oe(te,ee.classNameAliases[g.beginScope._wrap]||g.beginScope._wrap),te=""):g.beginScope._multi&&(ke(g.beginScope,y),te="")),F=Object.create(g,{parent:{value:F}}),F}function ge(g,y,v){let I=Nc(g.endRe,v);if(I){if(g["on:end"]){const V=new ka(g);g["on:end"](y,V),V.isMatchIgnored&&(I=!1)}if(I){for(;g.endsParent&&g.parent;)g=g.parent;return g}}if(g.endsWithParent)return ge(g.parent,y,v)}function we(g){return F.matcher.regexIndex===0?(te+=g[0],1):(P=!0,0)}function be(g){const y=g[0],v=g.rule,I=new ka(v),V=[v.__beforeBegin,v["on:begin"]];for(const J of V)if(J&&(J(g,I),I.isMatchIgnored))return we(y);return v.skip?te+=y:(v.excludeBegin&&(te+=y),ne(),!v.returnBegin&&!v.excludeBegin&&(te=y)),de(v,g),v.returnBegin?0:y.length}function Oe(g){const y=g[0],v=j.substring(g.index),I=ge(F,g,v);if(!I)return Aa;const V=F;F.endScope&&F.endScope._wrap?(ne(),oe(y,F.endScope._wrap)):F.endScope&&F.endScope._multi?(ne(),ke(F.endScope,g)):V.skip?te+=y:(V.returnEnd||V.excludeEnd||(te+=y),ne(),V.excludeEnd&&(te=y));do F.scope&&U.closeNode(),!F.skip&&!F.subLanguage&&(Ne+=F.relevance),F=F.parent;while(F!==I.parent);return I.starts&&de(I.starts,g),V.returnEnd?0:y.length}function Pe(){const g=[];for(let y=F;y!==ee;y=y.parent)y.scope&&g.unshift(y.scope);g.forEach(y=>U.openNode(y))}let B={};function W(g,y){const v=y&&y[0];if(te+=g,v==null)return ne(),0;if(B.type==="begin"&&y.type==="end"&&B.index===y.index&&v===""){if(te+=j.slice(y.index,y.index+1),!n){const I=new Error(`0 width match regex (${f})`);throw I.languageName=f,I.badRule=B.rule,I}return 1}if(B=y,y.type==="begin")return be(y);if(y.type==="illegal"&&!b){const I=new Error('Illegal lexeme "'+v+'" for mode "'+(F.scope||"<unnamed>")+'"');throw I.mode=F,I}else if(y.type==="end"){const I=Oe(y);if(I!==Aa)return I}if(y.type==="illegal"&&v==="")return 1;if(_e>1e5&&_e>y.index*3)throw new Error("potential infinite loop, way more iterations than matches");return te+=v,v.length}const ee=O(f);if(!ee)throw at(l.replace("{}",f)),new Error('Unknown language: "'+f+'"');const ie=ad(ee);let M="",F=S||ie;const re={},U=new o.__emitter(o);Pe();let te="",Ne=0,Se=0,_e=0,P=!1;try{if(ee.__emitTokens)ee.__emitTokens(j,U);else{for(F.matcher.considerAll();;){_e++,P?P=!1:F.matcher.considerAll(),F.matcher.lastIndex=Se;const g=F.matcher.exec(j);if(!g)break;const y=j.substring(Se,g.index),v=W(y,g);Se=g.index+v}W(j.substring(Se))}return U.finalize(),M=U.toHTML(),{language:f,value:M,relevance:Ne,illegal:!1,_emitter:U,_top:F}}catch(g){if(g.message&&g.message.includes("Illegal"))return{language:f,value:Ys(j),illegal:!0,relevance:0,_illegalBy:{message:g.message,index:Se,context:j.slice(Se-100,Se+100),mode:g.mode,resultSoFar:M},_emitter:U};if(n)return{language:f,value:Ys(j),illegal:!1,relevance:0,errorRaised:g,_emitter:U,_top:F};throw g}}function p(f){const j={value:Ys(f),illegal:!1,relevance:0,_top:i,_emitter:new o.__emitter(o)};return j._emitter.addText(f),j}function m(f,j){j=j||o.languages||Object.keys(s);const b=p(f),S=j.filter(O).filter(Z).map(ne=>h(ne,f,!1));S.unshift(b);const L=S.sort((ne,oe)=>{if(ne.relevance!==oe.relevance)return oe.relevance-ne.relevance;if(ne.language&&oe.language){if(O(ne.language).supersetOf===oe.language)return 1;if(O(oe.language).supersetOf===ne.language)return-1}return 0}),[q,Q]=L,C=q;return C.secondBest=Q,C}function k(f,j,b){const S=j&&r[j]||b;f.classList.add("hljs"),f.classList.add(`language-${S}`)}function A(f){let j=null;const b=x(f);if(c(b))return;if(ce("before:highlightElement",{el:f,language:b}),f.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",f);return}if(f.children.length>0&&(o.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(f)),o.throwUnescapedHTML))throw new ld("One of your code blocks includes unescaped HTML.",f.innerHTML);j=f;const S=j.textContent,L=b?u(S,{language:b,ignoreIllegals:!0}):m(S);f.innerHTML=L.value,f.dataset.highlighted="yes",k(f,b,L.language),f.result={language:L.language,re:L.relevance,relevance:L.relevance},L.secondBest&&(f.secondBest={language:L.secondBest.language,relevance:L.secondBest.relevance}),ce("after:highlightElement",{el:f,result:L,text:S})}function N(f){o=Ca(o,f)}const E=()=>{T(),ct("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function _(){T(),ct("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let K=!1;function T(){if(document.readyState==="loading"){K=!0;return}document.querySelectorAll(o.cssSelector).forEach(A)}function H(){K&&T()}typeof window<"u"&&window.addEventListener&&window.addEventListener("DOMContentLoaded",H,!1);function $(f,j){let b=null;try{b=j(e)}catch(S){if(at("Language definition for '{}' could not be registered.".replace("{}",f)),n)at(S);else throw S;b=i}b.name||(b.name=f),s[f]=b,b.rawDefinition=j.bind(null,e),b.aliases&&X(b.aliases,{languageName:f})}function G(f){delete s[f];for(const j of Object.keys(r))r[j]===f&&delete r[j]}function z(){return Object.keys(s)}function O(f){return f=(f||"").toLowerCase(),s[f]||s[r[f]]}function X(f,{languageName:j}){typeof f=="string"&&(f=[f]),f.forEach(b=>{r[b.toLowerCase()]=j})}function Z(f){const j=O(f);return j&&!j.disableAutodetect}function Y(f){f["before:highlightBlock"]&&!f["before:highlightElement"]&&(f["before:highlightElement"]=j=>{f["before:highlightBlock"](Object.assign({block:j.el},j))}),f["after:highlightBlock"]&&!f["after:highlightElement"]&&(f["after:highlightElement"]=j=>{f["after:highlightBlock"](Object.assign({block:j.el},j))})}function ue(f){Y(f),a.push(f)}function le(f){const j=a.indexOf(f);j!==-1&&a.splice(j,1)}function ce(f,j){const b=f;a.forEach(function(S){S[b]&&S[b](j)})}function w(f){return ct("10.7.0","highlightBlock will be removed entirely in v12.0"),ct("10.7.0","Please use highlightElement now."),A(f)}Object.assign(e,{highlight:u,highlightAuto:m,highlightAll:T,highlightElement:A,highlightBlock:w,configure:N,initHighlighting:E,initHighlightingOnLoad:_,registerLanguage:$,unregisterLanguage:G,listLanguages:z,getLanguage:O,registerAliases:X,autoDetection:Z,inherit:Ca,addPlugin:ue,removePlugin:le}),e.debugMode=function(){n=!1},e.safeMode=function(){n=!0},e.versionString=id,e.regex={concat:lt,lookahead:Nn,either:Or,optional:kc,anyNumberOfTimes:jc};for(const f in Zt)typeof Zt[f]=="object"&&jn(Zt[f]);return Object.assign(e,Zt),e},yt=In({});yt.newInstance=()=>In({});var cd=yt;yt.HighlightJS=yt;yt.default=yt;var dd=fc(cd);function On(e,s=[]){return e.map(r=>{const a=[...s,...r.properties?r.properties.className:[]];return r.children?On(r.children,a):{text:r.value,classes:a}}).flat()}function Ra(e){return e.value||e.children||[]}function ud(e){return!!dd.getLanguage(e)}function Ma({doc:e,name:s,lowlight:r,defaultLanguage:a}){const n=[];return sr(e,l=>l.type.name===s).forEach(l=>{var i;let o=l.pos+1;const c=l.node.attrs.language||a,x=r.listLanguages(),u=c&&(x.includes(c)||ud(c)||!((i=r.registered)===null||i===void 0)&&i.call(r,c))?Ra(r.highlight(c,l.node.textContent)):Ra(r.highlightAuto(l.node.textContent));On(u).forEach(h=>{const p=o+h.text.length;if(h.classes.length){const m=oi.inline(o,p,{class:h.classes.join(" ")});n.push(m)}o=p})}),ci.create(e,n)}function hd(e){return typeof e=="function"}function gd({name:e,lowlight:s,defaultLanguage:r}){if(!["highlight","highlightAuto","listLanguages"].every(n=>hd(s[n])))throw Error("You should provide an instance of lowlight to use the code-block-lowlight extension");const a=new $s({key:new Ds("lowlight"),state:{init:(n,{doc:l})=>Ma({doc:l,name:e,lowlight:s,defaultLanguage:r}),apply:(n,l,i,o)=>{const c=i.selection.$head.parent.type.name,x=o.selection.$head.parent.type.name,u=sr(i.doc,p=>p.type.name===e),h=sr(o.doc,p=>p.type.name===e);return n.docChanged&&([c,x].includes(e)||h.length!==u.length||n.steps.some(p=>p.from!==void 0&&p.to!==void 0&&u.some(m=>m.pos>=p.from&&m.pos+m.node.nodeSize<=p.to)))?Ma({doc:n.doc,name:e,lowlight:s,defaultLanguage:r}):l.map(n.mapping,n.doc)}},props:{decorations(n){return a.getState(n)}}});return a}const xd=li.extend({addOptions(){var e;return{...(e=this.parent)===null||e===void 0?void 0:e.call(this),lowlight:{},languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},addProseMirrorPlugins(){var e;return[...((e=this.parent)===null||e===void 0?void 0:e.call(this))||[],gd({name:this.name,lowlight:this.options.lowlight,defaultLanguage:this.options.defaultLanguage})]}});function Pn(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(s=>{const r=e[s],a=typeof r;(a==="object"||a==="function")&&!Object.isFrozen(r)&&Pn(r)}),e}class Ta{constructor(s){s.data===void 0&&(s.data={}),this.data=s.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function _n(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function Je(e,...s){const r=Object.create(null);for(const a in e)r[a]=e[a];return s.forEach(function(a){for(const n in a)r[n]=a[n]}),r}const fd="</span>",La=e=>!!e.scope,md=(e,{prefix:s})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const r=e.split(".");return[`${s}${r.shift()}`,...r.map((a,n)=>`${a}${"_".repeat(n+1)}`)].join(" ")}return`${s}${e}`};class pd{constructor(s,r){this.buffer="",this.classPrefix=r.classPrefix,s.walk(this)}addText(s){this.buffer+=_n(s)}openNode(s){if(!La(s))return;const r=md(s.scope,{prefix:this.classPrefix});this.span(r)}closeNode(s){La(s)&&(this.buffer+=fd)}value(){return this.buffer}span(s){this.buffer+=`<span class="${s}">`}}const Ia=(e={})=>{const s={children:[]};return Object.assign(s,e),s};class zr{constructor(){this.rootNode=Ia(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(s){this.top.children.push(s)}openNode(s){const r=Ia({scope:s});this.add(r),this.stack.push(r)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(s){return this.constructor._walk(s,this.rootNode)}static _walk(s,r){return typeof r=="string"?s.addText(r):r.children&&(s.openNode(r),r.children.forEach(a=>this._walk(s,a)),s.closeNode(r)),s}static _collapse(s){typeof s!="string"&&s.children&&(s.children.every(r=>typeof r=="string")?s.children=[s.children.join("")]:s.children.forEach(r=>{zr._collapse(r)}))}}class yd extends zr{constructor(s){super(),this.options=s}addText(s){s!==""&&this.add(s)}startScope(s){this.openNode(s)}endScope(){this.closeNode()}__addSublanguage(s,r){const a=s.root;r&&(a.scope=`language:${r}`),this.add(a)}toHTML(){return new pd(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function Bt(e){return e?typeof e=="string"?e:e.source:null}function zn(e){return ot("(?=",e,")")}function bd(e){return ot("(?:",e,")*")}function vd(e){return ot("(?:",e,")?")}function ot(...e){return e.map(r=>Bt(r)).join("")}function jd(e){const s=e[e.length-1];return typeof s=="object"&&s.constructor===Object?(e.splice(e.length-1,1),s):{}}function $r(...e){return"("+(jd(e).capture?"":"?:")+e.map(a=>Bt(a)).join("|")+")"}function $n(e){return new RegExp(e.toString()+"|").exec("").length-1}function kd(e,s){const r=e&&e.exec(s);return r&&r.index===0}const wd=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function Dr(e,{joinWith:s}){let r=0;return e.map(a=>{r+=1;const n=r;let l=Bt(a),i="";for(;l.length>0;){const o=wd.exec(l);if(!o){i+=l;break}i+=l.substring(0,o.index),l=l.substring(o.index+o[0].length),o[0][0]==="\\"&&o[1]?i+="\\"+String(Number(o[1])+n):(i+=o[0],o[0]==="("&&r++)}return i}).map(a=>`(${a})`).join(s)}const Nd=/\b\B/,Dn="[a-zA-Z]\\w*",Ur="[a-zA-Z_]\\w*",Un="\\b\\d+(\\.\\d+)?",Hn="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Bn="\\b(0b[01]+)",Ed="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",Sd=(e={})=>{const s=/^#![ ]*\//;return e.binary&&(e.begin=ot(s,/.*\b/,e.binary,/\b.*/)),Je({scope:"meta",begin:s,end:/$/,relevance:0,"on:begin":(r,a)=>{r.index!==0&&a.ignoreMatch()}},e)},Wt={begin:"\\\\[\\s\\S]",relevance:0},Cd={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[Wt]},Ad={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[Wt]},Rd={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Fs=function(e,s,r={}){const a=Je({scope:"comment",begin:e,end:s,contains:[]},r);a.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const n=$r("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return a.contains.push({begin:ot(/[ ]+/,"(",n,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),a},Md=Fs("//","$"),Td=Fs("/\\*","\\*/"),Ld=Fs("#","$"),Id={scope:"number",begin:Un,relevance:0},Od={scope:"number",begin:Hn,relevance:0},Pd={scope:"number",begin:Bn,relevance:0},_d={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[Wt,{begin:/\[/,end:/\]/,relevance:0,contains:[Wt]}]},zd={scope:"title",begin:Dn,relevance:0},$d={scope:"title",begin:Ur,relevance:0},Dd={begin:"\\.\\s*"+Ur,relevance:0},Ud=function(e){return Object.assign(e,{"on:begin":(s,r)=>{r.data._beginMatch=s[1]},"on:end":(s,r)=>{r.data._beginMatch!==s[1]&&r.ignoreMatch()}})};var Yt=Object.freeze({__proto__:null,APOS_STRING_MODE:Cd,BACKSLASH_ESCAPE:Wt,BINARY_NUMBER_MODE:Pd,BINARY_NUMBER_RE:Bn,COMMENT:Fs,C_BLOCK_COMMENT_MODE:Td,C_LINE_COMMENT_MODE:Md,C_NUMBER_MODE:Od,C_NUMBER_RE:Hn,END_SAME_AS_BEGIN:Ud,HASH_COMMENT_MODE:Ld,IDENT_RE:Dn,MATCH_NOTHING_RE:Nd,METHOD_GUARD:Dd,NUMBER_MODE:Id,NUMBER_RE:Un,PHRASAL_WORDS_MODE:Rd,QUOTE_STRING_MODE:Ad,REGEXP_MODE:_d,RE_STARTERS_RE:Ed,SHEBANG:Sd,TITLE_MODE:zd,UNDERSCORE_IDENT_RE:Ur,UNDERSCORE_TITLE_MODE:$d});function Hd(e,s){e.input[e.index-1]==="."&&s.ignoreMatch()}function Bd(e,s){e.className!==void 0&&(e.scope=e.className,delete e.className)}function Wd(e,s){s&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=Hd,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function Fd(e,s){Array.isArray(e.illegal)&&(e.illegal=$r(...e.illegal))}function Kd(e,s){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function qd(e,s){e.relevance===void 0&&(e.relevance=1)}const Gd=(e,s)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const r=Object.assign({},e);Object.keys(e).forEach(a=>{delete e[a]}),e.keywords=r.keywords,e.begin=ot(r.beforeMatch,zn(r.begin)),e.starts={relevance:0,contains:[Object.assign(r,{endsParent:!0})]},e.relevance=0,delete r.beforeMatch},Vd=["of","and","for","in","not","or","if","then","parent","list","value"],Jd="keyword";function Wn(e,s,r=Jd){const a=Object.create(null);return typeof e=="string"?n(r,e.split(" ")):Array.isArray(e)?n(r,e):Object.keys(e).forEach(function(l){Object.assign(a,Wn(e[l],s,l))}),a;function n(l,i){s&&(i=i.map(o=>o.toLowerCase())),i.forEach(function(o){const c=o.split("|");a[c[0]]=[l,Xd(c[0],c[1])]})}}function Xd(e,s){return s?Number(s):Qd(e)?0:1}function Qd(e){return Vd.includes(e.toLowerCase())}const Oa={},nt=e=>{console.error(e)},Pa=(e,...s)=>{console.log(`WARN: ${e}`,...s)},dt=(e,s)=>{Oa[`${e}/${s}`]||(console.log(`Deprecated as of ${e}. ${s}`),Oa[`${e}/${s}`]=!0)},zs=new Error;function Fn(e,s,{key:r}){let a=0;const n=e[r],l={},i={};for(let o=1;o<=s.length;o++)i[o+a]=n[o],l[o+a]=!0,a+=$n(s[o-1]);e[r]=i,e[r]._emit=l,e[r]._multi=!0}function Zd(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw nt("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),zs;if(typeof e.beginScope!="object"||e.beginScope===null)throw nt("beginScope must be object"),zs;Fn(e,e.begin,{key:"beginScope"}),e.begin=Dr(e.begin,{joinWith:""})}}function Yd(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw nt("skip, excludeEnd, returnEnd not compatible with endScope: {}"),zs;if(typeof e.endScope!="object"||e.endScope===null)throw nt("endScope must be object"),zs;Fn(e,e.end,{key:"endScope"}),e.end=Dr(e.end,{joinWith:""})}}function eu(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function tu(e){eu(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),Zd(e),Yd(e)}function su(e){function s(i,o){return new RegExp(Bt(i),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(o?"g":""))}class r{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(o,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,o]),this.matchAt+=$n(o)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const o=this.regexes.map(c=>c[1]);this.matcherRe=s(Dr(o,{joinWith:"|"}),!0),this.lastIndex=0}exec(o){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(o);if(!c)return null;const x=c.findIndex((h,p)=>p>0&&h!==void 0),u=this.matchIndexes[x];return c.splice(0,x),Object.assign(c,u)}}class a{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(o){if(this.multiRegexes[o])return this.multiRegexes[o];const c=new r;return this.rules.slice(o).forEach(([x,u])=>c.addRule(x,u)),c.compile(),this.multiRegexes[o]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(o,c){this.rules.push([o,c]),c.type==="begin"&&this.count++}exec(o){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let x=c.exec(o);if(this.resumingScanAtSamePosition()&&!(x&&x.index===this.lastIndex)){const u=this.getMatcher(0);u.lastIndex=this.lastIndex+1,x=u.exec(o)}return x&&(this.regexIndex+=x.position+1,this.regexIndex===this.count&&this.considerAll()),x}}function n(i){const o=new a;return i.contains.forEach(c=>o.addRule(c.begin,{rule:c,type:"begin"})),i.terminatorEnd&&o.addRule(i.terminatorEnd,{type:"end"}),i.illegal&&o.addRule(i.illegal,{type:"illegal"}),o}function l(i,o){const c=i;if(i.isCompiled)return c;[Bd,Kd,tu,Gd].forEach(u=>u(i,o)),e.compilerExtensions.forEach(u=>u(i,o)),i.__beforeBegin=null,[Wd,Fd,qd].forEach(u=>u(i,o)),i.isCompiled=!0;let x=null;return typeof i.keywords=="object"&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),x=i.keywords.$pattern,delete i.keywords.$pattern),x=x||/\w+/,i.keywords&&(i.keywords=Wn(i.keywords,e.case_insensitive)),c.keywordPatternRe=s(x,!0),o&&(i.begin||(i.begin=/\B|\b/),c.beginRe=s(c.begin),!i.end&&!i.endsWithParent&&(i.end=/\B|\b/),i.end&&(c.endRe=s(c.end)),c.terminatorEnd=Bt(c.end)||"",i.endsWithParent&&o.terminatorEnd&&(c.terminatorEnd+=(i.end?"|":"")+o.terminatorEnd)),i.illegal&&(c.illegalRe=s(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(u){return ru(u==="self"?i:u)})),i.contains.forEach(function(u){l(u,c)}),i.starts&&l(i.starts,o),c.matcher=n(c),c}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=Je(e.classNameAliases||{}),l(e)}function Kn(e){return e?e.endsWithParent||Kn(e.starts):!1}function ru(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(s){return Je(e,{variants:null},s)})),e.cachedVariants?e.cachedVariants:Kn(e)?Je(e,{starts:e.starts?Je(e.starts):null}):Object.isFrozen(e)?Je(e):e}var au="11.11.1";class nu extends Error{constructor(s,r){super(s),this.name="HTMLInjectionError",this.html=r}}const er=_n,_a=Je,za=Symbol("nomatch"),iu=7,qn=function(e){const s=Object.create(null),r=Object.create(null),a=[];let n=!0;const l="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let o={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:yd};function c(w){return o.noHighlightRe.test(w)}function x(w){let f=w.className+" ";f+=w.parentNode?w.parentNode.className:"";const j=o.languageDetectRe.exec(f);if(j){const b=z(j[1]);return b||(Pa(l.replace("{}",j[1])),Pa("Falling back to no-highlight mode for this block.",w)),b?j[1]:"no-highlight"}return f.split(/\s+/).find(b=>c(b)||z(b))}function u(w,f,j){let b="",S="";typeof f=="object"?(b=w,j=f.ignoreIllegals,S=f.language):(dt("10.7.0","highlight(lang, code, ...args) has been deprecated."),dt("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),S=w,b=f),j===void 0&&(j=!0);const L={code:b,language:S};le("before:highlight",L);const q=L.result?L.result:h(L.language,L.code,j);return q.code=L.code,le("after:highlight",q),q}function h(w,f,j,b){const S=Object.create(null);function L(P,g){return P.keywords[g]}function q(){if(!M.keywords){re.addText(U);return}let P=0;M.keywordPatternRe.lastIndex=0;let g=M.keywordPatternRe.exec(U),y="";for(;g;){y+=U.substring(P,g.index);const v=W.case_insensitive?g[0].toLowerCase():g[0],I=L(M,v);if(I){const[V,J]=I;if(re.addText(y),y="",S[v]=(S[v]||0)+1,S[v]<=iu&&(te+=J),V.startsWith("_"))y+=g[0];else{const Ee=W.classNameAliases[V]||V;ne(g[0],Ee)}}else y+=g[0];P=M.keywordPatternRe.lastIndex,g=M.keywordPatternRe.exec(U)}y+=U.substring(P),re.addText(y)}function Q(){if(U==="")return;let P=null;if(typeof M.subLanguage=="string"){if(!s[M.subLanguage]){re.addText(U);return}P=h(M.subLanguage,U,!0,F[M.subLanguage]),F[M.subLanguage]=P._top}else P=m(U,M.subLanguage.length?M.subLanguage:null);M.relevance>0&&(te+=P.relevance),re.__addSublanguage(P._emitter,P.language)}function C(){M.subLanguage!=null?Q():q(),U=""}function ne(P,g){P!==""&&(re.startScope(g),re.addText(P),re.endScope())}function oe(P,g){let y=1;const v=g.length-1;for(;y<=v;){if(!P._emit[y]){y++;continue}const I=W.classNameAliases[P[y]]||P[y],V=g[y];I?ne(V,I):(U=V,q(),U=""),y++}}function ke(P,g){return P.scope&&typeof P.scope=="string"&&re.openNode(W.classNameAliases[P.scope]||P.scope),P.beginScope&&(P.beginScope._wrap?(ne(U,W.classNameAliases[P.beginScope._wrap]||P.beginScope._wrap),U=""):P.beginScope._multi&&(oe(P.beginScope,g),U="")),M=Object.create(P,{parent:{value:M}}),M}function de(P,g,y){let v=kd(P.endRe,y);if(v){if(P["on:end"]){const I=new Ta(P);P["on:end"](g,I),I.isMatchIgnored&&(v=!1)}if(v){for(;P.endsParent&&P.parent;)P=P.parent;return P}}if(P.endsWithParent)return de(P.parent,g,y)}function ge(P){return M.matcher.regexIndex===0?(U+=P[0],1):(_e=!0,0)}function we(P){const g=P[0],y=P.rule,v=new Ta(y),I=[y.__beforeBegin,y["on:begin"]];for(const V of I)if(V&&(V(P,v),v.isMatchIgnored))return ge(g);return y.skip?U+=g:(y.excludeBegin&&(U+=g),C(),!y.returnBegin&&!y.excludeBegin&&(U=g)),ke(y,P),y.returnBegin?0:g.length}function be(P){const g=P[0],y=f.substring(P.index),v=de(M,P,y);if(!v)return za;const I=M;M.endScope&&M.endScope._wrap?(C(),ne(g,M.endScope._wrap)):M.endScope&&M.endScope._multi?(C(),oe(M.endScope,P)):I.skip?U+=g:(I.returnEnd||I.excludeEnd||(U+=g),C(),I.excludeEnd&&(U=g));do M.scope&&re.closeNode(),!M.skip&&!M.subLanguage&&(te+=M.relevance),M=M.parent;while(M!==v.parent);return v.starts&&ke(v.starts,P),I.returnEnd?0:g.length}function Oe(){const P=[];for(let g=M;g!==W;g=g.parent)g.scope&&P.unshift(g.scope);P.forEach(g=>re.openNode(g))}let Pe={};function B(P,g){const y=g&&g[0];if(U+=P,y==null)return C(),0;if(Pe.type==="begin"&&g.type==="end"&&Pe.index===g.index&&y===""){if(U+=f.slice(g.index,g.index+1),!n){const v=new Error(`0 width match regex (${w})`);throw v.languageName=w,v.badRule=Pe.rule,v}return 1}if(Pe=g,g.type==="begin")return we(g);if(g.type==="illegal"&&!j){const v=new Error('Illegal lexeme "'+y+'" for mode "'+(M.scope||"<unnamed>")+'"');throw v.mode=M,v}else if(g.type==="end"){const v=be(g);if(v!==za)return v}if(g.type==="illegal"&&y==="")return U+=`
`,1;if(Se>1e5&&Se>g.index*3)throw new Error("potential infinite loop, way more iterations than matches");return U+=y,y.length}const W=z(w);if(!W)throw nt(l.replace("{}",w)),new Error('Unknown language: "'+w+'"');const ee=su(W);let ie="",M=b||ee;const F={},re=new o.__emitter(o);Oe();let U="",te=0,Ne=0,Se=0,_e=!1;try{if(W.__emitTokens)W.__emitTokens(f,re);else{for(M.matcher.considerAll();;){Se++,_e?_e=!1:M.matcher.considerAll(),M.matcher.lastIndex=Ne;const P=M.matcher.exec(f);if(!P)break;const g=f.substring(Ne,P.index),y=B(g,P);Ne=P.index+y}B(f.substring(Ne))}return re.finalize(),ie=re.toHTML(),{language:w,value:ie,relevance:te,illegal:!1,_emitter:re,_top:M}}catch(P){if(P.message&&P.message.includes("Illegal"))return{language:w,value:er(f),illegal:!0,relevance:0,_illegalBy:{message:P.message,index:Ne,context:f.slice(Ne-100,Ne+100),mode:P.mode,resultSoFar:ie},_emitter:re};if(n)return{language:w,value:er(f),illegal:!1,relevance:0,errorRaised:P,_emitter:re,_top:M};throw P}}function p(w){const f={value:er(w),illegal:!1,relevance:0,_top:i,_emitter:new o.__emitter(o)};return f._emitter.addText(w),f}function m(w,f){f=f||o.languages||Object.keys(s);const j=p(w),b=f.filter(z).filter(X).map(C=>h(C,w,!1));b.unshift(j);const S=b.sort((C,ne)=>{if(C.relevance!==ne.relevance)return ne.relevance-C.relevance;if(C.language&&ne.language){if(z(C.language).supersetOf===ne.language)return 1;if(z(ne.language).supersetOf===C.language)return-1}return 0}),[L,q]=S,Q=L;return Q.secondBest=q,Q}function k(w,f,j){const b=f&&r[f]||j;w.classList.add("hljs"),w.classList.add(`language-${b}`)}function A(w){let f=null;const j=x(w);if(c(j))return;if(le("before:highlightElement",{el:w,language:j}),w.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",w);return}if(w.children.length>0&&(o.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(w)),o.throwUnescapedHTML))throw new nu("One of your code blocks includes unescaped HTML.",w.innerHTML);f=w;const b=f.textContent,S=j?u(b,{language:j,ignoreIllegals:!0}):m(b);w.innerHTML=S.value,w.dataset.highlighted="yes",k(w,j,S.language),w.result={language:S.language,re:S.relevance,relevance:S.relevance},S.secondBest&&(w.secondBest={language:S.secondBest.language,relevance:S.secondBest.relevance}),le("after:highlightElement",{el:w,result:S,text:b})}function N(w){o=_a(o,w)}const E=()=>{T(),dt("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function _(){T(),dt("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let K=!1;function T(){function w(){T()}if(document.readyState==="loading"){K||window.addEventListener("DOMContentLoaded",w,!1),K=!0;return}document.querySelectorAll(o.cssSelector).forEach(A)}function H(w,f){let j=null;try{j=f(e)}catch(b){if(nt("Language definition for '{}' could not be registered.".replace("{}",w)),n)nt(b);else throw b;j=i}j.name||(j.name=w),s[w]=j,j.rawDefinition=f.bind(null,e),j.aliases&&O(j.aliases,{languageName:w})}function $(w){delete s[w];for(const f of Object.keys(r))r[f]===w&&delete r[f]}function G(){return Object.keys(s)}function z(w){return w=(w||"").toLowerCase(),s[w]||s[r[w]]}function O(w,{languageName:f}){typeof w=="string"&&(w=[w]),w.forEach(j=>{r[j.toLowerCase()]=f})}function X(w){const f=z(w);return f&&!f.disableAutodetect}function Z(w){w["before:highlightBlock"]&&!w["before:highlightElement"]&&(w["before:highlightElement"]=f=>{w["before:highlightBlock"](Object.assign({block:f.el},f))}),w["after:highlightBlock"]&&!w["after:highlightElement"]&&(w["after:highlightElement"]=f=>{w["after:highlightBlock"](Object.assign({block:f.el},f))})}function Y(w){Z(w),a.push(w)}function ue(w){const f=a.indexOf(w);f!==-1&&a.splice(f,1)}function le(w,f){const j=w;a.forEach(function(b){b[j]&&b[j](f)})}function ce(w){return dt("10.7.0","highlightBlock will be removed entirely in v12.0"),dt("10.7.0","Please use highlightElement now."),A(w)}Object.assign(e,{highlight:u,highlightAuto:m,highlightAll:T,highlightElement:A,highlightBlock:ce,configure:N,initHighlighting:E,initHighlightingOnLoad:_,registerLanguage:H,unregisterLanguage:$,listLanguages:G,getLanguage:z,registerAliases:O,autoDetection:X,inherit:_a,addPlugin:Y,removePlugin:ue}),e.debugMode=function(){n=!1},e.safeMode=function(){n=!0},e.versionString=au,e.regex={concat:ot,lookahead:zn,either:$r,optional:vd,anyNumberOfTimes:bd};for(const w in Yt)typeof Yt[w]=="object"&&Pn(Yt[w]);return Object.assign(e,Yt),e},bt=qn({});bt.newInstance=()=>qn({});var lu=bt;bt.HighlightJS=bt;bt.default=bt;const ou=Xn(lu),$a={},cu="hljs-";function du(e){const s=ou.newInstance();return e&&l(e),{highlight:r,highlightAuto:a,listLanguages:n,register:l,registerAlias:i,registered:o};function r(c,x,u){const h=u||$a,p=typeof h.prefix=="string"?h.prefix:cu;if(!s.getLanguage(c))throw new Error("Unknown language: `"+c+"` is not registered");s.configure({__emitter:uu,classPrefix:p});const m=s.highlight(x,{ignoreIllegals:!0,language:c});if(m.errorRaised)throw new Error("Could not highlight with `Highlight.js`",{cause:m.errorRaised});const k=m._emitter.root,A=k.data;return A.language=m.language,A.relevance=m.relevance,k}function a(c,x){const h=(x||$a).subset||n();let p=-1,m=0,k;for(;++p<h.length;){const A=h[p];if(!s.getLanguage(A))continue;const N=r(A,c,x);N.data&&N.data.relevance!==void 0&&N.data.relevance>m&&(m=N.data.relevance,k=N)}return k||{type:"root",children:[],data:{language:void 0,relevance:m}}}function n(){return s.listLanguages()}function l(c,x){if(typeof c=="string")s.registerLanguage(c,x);else{let u;for(u in c)Object.hasOwn(c,u)&&s.registerLanguage(u,c[u])}}function i(c,x){if(typeof c=="string")s.registerAliases(typeof x=="string"?x:[...x],{languageName:c});else{let u;for(u in c)if(Object.hasOwn(c,u)){const h=c[u];s.registerAliases(typeof h=="string"?h:[...h],{languageName:u})}}}function o(c){return!!s.getLanguage(c)}}class uu{constructor(s){this.options=s,this.root={type:"root",children:[],data:{language:void 0,relevance:0}},this.stack=[this.root]}addText(s){if(s==="")return;const r=this.stack[this.stack.length-1],a=r.children[r.children.length-1];a&&a.type==="text"?a.value+=s:r.children.push({type:"text",value:s})}startScope(s){this.openNode(String(s))}endScope(){this.closeNode()}__addSublanguage(s,r){const a=this.stack[this.stack.length-1],n=s.root.children;r?a.children.push({type:"element",tagName:"span",properties:{className:[r]},children:n}):a.children.push(...n)}openNode(s){const r=this,a=s.split(".").map(function(i,o){return o?i+"_".repeat(o):r.options.classPrefix+i}),n=this.stack[this.stack.length-1],l={type:"element",tagName:"span",properties:{className:a},children:[]};n.children.push(l),this.stack.push(l)}closeNode(){this.stack.pop()}finalize(){}toHTML(){return""}}const hu=du();function me({onClick:e,isActive:s,disabled:r,loading:a,title:n,children:l,variant:i="default"}){const o="p-2 rounded-md transition-all duration-200 flex items-center justify-center",c={default:s?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400",primary:"bg-blue-600 hover:bg-blue-700 text-white",secondary:"bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300"};return t.jsx("button",{onClick:e,disabled:r||a,title:n,className:`${o} ${c[i]} ${r?"opacity-50 cursor-not-allowed":""}`,children:a?t.jsx(mt,{size:16,className:"animate-spin"}):l})}function gu({content:e,onChange:s,placeholder:r="开始写作...",coverImage:a,onCoverImageChange:n,autoSave:l=!1,onAutoSave:i}){const[o,c]=d.useState(!1),[x,u]=d.useState(!1),[h,p]=d.useState(!1),[m,k]=d.useState(!1),[A,N]=d.useState(!1),[E,_]=d.useState([]),[K,T]=d.useState(!1),[H,$]=d.useState(!1),[G,z]=d.useState(!1),[O,X]=d.useState(!1),[Z,Y]=d.useState(!1),[ue,le]=d.useState(!1),[ce,w]=d.useState(0),[f,j]=d.useState(null),[b,S]=d.useState(!1),L=Er(),q=d.useCallback(async B=>{if(!(!l||!i||b))try{S(!0),await i(B),j(new Date)}catch(W){console.error("自动保存失败:",W)}finally{S(!1)}},[l,i,b]);d.useEffect(()=>{if(!e||!l)return;const B=setTimeout(()=>{q(e)},3e3);return()=>clearTimeout(B)},[e,q,l]),d.useEffect(()=>{if(e){const B=e.replace(/<[^>]*>/g,"");w(B.length)}else w(0)},[e]);const Q=async B=>{const W=new FormData;W.append("file",B);try{const ee=await Te(fe.uploadImage,{method:"POST",body:W,headers:{}});return(await ve(ee)).url}catch(ee){throw console.error("图片上传失败:",ee),new Error("图片上传失败")}},C=di({extensions:[ui,Ko.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),xc.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-600 dark:text-blue-400 underline"}}),xd.configure({lowlight:hu,HTMLAttributes:{class:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"}})],content:e,onUpdate:({editor:B})=>{s(B.getHTML())},editorProps:{attributes:{class:`prose prose-gray dark:prose-dark max-w-none focus:outline-none transition-all duration-200 ${Z?"min-h-[calc(100vh-200px)]":"min-h-[400px]"} p-6`,"data-placeholder":r,spellcheck:"false"},handleKeyDown:(B,W)=>{if(W.ctrlKey||W.metaKey)switch(W.key){case"s":return W.preventDefault(),l&&i&&q(e),!0;case"b":return W.preventDefault(),C==null||C.chain().focus().toggleBold().run(),!0;case"i":return W.preventDefault(),C==null||C.chain().focus().toggleItalic().run(),!0;case"k":return W.preventDefault(),ge(),!0;case"Enter":if(W.shiftKey)return W.preventDefault(),C==null||C.chain().focus().setHardBreak().run(),!0;break}return!1},handlePaste:(B,W,ee)=>{var F;const M=Array.from(((F=W.clipboardData)==null?void 0:F.items)||[]).filter(re=>re.type.indexOf("image")===0);return M.length>0?(W.preventDefault(),M.forEach(async re=>{const U=re.getAsFile();if(U)try{p(!0);const te=await Q(U);C==null||C.chain().focus().setImage({src:te}).run(),L.success("图片上传成功","图片已插入到文章中")}catch(te){console.error("图片上传失败:",te),L.error("图片上传失败","请检查网络连接后重试")}finally{p(!1)}}),!0):!1},handleDrop:(B,W,ee,ie)=>{var re;const F=Array.from(((re=W.dataTransfer)==null?void 0:re.files)||[]).filter(U=>U.type.indexOf("image")===0);return F.length>0?(W.preventDefault(),F.forEach(async U=>{try{p(!0);const te=await Q(U);C==null||C.chain().focus().setImage({src:te}).run(),L.success("图片上传成功","图片已插入到文章中")}catch(te){console.error("图片上传失败:",te),L.error("图片上传失败","请检查网络连接后重试")}finally{p(!1)}}),!0):!1}}}),ne=async()=>{if(!C)return;const B=C.state.selection.empty?"现代科技风格的文章配图":C.state.doc.textBetween(C.state.selection.from,C.state.selection.to),W=window.prompt(`AI智能配图 - 请描述您想要的配图风格和内容：

提示：可以描述场景、颜色、风格等
例如：蓝色科技风格、自然风光、商务办公等`,B);if(W===null)return;const ee=W.trim()||B;c(!0);const ie=L.loading("AI图片生成中...","通常需要15-45秒，请耐心等待");try{console.log("开始生成AI配图，提示词:",ee),console.log("API端点:",fe.aiImage);const M=new AbortController,F=setTimeout(()=>M.abort(),6e4),re=await Te(fe.aiImage,{method:"POST",body:JSON.stringify({prompt:ee,style:"tech"}),signal:M.signal});clearTimeout(F),console.log("API响应状态:",re.status),console.log("API响应头:",Object.fromEntries(re.headers.entries()));const U=await ve(re);if(console.log("API响应数据:",U),U.success&&U.images&&U.images.length>0){const te=U.images[0].url;console.log("插入AI生成图片URL:",te),C.chain().focus().setImage({src:te}).run(),L.update(ie,{type:"success",title:"AI配图生成成功！",message:"图片已插入到文章中",duration:3e3}),k(!1)}else throw console.error("API响应数据格式错误:",U),new Error("AI配图生成失败：没有返回图片")}catch(M){console.error("AI配图生成错误:",M),console.error("错误详情:",{name:M==null?void 0:M.name,message:M==null?void 0:M.message,stack:M==null?void 0:M.stack}),(M==null?void 0:M.name)==="AbortError"?L.update(ie,{type:"warning",title:"AI生成超时",message:"AI图片生成通常需要15-45秒，请稍后重试",duration:5e3}):M instanceof os?L.update(ie,{type:"error",title:"AI配图生成失败",message:M.message,duration:5e3}):L.update(ie,{type:"error",title:"AI配图生成失败",message:(M==null?void 0:M.message)||"未知错误",duration:5e3})}finally{c(!1)}},oe=async()=>{if(!C)return;const B=window.confirm(`选择图片类型：

确定 = 一般图片（适合所有场景）
取消 = R18图片（仅限成人内容）

注意：所有图片均来自Pixiv艺术创作平台`);if(B===null)return;const W=B?"safe":"r18";u(!0);try{console.log("开始获取Mossia API图片，类型:",W);const ee=await Te(fe.aiImage,{method:"POST",body:JSON.stringify({type:"mossia",source:W,num:9})}),ie=await ve(ee);if(console.log("Mossia API响应数据:",ie),ie.success&&ie.images&&ie.images.length>0){const M=ie.images.filter(U=>U.url&&U.url.trim()!=="");if(M.length===0)throw new Error("没有可用的图片");_(M),N(!0),k(!1);const F=ie.cached?"（来自缓存，快速加载）":"（实时获取）",re=W==="r18"?"R18":"一般";console.log(`获取到 ${M.length} 张${re}图片${F}`)}else throw new Error("Mossia API没有返回图片")}catch(ee){console.error("Mossia图片获取错误:",ee),L.error("配图获取失败",(ee==null?void 0:ee.message)||"未知错误")}finally{u(!1)}},ke=B=>{console.log("选择图片URL:",B),O?(n==null||n(B),N(!1),_([]),T(!1),X(!1),L.success("封面图片设置成功！","封面已更新")):C&&(C.chain().focus().setImage({src:B}).run(),N(!1),L.success("配图添加成功！","图片已插入到文章中"))},de=()=>{N(!1),_([]),O&&(X(!1),T(!1))},ge=()=>{if(!C)return;const B=window.prompt("请输入链接地址:");B&&C.chain().focus().setLink({href:B}).run()},we=()=>{if(!C)return;const B=window.prompt("请输入图片地址:");B&&C.chain().focus().setImage({src:B}).run()},be=async()=>{const B=window.confirm(`选择封面图片类型：

确定 = 一般图片（适合所有场景）
取消 = R18图片（仅限成人内容）

注意：所有图片均来自Pixiv艺术创作平台`);if(B===null)return;const W=B?"safe":"r18";try{$(!0),console.log("开始获取封面图片，类型:",W);const ee=await Te(fe.aiImage,{method:"POST",body:JSON.stringify({type:"mossia",source:W,num:9})}),ie=await ve(ee);if(console.log("封面图片API响应数据:",ie),ie.success&&ie.images&&ie.images.length>0){const M=ie.images.filter(U=>U.url&&U.url.trim()!=="");if(console.log("有效图片数量:",M.length),M.length===0)throw new Error("没有可用的图片");_(M),X(!0),N(!0);const F=ie.cached?"（来自缓存）":"（实时获取）",re=W==="r18"?"R18":"一般";console.log(`获取到${M.length}张${re}封面图片${F}`)}else throw console.error("API响应格式错误:",ie),new Error("API没有返回可用的图片")}catch(ee){console.error("获取精美封面失败:",ee),alert(`获取精美封面失败: ${(ee==null?void 0:ee.message)||"未知错误"}`)}finally{$(!1)}},Oe=async()=>{const B=window.prompt(`生成文章封面图 - 请描述封面图的风格和内容：

提示：可以描述主题、颜色、风格等
例如：现代科技、蓝色调、商务风格`,"现代简约风格的文章封面");if(B!==null)try{z(!0);const W=new AbortController,ee=setTimeout(()=>W.abort(),6e4),ie=await Te(fe.aiImage,{method:"POST",body:JSON.stringify({prompt:B.trim()||"现代简约风格的文章封面",style:"tech"}),signal:W.signal});clearTimeout(ee);const M=await ve(ie);M.url&&(n==null||n(M.url),T(!1),alert("AI封面生成成功！"))}catch(W){console.error("AI生成封面失败:",W),W instanceof os?alert(`AI生成封面失败: ${W.message}`):alert("AI生成封面失败，请重试")}finally{z(!1)}},Pe=()=>{n==null||n("")};return C?t.jsxs("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[t.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:[t.jsxs("div",{className:"flex items-center justify-between mb-3",children:[t.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"文章封面图"}),a&&t.jsx("button",{onClick:Pe,className:"text-red-600 hover:text-red-700 text-sm",title:"删除封面图",children:"删除封面"})]}),a?t.jsxs("div",{className:"relative",children:[t.jsx("img",{src:a,alt:"文章封面",className:"w-full h-32 object-cover rounded-lg",onError:B=>{const W=B.target;W.style.display="none"}}),t.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center",children:t.jsx("div",{className:"opacity-0 hover:opacity-100 flex space-x-2",children:t.jsx("button",{onClick:()=>T(!K),className:"px-3 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"更换封面"})})})]}):t.jsxs("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center",children:[t.jsx(es,{size:32,className:"mx-auto text-gray-400 mb-2"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4",children:"添加文章封面图，让您的文章更吸引人"}),t.jsx("button",{onClick:()=>T(!K),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"添加封面图"})]}),K&&t.jsx("div",{className:"mt-4 p-4 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600",children:t.jsxs("div",{className:"flex flex-wrap gap-3 items-center",children:[t.jsxs("button",{onClick:be,disabled:H,className:"flex items-center space-x-2 px-3 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 disabled:opacity-50 transition-colors",children:[H?t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):t.jsx(es,{size:16}),t.jsx("span",{children:H?"获取中":"精美图片"})]}),t.jsxs("button",{onClick:Oe,disabled:G||H,className:"flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 transition-colors",children:[G?t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):t.jsx(Ct,{size:16}),t.jsx("span",{children:G?"AI生成中":"AI生成"})]}),t.jsx("button",{onClick:()=>T(!1),className:"px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors",children:"取消"})]})})]}),t.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2 p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:[t.jsxs("div",{className:"flex flex-wrap items-center gap-1",children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>C.chain().focus().toggleBold().run(),isActive:C.isActive("bold"),title:"粗体 (Ctrl+B)",children:t.jsx(Nl,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleItalic().run(),isActive:C.isActive("italic"),title:"斜体 (Ctrl+I)",children:t.jsx($l,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleStrike().run(),isActive:C.isActive("strike"),title:"删除线",children:t.jsx(no,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleCode().run(),isActive:C.isActive("code"),title:"行内代码",children:t.jsx(Cl,{size:16})})]}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>C.chain().focus().toggleHeading({level:1}).run(),isActive:C.isActive("heading",{level:1}),title:"标题1",children:t.jsx(Ol,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleHeading({level:2}).run(),isActive:C.isActive("heading",{level:2}),title:"标题2",children:t.jsx(Pl,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleHeading({level:3}).run(),isActive:C.isActive("heading",{level:3}),title:"标题3",children:t.jsx(_l,{size:16})})]}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>C.chain().focus().toggleBulletList().run(),isActive:C.isActive("bulletList"),title:"无序列表",children:t.jsx(sn,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleOrderedList().run(),isActive:C.isActive("orderedList"),title:"有序列表",children:t.jsx(Ul,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleBlockquote().run(),isActive:C.isActive("blockquote"),title:"引用",children:t.jsx(Ql,{size:16})})]}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:ge,title:"插入链接 (Ctrl+K)",children:t.jsx(Dl,{size:16})}),t.jsx(me,{onClick:we,title:"插入图片",children:t.jsx(es,{size:16})}),t.jsx(me,{onClick:()=>{const B=document.createElement("input");B.type="file",B.accept="image/*",B.onchange=async W=>{var ie;const ee=(ie=W.target.files)==null?void 0:ie[0];if(ee)try{p(!0);const M=await Q(ee);C.chain().focus().setImage({src:M}).run(),L.success("图片上传成功","图片已插入到文章中")}catch(M){console.error("图片上传失败:",M),L.error("图片上传失败","请检查网络连接后重试")}finally{p(!1)}},B.click()},loading:h,title:"上传图片",children:t.jsx(uo,{size:16})})]})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[m?t.jsxs("div",{className:"flex items-center gap-1 p-1 bg-gray-100 dark:bg-gray-700 rounded-lg",children:[t.jsx(me,{onClick:ne,disabled:o||x,loading:o,variant:"primary",title:o?"AI生成中...":"AI智能生成",children:t.jsx(Ct,{size:14})}),t.jsx(me,{onClick:oe,disabled:o||x,loading:x,variant:"secondary",title:x?"获取中...":"精美图片",children:t.jsx(Vl,{size:14})}),t.jsx(me,{onClick:()=>k(!1),title:"关闭",children:"×"})]}):t.jsx(me,{onClick:()=>k(!0),disabled:o||x,variant:"primary",title:"文章配图 - AI生成或精美图片",children:t.jsx(Ct,{size:16})}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>C.chain().focus().undo().run(),disabled:!C.can().undo(),title:"撤销 (Ctrl+Z)",children:t.jsx(co,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().redo().run(),disabled:!C.can().redo(),title:"重做 (Ctrl+Y)",children:t.jsx(Zl,{size:16})})]}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>le(!ue),isActive:ue,title:"预览模式",children:ue?t.jsx(wr,{size:16}):t.jsx(Qe,{size:16})}),t.jsx(me,{onClick:()=>Y(!Z),isActive:Z,title:"全屏模式",children:Z?t.jsx(Fl,{size:16}):t.jsx(an,{size:16})})]}),t.jsxs("div",{className:"flex items-center gap-3 text-sm text-gray-500 dark:text-gray-400 ml-2",children:[t.jsxs("span",{children:[ce," 字"]}),l&&t.jsx("div",{className:"flex items-center gap-1",children:b?t.jsxs(t.Fragment,{children:[t.jsx(mt,{size:12,className:"animate-spin"}),t.jsx("span",{children:"保存中"})]}):f?t.jsxs("span",{children:["已保存 ",f.toLocaleTimeString()]}):t.jsx("span",{children:"未保存"})})]})]})]}),t.jsx("div",{className:`relative ${Z?"min-h-[calc(100vh-200px)]":"min-h-[400px]"}`,children:ue?t.jsx("div",{className:"p-6 prose prose-gray dark:prose-dark max-w-none",children:t.jsx("div",{dangerouslySetInnerHTML:{__html:e||'<p class="text-gray-400">开始写作以查看预览...</p>'}})}):t.jsxs("div",{className:"relative",children:[t.jsx(hi,{editor:C}),h&&t.jsx("div",{className:"absolute inset-0 bg-blue-50 dark:bg-blue-900/20 border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx(mt,{size:32,className:"animate-spin text-blue-600 mx-auto mb-2"}),t.jsx("p",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"图片上传中..."})]})})]})}),A&&t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:t.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto",children:[t.jsxs("div",{className:"flex justify-between items-center mb-4",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:O?`选择封面图片 (${E.length} 张)`:`选择文章配图 (${E.length} 张)`}),t.jsx("button",{onClick:de,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",title:"关闭画廊",children:"✕"})]}),t.jsx("div",{className:"grid grid-cols-3 gap-4",children:E.map((B,W)=>t.jsxs("div",{className:"relative group cursor-pointer border-2 border-transparent hover:border-blue-500 rounded-lg overflow-hidden",onClick:()=>ke(B.url),children:[t.jsx("img",{src:B.url,alt:B.title||"配图选项",className:"w-full h-32 object-cover",loading:"lazy",onError:ee=>{console.error("图片加载失败:",B.url),ee.currentTarget.style.display="none"},onLoad:()=>{console.log("图片加载成功:",B.url)}}),t.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center",children:t.jsx("span",{className:"text-white opacity-0 group-hover:opacity-100 font-medium",children:"选择"})}),B.title&&t.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-2",children:[t.jsx("p",{className:"truncate",title:B.title,children:B.title}),B.author&&t.jsxs("p",{className:"truncate text-gray-300",title:`作者: ${B.author}`,children:["作者: ",B.author]})]})]},`${B.url}-${W}`))}),t.jsxs("div",{className:"mt-4 flex justify-between",children:[t.jsx("button",{onClick:()=>{N(!1),O?be():oe()},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",children:"换一批图片"}),t.jsx("button",{onClick:de,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors",children:"取消选择"})]})]})})]}):t.jsx("div",{children:"编辑器加载中..."})}function Da(){const e=Kt(),{slug:s}=qt(),{isAdmin:r,isLoading:a}=Gt(),n=!!s,l=Er(),[i,o]=d.useState(""),[c,x]=d.useState({title:"",excerpt:"",tags:[],category:"",featured:!1,imageUrl:""}),[u,h]=d.useState(!1),[p,m]=d.useState(!1),[k,A]=d.useState("idle"),[N,E]=d.useState(""),[_,K]=d.useState(!1),[T,H]=d.useState(!1),[$,G]=d.useState([]),[z,O]=d.useState([]),[X,Z]=d.useState(!1),[Y,ue]=d.useState(!1);d.useEffect(()=>{!a&&r&&(async()=>{try{console.log("开始加载分类和标签...");const L=(await je.getPosts(1,100)).posts||[];console.log("获取到文章数量:",L.length);const q=L.filter(oe=>oe.category).map(oe=>oe.category),Q=Array.from(new Set(q));G(Q),console.log("可用分类:",Q);const C=L.flatMap(oe=>oe.tags||[]),ne=Array.from(new Set(C));O(ne),console.log("可用标签:",ne)}catch(S){console.error("加载分类和标签失败:",S),G([]),O([])}})()},[a,r]),d.useEffect(()=>{n&&s&&(async()=>{K(!0);try{const S=await je.getPost(s);o(S.content||""),x({title:S.title,excerpt:S.excerpt,tags:S.tags||[],category:S.category||"",featured:S.featured||!1,imageUrl:S.imageUrl||""})}catch(S){console.error("加载文章失败:",S),l.error("加载文章失败","请检查文章是否存在，即将返回首页"),setTimeout(()=>e("/"),2e3)}finally{K(!1)}})()},[n,s,e]),d.useEffect(()=>{const b=S=>{const L=S.target;L.closest(".category-dropdown")||Z(!1),L.closest(".tag-dropdown")||ue(!1)};return document.addEventListener("mousedown",b),()=>document.removeEventListener("mousedown",b)},[]);const le=async(b=0)=>{if(!c.title.trim()){l.warning("请输入文章标题","标题是必填项");return}if(!i.trim()){l.warning("请输入文章内容","内容不能为空");return}m(!0),A("saving"),E(b>0?`正在重试保存... (${b+1}/3)`:"正在保存文章...");const S=l.loading(b>0?"重试保存中...":"保存文章中...",b>0?`第 ${b+1} 次尝试`:"请稍候");try{const L={...c,content:i};if(console.log("开始保存文章:",{isEditing:n,slug:s,postData:{...L,content:i.substring(0,100)+"..."}}),n)await je.updatePost(s,L),console.log("文章更新成功"),A("success"),E("文章更新成功！"),l.update(S,{type:"success",title:"文章更新成功！",message:"即将跳转到文章页面",duration:3e3}),setTimeout(()=>e(`/post/${s}`),1e3);else{const q=await je.createPost(L);console.log("文章创建成功:",q),A("success"),E("文章创建成功！"),l.update(S,{type:"success",title:"文章创建成功！",message:"即将跳转到文章页面",duration:3e3}),setTimeout(()=>e(`/post/${q.slug}`),1e3)}}catch(L){console.error("保存失败:",L);let q="保存失败，请重试",Q=!0;if(L.status===400?(q="请检查文章内容格式是否正确",Q=!1):L.status===401?(q="登录已过期，请重新登录",Q=!1):L.status===409?(q="文章标题已存在，请修改标题",Q=!1):L.status===413?(q="文章内容过大，请减少内容长度",Q=!1):L.status>=500?q="服务器错误，正在重试...":L.name==="AbortError"?q="请求超时，正在重试...":L.message&&(q=`保存失败: ${L.message}`),A("error"),E(q),l.update(S,{type:"error",title:"保存失败",message:q,duration:Q?0:5e3,action:Q?{label:"重试",onClick:()=>{l.remove(S),le(0)}}:void 0}),Q&&b<2){console.log(`第 ${b+1} 次重试保存...`),setTimeout(()=>{l.remove(S),le(b+1)},1e3*(b+1));return}Q||setTimeout(()=>{A("idle"),E("")},3e3)}finally{m(!1)}},ce=b=>{b&&!c.tags.includes(b)&&x(S=>({...S,tags:[...S.tags,b]}))},w=b=>{x(S=>({...S,tags:S.tags.filter(L=>L!==b)}))},f=b=>{x(S=>({...S,imageUrl:b}))},j=async b=>{if(!c.title.trim())return;const S=`draft_${n?s:"new"}_${Date.now()}`,L={...c,content:b,lastSaved:new Date().toISOString()};try{localStorage.setItem(S,JSON.stringify(L)),console.log("草稿已自动保存")}catch(q){console.error("草稿保存失败:",q)}};return a?t.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"检查权限中..."})]}):r?_?t.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载文章中..."})]}):t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"flex items-center justify-between mb-8",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:n?"编辑文章":"写新文章"}),t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("button",{onClick:()=>h(!u),title:u?"切换到编辑模式":"切换到预览模式","aria-label":u?"切换到编辑模式":"切换到预览模式",className:"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[u?t.jsx(wr,{size:16}):t.jsx(Qe,{size:16}),t.jsx("span",{children:u?"编辑":"预览"})]}),t.jsxs("button",{onClick:()=>le(0),disabled:p,title:p?"正在保存文章":"保存文章","aria-label":p?"正在保存文章":"保存文章",className:`flex items-center space-x-2 px-6 py-2 rounded-lg transition-all duration-300 ${k==="success"?"bg-green-600 hover:bg-green-700 text-white":k==="error"?"bg-red-600 hover:bg-red-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"}`,children:[k==="saving"&&t.jsx(mt,{size:16,className:"animate-spin"}),k==="success"&&t.jsx(or,{size:16}),k==="error"&&t.jsx(_t,{size:16}),k==="idle"&&t.jsx(to,{size:16}),t.jsx("span",{children:k==="saving"?"保存中...":k==="success"?"已保存":k==="error"?"保存失败":"保存"})]})]})]}),N&&t.jsx("div",{className:`mb-6 p-4 rounded-lg border transition-all duration-300 ${k==="success"?"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200":k==="error"?"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200":"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200"}`,children:t.jsxs("div",{className:"flex items-center space-x-2",children:[k==="saving"&&t.jsx(mt,{size:16,className:"animate-spin"}),k==="success"&&t.jsx(or,{size:16}),k==="error"&&t.jsx(_t,{size:16}),t.jsx("span",{children:N})]})}),t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[t.jsx("div",{className:"lg:col-span-2",children:t.jsx("div",{className:"card p-6",children:u?t.jsxs("div",{className:"prose prose-gray dark:prose-dark max-w-none",children:[t.jsx("h1",{children:c.title}),t.jsx("div",{dangerouslySetInnerHTML:{__html:i}})]}):t.jsx(gu,{content:i,onChange:o,placeholder:"开始写作...",coverImage:c.imageUrl,onCoverImageChange:f,autoSave:!0,onAutoSave:j})})}),t.jsx("div",{className:"lg:col-span-1",children:t.jsxs("div",{className:"card p-6",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"文章设置"}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标题"}),t.jsx("input",{type:"text",value:c.title,onChange:b=>x(S=>({...S,title:b.target.value})),className:"input w-full",placeholder:"输入文章标题"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"摘要"}),t.jsx("textarea",{value:c.excerpt,onChange:b=>x(S=>({...S,excerpt:b.target.value})),className:"input w-full h-20 resize-none",placeholder:"输入文章摘要"})]}),t.jsxs("div",{className:"relative category-dropdown",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"分类"}),t.jsxs("button",{onClick:()=>Z(!X),className:"w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[t.jsx("span",{className:c.category?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:c.category||"选择或输入分类"}),t.jsx(gt,{size:16})]}),X&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[t.jsx("div",{className:"px-3 py-2 border-b border-gray-100 dark:border-gray-700",children:t.jsx("input",{type:"text",placeholder:"输入新分类",className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onKeyPress:b=>{if(b.key==="Enter"){const S=b.currentTarget.value.trim();S&&(x(L=>({...L,category:S})),Z(!1),b.currentTarget.value="")}}})}),$.map(b=>t.jsx("button",{onClick:()=>{x(S=>({...S,category:b})),Z(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:b},b)),$.length===0&&t.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 dark:text-gray-400",children:"暂无现有分类"})]})}),c.category&&t.jsx("button",{onClick:()=>x(b=>({...b,category:""})),className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:t.jsx(We,{size:14})})]}),t.jsxs("div",{className:"relative tag-dropdown",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标签"}),t.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:c.tags.map(b=>t.jsxs("span",{className:"inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full",children:[b,t.jsx("button",{onClick:()=>w(b),title:`移除标签: ${b}`,"aria-label":`移除标签: ${b}`,className:"ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200",children:"×"})]},b))}),t.jsxs("button",{onClick:()=>ue(!Y),className:"w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[t.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"选择或输入标签"}),t.jsx(gt,{size:16})]}),Y&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[t.jsx("div",{className:"px-3 py-2 border-b border-gray-100 dark:border-gray-700",children:t.jsx("input",{type:"text",placeholder:"输入新标签后按回车",className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onKeyPress:b=>{if(b.key==="Enter"){const S=b.currentTarget.value.trim();S&&(ce(S),b.currentTarget.value="",ue(!1))}}})}),z.filter(b=>!c.tags.includes(b)).map(b=>t.jsx("button",{onClick:()=>{ce(b),ue(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:b},b)),z.filter(b=>!c.tags.includes(b)).length===0&&t.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 dark:text-gray-400",children:z.length===0?"暂无现有标签":"所有标签已选择"})]})})]}),t.jsx("div",{children:t.jsxs("label",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",checked:c.featured,onChange:b=>x(S=>({...S,featured:b.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),t.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"设为特色文章"})]})})]})]})})]})]}):t.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[t.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:t.jsx(xt,{className:"text-red-600 dark:text-red-400",size:32})}),t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"需要管理员权限"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"只有管理员才能创建和编辑文章"}),t.jsxs("button",{onClick:()=>H(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[t.jsx(xt,{size:20}),t.jsx("span",{children:"管理员登录"})]}),t.jsx(dn,{isOpen:T,onClose:()=>H(!1)})]})}function Ua(){const{category:e}=qt(),[s,r]=d.useState([]),[a,n]=d.useState([]),[l,i]=d.useState(!0),[o,c]=d.useState(null);d.useEffect(()=>{x()},[e]);const x=async()=>{i(!0),c(null);try{if(e){const u=await je.getPosts(1,20,e);r(u.posts)}else{const u=await je.getPosts(1,100);r(u.posts);const h=new Map;u.posts.forEach(m=>{m.category&&h.set(m.category,(h.get(m.category)||0)+1)}),h.set("General",u.posts.length);const p=Array.from(h.entries()).map(([m,k])=>({name:m,count:k})).sort((m,k)=>m.name==="General"?-1:k.name==="General"?1:k.count-m.count);n(p)}}catch(u){console.error("加载分类数据失败:",u),c("加载失败，请重试")}finally{i(!1)}};return l?t.jsxs("div",{className:"text-center py-12",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载中..."})]}):o?t.jsxs("div",{className:"text-center py-12",children:[t.jsx("p",{className:"text-red-600 dark:text-red-400",children:o}),t.jsx("button",{onClick:x,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]}):e?t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-6",children:t.jsxs(ae,{to:"/categories",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[t.jsx(is,{size:16}),t.jsx("span",{children:"返回所有分类"})]})}),t.jsxs("div",{className:"mb-8",children:[t.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[t.jsx(kt,{size:24,className:"text-blue-600"}),t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e})]}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共找到 ",s.length," 篇文章"]})]}),s.length>0?t.jsx("div",{className:"space-y-6",children:s.map(u=>t.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[t.jsx("h2",{className:"text-xl font-semibold mb-3",children:t.jsx(ae,{to:`/post/${u.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:u.title})}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:u.excerpt}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ze,{size:14}),t.jsx("span",{children:u.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:u.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:14}),t.jsxs("span",{children:[u.readTime," 分钟阅读"]})]}),u.category&&t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(kt,{size:14}),t.jsx("span",{className:"px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded",children:u.category})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:u.tags.map(h=>t.jsxs(ae,{to:`/tag/${h}`,className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:["#",h]},h))})]},u.id))}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx(kt,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无相关文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"该分类下还没有文章"}),t.jsx(ae,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]}):t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"所有分类"}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共 ",a.length," 个分类"]})]}),a.length>0?t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map(({name:u,count:h})=>t.jsxs(ae,{to:`/category/${u}`,className:"group p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 hover:shadow-lg transition-all",children:[t.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[t.jsx(kt,{size:20,className:"text-blue-600"}),t.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:u})]}),t.jsxs("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:[h," 篇文章"]})]},u))}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx(kt,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无分类"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"还没有任何分类"}),t.jsx(ae,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})]})}function Ha(){const{tag:e}=qt(),[s,r]=d.useState([]),[a,n]=d.useState([]),[l,i]=d.useState(!0),[o,c]=d.useState(null);d.useEffect(()=>{x()},[e]);const x=async()=>{i(!0),c(null);try{if(e){const u=await je.getPosts(1,20,void 0,e);r(u.posts)}else{const u=await je.getPosts(1,100);r(u.posts);const h=new Map;u.posts.forEach(m=>{m.tags.forEach(k=>{h.set(k,(h.get(k)||0)+1)})});const p=Array.from(h.entries()).map(([m,k])=>({name:m,count:k})).sort((m,k)=>k.count-m.count);n(p)}}catch(u){console.error("加载标签数据失败:",u),c("加载失败，请重试")}finally{i(!1)}};return l?t.jsxs("div",{className:"text-center py-12",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载中..."})]}):o?t.jsxs("div",{className:"text-center py-12",children:[t.jsx("p",{className:"text-red-600 dark:text-red-400",children:o}),t.jsx("button",{onClick:x,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]}):e?t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-6",children:t.jsxs(ae,{to:"/tags",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[t.jsx(is,{size:16}),t.jsx("span",{children:"返回所有标签"})]})}),t.jsxs("div",{className:"mb-8",children:[t.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[t.jsx(ft,{size:24,className:"text-blue-600"}),t.jsxs("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["#",e]})]}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共找到 ",s.length," 篇文章"]})]}),s.length>0?t.jsx("div",{className:"space-y-6",children:s.map(u=>t.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[t.jsx("h2",{className:"text-xl font-semibold mb-3",children:t.jsx(ae,{to:`/post/${u.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:u.title})}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:u.excerpt}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ze,{size:14}),t.jsx("span",{children:u.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:u.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:14}),t.jsxs("span",{children:[u.readTime," 分钟阅读"]})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:u.tags.map(h=>t.jsxs(ae,{to:`/tag/${h}`,className:`px-2 py-1 text-xs rounded transition-colors ${h===e?"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:["#",h]},h))})]},u.id))}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx(ft,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无相关文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"该标签下还没有文章"}),t.jsx(ae,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]}):t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"所有标签"}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共 ",a.length," 个标签"]})]}),a.length>0?t.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:a.map(({name:u,count:h})=>t.jsx(ae,{to:`/tag/${u}`,className:"group p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 hover:shadow-lg transition-all",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("span",{className:"font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:["#",u]}),t.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:h})]})},u))}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx(ft,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无标签"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"还没有任何标签"}),t.jsx(ae,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})]})}function xu(){const[e,s]=vl(),[r,a]=d.useState(e.get("q")||""),[n,l]=d.useState([]),[i,o]=d.useState(!1),[c,x]=d.useState(!1);d.useEffect(()=>{const m=e.get("q");m&&(a(m),u(m))},[e]);const u=async m=>{if(m.trim()){o(!0),x(!0);try{const k=await je.searchPosts(m);l(k)}catch(k){console.error("搜索失败:",k),l([])}finally{o(!1)}}},h=m=>{m.preventDefault(),r.trim()&&s({q:r.trim()})},p=m=>{a(m.target.value)};return t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-8",children:t.jsxs("form",{onSubmit:h,className:"relative",children:[t.jsxs("div",{className:"relative",children:[t.jsx(De,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),t.jsx("input",{type:"text",value:r,onChange:p,placeholder:"搜索文章...",className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),t.jsx("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-4 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"搜索"})]})}),t.jsxs("div",{children:[i&&t.jsxs("div",{className:"text-center py-8",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"搜索中..."})]}),!i&&c&&t.jsx("div",{className:"mb-6",children:t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:[n.length>0?`找到 ${n.length} 篇文章`:"没有找到相关文章",r&&` 关于 "${r}"`]})}),!i&&n.length>0&&t.jsx("div",{className:"space-y-6",children:n.map(m=>t.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[t.jsx("h2",{className:"text-xl font-semibold mb-3",children:t.jsx(ae,{to:`/post/${m.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:m.title})}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:m.excerpt}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ze,{size:14}),t.jsx("span",{children:m.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:m.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:14}),t.jsxs("span",{children:[m.readTime," 分钟阅读"]})]}),m.category&&t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(ft,{size:14}),t.jsx("span",{children:m.category})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:m.tags.map(k=>t.jsxs(ae,{to:`/tag/${k}`,className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:["#",k]},k))})]},m.id))}),!i&&c&&n.length===0&&t.jsxs("div",{className:"text-center py-12",children:[t.jsx(De,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"没有找到相关文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"尝试使用不同的关键词或查看所有文章"}),t.jsx(ae,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]})]})}function fu(){return t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsxs("div",{className:"card p-8",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-6",children:"关于我"}),t.jsxs("div",{className:"prose prose-gray dark:prose-dark max-w-none",children:[t.jsx("p",{children:"欢迎来到我的个人博客！这里是我分享技术见解、开发经验和生活感悟的地方。"}),t.jsx("h2",{children:"技术栈"}),t.jsxs("ul",{children:[t.jsx("li",{children:"前端：React, TypeScript, Tailwind CSS"}),t.jsx("li",{children:"后端：Cloudflare Workers, Node.js"}),t.jsx("li",{children:"数据库：Cloudflare KV, MongoDB"}),t.jsx("li",{children:"部署：Cloudflare Pages, Vercel"})]}),t.jsx("h2",{children:"联系方式"}),t.jsx("p",{children:"如果您想与我交流或有任何问题，请通过以下方式联系我："}),t.jsxs("ul",{children:[t.jsx("li",{children:"<EMAIL>"}),t.jsx("li",{children:"https://github.com/ajkdfe2e2e"}),t.jsx("li",{children:"https://x.com/x2a1HRjxs552213"})]})]})]})})}function mu(){const e=d.useRef(null),s=()=>{var r;(r=e.current)==null||r.toggleVisibility()};return t.jsxs(bo,{children:[t.jsx(Eo,{onMusicToggle:s,children:t.jsxs(ol,{children:[t.jsx(Le,{path:"/",element:t.jsx(Ho,{})}),t.jsx(Le,{path:"/post/:slug",element:t.jsx(Wo,{})}),t.jsx(Le,{path:"/write",element:t.jsx(Da,{})}),t.jsx(Le,{path:"/write/:slug",element:t.jsx(Da,{})}),t.jsx(Le,{path:"/categories",element:t.jsx(Ua,{})}),t.jsx(Le,{path:"/category/:category",element:t.jsx(Ua,{})}),t.jsx(Le,{path:"/tags",element:t.jsx(Ha,{})}),t.jsx(Le,{path:"/tag/:tag",element:t.jsx(Ha,{})}),t.jsx(Le,{path:"/search",element:t.jsx(xu,{})}),t.jsx(Le,{path:"/about",element:t.jsx(fu,{})})]})}),t.jsx(So,{ref:e})]})}ur.setupNetworkListener();rr.createRoot(document.getElementById("root")).render(t.jsx(Qn.StrictMode,{children:t.jsx(ml,{children:t.jsx(Uo,{children:t.jsx(mu,{})})})}));
//# sourceMappingURL=index-64e4ab56.js.map
