/*!
  Theme: PaperColor Dark
  Author: <PERSON> (http://github.com/j<PERSON><PERSON>) based on PaperColor Theme (https://github.com/NLKNguyen/papercolor-theme)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:grey;background:#1c1c1c}.hljs ::selection,.hljs::selection{background-color:#5faf00;color:grey}.hljs-comment{color:#d7af5f}.hljs-tag{color:#5fafd7}.hljs-operator,.hljs-punctuation,.hljs-subst{color:grey}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#585858}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#5faf5f}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#afd700}.hljs-strong{font-weight:700;color:#afd700}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#af87d7}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#ffaf00}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#ff5faf}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#00afaf}.hljs-emphasis{color:#00afaf;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#5f8787}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}