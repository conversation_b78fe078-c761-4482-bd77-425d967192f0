@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200 dark:border-gray-700;
  }
  
  body {
    @apply bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100;
    font-feature-settings: "rlig" 1, "calt" 1;
    /* 兼容性修复 - 正确的属性顺序 */
    -webkit-user-select: auto;
    -moz-user-select: auto;
    -ms-user-select: auto;
    user-select: auto;
    
    /* text-size-adjust 添加所有前缀以支持所有浏览器 */
    -webkit-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }
}

@layer components {
  .prose-custom {
    @apply prose prose-gray dark:prose-dark max-w-none;
  }
  
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
    /* 兼容性修复 - touch-action 支持 */
    -webkit-touch-action: manipulation;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    
    /* user-select 添加 webkit 前缀支持 Safari */
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  .btn-primary {
    @apply btn bg-blue-600 text-white hover:bg-blue-700;
  }
  
  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-100 dark:hover:bg-gray-600;
  }
  
  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100;
  }
  
  .input {
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-100 dark:placeholder:text-gray-500;
    /* 兼容性修复 - 添加 webkit 前缀 */
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
  
  .card {
    @apply rounded-lg border border-gray-200 bg-white text-gray-900 shadow-sm dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100;
  }
  
  /* 拖拽相关样式 - 兼容性修复 */
  .draggable {
    /* 移除 -webkit-user-drag，因为 Firefox 不支持 */
    -moz-user-drag: element;
    user-drag: element;
    /* 添加标准的拖拽属性 */
    draggable: true;
  }
  
  .no-drag {
    /* 移除 -webkit-user-drag，因为 Firefox 不支持 */
    -moz-user-drag: none;
    user-drag: none;
    draggable: false;
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  /* 兼容性修复 - background-clip 正确顺序：webkit前缀在前 */
  -webkit-background-clip: padding-box;
  background-clip: padding-box;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* TipTap 编辑器样式 */
.ProseMirror {
  outline: none;
  padding: 1rem;
  min-height: 200px;
  /* 兼容性修复 - 添加 webkit 前缀 */
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* 性能优化的动画 - 避免使用height/width，使用transform和opacity */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 性能优化 - 使用 transform 而不是修改 width/height */
@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes expandScale {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 应用动画的类 */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.3s ease-out;
}

.animate-expand {
  animation: expandScale 0.2s ease-out;
}

.animate-pulse-custom {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-spin-custom {
  animation: spin 1s linear infinite;
}

/* 主题切换动画优化 */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .btn {
    border: 2px solid currentColor;
  }
  
  .card {
    border-width: 2px;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .btn, button {
    -webkit-appearance: none;
    appearance: none;
    background: white !important;
    color: black !important;
    border: 1px solid black;
  }
}

/* 代码高亮主题 */
.hljs {
  @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
}

/* 暗色模式样式已通过 Tailwind CSS 类实现 */

/* 行截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 音乐播放器移动端样式 */
@layer components {
  /* 触摸友好的按钮大小 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    /* 添加触摸反馈 */
    -webkit-tap-highlight-color: rgba(168, 85, 247, 0.2);
    tap-highlight-color: rgba(168, 85, 247, 0.2);
  }
  
  /* 自定义滑块样式 */
  .slider {
    background: transparent;
    outline: none;
    /* 移除默认外观 */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
  }
  
  /* WebKit 滑块轨道 */
  .slider::-webkit-slider-track {
    width: 100%;
    height: 6px;
    background: rgb(75, 85, 99); /* gray-600 */
    border-radius: 3px;
  }
  
  /* WebKit 滑块滑块 */
  .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: rgb(168, 85, 247); /* purple-600 */
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }
  
  .slider::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  /* Firefox 滑块样式 */
  .slider::-moz-range-track {
    width: 100%;
    height: 6px;
    background: rgb(75, 85, 99);
    border-radius: 3px;
    border: none;
  }
  
  .slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: rgb(168, 85, 247);
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
  }
  
  .slider::-moz-range-thumb:hover {
    transform: scale(1.2);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
  
  /* 移动端特定的音乐播放器样式 */
  @media (max-width: 640px) {
    /* 确保播放器在移动端全屏显示 */
    .music-player-mobile {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      width: 100vw !important;
      height: 100vh !important;
      height: 100dvh !important; /* 动态视口高度 */
      border-radius: 0 !important;
      z-index: 9998 !important;
    }
    
    /* 移动端触摸优化 */
    .touch-target {
      min-height: 48px;
      min-width: 48px;
    }
    
    /* 移动端滑块增大触摸区域 */
    .slider::-webkit-slider-thumb {
      width: 20px;
      height: 20px;
    }
    
    .slider::-moz-range-thumb {
      width: 20px;
      height: 20px;
    }
    
    /* 优化移动端文字大小 */
    .mobile-text-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
    
    .mobile-text-xs {
      font-size: 0.75rem;
      line-height: 1rem;
    }
  }
  
  /* 防止在小屏幕上出现横向滚动 */
  @media (max-width: 480px) {
    .music-player-container {
      overflow-x: hidden;
    }
    
    /* 确保歌曲列表项在小屏幕上正确显示 */
    .song-item {
      padding: 12px 8px;
    }
    
    .song-item img {
      width: 48px;
      height: 48px;
      flex-shrink: 0;
    }
  }
}

/* 确保删除确认弹窗始终在最顶层 */
.delete-confirm-modal {
  z-index: 10000 !important;
  position: fixed !important;
  inset: 0 !important;
}

.delete-confirm-backdrop {
  position: fixed !important;
  inset: 0 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 10000 !important;
}

.delete-confirm-dialog {
  position: relative !important;
  z-index: 10001 !important;
  background-color: white !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
  max-width: 32rem !important;
  width: 100% !important;
  margin: 0 auto !important;
}

.dark .delete-confirm-dialog {
  background-color: rgb(31, 41, 55) !important;
}