var qn=Object.defineProperty;var Gn=(e,s,r)=>s in e?qn(e,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[s]=r;var Fs=(e,s,r)=>(Gn(e,typeof s!="symbol"?s+"":s,r),r);import{r as d,b as Vn,c as Jn,g as Xn,R as Qn}from"./vendor-5f6cd04d.js";import{N as Zn,m as er,n as Yn,M as ei,a as ti,P as zs,b as $s,c as si,g as ri,f as ai,d as ni,e as ii,C as li,h as tr,D as oi,i as ci,u as di,S as ui,E as hi}from"./editor-cda9e262.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))a(n);new MutationObserver(n=>{for(const o of n)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&a(i)}).observe(document,{childList:!0,subtree:!0});function r(n){const o={};return n.integrity&&(o.integrity=n.integrity),n.referrerPolicy&&(o.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?o.credentials="include":n.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function a(n){if(n.ep)return;n.ep=!0;const o=r(n);fetch(n.href,o)}})();var Ha={exports:{}},Ds={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gi=d,xi=Symbol.for("react.element"),fi=Symbol.for("react.fragment"),mi=Object.prototype.hasOwnProperty,pi=gi.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,yi={key:!0,ref:!0,__self:!0,__source:!0};function Ba(e,s,r){var a,n={},o=null,i=null;r!==void 0&&(o=""+r),s.key!==void 0&&(o=""+s.key),s.ref!==void 0&&(i=s.ref);for(a in s)mi.call(s,a)&&!yi.hasOwnProperty(a)&&(n[a]=s[a]);if(e&&e.defaultProps)for(a in s=e.defaultProps,s)n[a]===void 0&&(n[a]=s[a]);return{$$typeof:xi,type:e,key:o,ref:i,props:n,_owner:pi.current}}Ds.Fragment=fi;Ds.jsx=Ba;Ds.jsxs=Ba;Ha.exports=Ds;var t=Ha.exports,sr={},Qr=Vn;sr.createRoot=Qr.createRoot,sr.hydrateRoot=Qr.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ot(){return Ot=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},Ot.apply(this,arguments)}var qe;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(qe||(qe={}));const Zr="popstate";function bi(e){e===void 0&&(e={});function s(a,n){let{pathname:o,search:i,hash:l}=a.location;return rr("",{pathname:o,search:i,hash:l},n.state&&n.state.usr||null,n.state&&n.state.key||"default")}function r(a,n){return typeof n=="string"?n:rs(n)}return ji(s,r,null,e)}function pe(e,s){if(e===!1||e===null||typeof e>"u")throw new Error(s)}function Wa(e,s){if(!e){typeof console<"u"&&console.warn(s);try{throw new Error(s)}catch{}}}function vi(){return Math.random().toString(36).substr(2,8)}function Yr(e,s){return{usr:e.state,key:e.key,idx:s}}function rr(e,s,r,a){return r===void 0&&(r=null),Ot({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof s=="string"?vt(s):s,{state:r,key:s&&s.key||a||vi()})}function rs(e){let{pathname:s="/",search:r="",hash:a=""}=e;return r&&r!=="?"&&(s+=r.charAt(0)==="?"?r:"?"+r),a&&a!=="#"&&(s+=a.charAt(0)==="#"?a:"#"+a),s}function vt(e){let s={};if(e){let r=e.indexOf("#");r>=0&&(s.hash=e.substr(r),e=e.substr(0,r));let a=e.indexOf("?");a>=0&&(s.search=e.substr(a),e=e.substr(0,a)),e&&(s.pathname=e)}return s}function ji(e,s,r,a){a===void 0&&(a={});let{window:n=document.defaultView,v5Compat:o=!1}=a,i=n.history,l=qe.Pop,c=null,g=u();g==null&&(g=0,i.replaceState(Ot({},i.state,{idx:g}),""));function u(){return(i.state||{idx:null}).idx}function x(){l=qe.Pop;let N=u(),A=N==null?null:N-g;g=N,c&&c({action:l,location:T.location,delta:A})}function p(N,A){l=qe.Push;let E=rr(T.location,N,A);r&&r(E,N),g=u()+1;let z=Yr(E,g),R=T.createHref(E);try{i.pushState(z,"",R)}catch(D){if(D instanceof DOMException&&D.name==="DataCloneError")throw D;n.location.assign(R)}o&&c&&c({action:l,location:T.location,delta:1})}function m(N,A){l=qe.Replace;let E=rr(T.location,N,A);r&&r(E,N),g=u();let z=Yr(E,g),R=T.createHref(E);i.replaceState(z,"",R),o&&c&&c({action:l,location:T.location,delta:0})}function w(N){let A=n.location.origin!=="null"?n.location.origin:n.location.href,E=typeof N=="string"?N:rs(N);return E=E.replace(/ $/,"%20"),pe(A,"No window.location.(origin|href) available to create URL for href: "+E),new URL(E,A)}let T={get action(){return l},get location(){return e(n,i)},listen(N){if(c)throw new Error("A history only accepts one active listener");return n.addEventListener(Zr,x),c=N,()=>{n.removeEventListener(Zr,x),c=null}},createHref(N){return s(n,N)},createURL:w,encodeLocation(N){let A=w(N);return{pathname:A.pathname,search:A.search,hash:A.hash}},push:p,replace:m,go(N){return i.go(N)}};return T}var ea;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(ea||(ea={}));function ki(e,s,r){return r===void 0&&(r="/"),wi(e,s,r,!1)}function wi(e,s,r,a){let n=typeof s=="string"?vt(s):s,o=vr(n.pathname||"/",r);if(o==null)return null;let i=Fa(e);Ni(i);let l=null;for(let c=0;l==null&&c<i.length;++c){let g=Pi(o);l=Ii(i[c],g,a)}return l}function Fa(e,s,r,a){s===void 0&&(s=[]),r===void 0&&(r=[]),a===void 0&&(a="");let n=(o,i,l)=>{let c={relativePath:l===void 0?o.path||"":l,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};c.relativePath.startsWith("/")&&(pe(c.relativePath.startsWith(a),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+a+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(a.length));let g=Je([a,c.relativePath]),u=r.concat(c);o.children&&o.children.length>0&&(pe(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+g+'".')),Fa(o.children,s,u,g)),!(o.path==null&&!o.index)&&s.push({path:g,score:Ti(g,o.index),routesMeta:u})};return e.forEach((o,i)=>{var l;if(o.path===""||!((l=o.path)!=null&&l.includes("?")))n(o,i);else for(let c of Ka(o.path))n(o,i,c)}),s}function Ka(e){let s=e.split("/");if(s.length===0)return[];let[r,...a]=s,n=r.endsWith("?"),o=r.replace(/\?$/,"");if(a.length===0)return n?[o,""]:[o];let i=Ka(a.join("/")),l=[];return l.push(...i.map(c=>c===""?o:[o,c].join("/"))),n&&l.push(...i),l.map(c=>e.startsWith("/")&&c===""?"/":c)}function Ni(e){e.sort((s,r)=>s.score!==r.score?r.score-s.score:Li(s.routesMeta.map(a=>a.childrenIndex),r.routesMeta.map(a=>a.childrenIndex)))}const Ei=/^:[\w-]+$/,Si=3,Ci=2,Ai=1,Ri=10,Mi=-2,ta=e=>e==="*";function Ti(e,s){let r=e.split("/"),a=r.length;return r.some(ta)&&(a+=Mi),s&&(a+=Ci),r.filter(n=>!ta(n)).reduce((n,o)=>n+(Ei.test(o)?Si:o===""?Ai:Ri),a)}function Li(e,s){return e.length===s.length&&e.slice(0,-1).every((a,n)=>a===s[n])?e[e.length-1]-s[s.length-1]:0}function Ii(e,s,r){r===void 0&&(r=!1);let{routesMeta:a}=e,n={},o="/",i=[];for(let l=0;l<a.length;++l){let c=a[l],g=l===a.length-1,u=o==="/"?s:s.slice(o.length)||"/",x=sa({path:c.relativePath,caseSensitive:c.caseSensitive,end:g},u),p=c.route;if(!x&&g&&r&&!a[a.length-1].route.index&&(x=sa({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},u)),!x)return null;Object.assign(n,x.params),i.push({params:n,pathname:Je([o,x.pathname]),pathnameBase:Di(Je([o,x.pathnameBase])),route:p}),x.pathnameBase!=="/"&&(o=Je([o,x.pathnameBase]))}return i}function sa(e,s){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[r,a]=Oi(e.path,e.caseSensitive,e.end),n=s.match(r);if(!n)return null;let o=n[0],i=o.replace(/(.)\/+$/,"$1"),l=n.slice(1);return{params:a.reduce((g,u,x)=>{let{paramName:p,isOptional:m}=u;if(p==="*"){let T=l[x]||"";i=o.slice(0,o.length-T.length).replace(/(.)\/+$/,"$1")}const w=l[x];return m&&!w?g[p]=void 0:g[p]=(w||"").replace(/%2F/g,"/"),g},{}),pathname:o,pathnameBase:i,pattern:e}}function Oi(e,s,r){s===void 0&&(s=!1),r===void 0&&(r=!0),Wa(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let a=[],n="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,l,c)=>(a.push({paramName:l,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(a.push({paramName:"*"}),n+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?n+="\\/*$":e!==""&&e!=="/"&&(n+="(?:(?=\\/|$))"),[new RegExp(n,s?void 0:"i"),a]}function Pi(e){try{return e.split("/").map(s=>decodeURIComponent(s).replace(/\//g,"%2F")).join("/")}catch(s){return Wa(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+s+").")),e}}function vr(e,s){if(s==="/")return e;if(!e.toLowerCase().startsWith(s.toLowerCase()))return null;let r=s.endsWith("/")?s.length-1:s.length,a=e.charAt(r);return a&&a!=="/"?null:e.slice(r)||"/"}function _i(e,s){s===void 0&&(s="/");let{pathname:r,search:a="",hash:n=""}=typeof e=="string"?vt(e):e;return{pathname:r?r.startsWith("/")?r:zi(r,s):s,search:Ui(a),hash:Hi(n)}}function zi(e,s){let r=s.replace(/\/+$/,"").split("/");return e.split("/").forEach(n=>{n===".."?r.length>1&&r.pop():n!=="."&&r.push(n)}),r.length>1?r.join("/"):"/"}function Ks(e,s,r,a){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+s+"` field ["+JSON.stringify(a)+"].  Please separate it out to the ")+("`to."+r+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function $i(e){return e.filter((s,r)=>r===0||s.route.path&&s.route.path.length>0)}function qa(e,s){let r=$i(e);return s?r.map((a,n)=>n===r.length-1?a.pathname:a.pathnameBase):r.map(a=>a.pathnameBase)}function Ga(e,s,r,a){a===void 0&&(a=!1);let n;typeof e=="string"?n=vt(e):(n=Ot({},e),pe(!n.pathname||!n.pathname.includes("?"),Ks("?","pathname","search",n)),pe(!n.pathname||!n.pathname.includes("#"),Ks("#","pathname","hash",n)),pe(!n.search||!n.search.includes("#"),Ks("#","search","hash",n)));let o=e===""||n.pathname==="",i=o?"/":n.pathname,l;if(i==null)l=r;else{let x=s.length-1;if(!a&&i.startsWith("..")){let p=i.split("/");for(;p[0]==="..";)p.shift(),x-=1;n.pathname=p.join("/")}l=x>=0?s[x]:"/"}let c=_i(n,l),g=i&&i!=="/"&&i.endsWith("/"),u=(o||i===".")&&r.endsWith("/");return!c.pathname.endsWith("/")&&(g||u)&&(c.pathname+="/"),c}const Je=e=>e.join("/").replace(/\/\/+/g,"/"),Di=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Ui=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Hi=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Bi(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const Va=["post","put","patch","delete"];new Set(Va);const Wi=["get",...Va];new Set(Wi);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Pt(){return Pt=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},Pt.apply(this,arguments)}const jr=d.createContext(null),Fi=d.createContext(null),it=d.createContext(null),Us=d.createContext(null),Ye=d.createContext({outlet:null,matches:[],isDataRoute:!1}),Ja=d.createContext(null);function Ki(e,s){let{relative:r}=s===void 0?{}:s;Wt()||pe(!1);let{basename:a,navigator:n}=d.useContext(it),{hash:o,pathname:i,search:l}=Qa(e,{relative:r}),c=i;return a!=="/"&&(c=i==="/"?a:Je([a,i])),n.createHref({pathname:c,search:l,hash:o})}function Wt(){return d.useContext(Us)!=null}function jt(){return Wt()||pe(!1),d.useContext(Us).location}function Xa(e){d.useContext(it).static||d.useLayoutEffect(e)}function Ft(){let{isDataRoute:e}=d.useContext(Ye);return e?al():qi()}function qi(){Wt()||pe(!1);let e=d.useContext(jr),{basename:s,future:r,navigator:a}=d.useContext(it),{matches:n}=d.useContext(Ye),{pathname:o}=jt(),i=JSON.stringify(qa(n,r.v7_relativeSplatPath)),l=d.useRef(!1);return Xa(()=>{l.current=!0}),d.useCallback(function(g,u){if(u===void 0&&(u={}),!l.current)return;if(typeof g=="number"){a.go(g);return}let x=Ga(g,JSON.parse(i),o,u.relative==="path");e==null&&s!=="/"&&(x.pathname=x.pathname==="/"?s:Je([s,x.pathname])),(u.replace?a.replace:a.push)(x,u.state,u)},[s,a,i,o,e])}function Kt(){let{matches:e}=d.useContext(Ye),s=e[e.length-1];return s?s.params:{}}function Qa(e,s){let{relative:r}=s===void 0?{}:s,{future:a}=d.useContext(it),{matches:n}=d.useContext(Ye),{pathname:o}=jt(),i=JSON.stringify(qa(n,a.v7_relativeSplatPath));return d.useMemo(()=>Ga(e,JSON.parse(i),o,r==="path"),[e,i,o,r])}function Gi(e,s){return Vi(e,s)}function Vi(e,s,r,a){Wt()||pe(!1);let{navigator:n}=d.useContext(it),{matches:o}=d.useContext(Ye),i=o[o.length-1],l=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let g=jt(),u;if(s){var x;let N=typeof s=="string"?vt(s):s;c==="/"||(x=N.pathname)!=null&&x.startsWith(c)||pe(!1),u=N}else u=g;let p=u.pathname||"/",m=p;if(c!=="/"){let N=c.replace(/^\//,"").split("/");m="/"+p.replace(/^\//,"").split("/").slice(N.length).join("/")}let w=ki(e,{pathname:m}),T=Yi(w&&w.map(N=>Object.assign({},N,{params:Object.assign({},l,N.params),pathname:Je([c,n.encodeLocation?n.encodeLocation(N.pathname).pathname:N.pathname]),pathnameBase:N.pathnameBase==="/"?c:Je([c,n.encodeLocation?n.encodeLocation(N.pathnameBase).pathname:N.pathnameBase])})),o,r,a);return s&&T?d.createElement(Us.Provider,{value:{location:Pt({pathname:"/",search:"",hash:"",state:null,key:"default"},u),navigationType:qe.Pop}},T):T}function Ji(){let e=rl(),s=Bi(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,n={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},o=null;return d.createElement(d.Fragment,null,d.createElement("h2",null,"Unexpected Application Error!"),d.createElement("h3",{style:{fontStyle:"italic"}},s),r?d.createElement("pre",{style:n},r):null,o)}const Xi=d.createElement(Ji,null);class Qi extends d.Component{constructor(s){super(s),this.state={location:s.location,revalidation:s.revalidation,error:s.error}}static getDerivedStateFromError(s){return{error:s}}static getDerivedStateFromProps(s,r){return r.location!==s.location||r.revalidation!=="idle"&&s.revalidation==="idle"?{error:s.error,location:s.location,revalidation:s.revalidation}:{error:s.error!==void 0?s.error:r.error,location:r.location,revalidation:s.revalidation||r.revalidation}}componentDidCatch(s,r){console.error("React Router caught the following error during render",s,r)}render(){return this.state.error!==void 0?d.createElement(Ye.Provider,{value:this.props.routeContext},d.createElement(Ja.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Zi(e){let{routeContext:s,match:r,children:a}=e,n=d.useContext(jr);return n&&n.static&&n.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(n.staticContext._deepestRenderedBoundaryId=r.route.id),d.createElement(Ye.Provider,{value:s},a)}function Yi(e,s,r,a){var n;if(s===void 0&&(s=[]),r===void 0&&(r=null),a===void 0&&(a=null),e==null){var o;if(!r)return null;if(r.errors)e=r.matches;else if((o=a)!=null&&o.v7_partialHydration&&s.length===0&&!r.initialized&&r.matches.length>0)e=r.matches;else return null}let i=e,l=(n=r)==null?void 0:n.errors;if(l!=null){let u=i.findIndex(x=>x.route.id&&(l==null?void 0:l[x.route.id])!==void 0);u>=0||pe(!1),i=i.slice(0,Math.min(i.length,u+1))}let c=!1,g=-1;if(r&&a&&a.v7_partialHydration)for(let u=0;u<i.length;u++){let x=i[u];if((x.route.HydrateFallback||x.route.hydrateFallbackElement)&&(g=u),x.route.id){let{loaderData:p,errors:m}=r,w=x.route.loader&&p[x.route.id]===void 0&&(!m||m[x.route.id]===void 0);if(x.route.lazy||w){c=!0,g>=0?i=i.slice(0,g+1):i=[i[0]];break}}}return i.reduceRight((u,x,p)=>{let m,w=!1,T=null,N=null;r&&(m=l&&x.route.id?l[x.route.id]:void 0,T=x.route.errorElement||Xi,c&&(g<0&&p===0?(nl("route-fallback",!1),w=!0,N=null):g===p&&(w=!0,N=x.route.hydrateFallbackElement||null)));let A=s.concat(i.slice(0,p+1)),E=()=>{let z;return m?z=T:w?z=N:x.route.Component?z=d.createElement(x.route.Component,null):x.route.element?z=x.route.element:z=u,d.createElement(Zi,{match:x,routeContext:{outlet:u,matches:A,isDataRoute:r!=null},children:z})};return r&&(x.route.ErrorBoundary||x.route.errorElement||p===0)?d.createElement(Qi,{location:r.location,revalidation:r.revalidation,component:T,error:m,children:E(),routeContext:{outlet:null,matches:A,isDataRoute:!0}}):E()},null)}var Za=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Za||{}),as=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(as||{});function el(e){let s=d.useContext(jr);return s||pe(!1),s}function tl(e){let s=d.useContext(Fi);return s||pe(!1),s}function sl(e){let s=d.useContext(Ye);return s||pe(!1),s}function Ya(e){let s=sl(),r=s.matches[s.matches.length-1];return r.route.id||pe(!1),r.route.id}function rl(){var e;let s=d.useContext(Ja),r=tl(as.UseRouteError),a=Ya(as.UseRouteError);return s!==void 0?s:(e=r.errors)==null?void 0:e[a]}function al(){let{router:e}=el(Za.UseNavigateStable),s=Ya(as.UseNavigateStable),r=d.useRef(!1);return Xa(()=>{r.current=!0}),d.useCallback(function(n,o){o===void 0&&(o={}),r.current&&(typeof n=="number"?e.navigate(n):e.navigate(n,Pt({fromRouteId:s},o)))},[e,s])}const ra={};function nl(e,s,r){!s&&!ra[e]&&(ra[e]=!0)}function il(e,s){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!s||s.v7_relativeSplatPath),s&&(s.v7_fetcherPersist,s.v7_normalizeFormMethod,s.v7_partialHydration,s.v7_skipActionErrorRevalidation)}function Le(e){pe(!1)}function ll(e){let{basename:s="/",children:r=null,location:a,navigationType:n=qe.Pop,navigator:o,static:i=!1,future:l}=e;Wt()&&pe(!1);let c=s.replace(/^\/*/,"/"),g=d.useMemo(()=>({basename:c,navigator:o,static:i,future:Pt({v7_relativeSplatPath:!1},l)}),[c,l,o,i]);typeof a=="string"&&(a=vt(a));let{pathname:u="/",search:x="",hash:p="",state:m=null,key:w="default"}=a,T=d.useMemo(()=>{let N=vr(u,c);return N==null?null:{location:{pathname:N,search:x,hash:p,state:m,key:w},navigationType:n}},[c,u,x,p,m,w,n]);return T==null?null:d.createElement(it.Provider,{value:g},d.createElement(Us.Provider,{children:r,value:T}))}function ol(e){let{children:s,location:r}=e;return Gi(ar(s),r)}new Promise(()=>{});function ar(e,s){s===void 0&&(s=[]);let r=[];return d.Children.forEach(e,(a,n)=>{if(!d.isValidElement(a))return;let o=[...s,n];if(a.type===d.Fragment){r.push.apply(r,ar(a.props.children,o));return}a.type!==Le&&pe(!1),!a.props.index||!a.props.children||pe(!1);let i={id:a.props.id||o.join("-"),caseSensitive:a.props.caseSensitive,element:a.props.element,Component:a.props.Component,index:a.props.index,path:a.props.path,loader:a.props.loader,action:a.props.action,errorElement:a.props.errorElement,ErrorBoundary:a.props.ErrorBoundary,hasErrorBoundary:a.props.ErrorBoundary!=null||a.props.errorElement!=null,shouldRevalidate:a.props.shouldRevalidate,handle:a.props.handle,lazy:a.props.lazy};a.props.children&&(i.children=ar(a.props.children,o)),r.push(i)}),r}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function nr(){return nr=Object.assign?Object.assign.bind():function(e){for(var s=1;s<arguments.length;s++){var r=arguments[s];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},nr.apply(this,arguments)}function cl(e,s){if(e==null)return{};var r={},a=Object.keys(e),n,o;for(o=0;o<a.length;o++)n=a[o],!(s.indexOf(n)>=0)&&(r[n]=e[n]);return r}function dl(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function ul(e,s){return e.button===0&&(!s||s==="_self")&&!dl(e)}function ir(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((s,r)=>{let a=e[r];return s.concat(Array.isArray(a)?a.map(n=>[r,n]):[[r,a]])},[]))}function hl(e,s){let r=ir(e);return s&&s.forEach((a,n)=>{r.has(n)||s.getAll(n).forEach(o=>{r.append(n,o)})}),r}const gl=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],xl="6";try{window.__reactRouterVersion=xl}catch{}const fl="startTransition",aa=Jn[fl];function ml(e){let{basename:s,children:r,future:a,window:n}=e,o=d.useRef();o.current==null&&(o.current=bi({window:n,v5Compat:!0}));let i=o.current,[l,c]=d.useState({action:i.action,location:i.location}),{v7_startTransition:g}=a||{},u=d.useCallback(x=>{g&&aa?aa(()=>c(x)):c(x)},[c,g]);return d.useLayoutEffect(()=>i.listen(u),[i,u]),d.useEffect(()=>il(a),[a]),d.createElement(ll,{basename:s,children:r,location:l.location,navigationType:l.action,navigator:i,future:a})}const pl=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",yl=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ae=d.forwardRef(function(s,r){let{onClick:a,relative:n,reloadDocument:o,replace:i,state:l,target:c,to:g,preventScrollReset:u,viewTransition:x}=s,p=cl(s,gl),{basename:m}=d.useContext(it),w,T=!1;if(typeof g=="string"&&yl.test(g)&&(w=g,pl))try{let z=new URL(window.location.href),R=g.startsWith("//")?new URL(z.protocol+g):new URL(g),D=vr(R.pathname,m);R.origin===z.origin&&D!=null?g=D+R.search+R.hash:T=!0}catch{}let N=Ki(g,{relative:n}),A=bl(g,{replace:i,state:l,target:c,preventScrollReset:u,relative:n,viewTransition:x});function E(z){a&&a(z),z.defaultPrevented||A(z)}return d.createElement("a",nr({},p,{href:w||N,onClick:T||o?a:E,ref:r,target:c}))});var na;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(na||(na={}));var ia;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(ia||(ia={}));function bl(e,s){let{target:r,replace:a,state:n,preventScrollReset:o,relative:i,viewTransition:l}=s===void 0?{}:s,c=Ft(),g=jt(),u=Qa(e,{relative:i});return d.useCallback(x=>{if(ul(x,r)){x.preventDefault();let p=a!==void 0?a:rs(g)===rs(u);c(e,{replace:p,state:n,preventScrollReset:o,relative:i,viewTransition:l})}},[g,c,u,a,n,r,e,o,i,l])}function vl(e){let s=d.useRef(ir(e)),r=d.useRef(!1),a=jt(),n=d.useMemo(()=>hl(a.search,r.current?null:s.current),[a.search]),o=Ft(),i=d.useCallback((l,c)=>{const g=ir(typeof l=="function"?l(n):l);r.current=!0,o("?"+g,c)},[o,n]);return[n,i]}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var jl={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kl=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),$=(e,s)=>{const r=d.forwardRef(({color:a="currentColor",size:n=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:l="",children:c,...g},u)=>d.createElement("svg",{ref:u,...jl,width:n,height:n,stroke:a,strokeWidth:i?Number(o)*24/Number(n):o,className:["lucide",`lucide-${kl(e)}`,l].join(" "),...g},[...s.map(([x,p])=>d.createElement(x,p)),...Array.isArray(c)?c:[c]]));return r.displayName=`${e}`,r};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=$("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ns=$("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wl=$("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nl=$("Bold",[["path",{d:"M14 12a4 4 0 0 0 0-8H6v8",key:"v2sylx"}],["path",{d:"M15 20a4 4 0 0 0 0-8H6v8Z",key:"1ef5ya"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ae=$("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lr=$("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gt=$("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const El=$("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const en=$("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=$("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sl=$("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cl=$("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Al=$("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=$("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kr=$("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xe=$("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ml=$("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=$("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tl=$("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=$("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Il=$("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=$("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pl=$("Heading2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=$("Heading3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=$("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const is=$("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const la=$("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yt=$("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oa=$("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=$("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ca=$("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=$("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=$("ListOrdered",[["line",{x1:"10",x2:"21",y1:"6",y2:"6",key:"76qw6h"}],["line",{x1:"10",x2:"21",y1:"12",y2:"12",key:"16nom4"}],["line",{x1:"10",x2:"21",y1:"18",y2:"18",key:"u3jurt"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tn=$("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mt=$("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hl=$("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bl=$("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sn=$("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const da=$("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rn=$("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wl=$("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const es=$("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fl=$("Minimize2",[["polyline",{points:"4 14 10 14 10 20",key:"11kfnr"}],["polyline",{points:"20 10 14 10 14 4",key:"rlmsce"}],["line",{x1:"14",x2:"21",y1:"10",y2:"3",key:"o5lafz"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kl=$("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ql=$("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gl=$("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nt=$("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vl=$("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",key:"1xcu5"}],["circle",{cx:"17.5",cy:"10.5",r:".5",key:"736e4u"}],["circle",{cx:"8.5",cy:"7.5",r:".5",key:"clrty"}],["circle",{cx:"6.5",cy:"12.5",r:".5",key:"1s4xz9"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jl=$("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const or=$("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xl=$("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ql=$("Quote",[["path",{d:"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z",key:"4rm80e"}],["path",{d:"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z",key:"10za9r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zl=$("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yl=$("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const eo=$("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const to=$("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=$("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const so=$("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xt=$("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ro=$("SkipBack",[["polygon",{points:"19 20 9 12 19 4 19 20",key:"o2sva"}],["line",{x1:"5",x2:"5",y1:"19",y2:"5",key:"1ocqjk"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ao=$("SkipForward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ct=$("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const an=$("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const no=$("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const io=$("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ft=$("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lo=$("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=$("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oo=$("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const co=$("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uo=$("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qe=$("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ho=$("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const go=$("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ze=$("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xo=$("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fo=$("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]);function mo(){const[e,s]=d.useState(()=>{const a=localStorage.getItem("theme");return a||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")});return d.useEffect(()=>{const a=document.documentElement;a.classList.remove("light","dark"),a.classList.add(e),localStorage.setItem("theme",e)},[e]),{theme:e,toggleTheme:()=>{s(a=>a==="light"?"dark":"light")},setTheme:s,isDark:e==="dark"}}const po={weather:"https://blog.fddfffff.site",aiImage:"https://blog.fddfffff.site",blog:"https://blog.fddfffff.site",music:"https://blog.fddfffff.site",r2Storage:"https://pub-02490a32db742d596d4d7c00aec.r2.dev"},ye=po,fe={weather:`${ye.weather}/weather`,aiImage:`${ye.aiImage}/ai/generate`,posts:`${ye.blog}/api/posts`,post:e=>`${ye.blog}/api/posts/${e}`,comments:e=>`${ye.blog}/api/posts/${e}/comments`,comment:(e,s)=>`${ye.blog}/api/posts/${e}/comments/${s}`,uploadImage:`${ye.blog}/api/upload-image`,auth:{verify:`${ye.blog}/api/auth/verify`},music:{search:`${ye.music}/music/search`,songUrl:`${ye.music}/music/url`,lyric:`${ye.music}/music/lyric`,songDetail:`${ye.music}/music/song/detail`,playlist:`${ye.music}/music/playlist/detail`},r2Images:`${ye.r2Storage}`,categories:`${ye.blog}/api/categories`,tags:`${ye.blog}/api/tags`,search:`${ye.blog}/api/search`},nn={"Content-Type":"application/json",Accept:"application/json"},ln=6e4;function yo(){return localStorage.getItem("admin_api_key")}const Te=(e,s={})=>{const r=new AbortController,a=setTimeout(()=>r.abort(),ln);return fetch(e,{headers:{...nn,...s.headers},signal:r.signal,...s}).finally(()=>{clearTimeout(a)})},ts=(e,s={})=>{const r=yo(),a={...nn,...s.headers};r&&(a["X-API-Key"]=r);const n=new AbortController,o=setTimeout(()=>n.abort(),ln);return fetch(e,{headers:a,signal:n.signal,...s}).finally(()=>{clearTimeout(o)})};class ls extends Error{constructor(s,r,a){super(a||`API Error: ${s} ${r}`),this.status=s,this.statusText=r,this.name="ApiError"}}const ve=async e=>{if(!e.ok){let s;try{const r=await e.json();s=r.error||r.message||e.statusText}catch{s=e.statusText}throw new ls(e.status,e.statusText,s)}return e.json()},on=d.createContext(void 0);function qt(){const e=d.useContext(on);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e}function bo({children:e}){const[s,r]=d.useState(!1),[a,n]=d.useState(null),[o,i]=d.useState(!0);d.useEffect(()=>{const x=localStorage.getItem("admin_api_key");x?l(x):i(!1)},[]);const l=async x=>{try{return(await(await fetch(fe.auth.verify,{method:"POST",headers:{"Content-Type":"application/json","X-API-Key":x}})).json()).isAdmin?(r(!0),n(x),localStorage.setItem("admin_api_key",x),!0):(r(!1),n(null),localStorage.removeItem("admin_api_key"),!1)}catch(p){return console.error("API密钥验证失败:",p),r(!1),n(null),localStorage.removeItem("admin_api_key"),!1}finally{i(!1)}},u={isAdmin:s,apiKey:a,login:async x=>(i(!0),await l(x)),logout:()=>{r(!1),n(null),localStorage.removeItem("admin_api_key")},isLoading:o};return t.jsx(on.Provider,{value:u,children:e})}function cn({isOpen:e,onClose:s}){const[r,a]=d.useState(""),[n,o]=d.useState(!1),[i,l]=d.useState(""),[c,g]=d.useState(!1),{login:u}=qt();if(!e)return null;const x=async m=>{if(m.preventDefault(),!r.trim()){l("请输入API密钥");return}g(!0),l("");try{await u(r.trim())?(a(""),s()):l("API密钥无效，请检查后重试")}catch{l("登录失败，请稍后重试")}finally{g(!1)}},p=()=>{a(""),l(""),o(!1),s()};return t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:t.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:[t.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:t.jsx(xt,{className:"text-white",size:20})}),t.jsxs("div",{children:[t.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"管理员登录"}),t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"输入API密钥以获取管理权限"})]})]}),t.jsx("button",{onClick:p,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"关闭",children:t.jsx(Ze,{size:20})})]}),t.jsxs("form",{onSubmit:x,className:"p-6",children:[t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[t.jsx(ca,{size:16,className:"inline mr-2"}),"API密钥"]}),t.jsxs("div",{className:"relative",children:[t.jsx("input",{type:n?"text":"password",value:r,onChange:m=>a(m.target.value),placeholder:"请输入管理员API密钥",className:"w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",disabled:c}),t.jsx("button",{type:"button",onClick:()=>o(!n),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",disabled:c,title:n?"隐藏密钥":"显示密钥",children:n?t.jsx(kr,{size:16}):t.jsx(Xe,{size:16})})]})]}),i&&t.jsx("div",{className:"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:t.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[t.jsx(Hl,{size:16,className:"mr-2"}),i]})})]}),t.jsxs("div",{className:"flex space-x-3 mt-6",children:[t.jsx("button",{type:"button",onClick:p,className:"flex-1 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",disabled:c,children:"取消"}),t.jsx("button",{type:"submit",disabled:c||!r.trim(),className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:c?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),t.jsx("span",{children:"验证中..."})]}):t.jsxs(t.Fragment,{children:[t.jsx(ca,{size:16}),t.jsx("span",{children:"登录"})]})})]})]})]})})}class je{static async getPosts(s=1,r=10,a,n){var l,c,g;const o=new URLSearchParams({page:s.toString(),pageSize:r.toString()});a&&o.append("category",a),n&&o.append("tag",n);const i=`${fe.posts}?${o.toString()}`;try{const u=await Te(i),x=await ve(u);return{posts:x.posts||[],total:((l=x.pagination)==null?void 0:l.totalPosts)||0,page:((c=x.pagination)==null?void 0:c.page)||1,pageSize:((g=x.pagination)==null?void 0:g.pageSize)||10}}catch(u){throw console.error("获取文章列表失败:",u),u}}static async getPost(s){try{const r=await Te(fe.post(s));return await ve(r)}catch(r){throw console.error("获取文章失败:",r),r}}static async createPost(s){try{const r=await ts(fe.posts,{method:"POST",body:JSON.stringify(s)});return await ve(r)}catch(r){throw console.error("创建文章失败:",r),r}}static async updatePost(s,r){try{const a=await ts(fe.post(s),{method:"PUT",body:JSON.stringify(r)});return await ve(a)}catch(a){throw console.error("更新文章失败:",a),a}}static async deletePost(s){try{const r=await ts(fe.post(s),{method:"DELETE"});await ve(r)}catch(r){throw console.error("删除文章失败:",r),r}}static async searchPosts(s){const r=new URLSearchParams({q:s}),a=`${fe.search}?${r.toString()}`;try{const n=await Te(a);return(await ve(n)).posts}catch(n){throw console.error("搜索文章失败:",n),n}}}function vo({onMusicToggle:e}){const[s,r]=d.useState(!1),[a,n]=d.useState(!1),[o,i]=d.useState(!1),[l,c]=d.useState(""),[g,u]=d.useState([]),[x,p]=d.useState(!1),{theme:m,toggleTheme:w}=mo(),{isAdmin:T,logout:N}=qt(),A=Ft(),E=d.useRef(null),z=d.useRef(null),R=[{name:"首页",href:"/"},{name:"分类",href:"/categories"},{name:"标签",href:"/tags"},{name:"关于",href:"/about"}],D=async _=>{if(!_.trim()){u([]);return}p(!0);try{const G=await je.searchPosts(_.trim());u(G.slice(0,5))}catch(G){console.error("搜索失败:",G),u([])}finally{p(!1)}};return d.useEffect(()=>{const _=G=>{(G.ctrlKey||G.metaKey)&&G.key==="k"&&(G.preventDefault(),i(Z=>{const B=!Z;return B?setTimeout(()=>{var se;return(se=z.current)==null?void 0:se.focus()},100):(c(""),u([])),B})),G.key==="Escape"&&(i(!1),c(""),u([]))};return document.addEventListener("keydown",_),()=>document.removeEventListener("keydown",_)},[]),d.useEffect(()=>{const _=G=>{E.current&&!E.current.contains(G.target)&&(i(!1),c(""),u([]))};if(o)return document.addEventListener("mousedown",_),()=>document.removeEventListener("mousedown",_)},[o]),d.useEffect(()=>{const _=setTimeout(()=>{D(l)},300);return()=>clearTimeout(_)},[l]),t.jsxs("header",{className:"sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700",children:[t.jsxs("div",{className:"container mx-auto px-4",children:[t.jsxs("div",{className:"flex items-center justify-between h-16",children:[t.jsxs(ae,{to:"/",className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:t.jsx("span",{className:"text-white font-bold text-sm",children:"B"})}),t.jsx("span",{className:"font-semibold text-xl text-gray-900 dark:text-white",children:"个人博客"})]}),t.jsx("nav",{className:"hidden md:flex items-center space-x-8",children:R.map(_=>t.jsx(ae,{to:_.href,className:"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:_.name},_.name))}),t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("div",{className:"relative",ref:E,children:[t.jsx("button",{onClick:()=>i(!o),title:"快捷搜索 (Ctrl+K)","aria-label":"快捷搜索",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(De,{size:20})}),o&&t.jsx("div",{className:"absolute top-full mt-2 right-0 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50",children:t.jsxs("div",{className:"p-4",children:[t.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[t.jsx(De,{size:16,className:"text-gray-400"}),t.jsx("input",{ref:z,type:"text",placeholder:"搜索文章...",value:l,onChange:_=>c(_.target.value),className:"flex-1 outline-none bg-transparent text-sm",onKeyDown:_=>{_.key==="Enter"&&l.trim()&&(A(`/search?q=${encodeURIComponent(l.trim())}`),i(!1),c(""))}}),t.jsxs("kbd",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded text-gray-500 dark:text-gray-400",children:[t.jsx(Al,{size:12,className:"inline mr-1"}),"K"]})]}),x&&t.jsx("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:"搜索中..."}),g.length>0&&!x&&t.jsxs("div",{className:"space-y-2",children:[t.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:"搜索结果"}),g.map(_=>t.jsxs(ae,{to:`/post/${_.slug}`,onClick:()=>{i(!1),c("")},className:"block p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-sm",children:[t.jsx("div",{className:"font-medium text-gray-900 dark:text-white truncate",children:_.title}),t.jsx("div",{className:"text-gray-500 dark:text-gray-400 text-xs truncate mt-1",children:_.excerpt})]},_.slug)),l.trim()&&t.jsx(ae,{to:`/search?q=${encodeURIComponent(l.trim())}`,onClick:()=>{i(!1),c("")},className:"block p-2 text-center text-blue-600 dark:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-sm",children:"查看所有结果 →"})]}),l.trim()&&g.length===0&&!x&&t.jsxs("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:["未找到相关文章",t.jsx(ae,{to:`/search?q=${encodeURIComponent(l.trim())}`,onClick:()=>{i(!1),c("")},className:"block mt-2 text-blue-600 dark:text-blue-400 hover:underline",children:"在搜索页面查看 →"})]})]})})]}),t.jsx("button",{onClick:e,title:"音乐播放器","aria-label":"打开音乐播放器",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors",children:t.jsx(Nt,{size:20})}),T?t.jsx("button",{onClick:N,title:"管理员已登录 - 点击退出","aria-label":"管理员退出",className:"p-2 text-green-600 dark:text-green-400 hover:text-red-600 dark:hover:text-red-400 transition-colors",children:t.jsx(xt,{size:20})}):t.jsx("button",{onClick:()=>n(!0),title:"管理员登录","aria-label":"管理员登录",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(xt,{size:20})}),T&&t.jsxs(ae,{to:"/write",title:"写文章",className:"hidden md:flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[t.jsx(or,{size:16}),t.jsx("span",{children:"写文章"})]}),t.jsx("button",{onClick:w,title:m==="dark"?"切换到浅色模式":"切换到深色模式","aria-label":m==="dark"?"切换到浅色模式":"切换到深色模式",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:m==="dark"?t.jsx(io,{size:20}):t.jsx(Kl,{size:20})}),t.jsx("button",{onClick:()=>r(!s),title:s?"关闭菜单":"打开菜单","aria-label":s?"关闭菜单":"打开菜单","aria-expanded":s,className:"md:hidden p-2 text-gray-600 dark:text-gray-400",children:s?t.jsx(Ze,{size:20}):t.jsx(Wl,{size:20})})]})]}),s&&t.jsx("div",{className:"md:hidden py-4 border-t border-gray-200 dark:border-gray-700",children:t.jsxs("nav",{className:"space-y-2",children:[R.map(_=>t.jsx(ae,{to:_.href,className:"block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors",onClick:()=>r(!1),children:_.name},_.name)),T?t.jsxs(t.Fragment,{children:[t.jsxs(ae,{to:"/write",className:"flex items-center space-x-2 px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors",onClick:()=>r(!1),children:[t.jsx(or,{size:16}),t.jsx("span",{children:"写文章"})]}),t.jsxs("button",{onClick:()=>{N(),r(!1)},className:"flex items-center space-x-2 px-4 py-2 text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left",children:[t.jsx(Bl,{size:16}),t.jsx("span",{children:"管理员退出"})]})]}):t.jsxs("button",{onClick:()=>{n(!0),r(!1)},className:"flex items-center space-x-2 px-4 py-2 text-green-600 dark:text-green-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left",children:[t.jsx(xt,{size:16}),t.jsx("span",{children:"管理员登录"})]})]})})]}),t.jsx(cn,{isOpen:a,onClose:()=>n(!1)})]})}function jo(){return t.jsx("footer",{className:"bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700",children:t.jsxs("div",{className:"container mx-auto px-4 py-8",children:[t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[t.jsxs("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"个人博客"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"基于 Cloudflare Workers 构建的现代个人博客系统，集成 AI 配图、天气显示等功能。"})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"快速链接"}),t.jsxs("ul",{className:"space-y-2 text-sm",children:[t.jsx("li",{children:t.jsx("a",{href:"/",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"首页"})}),t.jsx("li",{children:t.jsx("a",{href:"/about",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"关于我"})}),t.jsx("li",{children:t.jsx("a",{href:"/write",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"写文章"})})]})]}),t.jsxs("div",{children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"联系方式"}),t.jsxs("div",{className:"flex space-x-4",children:[t.jsx("a",{href:"https://github.com/ajkdfe2e2e",target:"_blank",rel:"noopener noreferrer",title:"GitHub","aria-label":"访问 GitHub 主页",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(Tl,{size:20})}),t.jsx("a",{href:"https://x.com/x2a1HRjxs552213",target:"_blank",rel:"noopener noreferrer",title:"Twitter","aria-label":"访问 Twitter 主页",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(oo,{size:20})}),t.jsx("a",{href:"mailto:<EMAIL>",title:"邮箱","aria-label":"发送邮件联系我",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:t.jsx(sn,{size:20})})]})]})]}),t.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center",children:t.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"© 2025 个人博客. 使用 Cloudflare Workers 强力驱动"})})]})})}class ua{static async getCurrentWeather(){try{let s;try{s=await this.getCurrentPosition()}catch(o){console.warn("无法获取当前位置，使用默认位置（北京）:",o),s={latitude:39.9042,longitude:116.4074}}const r=new URLSearchParams({lat:s.latitude.toString(),lon:s.longitude.toString()}),a=`${fe.weather}?${r.toString()}`,n=await Te(a);return await ve(n)}catch(s){throw console.error("获取天气信息失败:",s),s}}static async getWeatherByLocation(s,r){try{const a=new URLSearchParams({lat:s.toString(),lon:r.toString()}),n=`${fe.weather}?${a.toString()}`,o=await Te(n);return await ve(o)}catch(a){throw console.error("获取天气信息失败:",a),a}}static getCurrentPosition(){return new Promise((s,r)=>{if(!navigator.geolocation){r(new Error("该浏览器不支持地理定位"));return}const a={enableHighAccuracy:!0,timeout:5e3,maximumAge:3e5};navigator.geolocation.getCurrentPosition(n=>{s({latitude:n.coords.latitude,longitude:n.coords.longitude})},n=>{let o="获取位置失败";switch(n.code){case n.PERMISSION_DENIED:o="用户拒绝了位置请求";break;case n.POSITION_UNAVAILABLE:o="位置信息不可用";break;case n.TIMEOUT:o="获取位置超时";break}r(new Error(o))},a)})}static isGeolocationSupported(){return"geolocation"in navigator}}function ko(){const[e,s]=d.useState(null),[r,a]=d.useState(!0),[n,o]=d.useState(null);return d.useEffect(()=>{(async()=>{try{if(a(!0),o(null),!ua.isGeolocationSupported())throw new Error("浏览器不支持地理定位");const l=await ua.getCurrentWeather();s(l)}catch(l){console.error("获取天气信息失败:",l),o(l instanceof Error?l.message:"获取天气信息失败")}finally{a(!1)}})()},[]),r?t.jsx("div",{className:"card p-4",children:t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"animate-spin",children:t.jsx(Sl,{size:20,className:"text-gray-400"})}),t.jsxs("div",{children:[t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-1"}),t.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"})]})]})}):n||!e?t.jsx("div",{className:"card p-4",children:t.jsxs("div",{className:"flex items-center space-x-3 text-gray-500 dark:text-gray-400",children:[t.jsx(da,{size:20}),t.jsxs("div",{children:[t.jsx("p",{className:"text-sm",children:"位置信息"}),t.jsx("p",{className:"text-xs",children:"获取失败"})]})]})}):t.jsx("div",{className:"card p-4",children:t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"flex-shrink-0",children:t.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center",children:t.jsx(lo,{size:20,className:"text-white"})})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(da,{size:14,className:"text-gray-500 dark:text-gray-400"}),t.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.location})]}),t.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[t.jsx("span",{className:"text-lg font-semibold text-blue-600 dark:text-blue-400",children:e.temperature}),t.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.weather})]})]})]})})}function wo(){const[e,s]=d.useState([]),[r,a]=d.useState([]),[n,o]=d.useState(!0);return d.useEffect(()=>{(async()=>{try{o(!0);const l=await je.getPosts(1,5);s(l.posts);const c=await je.getPosts(1,100),g=new Set;c.posts.forEach(u=>{u.tags.forEach(x=>g.add(x))}),a(Array.from(g))}catch(l){console.error("加载侧边栏数据失败:",l),s([]),a([])}finally{o(!1)}})()},[]),t.jsxs("div",{className:"sticky top-8 space-y-6",children:[t.jsx(ko,{}),t.jsxs("div",{className:"card p-4",children:[t.jsx("h3",{className:"font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"最新文章"}),t.jsx("div",{className:"space-y-2",children:n?Array.from({length:3}).map((i,l)=>t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"},l)):e.length>0?e.map(i=>t.jsx(ae,{to:`/post/${i.slug}`,className:"block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors line-clamp-2",title:i.title,children:i.title},i.id)):t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"暂无文章"})})]}),t.jsxs("div",{className:"card p-4",children:[t.jsx("h3",{className:"font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"标签"}),t.jsx("div",{className:"flex flex-wrap gap-2",children:n?Array.from({length:6}).map((i,l)=>t.jsx("div",{className:"h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"},l)):r.length>0?r.map(i=>t.jsx(ae,{to:`/tag/${encodeURIComponent(i)}`,className:"px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-blue-600 hover:text-white transition-colors cursor-pointer",children:i},i)):t.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"暂无标签"})})]})]})}function No(){const e=jt(),s=Kt(),[r,a]=d.useState([]),[n,o]=d.useState(null);return d.useEffect(()=>{(async()=>{if(e.pathname.startsWith("/post/")&&s.slug)try{const l=await je.getPost(s.slug);o({title:l.title,category:l.category})}catch(l){console.error("获取文章详情失败:",l),o({title:"文章详情",category:void 0})}})()},[e.pathname,s.slug]),d.useEffect(()=>{a((()=>{const l=e.pathname,c=[{label:"首页",href:"/"}];return l==="/"?[{label:"首页"}]:(l==="/categories"?c.push({label:"分类"}):l.startsWith("/category/")?(c.push({label:"分类",href:"/categories"}),c.push({label:decodeURIComponent(s.category||"")})):l==="/tags"?c.push({label:"标签"}):l.startsWith("/tag/")?(c.push({label:"标签",href:"/tags"}),c.push({label:decodeURIComponent(s.tag||"")})):l==="/search"?c.push({label:"搜索"}):l==="/about"?c.push({label:"关于"}):l==="/write"?c.push({label:"写文章"}):l.startsWith("/write/")?(c.push({label:"写文章",href:"/write"}),c.push({label:"编辑文章"})):l.startsWith("/post/")&&(n?(n.category&&(c.push({label:"分类",href:"/categories"}),c.push({label:n.category,href:`/category/${encodeURIComponent(n.category)}`})),c.push({label:n.title})):c.push({label:"文章详情"})),c)})())},[e.pathname,s,n]),e.pathname==="/"?null:t.jsx("nav",{"aria-label":"面包屑导航",className:"mb-6",children:t.jsx("ol",{className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400",children:r.map((i,l)=>t.jsxs("li",{className:"flex items-center",children:[l>0&&t.jsx(en,{size:16,className:"mr-2 text-gray-400"}),i.href?t.jsxs(ae,{to:i.href,className:"flex items-center hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[l===0&&t.jsx(la,{size:16,className:"mr-1"}),t.jsx("span",{className:"truncate max-w-[200px]",title:i.label,children:i.label})]}):t.jsxs("span",{className:"flex items-center text-gray-900 dark:text-gray-100",children:[l===0&&t.jsx(la,{size:16,className:"mr-1"}),t.jsx("span",{className:"truncate max-w-[200px]",title:i.label,children:i.label})]})]},l))})})}function Eo({children:e,onMusicToggle:s}){return t.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors",children:[t.jsx(vo,{onMusicToggle:s}),t.jsx("main",{className:"container mx-auto px-4 py-8 max-w-6xl",children:t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[t.jsxs("div",{className:"lg:col-span-3",children:[t.jsx(No,{}),e]}),t.jsx("aside",{className:"lg:col-span-1",children:t.jsx(wo,{})})]})}),t.jsx(jo,{})]})}const ha=(e,s,r)=>{if(console.log(`[Music Player] 处理图片URL: ${e} (歌曲: ${s})`),!e)return console.log("[Music Player] 图片URL为空，使用默认图片"),"/default-album.jpg";if(r!=null&&r.has(e))return console.log(`[Music Player] URL之前加载失败过，使用默认图片: ${e}`),"/default-album.jpg";let a=e;return a.startsWith("http:")&&(console.log(`[Music Player] 转换HTTP到HTTPS: ${a}`),a.match(/http:\/\/p\d+\.music\.126\.net/)?a=a.replace(/^http:\/\/p(\d+)\.music\.126\.net/,"https://p$1.music.126.net"):a.match(/http:\/\/y\.gtimg\.cn/)?a=a.replace(/^http:\/\//,"https://"):a=a.replace("http:","https:"),console.log(`[Music Player] 转换后URL: ${a}`)),!a||a==="null"||a==="undefined"?(console.log(`[Music Player] URL无效，使用默认图片: ${a}`),"/default-album.jpg"):(console.log(`[Music Player] 最终图片URL: ${a}`),a)},So=d.forwardRef((e,s)=>{var _e,P;const[r,a]=d.useState(!1),[n,o]=d.useState(null),[i,l]=d.useState([]),[c,g]=d.useState(0),[u,x]=d.useState(!1),[p,m]=d.useState(!1),[w,T]=d.useState(0),[N,A]=d.useState(0),[E,z]=d.useState(.7),[R,D]=d.useState(!1),[_,G]=d.useState(""),[Z,B]=d.useState([]),[se,Y]=d.useState(!1),[te,ue]=d.useState([]),[le,ce]=d.useState(-1),[k,f]=d.useState([]),[j,b]=d.useState("auto"),[S,I]=d.useState(!1),[K,J]=d.useState(""),C=d.useRef(null),ne=d.useRef(null),[oe,ke]=d.useState(new Set);d.useImperativeHandle(s,()=>({toggleVisibility:()=>{x(!u),m(!0)}})),d.useEffect(()=>{(async()=>{try{const v=await(await fetch(`${fe.music.search.replace("/search","/sources")}`)).json();if(v&&v.code===200&&Array.isArray(v.sources))f(v.sources);else throw new Error("Invalid response format")}catch(y){console.error("Error fetching music sources:",y),f([{type:"wyy",name:"网易云音乐",enabled:!0,priority:1},{type:"qq",name:"QQ音乐",enabled:!0,priority:2},{type:"kg",name:"酷狗音乐",enabled:!0,priority:3},{type:"kw",name:"酷我音乐",enabled:!0,priority:4},{type:"xmla",name:"喜马拉雅",enabled:!0,priority:5},{type:"qishui",name:"汽水音乐",enabled:!0,priority:6},{type:"qt",name:"蜻蜓FM",enabled:!0,priority:7}])}})()},[]),d.useEffect(()=>{let h=window.innerWidth;const y=()=>{const v=window.innerWidth;if(Math.abs(v-h)<50)return;const O=v<640;h<640!==O&&(O&&!n&&u&&!p?x(!1):!O&&!u&&x(!0)),h=v};return window.addEventListener("resize",y),()=>window.removeEventListener("resize",y)},[n,u]),d.useEffect(()=>{const h=C.current;if(!h)return;const y=()=>T(h.currentTime),v=()=>A(h.duration),O=()=>H();return h.addEventListener("timeupdate",y),h.addEventListener("durationchange",v),h.addEventListener("ended",O),()=>{h.removeEventListener("timeupdate",y),h.removeEventListener("durationchange",v),h.removeEventListener("ended",O)}},[]),d.useEffect(()=>{C.current&&(C.current.volume=R?0:E)},[E,R]),d.useEffect(()=>{var y;if(!te.length)return;const h=te.findIndex((v,O)=>{const q=te[O+1];return w>=v.time&&(!q||w<q.time)});if(h!==-1&&h!==le){ce(h);const v=(y=ne.current)==null?void 0:y.children[h];v&&v.scrollIntoView({behavior:"smooth",block:"center"})}},[w,te,le]);const de=async h=>{if(h.trim()){Y(!0);try{let y=`${fe.music.search}?keywords=${encodeURIComponent(h)}`;j!=="auto"&&(y+=`&source=${j}`);const v=await fetch(y);if(!v.ok)throw new Error(`HTTP error! status: ${v.status}`);const O=await v.json();if(console.log("[Music Player] 后端返回的原始数据:",O),O.code===200&&O.result&&Array.isArray(O.result.songs)){const q=O.result.songs.map(V=>{var et,Ur,Hr,Br,Wr,Fr,Kr,qr,Gr,Vr,Jr,Xr;console.log("[Music Player] 处理单首歌曲数据:",V);const Ee={id:((et=V.album)==null?void 0:et.id)||((Ur=V.al)==null?void 0:Ur.id)||0,name:((Hr=V.album)==null?void 0:Hr.name)||((Br=V.al)==null?void 0:Br.name)||"未知专辑",picUrl:((Wr=V.album)==null?void 0:Wr.picUrl)||((Fr=V.al)==null?void 0:Fr.picUrl)||"/default-album.jpg"};return console.log("[Music Player] 提取的专辑信息:",Ee),{id:V.id,name:V.name||"未知歌曲",artists:Array.isArray(V.artists)?V.artists:Array.isArray(V.ar)?V.ar:[{id:0,name:"未知艺术家"}],album:Ee,duration:V.duration||V.dt||0,sourceType:V.sourceType||O.source,sourceName:V.sourceName||O.sourceName,mid:((Kr=V.originalData)==null?void 0:Kr.mid)||V.mid,media_mid:((qr=V.originalData)==null?void 0:qr.media_mid)||V.media_mid,vid:((Gr=V.originalData)==null?void 0:Gr.vid)||V.vid,hash:((Vr=V.originalData)==null?void 0:Vr.hash)||V.hash,album_id:((Jr=V.originalData)==null?void 0:Jr.album_id)||V.album_id,album_audio_id:((Xr=V.originalData)==null?void 0:Xr.album_audio_id)||V.album_audio_id}});if(B(q),O.sourceName)J(`搜索来源：${O.sourceName}`);else if(j!=="auto"){const V=k.find(Ee=>Ee.type===j);J(`搜索来源：${(V==null?void 0:V.name)||j}`)}else J("多源智能搜索")}else B([]),J("搜索无结果")}catch(y){console.error("Error searching music:",y),B([]),J("搜索失败，请检查网络连接")}finally{Y(!1)}}},ge=async h=>{var y,v;try{let O=`${fe.music.songUrl}?`;const q=new URLSearchParams;switch(h.sourceType&&q.append("source",h.sourceType),h.sourceType){case"qq":if(h.mid&&h.media_mid&&h.vid)q.append("mid",h.mid),q.append("media_mid",h.media_mid),q.append("vid",h.vid);else return console.error("QQ音乐缺少必要参数:",h),null;break;case"kg":if(h.hash&&h.album_id&&h.album_audio_id)q.append("hash",h.hash),q.append("album_id",h.album_id),q.append("album_audio_id",h.album_audio_id);else return console.error("酷狗音乐缺少必要参数:",h),null;break;default:q.append("id",h.id.toString());break}O+=q.toString(),console.log("调用URL获取接口:",O);const Ee=await(await fetch(O)).json();return console.log("URL API响应:",Ee),Ee.code===200&&((v=(y=Ee.data)==null?void 0:y[0])!=null&&v.url)?Ee.data[0].url:null}catch(O){return console.error("Error fetching song URL:",O),null}},we=async h=>{try{let y=`${fe.music.lyric}?`;const v=new URLSearchParams;switch(h.sourceType&&v.append("source",h.sourceType),h.sourceType){case"qq":v.append("id",h.id.toString());break;case"kg":if(h.hash)v.append("hash",h.hash);else{console.error("酷狗音乐歌词缺少hash参数:",h),ue([]);return}break;default:v.append("id",h.id.toString());break}y+=v.toString(),console.log("调用歌词获取接口:",y);const q=await(await fetch(y)).json();if(console.log("歌词API响应:",q),q.code===200&&q.lrc&&q.lrc.lyric){const V=be(q.lrc.lyric);ue(V)}else ue([])}catch(y){console.error("Error fetching lyrics:",y),ue([])}},be=h=>h.split(`
`).map(y=>{const v=y.match(/\[(\d{2}):(\d{2})\.(\d{2,3})\](.*)/);if(!v)return null;const O=parseInt(v[1]),q=parseInt(v[2]),V=parseInt(v[3].padEnd(3,"0")),Ee=O*60+q+V/1e3,et=v[4].trim();return et?{time:Ee,text:et}:null}).filter(Boolean),Oe=async(h,y)=>{if(console.log("Playing song:",h),y!==void 0)g(y),l(Z);else{const v=Z.findIndex(O=>O.id===h.id&&O.sourceType===h.sourceType);v!==-1?(g(v),l(Z)):(l([h]),g(0))}o(h);try{const v=await ge(h);if(v){if(C.current){let O=v;v.startsWith("http://")&&(v.match(/http:\/\/m\d+\.music\.126\.net/)?O=v.replace(/^http:\/\/m(\d+)\.music\.126\.net/,"https://m$1.music.126.net"):v.match(/http:\/\/isure\.stream\.qqmusic\.qq\.com/)?O=v.replace(/^http:\/\//,"https://"):O="https://"+v.substring(7)),C.current.src=O,C.current.play(),a(!0),we(h),h.sourceName&&J(`播放来源：${h.sourceName}`)}}else console.error("Failed to get song URL"),J("播放链接获取失败")}catch(v){console.error("Error playing song:",v),J("播放失败")}},Pe=()=>{C.current&&(r?C.current.pause():C.current.play(),a(!r))},H=()=>{if(i.length===0)return;const h=(c+1)%i.length;Oe(i[h],h)},W=()=>{if(i.length===0)return;const h=(c-1+i.length)%i.length;Oe(i[h],h)},X=h=>{const y=parseFloat(h.target.value);T(y),C.current&&(C.current.currentTime=y)},ie=()=>{C.current&&C.current.removeEventListener("timeupdate",()=>T(C.current.currentTime))},L=()=>{C.current&&C.current.addEventListener("timeupdate",()=>T(C.current.currentTime))},F=h=>{const y=parseFloat(h.target.value);z(y),D(!1)},re=()=>{D(!R)},U=h=>{const y=Math.floor(h/60),v=Math.floor(h%60);return`${y}:${v.toString().padStart(2,"0")}`},Q=()=>{I(!S)},Ne=h=>{if(b(h),I(!1),h==="auto")J("多源智能搜索");else{const y=k.find(v=>v.type===h);J(`当前音源：${(y==null?void 0:y.name)||h}`)}},Se=(h,y)=>{const v=h.currentTarget,O=v.src;console.error("[Music Player] 图片加载失败:",{songName:y.name,originalUrl:y.album.picUrl,currentSrc:O,sourceName:y.sourceName}),v.onerror=null,ke(q=>new Set(q).add(O)),O.includes("default-album.jpg")?(console.error(`[Music Player] 连默认图片都加载失败了: ${y.name}`),v.style.backgroundColor="#4b5563",v.style.display="flex",v.style.alignItems="center",v.style.justifyContent="center"):(console.log(`[Music Player] 回退到默认图片: ${y.name}`),v.src="/default-album.jpg")};return t.jsxs(t.Fragment,{children:[t.jsx("audio",{ref:C}),t.jsx("div",{className:`fixed bottom-0 right-0 z-50 transition-transform duration-300 ${u?"translate-y-0":"translate-y-full"}`,children:t.jsxs("div",{className:"w-screen sm:w-[450px] sm:right-4 sm:bottom-4 sm:mb-0 bg-gray-800/95 backdrop-blur-md text-white sm:rounded-lg shadow-lg flex flex-col h-[60vh] sm:h-[500px] sm:max-h-[80vh]",children:[t.jsxs("div",{className:"p-3 sm:p-4 flex justify-between items-center border-b border-gray-700",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsxs("h2",{className:"text-base sm:text-lg font-bold flex items-center",children:[t.jsx(Nt,{className:"mr-2",size:20})," 音乐播放器"]}),K&&t.jsx("span",{className:"text-xs text-purple-400 bg-purple-500/20 px-2 py-1 rounded-full",children:K})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsxs("div",{className:"relative",children:[t.jsx("button",{onClick:Q,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"音源设置",children:t.jsx(zl,{size:18})}),S&&t.jsx("div",{className:"absolute top-full right-0 mt-1 bg-gray-700 rounded-lg shadow-lg border border-gray-600 z-10 min-w-48",children:t.jsxs("div",{className:"p-2",children:[t.jsx("div",{className:"text-xs text-gray-400 mb-2",children:"选择音源"}),t.jsx("button",{onClick:()=>Ne("auto"),className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${j==="auto"?"bg-purple-600 text-white":"hover:bg-gray-600"}`,children:"🔄 智能多源 (推荐)"}),t.jsx("div",{className:"border-t border-gray-600 my-2"}),k.map(h=>t.jsxs("button",{onClick:()=>Ne(h.type),disabled:!h.enabled,className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${j===h.type?"bg-purple-600 text-white":h.enabled?"hover:bg-gray-600":"text-gray-500 cursor-not-allowed"}`,children:[h.name,!h.enabled&&" (禁用)"]},h.type))]})})]}),t.jsx("button",{onClick:()=>{x(!1),m(!0)},className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"收起播放器",children:t.jsx(gt,{size:20})})]})]}),t.jsxs("div",{className:"flex flex-col sm:flex-row flex-grow overflow-hidden",children:[t.jsxs("div",{className:"w-full sm:w-1/2 flex flex-col border-b sm:border-b-0 sm:border-r border-gray-700",children:[t.jsxs("div",{className:"p-3 flex-shrink-0",children:[t.jsxs("form",{onSubmit:h=>{h.preventDefault(),de(_)},className:"flex",children:[t.jsx("input",{type:"text",value:_,onChange:h=>G(h.target.value),placeholder:"搜索歌曲、歌手...",className:"flex-grow bg-gray-700/80 border border-gray-600 rounded-l-md px-3 py-2.5 sm:py-1.5 focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm touch-target"}),t.jsx("button",{type:"submit",className:"bg-purple-600 hover:bg-purple-700 px-4 sm:px-3 py-2.5 sm:py-1.5 rounded-r-md touch-target",title:"搜索",children:t.jsx(De,{size:18})})]}),t.jsx("div",{className:"mt-2 text-xs text-gray-400",children:j==="auto"?t.jsx("span",{children:"🔄 多源智能搜索"}):t.jsxs("span",{children:["🎵 ",((_e=k.find(h=>h.type===j))==null?void 0:_e.name)||j]})})]}),t.jsx("div",{className:"flex-grow overflow-y-auto px-1 mb-2",style:{maxHeight:"calc(60vh - 200px)"},children:se?t.jsx("p",{className:"p-4 text-center text-gray-400",children:"正在搜索..."}):Z.length>0?Z.map((h,y)=>t.jsxs("div",{className:"p-2 flex items-center gap-3 hover:bg-white/10 rounded-lg cursor-pointer transition-colors",onClick:()=>Oe(h,y),children:[t.jsx("img",{src:ha(h.album.picUrl,h.name,oe),alt:h.album.name||"专辑封面",className:"w-12 h-12 rounded-md object-cover bg-gray-700",onError:v=>Se(v,h)}),t.jsxs("div",{className:"flex-grow overflow-hidden",children:[t.jsx("p",{className:"font-semibold text-sm truncate",children:h.name}),t.jsxs("p",{className:"text-xs text-gray-400 truncate",children:[Array.isArray(h.artists)?h.artists.map(v=>v.name).join(" / "):"未知艺术家",t.jsxs("span",{className:"text-purple-400 ml-2",children:["• ",h.sourceName]})]})]})]},`${h.id}-${h.sourceType}-${y}`)):_?t.jsx("p",{className:"p-4 text-center text-gray-400",children:"没有找到相关歌曲"}):t.jsx("div",{className:"p-4 text-center text-gray-500",children:t.jsx("p",{className:"text-sm",children:"开始搜索音乐"})})})]}),t.jsx("div",{className:"w-full sm:w-1/2 flex flex-col p-3 min-h-0",children:n?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"text-center pb-3 border-b border-gray-700 mb-3 flex-shrink-0",children:t.jsxs("div",{className:"flex items-center gap-4",children:[t.jsx("img",{src:ha(n.album.picUrl,n.name,oe),alt:n.album.name,className:"w-16 h-16 rounded-lg shadow-lg",onError:h=>Se(h,n)}),t.jsxs("div",{className:"flex flex-col",children:[t.jsx("h3",{className:"text-lg font-bold",children:n.name||"未知歌曲"}),t.jsx("p",{className:"text-sm text-gray-400",children:Array.isArray(n.artists)&&n.artists.length>0?n.artists.map(h=>h.name).join(" / "):"未知艺术家"})]})]})}),t.jsx("div",{ref:ne,className:"flex-grow overflow-y-auto text-center space-y-2 text-gray-300 px-2",style:{maxHeight:"calc(60vh - 240px)"},children:te.length>0?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"sm:hidden",children:t.jsx("p",{className:"text-sm text-purple-400 font-medium text-center py-2",children:le>=0&&te[le]?te[le].text:((P=te[0])==null?void 0:P.text)||"♪ ♪"})}),t.jsx("div",{className:"hidden sm:block",children:te.map((h,y)=>t.jsx("p",{className:`transition-all duration-300 text-sm leading-relaxed ${y===le?"text-purple-400 font-bold scale-105":"hover:text-gray-200"}`,children:h.text},y))})]}):t.jsx("p",{className:"text-gray-500 text-sm",children:n?"暂无歌词":"♪ ♪"})})]}):t.jsxs("div",{className:"flex-grow flex flex-col items-center justify-center text-gray-500 px-4",children:[t.jsx(Nt,{size:48,className:"mb-4 opacity-50 hidden sm:block"}),t.jsx(Nt,{size:32,className:"mb-2 opacity-50 sm:hidden"}),t.jsx("p",{className:"text-center text-sm sm:text-base",children:"选择音乐开始播放"}),t.jsx("p",{className:"text-xs mt-2 text-gray-600 text-center hidden sm:block",children:"歌词和音乐信息将在此显示"}),t.jsx("p",{className:"text-xs mt-1 text-gray-600 text-center sm:hidden",children:"歌词将在此显示"})]})})]}),t.jsxs("div",{className:"p-3 sm:p-3 border-t border-gray-700 bg-gray-800/95",children:[t.jsxs("div",{className:"mb-3",children:[t.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400 mb-1",children:[t.jsx("span",{children:U(w)}),t.jsx("span",{children:U(N)})]}),t.jsx("input",{type:"range",value:w,max:N||0,onChange:X,onTouchStart:ie,onMouseDown:ie,onTouchEnd:L,onMouseUp:L,className:"w-full music-slider progress-slider",title:"播放进度",style:{"--progress":`${w/(N||1)*100}%`}})]}),t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("div",{className:"flex items-center gap-2",children:[t.jsx("button",{onClick:re,className:"p-2 hover:bg-white/10 rounded-full transition-colors",children:R||E===0?t.jsx(go,{size:20}):t.jsx(ho,{size:20})}),t.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:R?0:E,onChange:F,className:"volume-slider w-24","aria-label":"音量控制"})]}),t.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[t.jsx("button",{onClick:W,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"上一首",children:t.jsx(ro,{size:20})}),t.jsx("button",{onClick:Pe,className:"bg-purple-600 hover:bg-purple-700 rounded-full p-3 text-white touch-target transition-colors",title:r?"暂停":"播放",children:r?t.jsx(Jl,{size:24}):t.jsx(Xl,{size:24})}),t.jsx("button",{onClick:H,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"下一首",children:t.jsx(ao,{size:20})})]}),t.jsx("div",{className:"w-24 sm:w-20"})]})]})]})}),!u&&t.jsx("div",{className:"fixed bottom-4 right-4 z-40",children:t.jsx("button",{onClick:()=>{x(!0),m(!0)},className:"bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110 touch-target",title:"打开音乐播放器",children:t.jsx(Nt,{size:24})})})]})});function Co({onFilterChange:e,availableCategories:s,availableTags:r}){var N;const[a,n]=d.useState({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),[o,i]=d.useState(!1),[l,c]=d.useState(!1),[g,u]=d.useState(!1),x=[{value:"date",label:"发布时间",icon:Ae},{value:"readTime",label:"阅读时长",icon:Ie},{value:"featured",label:"推荐程度",icon:is}];d.useEffect(()=>{e(a)},[a,e]);const p=(A,E)=>{n(z=>({...z,[A]:E}))},m=A=>{const E=A==="sortBy"?"date":A==="sortOrder"?"desc":"";n(z=>({...z,[A]:E}))},w=()=>{n({category:"",tag:"",sortBy:"date",sortOrder:"desc"})},T=a.category||a.tag||a.sortBy!=="date"||a.sortOrder!=="desc";return t.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-6",children:[t.jsxs("div",{className:"flex items-center justify-between mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Ml,{size:20,className:"text-gray-500 dark:text-gray-400"}),t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"筛选文章"})]}),T&&t.jsx("button",{onClick:w,className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:"清除所有筛选"})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[t.jsxs("div",{className:"relative",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"分类"}),t.jsxs("button",{onClick:()=>i(!o),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[t.jsx("span",{className:a.category?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:a.category||"选择分类"}),t.jsx(gt,{size:16})]}),o&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[t.jsx("button",{onClick:()=>{p("category",""),i(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"全部分类"}),s.map(A=>t.jsx("button",{onClick:()=>{p("category",A),i(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:A},A))]})}),a.category&&t.jsx("button",{onClick:()=>m("category"),"aria-label":"清除分类筛选",className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:t.jsx(Ze,{size:14})})]}),t.jsxs("div",{className:"relative",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标签"}),t.jsxs("button",{onClick:()=>c(!l),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[t.jsx("span",{className:a.tag?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:a.tag||"选择标签"}),t.jsx(gt,{size:16})]}),l&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[t.jsx("button",{onClick:()=>{p("tag",""),c(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"全部标签"}),r.map(A=>t.jsx("button",{onClick:()=>{p("tag",A),c(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:A},A))]})}),a.tag&&t.jsx("button",{onClick:()=>m("tag"),"aria-label":"清除标签筛选",className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:t.jsx(Ze,{size:14})})]}),t.jsxs("div",{className:"relative",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"排序方式"}),t.jsxs("button",{onClick:()=>u(!g),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[t.jsx("span",{className:"text-gray-900 dark:text-white",children:(N=x.find(A=>A.value===a.sortBy))==null?void 0:N.label}),t.jsx(gt,{size:16})]}),g&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsx("div",{className:"py-1",children:x.map(A=>{const E=A.icon;return t.jsxs("button",{onClick:()=>{p("sortBy",A.value),u(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2",children:[t.jsx(E,{size:16}),t.jsx("span",{children:A.label})]},A.value)})})})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"排序顺序"}),t.jsxs("div",{className:"flex space-x-2",children:[t.jsx("button",{onClick:()=>p("sortOrder","desc"),className:`flex-1 px-3 py-2 text-sm rounded-md border ${a.sortOrder==="desc"?"bg-blue-600 text-white border-blue-600":"bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"}`,children:"倒序"}),t.jsx("button",{onClick:()=>p("sortOrder","asc"),className:`flex-1 px-3 py-2 text-sm rounded-md border ${a.sortOrder==="asc"?"bg-blue-600 text-white border-blue-600":"bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"}`,children:"正序"})]})]})]})]})}const st=class st{constructor(){Fs(this,"serviceWorkerRegistration",null);this.initServiceWorker()}static getInstance(){return st.instance||(st.instance=new st),st.instance}async initServiceWorker(){if("serviceWorker"in navigator)try{this.serviceWorkerRegistration=await navigator.serviceWorker.register("/sw.js"),console.log("[缓存管理] Service Worker 注册成功"),this.serviceWorkerRegistration.addEventListener("updatefound",()=>{console.log("[缓存管理] Service Worker 更新可用")})}catch(s){console.error("[缓存管理] Service Worker 注册失败:",s)}}async preloadImages(s){if(!this.serviceWorkerRegistration||!this.serviceWorkerRegistration.active)return console.warn("[缓存管理] Service Worker 未激活，使用浏览器预加载"),this.browserPreloadImages(s);try{const r=new MessageChannel,a=new Promise(n=>{r.port1.onmessage=o=>{n(o.data.success)}});return this.serviceWorkerRegistration.active.postMessage({type:"PRELOAD_IMAGES",urls:s},[r.port2]),await a}catch(r){return console.error("[缓存管理] 预加载失败:",r),!1}}async browserPreloadImages(s){try{const r=s.map(a=>new Promise((n,o)=>{const i=new Image;i.onload=()=>n(),i.onerror=()=>o(new Error(`Failed to load ${a}`)),i.src=a}));return await Promise.allSettled(r),console.log("[缓存管理] 浏览器预加载完成"),!0}catch(r){return console.error("[缓存管理] 浏览器预加载失败:",r),!1}}async clearImageCache(){if(!this.serviceWorkerRegistration||!this.serviceWorkerRegistration.active)return console.warn("[缓存管理] Service Worker 未激活，无法清理缓存"),!1;try{const s=new MessageChannel,r=new Promise(a=>{s.port1.onmessage=n=>{a(n.data.success)}});return this.serviceWorkerRegistration.active.postMessage({type:"CLEAR_IMAGE_CACHE"},[s.port2]),await r}catch(s){return console.error("[缓存管理] 清理缓存失败:",s),!1}}async getCacheSize(){if("storage"in navigator&&"estimate"in navigator.storage)try{return(await navigator.storage.estimate()).usage||0}catch(s){return console.error("[缓存管理] 获取缓存大小失败:",s),0}return 0}async checkCacheQuota(){if("storage"in navigator&&"estimate"in navigator.storage)try{const s=await navigator.storage.estimate(),r=s.usage||0,a=s.quota||0,n=a-r,o=a>0?r/a*100:0;return{used:r,available:n,percentage:o}}catch(s){console.error("[缓存管理] 检查缓存配额失败:",s)}return{used:0,available:0,percentage:0}}async smartPreload(s){try{const r=[],a=this.getFeaturedImages();r.push(...a),r.length>0&&(console.log("[缓存管理] 开始智能预加载:",r.length,"张图片"),await this.preloadImages(r))}catch(r){console.error("[缓存管理] 智能预加载失败:",r)}}getFeaturedImages(){try{const s=localStorage.getItem("featured_images");return s?JSON.parse(s):[]}catch{return[]}}cacheFeaturedImages(s){try{localStorage.setItem("featured_images",JSON.stringify(s))}catch(r){console.error("[缓存管理] 缓存特色图片列表失败:",r)}}adaptToNetworkCondition(){if("connection"in navigator){const s=navigator.connection;if(s){const{effectiveType:r,downlink:a}=s;if(r==="slow-2g"||r==="2g"||a<.5)return console.log("[缓存管理] 检测到慢速网络，减少预加载"),"conservative";if(r==="4g"&&a>2)return console.log("[缓存管理] 检测到快速网络，增加预加载"),"aggressive"}}return"normal"}setupNetworkListener(){if("connection"in navigator){const s=navigator.connection;s&&s.addEventListener("change",()=>{const r=this.adaptToNetworkCondition();console.log("[缓存管理] 网络状态变化，调整策略为:",r)})}window.addEventListener("online",()=>{console.log("[缓存管理] 网络已连接")}),window.addEventListener("offline",()=>{console.log("[缓存管理] 网络已断开")})}};Fs(st,"instance");let dr=st;const ur=dr.getInstance();function Ao({posts:e,onSearchResults:s,placeholder:r="搜索文章...",className:a=""}){const[n,o]=d.useState(""),[i,l]=d.useState(!1),[c,g]=d.useState([]),[u,x]=d.useState(-1),p=d.useRef(null),m=d.useRef(null),w=R=>{if(!R.trim())return[];const D=R.toLowerCase(),_=[];return e.forEach(G=>{if(G.title.toLowerCase().includes(D)){_.push({...G,matchType:"title",matchText:G.title});return}if(G.excerpt.toLowerCase().includes(D)){_.push({...G,matchType:"content",matchText:G.excerpt});return}const Z=G.tags.find(B=>B.toLowerCase().includes(D));if(Z){_.push({...G,matchType:"tag",matchText:Z});return}G.category&&G.category.toLowerCase().includes(D)&&_.push({...G,matchType:"category",matchText:G.category})}),_.slice(0,8)};d.useEffect(()=>{const R=w(n);g(R),x(-1),n.trim()?s(R):s(e)},[n,e,s]);const T=R=>{var D;if(i)switch(R.key){case"ArrowDown":R.preventDefault(),x(_=>_<c.length-1?_+1:_);break;case"ArrowUp":R.preventDefault(),x(_=>_>0?_-1:-1);break;case"Enter":R.preventDefault(),u>=0&&c[u]&&N(c[u]);break;case"Escape":l(!1),(D=m.current)==null||D.blur();break}},N=R=>{o(R.title),l(!1),s([R])},A=()=>{o(""),l(!1),s(e)};d.useEffect(()=>{const R=D=>{p.current&&!p.current.contains(D.target)&&l(!1)};return document.addEventListener("mousedown",R),()=>document.removeEventListener("mousedown",R)},[]);const E=R=>{switch(R){case"title":return t.jsx(De,{size:14,className:"text-blue-500"});case"content":return t.jsx(De,{size:14,className:"text-green-500"});case"tag":return t.jsx(ft,{size:14,className:"text-purple-500"});case"category":return t.jsx(Ae,{size:14,className:"text-orange-500"});default:return t.jsx(De,{size:14,className:"text-gray-500"})}},z=(R,D)=>{if(!D.trim())return R;const _=new RegExp(`(${D})`,"gi");return R.split(_).map((Z,B)=>_.test(Z)?t.jsx("mark",{className:"bg-yellow-200 dark:bg-yellow-800 px-1 rounded",children:Z},B):Z)};return t.jsxs("div",{ref:p,className:`relative ${a}`,children:[t.jsxs("div",{className:"relative",children:[t.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:t.jsx(De,{size:20,className:"text-gray-400"})}),t.jsx("input",{ref:m,type:"text",value:n,onChange:R=>o(R.target.value),onFocus:()=>l(!0),onKeyDown:T,placeholder:r,className:`w-full pl-10 pr-10 py-3 border border-gray-300 dark:border-gray-600 rounded-lg 
                   bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                   focus:ring-2 focus:ring-blue-500 focus:border-transparent
                   placeholder-gray-500 dark:placeholder-gray-400
                   transition-all duration-200`}),n&&t.jsx("button",{onClick:A,className:"absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:t.jsx(Ze,{size:20})})]}),i&&n.trim()&&t.jsx("div",{className:`absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 
                      border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto`,children:c.length>0?t.jsxs("div",{className:"py-2",children:[t.jsxs("div",{className:"px-4 py-2 text-xs text-gray-500 dark:text-gray-400 border-b border-gray-100 dark:border-gray-700",children:["找到 ",c.length," 个结果"]}),c.map((R,D)=>t.jsx("button",{onClick:()=>N(R),className:`w-full px-4 py-3 text-left hover:bg-gray-50 dark:hover:bg-gray-700 
                           transition-colors border-b border-gray-50 dark:border-gray-700 last:border-b-0
                           ${D===u?"bg-blue-50 dark:bg-blue-900/20":""}`,children:t.jsxs("div",{className:"flex items-start space-x-3",children:[t.jsx("div",{className:"flex-shrink-0 mt-1",children:E(R.matchType)}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("div",{className:"text-sm font-medium text-gray-900 dark:text-white truncate",children:z(R.title,n)}),t.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[R.matchType==="title"&&"标题匹配",R.matchType==="content"&&"内容匹配",R.matchType==="tag"&&`标签: ${R.matchText}`,R.matchType==="category"&&`分类: ${R.matchText}`]}),t.jsxs("div",{className:"flex items-center space-x-3 text-xs text-gray-400 mt-1",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:10}),t.jsx("span",{children:R.date})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:10}),t.jsxs("span",{children:[R.readTime,"分钟"]})]})]})]})]})},R.id))]}):t.jsxs("div",{className:"px-4 py-8 text-center text-gray-500 dark:text-gray-400",children:[t.jsx(De,{size:32,className:"mx-auto mb-2 opacity-50"}),t.jsx("p",{children:"没有找到匹配的文章"}),t.jsx("p",{className:"text-xs mt-1",children:"试试其他关键词"})]})})]})}function Ro({currentPage:e,totalPages:s,totalItems:r,itemsPerPage:a,onPageChange:n,showInfo:o=!0,className:i=""}){if(s<=1)return null;const c=(()=>{const x=[];if(s<=7)for(let m=1;m<=s;m++)x.push(m);else if(e<=4){for(let m=1;m<=5;m++)x.push(m);x.push("ellipsis"),x.push(s)}else if(e>=s-3){x.push(1),x.push("ellipsis");for(let m=s-4;m<=s;m++)x.push(m)}else{x.push(1),x.push("ellipsis");for(let m=e-1;m<=e+1;m++)x.push(m);x.push("ellipsis"),x.push(s)}return x})(),g=(e-1)*a+1,u=Math.min(e*a,r);return t.jsxs("div",{className:`flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0 ${i}`,children:[o&&t.jsxs("div",{className:"text-sm text-gray-700 dark:text-gray-300",children:["显示第 ",t.jsx("span",{className:"font-medium",children:g})," 到"," ",t.jsx("span",{className:"font-medium",children:u})," 项，共"," ",t.jsx("span",{className:"font-medium",children:r})," 项"]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsxs("button",{onClick:()=>n(e-1),disabled:e===1,className:`flex items-center px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 
                   bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md 
                   hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300
                   disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-gray-800
                   transition-colors duration-200`,title:"上一页",children:[t.jsx(El,{size:16,className:"mr-1"}),"上一页"]}),t.jsx("div",{className:"flex items-center space-x-1",children:c.map((x,p)=>x==="ellipsis"?t.jsx("span",{className:"flex items-center justify-center w-10 h-10 text-gray-500 dark:text-gray-400",children:t.jsx(ql,{size:16})},`ellipsis-${p}`):t.jsx("button",{onClick:()=>n(x),className:`flex items-center justify-center w-10 h-10 text-sm font-medium rounded-md transition-colors duration-200 ${e===x?"bg-blue-600 text-white border border-blue-600 hover:bg-blue-700":"text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700"}`,title:`第 ${x} 页`,children:x},x))}),t.jsxs("button",{onClick:()=>n(e+1),disabled:e===s,className:`flex items-center px-3 py-2 text-sm font-medium text-gray-500 dark:text-gray-400 
                   bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md 
                   hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-700 dark:hover:text-gray-300
                   disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-white dark:disabled:hover:bg-gray-800
                   transition-colors duration-200`,title:"下一页",children:["下一页",t.jsx(en,{size:16,className:"ml-1"})]})]}),s>10&&t.jsxs("div",{className:"flex items-center space-x-2 text-sm",children:[t.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"跳转到"}),t.jsx("input",{type:"number",min:1,max:s,className:`w-16 px-2 py-1 text-center border border-gray-300 dark:border-gray-600 rounded 
                     bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                     focus:ring-2 focus:ring-blue-500 focus:border-transparent`,onKeyPress:x=>{if(x.key==="Enter"){const p=parseInt(x.target.value);p>=1&&p<=s&&n(p)}},placeholder:e.toString()}),t.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"页"})]})]})}function Mo({pageSize:e,onPageSizeChange:s,options:r=[5,10,20,50],className:a=""}){return t.jsxs("div",{className:`flex items-center space-x-2 text-sm ${a}`,children:[t.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"每页显示"}),t.jsx("select",{value:e,onChange:n=>s(parseInt(n.target.value)),className:`px-2 py-1 border border-gray-300 dark:border-gray-600 rounded 
                 bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                 focus:ring-2 focus:ring-blue-500 focus:border-transparent`,children:r.map(n=>t.jsx("option",{value:n,children:n},n))}),t.jsx("span",{className:"text-gray-700 dark:text-gray-300",children:"项"})]})}function ht({slug:e,title:s,onDeleted:r,showEdit:a=!0,showDelete:n=!0}){const{isAdmin:o}=qt(),[i,l]=d.useState(!1),[c,g]=d.useState(!1),[u,x]=d.useState(!1),p=Ft(),m=()=>{p(`/write/${e}`),l(!1)},w=()=>{x(!0),l(!1)},T=async()=>{if(!c)try{g(!0),await je.deletePost(e),r==null||r(),window.location.pathname.includes(`/post/${e}`)&&p("/")}catch(A){console.error("删除文章失败:",A),alert("删除文章失败，请稍后重试")}finally{g(!1),x(!1)}},N=()=>{x(!1)};return!o||!a&&!n?null:t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"relative",children:[t.jsx("button",{onClick:()=>l(!i),className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",title:"文章操作",children:t.jsx(Gl,{size:18})}),i&&t.jsxs("div",{className:"absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-[9999]",children:[a&&t.jsxs("button",{onClick:m,className:"w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 first:rounded-t-lg",children:[t.jsx(or,{size:16}),t.jsx("span",{children:"编辑文章"})]}),n&&t.jsxs("button",{onClick:w,className:"w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2 last:rounded-b-lg",children:[t.jsx(cr,{size:16}),t.jsx("span",{children:"删除文章"})]})]}),i&&t.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>l(!1)})]}),u&&t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000]",children:t.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4 shadow-2xl",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"确认删除文章"}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:["你确定要删除文章 ",t.jsxs("strong",{children:['"',s,'"']})," 吗？此操作无法撤销。"]}),t.jsxs("div",{className:"flex space-x-3 justify-end",children:[t.jsx("button",{onClick:N,disabled:c,className:"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50",children:"取消"}),t.jsx("button",{onClick:T,disabled:c,className:"px-4 py-2 text-sm text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors disabled:opacity-50 flex items-center space-x-2",children:c?t.jsxs(t.Fragment,{children:[t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),t.jsx("span",{children:"删除中..."})]}):t.jsxs(t.Fragment,{children:[t.jsx(cr,{size:16}),t.jsx("span",{children:"确认删除"})]})})]})]})})]})}function To({className:e=""}){return t.jsx("div",{className:`animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 ${e}`,children:t.jsx("div",{className:"flex items-center justify-center h-full",children:t.jsx(Yt,{size:32,className:"text-gray-400 dark:text-gray-500"})})})}function Lo({onRetry:e,showRetry:s=!0,className:r=""}){return t.jsxs("div",{className:`flex flex-col items-center justify-center h-full bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 ${r}`,children:[t.jsx(_t,{size:32,className:"mb-2"}),t.jsx("p",{className:"text-sm text-center mb-2",children:"图片加载失败"}),s&&e&&t.jsxs("button",{onClick:e,className:"flex items-center space-x-1 px-3 py-1 text-xs bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded transition-colors",children:[t.jsx(Yl,{size:12}),t.jsx("span",{children:"重试"})]})]})}function Et({src:e,alt:s,className:r="",containerClassName:a="",maxHeight:n=400,minHeight:o=200,priority:i="auto",lazy:l=!0,preload:c=!1,quality:g=85,width:u,height:x,aspectRatio:p,showSkeleton:m=!0,enablePreview:w=!1,fallbackSrc:T,retryCount:N=2,onError:A,onLoad:E,onPreview:z}){const[R,D]=d.useState("cover"),[_,G]=d.useState(!1),[Z,B]=d.useState(!1),[se,Y]=d.useState(!1),[te,ue]=d.useState(!l),[le,ce]=d.useState(""),[k,f]=d.useState(0),[j,b]=d.useState(null),S=d.useRef(null),I=d.useRef(null),K=d.useCallback(de=>{if(!de.includes("pub-a1a2.r2.dev")||de.includes("/api/image-proxy/"))return de;const ge=de.split("pub-a1a2.r2.dev/");if(ge.length<2)return de;const we=ge[1],be=new URLSearchParams;return u&&be.set("w",u.toString()),x&&be.set("h",x.toString()),g!==85&&be.set("q",g.toString()),be.set("f","auto"),`${window.location.origin.includes("localhost")?"http://localhost:8787":"https://blog.fddfffff.site"}/api/image-proxy/${we}?${be.toString()}`},[u,x,g]);d.useEffect(()=>{if(!l||te)return;const de=new IntersectionObserver(ge=>{ge.forEach(we=>{we.isIntersecting&&(ue(!0),de.disconnect())})},{rootMargin:"50px",threshold:.1});return I.current&&de.observe(I.current),()=>de.disconnect()},[l,te]),d.useEffect(()=>{if(te&&e){B(!0),Y(!1),G(!1);const de=K(e);if(ce(de),c){const ge=new Image;ge.src=de,ge.onload=()=>{b({width:ge.naturalWidth,height:ge.naturalHeight})}}}},[te,e,K,c]);const J=d.useCallback(()=>{k<N&&(f(de=>de+1),Y(!1),B(!0),ce(k===0&&T?T:le!==e?e:e+"?retry="+Date.now()))},[k,N,T,le,e]),C=de=>{const ge=de.currentTarget;if(G(!0),B(!1),Y(!1),f(0),b({width:ge.naturalWidth,height:ge.naturalHeight}),i==="auto"){const we=ge.naturalWidth/ge.naturalHeight,be=p||(u&&x?u/x:16/9);Math.abs(we-be)>.5||we>2.5||we<.4?D("contain"):D("cover")}else D(i==="contain"?"contain":"cover");E==null||E()},ne=()=>{console.warn("图片加载失败:",le,"重试次数:",k),B(!1),G(!1),k<N?setTimeout(()=>{J()},1e3*(k+1)):(Y(!0),A==null||A())},oe=()=>{w&&_&&le&&(z==null||z(le))},ke={minHeight:`${o}px`,maxHeight:`${n}px`,height:R==="contain"?"auto":`${n}px`,aspectRatio:p?p.toString():void 0};return t.jsxs("div",{ref:I,className:`relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-lg ${R==="contain"?"flex items-center justify-center":""} ${a}`,style:ke,children:[!te&&m&&t.jsx(To,{className:"absolute inset-0"}),te&&Z&&!_&&t.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800",children:t.jsxs("div",{className:"flex flex-col items-center space-y-2",children:[t.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:"加载中..."})]})}),se&&t.jsx(Lo,{onRetry:k<N?J:void 0,showRetry:k<N,className:"absolute inset-0"}),te&&le&&!se&&t.jsx("img",{ref:S,src:le,alt:s,className:`transition-all duration-700 ${R==="contain"?"max-w-full max-h-full object-contain":"w-full h-full object-cover"} ${r} ${_?"opacity-100 scale-100":"opacity-0 scale-105"} ${w?"cursor-pointer hover:scale-105":""}`,loading:l?"lazy":"eager",onLoad:C,onError:ne,onClick:oe,style:{maxHeight:`${n}px`,minHeight:R==="contain"?`${o}px`:"auto"}}),w&&_&&t.jsx("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity",children:t.jsx("div",{className:"bg-black/50 text-white p-1 rounded",children:t.jsx(Xe,{size:16})})}),!1,R==="contain"&&_&&t.jsx("div",{className:"absolute inset-0 border border-gray-200/30 dark:border-gray-700/30 rounded-lg pointer-events-none"})]})}function Io({src:e,alt:s,isOpen:r,onClose:a}){const[n,o]=d.useState(1),[i,l]=d.useState(0),[c,g]=d.useState({x:0,y:0}),[u,x]=d.useState(!1),[p,m]=d.useState({x:0,y:0}),[w,T]=d.useState(!0),N=d.useRef(null),A=d.useRef(null),E=()=>{o(1),l(0),g({x:0,y:0})},z=()=>{E(),a()};d.useEffect(()=>{if(!r)return;const B=se=>{switch(se.key){case"Escape":z();break;case"+":case"=":o(Y=>Math.min(Y*1.2,5));break;case"-":o(Y=>Math.max(Y/1.2,.1));break;case"r":case"R":l(Y=>Y+90);break;case"0":E();break}};return document.addEventListener("keydown",B),()=>document.removeEventListener("keydown",B)},[r]);const R=B=>{n<=1||(x(!0),m({x:B.clientX-c.x,y:B.clientY-c.y}))},D=B=>{u&&g({x:B.clientX-p.x,y:B.clientY-p.y})},_=()=>{x(!1)},G=B=>{B.preventDefault();const se=B.deltaY>0?.9:1.1;o(Y=>Math.max(.1,Math.min(5,Y*se)))},Z=async()=>{try{const se=await(await fetch(e)).blob(),Y=window.URL.createObjectURL(se),te=document.createElement("a");te.href=Y,te.download=s||"image",document.body.appendChild(te),te.click(),document.body.removeChild(te),window.URL.revokeObjectURL(Y)}catch(B){console.error("下载图片失败:",B)}};return r?t.jsxs("div",{className:"fixed inset-0 z-50 bg-black/90 backdrop-blur-sm",children:[t.jsx("div",{className:"absolute top-4 left-1/2 transform -translate-x-1/2 z-10",children:t.jsxs("div",{className:"flex items-center space-x-2 bg-black/50 backdrop-blur-sm rounded-lg px-4 py-2",children:[t.jsx("button",{onClick:()=>o(B=>Math.max(B/1.2,.1)),className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"缩小 (-)",children:t.jsx(fo,{size:16})}),t.jsxs("span",{className:"text-white text-sm min-w-[60px] text-center",children:[Math.round(n*100),"%"]}),t.jsx("button",{onClick:()=>o(B=>Math.min(B*1.2,5)),className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"放大 (+)",children:t.jsx(xo,{size:16})}),t.jsx("div",{className:"w-px h-6 bg-white/30"}),t.jsx("button",{onClick:()=>l(B=>B+90),className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"旋转 (R)",children:t.jsx(eo,{size:16})}),t.jsx("button",{onClick:E,className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"重置 (0)",children:t.jsx(rn,{size:16})}),t.jsx("button",{onClick:Z,className:"p-2 text-white hover:bg-white/20 rounded transition-colors",title:"下载",children:t.jsx(Rl,{size:16})})]})}),t.jsx("button",{onClick:z,className:"absolute top-4 right-4 z-10 p-2 text-white hover:bg-white/20 rounded-full transition-colors",title:"关闭 (ESC)",children:t.jsx(Ze,{size:24})}),t.jsxs("div",{ref:A,className:"absolute inset-0 flex items-center justify-center cursor-move",onMouseDown:R,onMouseMove:D,onMouseUp:_,onMouseLeave:_,onWheel:G,onClick:B=>{B.target===B.currentTarget&&z()},children:[w&&t.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:t.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white"})}),t.jsx("img",{ref:N,src:e,alt:s,className:`max-w-none transition-all duration-300 ${u?"cursor-grabbing":n>1?"cursor-grab":"cursor-zoom-in"}`,style:{transform:`translate(${c.x}px, ${c.y}px) scale(${n}) rotate(${i}deg)`,maxHeight:n===1?"90vh":"none",maxWidth:n===1?"90vw":"none"},onLoad:()=>T(!1),onError:()=>T(!1),draggable:!1})]}),t.jsx("div",{className:"absolute bottom-4 left-1/2 transform -translate-x-1/2",children:t.jsx("div",{className:"bg-black/50 backdrop-blur-sm text-white text-xs px-4 py-2 rounded-lg",children:t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsx("span",{children:"滚轮: 缩放"}),t.jsx("span",{children:"拖拽: 移动"}),t.jsx("span",{children:"ESC: 关闭"}),t.jsx("span",{children:"R: 旋转"}),t.jsx("span",{children:"0: 重置"})]})})})]}):null}function wr(){const[e,s]=d.useState(null),r=(o,i="")=>{s({src:o,alt:i})},a=()=>{s(null)},n=e?t.jsx(Io,{src:e.src,alt:e.alt,isOpen:!!e,onClose:a}):null;return{openPreview:r,closePreview:a,PreviewComponent:n,isOpen:!!e}}function ga({post:e,variant:s="default",showActions:r=!1,showStats:a=!1,onDeleted:n,className:o=""}){const{openPreview:i,PreviewComponent:l}=wr();return s==="featured"?t.jsxs("div",{className:`card p-8 hover:shadow-lg transition-all duration-300 relative group ${o}`,children:[t.jsx("div",{className:"absolute top-4 left-4 z-10",children:t.jsxs("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200",children:[t.jsx(an,{size:12,className:"mr-1"}),"特色文章"]})}),r&&t.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:n})}),t.jsxs(ae,{to:`/post/${e.slug||e.id}`,className:"block",children:[e.imageUrl&&t.jsx("div",{className:"mb-6 rounded-xl overflow-hidden shadow-lg group",children:t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-105 transition-transform duration-500",containerClassName:"rounded-xl",maxHeight:400,minHeight:200,priority:"auto",lazy:!1,preload:!0,quality:90,width:800,height:400,aspectRatio:16/9,showSkeleton:!0,enablePreview:!0,retryCount:3,onPreview:c=>i(c,e.title),onError:()=>{console.warn("特色文章封面加载失败:",e.imageUrl)}})}),t.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 pr-12 leading-tight",children:e.title}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed mb-4 text-lg",children:e.excerpt}),t.jsxs("div",{className:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Qe,{size:16}),t.jsx("span",{children:e.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:16}),t.jsx("span",{children:e.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:16}),t.jsxs("span",{children:[e.readTime," 分钟阅读"]})]}),a&&t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Xe,{size:16}),t.jsx("span",{children:e.views||0})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(is,{size:16}),t.jsx("span",{children:e.likes||0})]})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:e.tags.map(c=>t.jsx("span",{className:"px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full",children:c},c))})]})]}):s==="compact"?t.jsxs("div",{className:`card p-4 hover:shadow-md transition-all duration-300 group relative ${o}`,children:[r&&t.jsx("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:n})}),t.jsx(ae,{to:`/post/${e.slug||e.id}`,className:"block",children:t.jsxs("div",{className:"flex space-x-4",children:[e.imageUrl&&t.jsx("div",{className:"flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden",children:t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-110 transition-transform duration-300",containerClassName:"rounded-lg",maxHeight:80,minHeight:80,priority:"cover",lazy:!0,quality:75,width:80,height:80,aspectRatio:1,showSkeleton:!0,enablePreview:!0,onPreview:c=>i(c,e.title)})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-2 line-clamp-2",children:e.title}),t.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2",children:e.excerpt}),t.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:10}),t.jsx("span",{children:e.date})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:10}),t.jsxs("span",{children:[e.readTime,"分钟"]})]})]})]})]})})]}):s==="list"?t.jsxs("div",{className:`border-b border-gray-200 dark:border-gray-700 py-6 group relative ${o}`,children:[r&&t.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:n})}),t.jsx(ae,{to:`/post/${e.slug||e.id}`,className:"block",children:t.jsxs("div",{className:"flex space-x-6",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-2",children:e.title}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-3 line-clamp-2",children:e.excerpt}),t.jsx("div",{className:"flex flex-wrap gap-2 mb-3",children:e.tags.slice(0,3).map(c=>t.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded",children:c},c))}),t.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Qe,{size:14}),t.jsx("span",{children:e.author})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:e.date})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:14}),t.jsxs("span",{children:[e.readTime," 分钟"]})]}),a&&t.jsxs(t.Fragment,{children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Xe,{size:14}),t.jsx("span",{children:e.views||0})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(es,{size:14}),t.jsx("span",{children:e.comments||0})]})]})]})]}),e.imageUrl&&t.jsx("div",{className:"flex-shrink-0 w-32 h-24 rounded-lg overflow-hidden",children:t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-110 transition-transform duration-300",containerClassName:"rounded-lg",maxHeight:96,minHeight:96,priority:"cover",lazy:!0,quality:75,width:128,height:96,aspectRatio:4/3,showSkeleton:!0,enablePreview:!0,onPreview:c=>i(c,e.title)})})]})})]}):t.jsxs("div",{className:`card p-0 hover:shadow-lg transition-all duration-300 group h-full relative overflow-hidden rounded-lg ${o}`,children:[r&&t.jsx("div",{className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-20 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm rounded-lg p-1",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:n})}),t.jsxs(ae,{to:`/post/${e.slug||e.id}`,className:"block h-full",children:[e.imageUrl&&t.jsx("div",{className:"w-full h-48 overflow-hidden bg-gray-100 dark:bg-gray-800",children:t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-110 transition-transform duration-500",containerClassName:"rounded-t-lg w-full h-full",maxHeight:192,minHeight:192,priority:"contain",lazy:!0,quality:80,width:400,height:192,aspectRatio:25/12,showSkeleton:!0,enablePreview:!0,onPreview:c=>i(c,e.title),onError:()=>{console.warn("文章封面加载失败:",e.imageUrl)}})}),t.jsxs("div",{className:"p-6 flex flex-col h-full",children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 line-clamp-2",children:e.title}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3 flex-grow",children:e.excerpt}),t.jsxs("div",{className:"mt-auto",children:[t.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[e.tags.slice(0,3).map(c=>t.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full",children:c},c)),e.tags.length>3&&t.jsxs("span",{className:"px-2 py-1 text-xs text-gray-400 dark:text-gray-500",children:["+",e.tags.length-3]})]}),t.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:12}),t.jsx("span",{children:e.date})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:12}),t.jsxs("span",{children:[e.readTime," 分钟"]})]})]}),a&&t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(Xe,{size:12}),t.jsx("span",{children:e.views||0})]}),t.jsxs("span",{className:"flex items-center space-x-1",children:[t.jsx(is,{size:12}),t.jsx("span",{children:e.likes||0})]})]})]})]})]})]}),l]})}function Oo({post:e,showActions:s=!1,onDeleted:r,className:a=""}){const{openPreview:n,PreviewComponent:o}=wr();return t.jsxs(t.Fragment,{children:[t.jsxs("div",{className:`relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 shadow-xl hover:shadow-2xl transition-all duration-500 group ${a}`,children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5 dark:from-blue-400/10 dark:to-purple-400/10"}),t.jsx("div",{className:"absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-200/20 to-transparent dark:from-yellow-400/10 rounded-full -translate-y-32 translate-x-32"}),t.jsx("div",{className:"absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-pink-200/20 to-transparent dark:from-pink-400/10 rounded-full translate-y-24 -translate-x-24"}),t.jsx("div",{className:"absolute top-6 left-6 z-20",children:t.jsxs("div",{className:"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white rounded-full shadow-lg",children:[t.jsx(an,{size:16,className:"animate-pulse"}),t.jsx("span",{className:"text-sm font-semibold",children:"特色文章"}),t.jsx(Ct,{size:14,className:"animate-bounce"})]})}),s&&t.jsx("div",{className:"absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity z-20",children:t.jsx(ht,{slug:e.slug,title:e.title,onDeleted:r})}),t.jsx(ae,{to:`/post/${e.slug}`,className:"block relative z-10",children:t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 p-8",children:[t.jsxs("div",{className:"flex flex-col justify-center space-y-6 order-2 lg:order-1",children:[t.jsxs("div",{className:"space-y-4",children:[t.jsx("h2",{className:"text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white leading-tight group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300",children:e.title}),t.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-300 leading-relaxed line-clamp-3",children:e.excerpt})]}),t.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.tags.slice(0,4).map(i=>t.jsxs("span",{className:"px-3 py-1 text-sm bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm text-gray-700 dark:text-gray-300 rounded-full border border-gray-200/50 dark:border-gray-700/50 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors",children:["#",i]},i)),e.tags.length>4&&t.jsxs("span",{className:"px-3 py-1 text-sm text-gray-500 dark:text-gray-400",children:["+",e.tags.length-4," 更多"]})]}),t.jsxs("div",{className:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center",children:t.jsx(Qe,{size:14,className:"text-white"})}),t.jsx("span",{className:"font-medium",children:e.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:16}),t.jsx("span",{children:e.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:16}),t.jsxs("span",{children:[e.readTime," 分钟阅读"]})]})]}),t.jsxs("div",{className:"flex items-center space-x-6 text-sm",children:[t.jsxs("div",{className:"flex items-center space-x-1 text-gray-500 dark:text-gray-400",children:[t.jsx(Xe,{size:16}),t.jsxs("span",{children:[e.views||0," 次浏览"]})]}),t.jsxs("div",{className:"flex items-center space-x-1 text-red-500",children:[t.jsx(is,{size:16}),t.jsxs("span",{children:[e.likes||0," 个赞"]})]})]}),t.jsx("div",{className:"pt-4",children:t.jsxs("div",{className:"inline-flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl",children:[t.jsx("span",{className:"font-semibold",children:"阅读全文"}),t.jsx(wl,{size:16,className:"group-hover:translate-x-1 transition-transform"})]})})]}),t.jsx("div",{className:"order-1 lg:order-2",children:e.imageUrl?t.jsxs("div",{className:"relative h-80 lg:h-full rounded-xl overflow-hidden shadow-2xl group-hover:shadow-3xl transition-shadow duration-500",children:[t.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent z-10"}),t.jsx(Et,{src:e.imageUrl,alt:e.title,className:"group-hover:scale-110 transition-transform duration-700",containerClassName:"rounded-xl",maxHeight:400,minHeight:320,priority:"cover",lazy:!1,preload:!0,quality:95,width:600,height:400,aspectRatio:3/2,showSkeleton:!0,enablePreview:!0,retryCount:3,onPreview:i=>n(i,e.title),onError:()=>{console.warn("特色文章封面加载失败:",e.imageUrl)}}),t.jsx("div",{className:"absolute inset-0 rounded-xl border-2 border-white/20 dark:border-gray-700/30 pointer-events-none"}),t.jsx("div",{className:"absolute inset-0 rounded-xl bg-gradient-to-tr from-blue-500/0 via-purple-500/0 to-pink-500/0 group-hover:from-blue-500/10 group-hover:via-purple-500/5 group-hover:to-pink-500/10 transition-all duration-700 pointer-events-none"})]}):t.jsx("div",{className:"h-80 lg:h-full rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 flex items-center justify-center",children:t.jsxs("div",{className:"text-center text-gray-400 dark:text-gray-600",children:[t.jsx(Ct,{size:48,className:"mx-auto mb-4 opacity-50"}),t.jsx("p",{className:"text-lg font-medium",children:"精彩内容"}),t.jsx("p",{className:"text-sm",children:"等待您的发现"})]})})})]})}),t.jsx("div",{className:"absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"})]}),o]})}const dn=d.createContext(void 0);function Po(){const e=d.useContext(dn);if(!e)throw new Error("useToast must be used within a ToastProvider");return e}function _o({type:e}){const s={size:20};switch(e){case"success":return t.jsx(lr,{...s,className:"text-green-500"});case"error":return t.jsx(_t,{...s,className:"text-red-500"});case"warning":return t.jsx(_t,{...s,className:"text-yellow-500"});case"info":return t.jsx(oa,{...s,className:"text-blue-500"});case"loading":return t.jsx(mt,{...s,className:"text-blue-500 animate-spin"});default:return t.jsx(oa,{...s,className:"text-gray-500"})}}function zo({toast:e,onRemove:s}){const[r,a]=d.useState(!1),[n,o]=d.useState(!1);d.useEffect(()=>{const g=setTimeout(()=>a(!0),10);return()=>clearTimeout(g)},[]),d.useEffect(()=>{if(e.type!=="loading"&&e.duration!==0){const g=e.duration||5e3,u=setTimeout(()=>{i()},g);return()=>clearTimeout(u)}},[e.duration,e.type]);const i=()=>{o(!0),setTimeout(()=>{s(e.id)},300)},l={success:"bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800",error:"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800",warning:"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800",info:"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800",loading:"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800"},c={success:"text-green-800 dark:text-green-200",error:"text-red-800 dark:text-red-200",warning:"text-yellow-800 dark:text-yellow-200",info:"text-blue-800 dark:text-blue-200",loading:"text-blue-800 dark:text-blue-200"};return t.jsx("div",{className:`
        relative max-w-sm w-full border rounded-lg shadow-lg p-4 mb-3
        transition-all duration-300 ease-in-out transform
        ${l[e.type]}
        ${r&&!n?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
        ${n?"scale-95":"scale-100"}
      `,children:t.jsxs("div",{className:"flex items-start",children:[t.jsx("div",{className:"flex-shrink-0 mr-3",children:t.jsx(_o,{type:e.type})}),t.jsxs("div",{className:"flex-1 min-w-0",children:[t.jsx("h4",{className:`text-sm font-medium ${c[e.type]}`,children:e.title}),e.message&&t.jsx("p",{className:`mt-1 text-sm ${c[e.type]} opacity-90`,children:e.message}),e.action&&t.jsx("div",{className:"mt-3",children:t.jsx("button",{onClick:e.action.onClick,className:`text-sm font-medium underline hover:no-underline ${c[e.type]}`,children:e.action.label})})]}),e.type!=="loading"&&t.jsx("button",{onClick:i,className:`flex-shrink-0 ml-2 p-1 rounded-md hover:bg-black hover:bg-opacity-10 transition-colors ${c[e.type]}`,children:t.jsx(Ze,{size:16})})]})})}function $o({toasts:e,removeToast:s}){return t.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:e.map(r=>t.jsx(zo,{toast:r,onRemove:s},r.id))})}function Do({children:e}){const[s,r]=d.useState([]),a=i=>{const l=Math.random().toString(36).substr(2,9),c={...i,id:l,duration:i.duration??5e3};return r(g=>[...g,c]),l},n=i=>{r(l=>l.filter(c=>c.id!==i))},o=(i,l)=>{r(c=>c.map(g=>g.id===i?{...g,...l}:g))};return t.jsxs(dn.Provider,{value:{toasts:s,addToast:a,removeToast:n,updateToast:o},children:[e,t.jsx($o,{toasts:s,removeToast:n})]})}function Nr(){const{addToast:e,updateToast:s,removeToast:r}=Po();return{success:(a,n,o)=>e({type:"success",title:a,message:n,...o}),error:(a,n,o)=>e({type:"error",title:a,message:n,...o}),warning:(a,n,o)=>e({type:"warning",title:a,message:n,...o}),info:(a,n,o)=>e({type:"info",title:a,message:n,...o}),loading:(a,n)=>e({type:"loading",title:a,message:n,duration:0}),update:s,remove:r}}function Uo(){const[e,s]=d.useState([]),[r,a]=d.useState([]),[n,o]=d.useState(!0),[i,l]=d.useState(null),[c,g]=d.useState({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),[u,x]=d.useState("grid"),[p,m]=d.useState(1),[w,T]=d.useState(12),N=Nr(),{PreviewComponent:A}=wr(),E=async()=>{try{o(!0),l(null),console.log("开始加载文章列表...");const f=await je.getPosts(1,50);console.log("获取文章响应:",f);const j=f.posts||[];if(s(j),a(j),j.length===0)console.warn("API返回空文章列表");else{const b=j.map(S=>S.imageUrl).filter(Boolean);b.length>0&&(ur.cacheFeaturedImages(b),ur.smartPreload().catch(S=>{console.warn("智能预加载失败:",S)})),console.log(`文章加载完成，共加载 ${j.length} 篇文章`)}}catch(f){console.error("加载文章失败详情:",f);let j="加载文章失败";f instanceof ls?j=`API错误 (${f.status}): ${f.message}`:f instanceof Error?j=`网络错误: ${f.message}`:j="未知错误，请检查网络连接",l(j),N.error("加载失败",j),s([]),a([])}finally{o(!1)}};d.useEffect(()=>{E()},[]);const z=()=>{E()},R=d.useCallback(f=>{g(f),m(1)},[]),D=d.useCallback(f=>{a(f),m(1)},[]),_=d.useCallback(f=>{m(f),window.scrollTo({top:0,behavior:"smooth"})},[]),G=d.useCallback(f=>{T(f),m(1)},[]),Z=d.useMemo(()=>{const f=e.filter(j=>j.category).map(j=>j.category);return Array.from(new Set(f))},[e]),B=d.useMemo(()=>{const f=e.flatMap(j=>j.tags);return Array.from(new Set(f))},[e]),se=d.useMemo(()=>{let f=[...r];return c.category&&(f=f.filter(j=>j.category===c.category)),c.tag&&(f=f.filter(j=>j.tags.includes(c.tag))),f.sort((j,b)=>{if(c.sortBy!=="date"){let C=0;switch(c.sortBy){case"readTime":C=j.readTime-b.readTime;break;case"featured":C=(j.featured?1:0)-(b.featured?1:0);break}return c.sortOrder==="desc"?-C:C}const S=C=>{const ne=[/^(.+?)-第(\d+)课/,/^(.+?)-课程(\d+)/,/^(.+?)\s+第(\d+)课/,/^(.+?)-(\d+)/,/^(.+?)\s+(\d+)/];for(const oe of ne){const ke=C.match(oe);if(ke)return{series:ke[1].trim(),number:parseInt(ke[2]),hasNumber:!0}}return{series:C,number:0,hasNumber:!1}},I=S(j.title),K=S(b.title);if(I.series===K.series&&I.hasNumber&&K.hasNumber)return c.sortOrder==="desc"?K.number-I.number:I.number-K.number;if(I.series===K.series){if(I.hasNumber&&!K.hasNumber)return c.sortOrder==="desc"?-1:1;if(!I.hasNumber&&K.hasNumber)return c.sortOrder==="desc"?1:-1}const J=new Date(j.date).getTime()-new Date(b.date).getTime();return c.sortOrder==="desc"?-J:J}),f},[r,c]),Y=Math.ceil(se.length/w),te=(p-1)*w,ue=te+w,le=se.slice(te,ue),ce=p===1?se.find(f=>f.featured):null,k=le.filter(f=>!f.featured||p>1);return n?t.jsxs("div",{className:"space-y-8",children:[t.jsxs("div",{className:"text-center py-12",children:[t.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto mb-4 animate-pulse"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"})]}),t.jsxs("div",{className:"card p-8 animate-pulse",children:[t.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]})]}):t.jsxs("div",{className:"space-y-12",children:[t.jsxs("div",{className:"text-center py-12",children:[t.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4",children:"欢迎来到杨博文的博客"}),t.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto mb-8",children:"分享技术见解、开发经验和生活感悟的个人空间"}),t.jsx("div",{className:"max-w-md mx-auto",children:t.jsx(Ao,{posts:e,onSearchResults:D,placeholder:"搜索文章、标签或分类...",className:"w-full"})})]}),i&&t.jsx("div",{className:"card p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800",children:t.jsxs("div",{className:"flex items-start justify-between",children:[t.jsxs("div",{className:"flex-1",children:[t.jsx("h3",{className:"text-red-800 dark:text-red-200 font-medium mb-2",children:"文章加载失败"}),t.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm mb-4",children:i}),t.jsx("div",{className:"text-xs text-red-600 dark:text-red-400 mb-4",children:"请打开浏览器开发者工具的控制台查看详细错误信息"})]}),t.jsx("button",{onClick:E,className:"ml-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm",disabled:n,children:n?"重试中...":"重试"})]})}),e.length>0&&t.jsxs("div",{className:"space-y-6",children:[t.jsx(Co,{onFilterChange:R,availableCategories:Z,availableTags:B}),t.jsxs("div",{className:"flex flex-col sm:flex-row items-center justify-between gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:["共 ",se.length," 篇文章"]}),t.jsx(Mo,{pageSize:w,onPageSizeChange:G,options:[6,12,24,48]})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400 mr-2",children:"视图:"}),t.jsx("button",{onClick:()=>x("grid"),className:`p-2 rounded-md transition-colors ${u==="grid"?"bg-blue-600 text-white":"bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600"}`,title:"网格视图",children:t.jsx(Il,{size:16})}),t.jsx("button",{onClick:()=>x("list"),className:`p-2 rounded-md transition-colors ${u==="list"?"bg-blue-600 text-white":"bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600"}`,title:"列表视图",children:t.jsx(tn,{size:16})})]})]})]}),!n&&e.length===0&&t.jsx("div",{className:"text-center py-16",children:t.jsxs("div",{className:"card p-8 max-w-md mx-auto",children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"还没有文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"开始写作，分享你的想法和经验吧！"}),t.jsx(ae,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})}),!n&&e.length>0&&se.length===0&&t.jsx("div",{className:"text-center py-16",children:t.jsxs("div",{className:"card p-8 max-w-md mx-auto",children:[t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"没有找到匹配的文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"试试调整筛选条件，或者浏览全部文章"}),t.jsx("button",{onClick:()=>g({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"清除筛选条件"})]})}),ce&&t.jsxs("div",{className:"mb-16",children:[t.jsxs("div",{className:"text-center mb-8",children:[t.jsx("h2",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-2",children:"✨ 精选推荐"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"为您精心挑选的优质内容"})]}),t.jsx(Oo,{post:ce,showActions:!0,onDeleted:z})]}),k.length>0&&t.jsxs("div",{children:[t.jsxs("div",{className:"flex items-center justify-between mb-6",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:p===1&&ce?"📚 最新文章":"📚 文章列表"}),t.jsxs("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["第 ",p," 页，共 ",Y," 页"]})]}),u==="grid"?t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:k.map(f=>t.jsx(ga,{post:f,variant:"default",showActions:!0,showStats:!1,onDeleted:z},f.id))}):t.jsx("div",{className:"space-y-0",children:k.map(f=>t.jsx(ga,{post:f,variant:"list",showActions:!0,showStats:!1,onDeleted:z},f.id))})]}),se.length>w&&t.jsx("div",{className:"mt-12",children:t.jsx(Ro,{currentPage:p,totalPages:Y,totalItems:se.length,itemsPerPage:w,onPageChange:_,showInfo:!0})}),e.length>0&&!ce&&k.length===0&&p===1&&t.jsx("div",{className:"text-center py-8",children:t.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"当前页面没有文章"})}),A]})}class Gt{static async getComments(s){try{const r=await Te(fe.comments(s));return(await ve(r)).comments||[]}catch(r){return console.error("获取评论失败:",r),[]}}static async createComment(s,r){try{const a=await Te(fe.comments(s),{method:"POST",body:JSON.stringify(r)});return await ve(a)}catch(a){throw console.error("创建评论失败:",a),a}}static async deleteComment(s,r){try{const a=await ts(fe.comment(s,r),{method:"DELETE"});await ve(a)}catch(a){throw console.error("删除评论失败:",a),a}}static validateComment(s){var r,a;return(r=s.content)!=null&&r.trim()?s.content.length>1e3?"评论内容过长，最多1000个字符":(a=s.author)!=null&&a.trim()?s.author.length>50?"姓名过长，最多50个字符":s.email&&!this.isValidEmail(s.email)?"请输入有效的邮箱地址":s.website&&!this.isValidUrl(s.website)?"请输入有效的网站地址":null:"请输入您的姓名":"评论内容不能为空"}static isValidEmail(s){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s)}static isValidUrl(s){try{return new URL(s.startsWith("http")?s:`https://${s}`),!0}catch{return!1}}}function Ho({slug:e}){const{isAdmin:s}=qt(),[r,a]=d.useState([]),[n,o]=d.useState(!0),[i,l]=d.useState(!1),[c,g]=d.useState(null),[u,x]=d.useState(!1),[p,m]=d.useState({content:"",author:"",email:"",website:""}),w=async()=>{try{o(!0),g(null);const E=await Gt.getComments(e);a(Array.isArray(E)?E:[])}catch(E){console.error("加载评论失败:",E),g("加载评论失败，请稍后重试"),a([])}finally{o(!1)}},T=async E=>{E.preventDefault();const z=Gt.validateComment(p);if(z){g(z);return}try{l(!0),g(null);const R=await Gt.createComment(e,p);a(D=>[R,...Array.isArray(D)?D:[]]),m({content:"",author:"",email:"",website:""}),x(!1)}catch(R){console.error("提交评论失败:",R),g(R.message||"提交评论失败，请稍后重试")}finally{l(!1)}},N=async E=>{if(confirm("确定要删除这条评论吗？"))try{await Gt.deleteComment(e,E),a(z=>Array.isArray(z)?z.filter(R=>R.id!==E):[])}catch(z){console.error("删除评论失败:",z),g(z.message||"删除评论失败，请稍后重试")}},A=E=>{const z=new Date(E),D=new Date().getTime()-z.getTime(),_=Math.floor(D/(1e3*60*60*24));return _===0?"今天":_===1?"昨天":_<7?`${_}天前`:z.toLocaleDateString("zh-CN")};return d.useEffect(()=>{w()},[e]),t.jsx("div",{className:"mt-12",children:t.jsxs("div",{className:"card p-8",children:[t.jsxs("div",{className:"flex items-center justify-between mb-6",children:[t.jsxs("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center space-x-2",children:[t.jsx(es,{size:20}),t.jsxs("span",{children:["评论讨论 (",(r==null?void 0:r.length)||0,")"]})]}),!u&&t.jsxs("button",{onClick:()=>x(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2",children:[t.jsx(es,{size:16}),t.jsx("span",{children:"发表评论"})]})]}),c&&t.jsx("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:t.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm",children:c})}),u&&t.jsxs("form",{onSubmit:T,className:"mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("textarea",{value:p.content,onChange:E=>m(z=>({...z,content:E.target.value})),placeholder:"写下您的评论...",rows:4,className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0}),t.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[p.content.length,"/1000 字符"]})]}),t.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[t.jsxs("div",{className:"relative",children:[t.jsx(Qe,{className:"absolute left-3 top-3 text-gray-400",size:16}),t.jsx("input",{type:"text",value:p.author,onChange:E=>m(z=>({...z,author:E.target.value})),placeholder:"您的姓名 *",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),t.jsxs("div",{className:"relative",children:[t.jsx(sn,{className:"absolute left-3 top-3 text-gray-400",size:16}),t.jsx("input",{type:"email",value:p.email,onChange:E=>m(z=>({...z,email:E.target.value})),placeholder:"邮箱 (可选)",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),t.jsxs("div",{className:"relative",children:[t.jsx(Ll,{className:"absolute left-3 top-3 text-gray-400",size:16}),t.jsx("input",{type:"url",value:p.website,onChange:E=>m(z=>({...z,website:E.target.value})),placeholder:"网站 (可选)",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),t.jsxs("div",{className:"flex items-center justify-between mt-4",children:[t.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"评论将会被公开显示，请文明发言"}),t.jsxs("div",{className:"flex space-x-3",children:[t.jsx("button",{type:"button",onClick:()=>x(!1),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:"取消"}),t.jsxs("button",{type:"submit",disabled:i,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:[t.jsx(so,{size:16}),t.jsx("span",{children:i?"发表中...":"发表评论"})]})]})]})]}),t.jsx("div",{className:"space-y-6",children:n?t.jsx("div",{className:"space-y-4",children:[...Array(3)].map((E,z)=>t.jsx("div",{className:"animate-pulse",children:t.jsxs("div",{className:"flex space-x-3",children:[t.jsx("div",{className:"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"}),t.jsxs("div",{className:"flex-1",children:[t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]})]})},z))}):((r==null?void 0:r.length)||0)===0?t.jsxs("div",{className:"text-center py-8",children:[t.jsx(es,{className:"mx-auto mb-4 text-gray-400",size:48}),t.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"还没有评论，来发表第一条吧！"})]}):(r||[]).map(E=>t.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0 last:pb-0",children:t.jsxs("div",{className:"flex items-start space-x-3",children:[t.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold",children:E.author.charAt(0).toUpperCase()}),t.jsxs("div",{className:"flex-1",children:[t.jsxs("div",{className:"flex items-center justify-between mb-2",children:[t.jsxs("div",{className:"flex items-center space-x-3",children:[t.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:E.website?t.jsx("a",{href:E.website.startsWith("http")?E.website:`https://${E.website}`,target:"_blank",rel:"noopener noreferrer",className:"hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:E.author}):E.author}),t.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:A(E.createdAt)})]})]}),s&&t.jsx("button",{onClick:()=>N(E.id),className:"text-gray-400 hover:text-red-500 transition-colors p-1 rounded",title:"删除评论",children:t.jsx(cr,{size:14})})]}),t.jsx("div",{className:"prose prose-sm dark:prose-dark max-w-none",children:t.jsx("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:E.content})})]})]})},E.id))})]})})}function Bo(){const{slug:e}=Kt(),[s,r]=d.useState(null),[a,n]=d.useState(!0),[o,i]=d.useState(null);if(d.useEffect(()=>{(async()=>{if(e)try{n(!0),i(null);const g=await je.getPost(e);r(g)}catch(g){console.error("获取文章详情失败:",g),i("文章加载失败，请稍后重试")}finally{n(!1)}})()},[e]),a)return t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsxs("div",{className:"animate-pulse",children:[t.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-8"}),t.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"}),t.jsxs("div",{className:"space-y-4",children:[t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"}),t.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"})]})]})});if(o||!s)return t.jsx("div",{className:"max-w-4xl mx-auto text-center",children:t.jsxs("div",{className:"card p-8",children:[t.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:o||"文章未找到"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:o||"请检查链接是否正确，或返回首页查看其他文章"}),t.jsxs(ae,{to:"/",className:"inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:underline",children:[t.jsx(ns,{size:16}),t.jsx("span",{children:"返回首页"})]})]})});const l=()=>{};return t.jsxs("article",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[t.jsxs(ae,{to:"/",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[t.jsx(ns,{size:16}),t.jsx("span",{children:"返回首页"})]}),t.jsx(ht,{slug:s.slug,title:s.title,onDeleted:l})]}),t.jsxs("header",{className:"mb-8",children:[s.imageUrl&&t.jsx("div",{className:"mb-8 rounded-xl overflow-hidden shadow-lg bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900",children:t.jsx("img",{src:s.imageUrl,alt:s.title,className:"w-full h-auto min-h-[300px] max-h-[600px] object-cover hover:scale-105 transition-transform duration-500",style:{aspectRatio:"16/10"},loading:"lazy",onError:c=>{console.warn("封面图片加载失败:",s.imageUrl);const u=c.target.parentElement;u&&(u.style.display="none")},onLoad:()=>{console.log("封面图片加载成功:",s.imageUrl)}})}),t.jsx("div",{className:"mb-6",children:t.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white leading-tight",children:s.title})}),t.jsxs("div",{className:"flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-6",children:[t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Qe,{size:16}),t.jsx("span",{children:s.author})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Ae,{size:16}),t.jsx("span",{children:s.date})]}),t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(Ie,{size:16}),t.jsxs("span",{children:[s.readTime," 分钟阅读"]})]}),s.category&&t.jsxs("div",{className:"flex items-center space-x-2",children:[t.jsx(ft,{size:16}),t.jsx("span",{children:s.category})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:s.tags.map(c=>t.jsx(ae,{to:`/tag/${c}`,className:"px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors",children:c},c))})]}),t.jsx("div",{className:"card p-8 mb-8",children:s.content&&s.content.trim()?t.jsx("div",{className:"prose prose-gray dark:prose-dark max-w-none prose-img:rounded-lg prose-img:shadow-md",dangerouslySetInnerHTML:{__html:s.content}}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx("div",{className:"text-gray-400 mb-4",children:t.jsx("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:t.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),t.jsx("h3",{className:"text-lg font-medium text-gray-500 dark:text-gray-400 mb-2",children:"文章内容为空"}),t.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-sm",children:"该文章可能正在编辑中，或者内容尚未加载完成"})]})}),t.jsx("footer",{className:"mt-12 pt-8 border-t border-gray-200 dark:border-gray-700",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:t.jsx("p",{children:"感谢阅读！如果您觉得这篇文章有用，请分享给更多人。"})}),t.jsxs("div",{className:"flex space-x-4",children:[t.jsx(ae,{to:"/",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"更多文章"}),t.jsx(ae,{to:"/about",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"关于作者"})]})]})}),t.jsx(Ho,{slug:s.slug})]})}const Wo=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,Fo=Zn.create({name:"image",addOptions(){return{inline:!1,allowBase64:!1,HTMLAttributes:{}}},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes(){return{src:{default:null},alt:{default:null},title:{default:null}}},parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:e}){return["img",er(this.options.HTMLAttributes,e)]},addCommands(){return{setImage:e=>({commands:s})=>s.insertContent({type:this.name,attrs:e})}},addInputRules(){return[Yn({find:Wo,type:this.type,getAttributes:e=>{const[,,s,r,a]=e;return{src:r,alt:s,title:a}}})]}}),Ko="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",qo="ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2",pt=(e,s)=>{for(const r in s)e[r]=s[r];return e},hr="numeric",gr="ascii",xr="alpha",At="asciinumeric",St="alphanumeric",fr="domain",un="emoji",Go="scheme",Vo="slashscheme",qs="whitespace";function Jo(e,s){return e in s||(s[e]=[]),s[e]}function rt(e,s,r){s[hr]&&(s[At]=!0,s[St]=!0),s[gr]&&(s[At]=!0,s[xr]=!0),s[At]&&(s[St]=!0),s[xr]&&(s[St]=!0),s[St]&&(s[fr]=!0),s[un]&&(s[fr]=!0);for(const a in s){const n=Jo(a,r);n.indexOf(e)<0&&n.push(e)}}function Xo(e,s){const r={};for(const a in s)s[a].indexOf(e)>=0&&(r[a]=!0);return r}function Ce(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}Ce.groups={};Ce.prototype={accepts(){return!!this.t},go(e){const s=this,r=s.j[e];if(r)return r;for(let a=0;a<s.jr.length;a++){const n=s.jr[a][0],o=s.jr[a][1];if(o&&n.test(e))return o}return s.jd},has(e,s=!1){return s?e in this.j:!!this.go(e)},ta(e,s,r,a){for(let n=0;n<e.length;n++)this.tt(e[n],s,r,a)},tr(e,s,r,a){a=a||Ce.groups;let n;return s&&s.j?n=s:(n=new Ce(s),r&&a&&rt(s,r,a)),this.jr.push([e,n]),n},ts(e,s,r,a){let n=this;const o=e.length;if(!o)return n;for(let i=0;i<o-1;i++)n=n.tt(e[i]);return n.tt(e[o-1],s,r,a)},tt(e,s,r,a){a=a||Ce.groups;const n=this;if(s&&s.j)return n.j[e]=s,s;const o=s;let i,l=n.go(e);if(l?(i=new Ce,pt(i.j,l.j),i.jr.push.apply(i.jr,l.jr),i.jd=l.jd,i.t=l.t):i=new Ce,o){if(a)if(i.t&&typeof i.t=="string"){const c=pt(Xo(i.t,a),r);rt(o,c,a)}else r&&rt(o,r,a);i.t=o}return n.j[e]=i,i}};const ee=(e,s,r,a,n)=>e.ta(s,r,a,n),xe=(e,s,r,a,n)=>e.tr(s,r,a,n),xa=(e,s,r,a,n)=>e.ts(s,r,a,n),M=(e,s,r,a,n)=>e.tt(s,r,a,n),Be="WORD",mr="UWORD",hn="ASCIINUMERICAL",gn="ALPHANUMERICAL",zt="LOCALHOST",pr="TLD",yr="UTLD",ss="SCHEME",ut="SLASH_SCHEME",Er="NUM",br="WS",Sr="NL",Rt="OPENBRACE",Mt="CLOSEBRACE",os="OPENBRACKET",cs="CLOSEBRACKET",ds="OPENPAREN",us="CLOSEPAREN",hs="OPENANGLEBRACKET",gs="CLOSEANGLEBRACKET",xs="FULLWIDTHLEFTPAREN",fs="FULLWIDTHRIGHTPAREN",ms="LEFTCORNERBRACKET",ps="RIGHTCORNERBRACKET",ys="LEFTWHITECORNERBRACKET",bs="RIGHTWHITECORNERBRACKET",vs="FULLWIDTHLESSTHAN",js="FULLWIDTHGREATERTHAN",ks="AMPERSAND",ws="APOSTROPHE",Ns="ASTERISK",Fe="AT",Es="BACKSLASH",Ss="BACKTICK",Cs="CARET",Ke="COLON",Cr="COMMA",As="DOLLAR",ze="DOT",Rs="EQUALS",Ar="EXCLAMATION",Me="HYPHEN",Tt="PERCENT",Ms="PIPE",Ts="PLUS",Ls="POUND",Lt="QUERY",Rr="QUOTE",xn="FULLWIDTHMIDDLEDOT",Mr="SEMI",$e="SLASH",It="TILDE",Is="UNDERSCORE",fn="EMOJI",Os="SYM";var mn=Object.freeze({__proto__:null,ALPHANUMERICAL:gn,AMPERSAND:ks,APOSTROPHE:ws,ASCIINUMERICAL:hn,ASTERISK:Ns,AT:Fe,BACKSLASH:Es,BACKTICK:Ss,CARET:Cs,CLOSEANGLEBRACKET:gs,CLOSEBRACE:Mt,CLOSEBRACKET:cs,CLOSEPAREN:us,COLON:Ke,COMMA:Cr,DOLLAR:As,DOT:ze,EMOJI:fn,EQUALS:Rs,EXCLAMATION:Ar,FULLWIDTHGREATERTHAN:js,FULLWIDTHLEFTPAREN:xs,FULLWIDTHLESSTHAN:vs,FULLWIDTHMIDDLEDOT:xn,FULLWIDTHRIGHTPAREN:fs,HYPHEN:Me,LEFTCORNERBRACKET:ms,LEFTWHITECORNERBRACKET:ys,LOCALHOST:zt,NL:Sr,NUM:Er,OPENANGLEBRACKET:hs,OPENBRACE:Rt,OPENBRACKET:os,OPENPAREN:ds,PERCENT:Tt,PIPE:Ms,PLUS:Ts,POUND:Ls,QUERY:Lt,QUOTE:Rr,RIGHTCORNERBRACKET:ps,RIGHTWHITECORNERBRACKET:bs,SCHEME:ss,SEMI:Mr,SLASH:$e,SLASH_SCHEME:ut,SYM:Os,TILDE:It,TLD:pr,UNDERSCORE:Is,UTLD:yr,UWORD:mr,WORD:Be,WS:br});const Ue=/[a-z]/,wt=/\p{L}/u,Gs=/\p{Emoji}/u,He=/\d/,Vs=/\s/,fa="\r",Js=`
`,Qo="️",Zo="‍",Xs="￼";let Vt=null,Jt=null;function Yo(e=[]){const s={};Ce.groups=s;const r=new Ce;Vt==null&&(Vt=ma(Ko)),Jt==null&&(Jt=ma(qo)),M(r,"'",ws),M(r,"{",Rt),M(r,"}",Mt),M(r,"[",os),M(r,"]",cs),M(r,"(",ds),M(r,")",us),M(r,"<",hs),M(r,">",gs),M(r,"（",xs),M(r,"）",fs),M(r,"「",ms),M(r,"」",ps),M(r,"『",ys),M(r,"』",bs),M(r,"＜",vs),M(r,"＞",js),M(r,"&",ks),M(r,"*",Ns),M(r,"@",Fe),M(r,"`",Ss),M(r,"^",Cs),M(r,":",Ke),M(r,",",Cr),M(r,"$",As),M(r,".",ze),M(r,"=",Rs),M(r,"!",Ar),M(r,"-",Me),M(r,"%",Tt),M(r,"|",Ms),M(r,"+",Ts),M(r,"#",Ls),M(r,"?",Lt),M(r,'"',Rr),M(r,"/",$e),M(r,";",Mr),M(r,"~",It),M(r,"_",Is),M(r,"\\",Es),M(r,"・",xn);const a=xe(r,He,Er,{[hr]:!0});xe(a,He,a);const n=xe(a,Ue,hn,{[At]:!0}),o=xe(a,wt,gn,{[St]:!0}),i=xe(r,Ue,Be,{[gr]:!0});xe(i,He,n),xe(i,Ue,i),xe(n,He,n),xe(n,Ue,n);const l=xe(r,wt,mr,{[xr]:!0});xe(l,Ue),xe(l,He,o),xe(l,wt,l),xe(o,He,o),xe(o,Ue),xe(o,wt,o);const c=M(r,Js,Sr,{[qs]:!0}),g=M(r,fa,br,{[qs]:!0}),u=xe(r,Vs,br,{[qs]:!0});M(r,Xs,u),M(g,Js,c),M(g,Xs,u),xe(g,Vs,u),M(u,fa),M(u,Js),xe(u,Vs,u),M(u,Xs,u);const x=xe(r,Gs,fn,{[un]:!0});M(x,"#"),xe(x,Gs,x),M(x,Qo,x);const p=M(x,Zo);M(p,"#"),xe(p,Gs,x);const m=[[Ue,i],[He,n]],w=[[Ue,null],[wt,l],[He,o]];for(let T=0;T<Vt.length;T++)We(r,Vt[T],pr,Be,m);for(let T=0;T<Jt.length;T++)We(r,Jt[T],yr,mr,w);rt(pr,{tld:!0,ascii:!0},s),rt(yr,{utld:!0,alpha:!0},s),We(r,"file",ss,Be,m),We(r,"mailto",ss,Be,m),We(r,"http",ut,Be,m),We(r,"https",ut,Be,m),We(r,"ftp",ut,Be,m),We(r,"ftps",ut,Be,m),rt(ss,{scheme:!0,ascii:!0},s),rt(ut,{slashscheme:!0,ascii:!0},s),e=e.sort((T,N)=>T[0]>N[0]?1:-1);for(let T=0;T<e.length;T++){const N=e[T][0],E=e[T][1]?{[Go]:!0}:{[Vo]:!0};N.indexOf("-")>=0?E[fr]=!0:Ue.test(N)?He.test(N)?E[At]=!0:E[gr]=!0:E[hr]=!0,xa(r,N,N,E)}return xa(r,"localhost",zt,{ascii:!0}),r.jd=new Ce(Os),{start:r,tokens:pt({groups:s},mn)}}function pn(e,s){const r=ec(s.replace(/[A-Z]/g,l=>l.toLowerCase())),a=r.length,n=[];let o=0,i=0;for(;i<a;){let l=e,c=null,g=0,u=null,x=-1,p=-1;for(;i<a&&(c=l.go(r[i]));)l=c,l.accepts()?(x=0,p=0,u=l):x>=0&&(x+=r[i].length,p++),g+=r[i].length,o+=r[i].length,i++;o-=x,i-=p,g-=x,n.push({t:u.t,v:s.slice(o-g,o),s:o-g,e:o})}return n}function ec(e){const s=[],r=e.length;let a=0;for(;a<r;){let n=e.charCodeAt(a),o,i=n<55296||n>56319||a+1===r||(o=e.charCodeAt(a+1))<56320||o>57343?e[a]:e.slice(a,a+2);s.push(i),a+=i.length}return s}function We(e,s,r,a,n){let o;const i=s.length;for(let l=0;l<i-1;l++){const c=s[l];e.j[c]?o=e.j[c]:(o=new Ce(a),o.jr=n.slice(),e.j[c]=o),e=o}return o=new Ce(r),o.jr=n.slice(),e.j[s[i-1]]=o,o}function ma(e){const s=[],r=[];let a=0,n="0123456789";for(;a<e.length;){let o=0;for(;n.indexOf(e[a+o])>=0;)o++;if(o>0){s.push(r.join(""));for(let i=parseInt(e.substring(a,a+o),10);i>0;i--)r.pop();a+=o}else r.push(e[a]),a++}return s}const $t={defaultProtocol:"http",events:null,format:pa,formatHref:pa,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Tr(e,s=null){let r=pt({},$t);e&&(r=pt(r,e instanceof Tr?e.o:e));const a=r.ignoreTags,n=[];for(let o=0;o<a.length;o++)n.push(a[o].toUpperCase());this.o=r,s&&(this.defaultRender=s),this.ignoreTags=n}Tr.prototype={o:$t,ignoreTags:[],defaultRender(e){return e},check(e){return this.get("validate",e.toString(),e)},get(e,s,r){const a=s!=null;let n=this.o[e];return n&&(typeof n=="object"?(n=r.t in n?n[r.t]:$t[e],typeof n=="function"&&a&&(n=n(s,r))):typeof n=="function"&&a&&(n=n(s,r.t,r)),n)},getObj(e,s,r){let a=this.o[e];return typeof a=="function"&&s!=null&&(a=a(s,r.t,r)),a},render(e){const s=e.render(this);return(this.get("render",null,e)||this.defaultRender)(s,e.t,e)}};function pa(e){return e}function yn(e,s){this.t="token",this.v=e,this.tk=s}yn.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){const s=this.toString(),r=e.get("truncate",s,this),a=e.get("format",s,this);return r&&a.length>r?a.substring(0,r)+"…":a},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=$t.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){const s=this,r=this.toHref(e.get("defaultProtocol")),a=e.get("formatHref",r,this),n=e.get("tagName",r,s),o=this.toFormattedString(e),i={},l=e.get("className",r,s),c=e.get("target",r,s),g=e.get("rel",r,s),u=e.getObj("attributes",r,s),x=e.getObj("events",r,s);return i.href=a,l&&(i.class=l),c&&(i.target=c),g&&(i.rel=g),u&&pt(i,u),{tagName:n,attributes:i,content:o,eventListeners:x}}};function Hs(e,s){class r extends yn{constructor(n,o){super(n,o),this.t=e}}for(const a in s)r.prototype[a]=s[a];return r.t=e,r}const ya=Hs("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),ba=Hs("text"),tc=Hs("nl"),Xt=Hs("url",{isLink:!0,toHref(e=$t.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){const e=this.tk;return e.length>=2&&e[0].t!==zt&&e[1].t===Ke}}),Re=e=>new Ce(e);function sc({groups:e}){const s=e.domain.concat([ks,Ns,Fe,Es,Ss,Cs,As,Rs,Me,Er,Tt,Ms,Ts,Ls,$e,Os,It,Is]),r=[ws,Ke,Cr,ze,Ar,Tt,Lt,Rr,Mr,hs,gs,Rt,Mt,cs,os,ds,us,xs,fs,ms,ps,ys,bs,vs,js],a=[ks,ws,Ns,Es,Ss,Cs,As,Rs,Me,Rt,Mt,Tt,Ms,Ts,Ls,Lt,$e,Os,It,Is],n=Re(),o=M(n,It);ee(o,a,o),ee(o,e.domain,o);const i=Re(),l=Re(),c=Re();ee(n,e.domain,i),ee(n,e.scheme,l),ee(n,e.slashscheme,c),ee(i,a,o),ee(i,e.domain,i);const g=M(i,Fe);M(o,Fe,g),M(l,Fe,g),M(c,Fe,g);const u=M(o,ze);ee(u,a,o),ee(u,e.domain,o);const x=Re();ee(g,e.domain,x),ee(x,e.domain,x);const p=M(x,ze);ee(p,e.domain,x);const m=Re(ya);ee(p,e.tld,m),ee(p,e.utld,m),M(g,zt,m);const w=M(x,Me);M(w,Me,w),ee(w,e.domain,x),ee(m,e.domain,x),M(m,ze,p),M(m,Me,w);const T=M(m,Ke);ee(T,e.numeric,ya);const N=M(i,Me),A=M(i,ze);M(N,Me,N),ee(N,e.domain,i),ee(A,a,o),ee(A,e.domain,i);const E=Re(Xt);ee(A,e.tld,E),ee(A,e.utld,E),ee(E,e.domain,i),ee(E,a,o),M(E,ze,A),M(E,Me,N),M(E,Fe,g);const z=M(E,Ke),R=Re(Xt);ee(z,e.numeric,R);const D=Re(Xt),_=Re();ee(D,s,D),ee(D,r,_),ee(_,s,D),ee(_,r,_),M(E,$e,D),M(R,$e,D);const G=M(l,Ke),Z=M(c,Ke),B=M(Z,$e),se=M(B,$e);ee(l,e.domain,i),M(l,ze,A),M(l,Me,N),ee(c,e.domain,i),M(c,ze,A),M(c,Me,N),ee(G,e.domain,D),M(G,$e,D),M(G,Lt,D),ee(se,e.domain,D),ee(se,s,D),M(se,$e,D);const Y=[[Rt,Mt],[os,cs],[ds,us],[hs,gs],[xs,fs],[ms,ps],[ys,bs],[vs,js]];for(let te=0;te<Y.length;te++){const[ue,le]=Y[te],ce=M(D,ue);M(_,ue,ce),M(ce,le,D);const k=Re(Xt);ee(ce,s,k);const f=Re();ee(ce,r),ee(k,s,k),ee(k,r,f),ee(f,s,k),ee(f,r,f),M(k,le,D),M(f,le,D)}return M(n,zt,E),M(n,Sr,tc),{start:n,tokens:mn}}function rc(e,s,r){let a=r.length,n=0,o=[],i=[];for(;n<a;){let l=e,c=null,g=null,u=0,x=null,p=-1;for(;n<a&&!(c=l.go(r[n].t));)i.push(r[n++]);for(;n<a&&(g=c||l.go(r[n].t));)c=null,l=g,l.accepts()?(p=0,x=l):p>=0&&p++,n++,u++;if(p<0)n-=u,n<a&&(i.push(r[n]),n++);else{i.length>0&&(o.push(Qs(ba,s,i)),i=[]),n-=p,u-=p;const m=x.t,w=r.slice(n-u,n);o.push(Qs(m,s,w))}}return i.length>0&&o.push(Qs(ba,s,i)),o}function Qs(e,s,r){const a=r[0].s,n=r[r.length-1].e,o=s.slice(a,n);return new e(o,r)}const ac=typeof console<"u"&&console&&console.warn||(()=>{}),nc="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",he={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function ic(){return Ce.groups={},he.scanner=null,he.parser=null,he.tokenQueue=[],he.pluginQueue=[],he.customSchemes=[],he.initialized=!1,he}function va(e,s=!1){if(he.initialized&&ac(`linkifyjs: already initialized - will not register custom scheme "${e}" ${nc}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw new Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);he.customSchemes.push([e,s])}function lc(){he.scanner=Yo(he.customSchemes);for(let e=0;e<he.tokenQueue.length;e++)he.tokenQueue[e][1]({scanner:he.scanner});he.parser=sc(he.scanner.tokens);for(let e=0;e<he.pluginQueue.length;e++)he.pluginQueue[e][1]({scanner:he.scanner,parser:he.parser});return he.initialized=!0,he}function Lr(e){return he.initialized||lc(),rc(he.parser.start,e,pn(he.scanner.start,e))}Lr.scan=pn;function bn(e,s=null,r=null){if(s&&typeof s=="object"){if(r)throw Error(`linkifyjs: Invalid link type ${s}; must be a string`);r=s,s=null}const a=new Tr(r),n=Lr(e),o=[];for(let i=0;i<n.length;i++){const l=n[i];l.isLink&&(!s||l.t===s)&&a.check(l)&&o.push(l.toFormattedObject(a))}return o}function oc(e){return e.length===1?e[0].isLink:e.length===3&&e[1].isLink?["()","[]"].includes(e[0].value+e[2].value):!1}function cc(e){return new zs({key:new $s("autolink"),appendTransaction:(s,r,a)=>{const n=s.some(g=>g.docChanged)&&!r.doc.eq(a.doc),o=s.some(g=>g.getMeta("preventAutolink"));if(!n||o)return;const{tr:i}=a,l=si(r.doc,[...s]);if(ri(l).forEach(({newRange:g})=>{const u=ai(a.doc,g,m=>m.isTextblock);let x,p;if(u.length>1?(x=u[0],p=a.doc.textBetween(x.pos,x.pos+x.node.nodeSize,void 0," ")):u.length&&a.doc.textBetween(g.from,g.to," "," ").endsWith(" ")&&(x=u[0],p=a.doc.textBetween(x.pos,g.to,void 0," ")),x&&p){const m=p.split(" ").filter(A=>A!=="");if(m.length<=0)return!1;const w=m[m.length-1],T=x.pos+p.lastIndexOf(w);if(!w)return!1;const N=Lr(w).map(A=>A.toObject(e.defaultProtocol));if(!oc(N))return!1;N.filter(A=>A.isLink).map(A=>({...A,from:T+A.start+1,to:T+A.end+1})).filter(A=>a.schema.marks.code?!a.doc.rangeHasMark(A.from,A.to,a.schema.marks.code):!0).filter(A=>e.validate(A.value)).filter(A=>e.shouldAutoLink(A.value)).forEach(A=>{ni(A.from,A.to,a.doc).some(E=>E.mark.type===e.type)||i.addMark(A.from,A.to,e.type.create({href:A.href}))})}}),!!i.steps.length)return i}})}function dc(e){return new zs({key:new $s("handleClickLink"),props:{handleClick:(s,r,a)=>{var n,o;if(a.button!==0||!s.editable)return!1;let i=a.target;const l=[];for(;i.nodeName!=="DIV";)l.push(i),i=i.parentNode;if(!l.find(p=>p.nodeName==="A"))return!1;const c=ii(s.state,e.type.name),g=a.target,u=(n=g==null?void 0:g.href)!==null&&n!==void 0?n:c.href,x=(o=g==null?void 0:g.target)!==null&&o!==void 0?o:c.target;return g&&u?(window.open(u,x),!0):!1}}})}function uc(e){return new zs({key:new $s("handlePasteLink"),props:{handlePaste:(s,r,a)=>{const{state:n}=s,{selection:o}=n,{empty:i}=o;if(i)return!1;let l="";a.content.forEach(g=>{l+=g.textContent});const c=bn(l,{defaultProtocol:e.defaultProtocol}).find(g=>g.isLink&&g.value===l);return!l||!c?!1:e.editor.commands.setMark(e.type,{href:c.href})}}})}const hc=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function tt(e,s){const r=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return s&&s.forEach(a=>{const n=typeof a=="string"?a:a.scheme;n&&r.push(n)}),!e||e.replace(hc,"").match(new RegExp(`^(?:(?:${r.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}const gc=ei.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if(typeof e=="string"){va(e);return}va(e.scheme,e.optionalSlashes)})},onDestroy(){ic()},inclusive(){return this.options.autolink},addOptions(){return{openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,s)=>!!tt(e,s.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}},addAttributes(){return{href:{default:null,parseHTML(e){return e.getAttribute("href")}},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{const s=e.getAttribute("href");return!s||!this.options.isAllowedUri(s,{defaultValidate:r=>!!tt(r,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:s=>!!tt(s,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",er(this.options.HTMLAttributes,e),0]:["a",er(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:s})=>{const{href:r}=e;return this.options.isAllowedUri(r,{defaultValidate:a=>!!tt(a,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?s().setMark(this.name,e).setMeta("preventAutolink",!0).run():!1},toggleLink:e=>({chain:s})=>{const{href:r}=e;return this.options.isAllowedUri(r,{defaultValidate:a=>!!tt(a,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?s().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run():!1},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[ti({find:e=>{const s=[];if(e){const{protocols:r,defaultProtocol:a}=this.options,n=bn(e).filter(o=>o.isLink&&this.options.isAllowedUri(o.value,{defaultValidate:i=>!!tt(i,r),protocols:r,defaultProtocol:a}));n.length&&n.forEach(o=>s.push({text:o.value,data:{href:o.href},index:o.start}))}return s},type:this.type,getAttributes:e=>{var s;return{href:(s=e.data)===null||s===void 0?void 0:s.href}}})]},addProseMirrorPlugins(){const e=[],{protocols:s,defaultProtocol:r}=this.options;return this.options.autolink&&e.push(cc({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:a=>this.options.isAllowedUri(a,{defaultValidate:n=>!!tt(n,s),protocols:s,defaultProtocol:r}),shouldAutoLink:this.options.shouldAutoLink})),this.options.openOnClick===!0&&e.push(dc({type:this.type})),this.options.linkOnPaste&&e.push(uc({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),e}});function xc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function vn(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(s=>{const r=e[s],a=typeof r;(a==="object"||a==="function")&&!Object.isFrozen(r)&&vn(r)}),e}let ja=class{constructor(s){s.data===void 0&&(s.data={}),this.data=s.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}};function jn(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function Ge(e,...s){const r=Object.create(null);for(const a in e)r[a]=e[a];return s.forEach(function(a){for(const n in a)r[n]=a[n]}),r}const fc="</span>",ka=e=>!!e.scope,mc=(e,{prefix:s})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const r=e.split(".");return[`${s}${r.shift()}`,...r.map((a,n)=>`${a}${"_".repeat(n+1)}`)].join(" ")}return`${s}${e}`};let pc=class{constructor(s,r){this.buffer="",this.classPrefix=r.classPrefix,s.walk(this)}addText(s){this.buffer+=jn(s)}openNode(s){if(!ka(s))return;const r=mc(s.scope,{prefix:this.classPrefix});this.span(r)}closeNode(s){ka(s)&&(this.buffer+=fc)}value(){return this.buffer}span(s){this.buffer+=`<span class="${s}">`}};const wa=(e={})=>{const s={children:[]};return Object.assign(s,e),s};let yc=class kn{constructor(){this.rootNode=wa(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(s){this.top.children.push(s)}openNode(s){const r=wa({scope:s});this.add(r),this.stack.push(r)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(s){return this.constructor._walk(s,this.rootNode)}static _walk(s,r){return typeof r=="string"?s.addText(r):r.children&&(s.openNode(r),r.children.forEach(a=>this._walk(s,a)),s.closeNode(r)),s}static _collapse(s){typeof s!="string"&&s.children&&(s.children.every(r=>typeof r=="string")?s.children=[s.children.join("")]:s.children.forEach(r=>{kn._collapse(r)}))}},bc=class extends yc{constructor(s){super(),this.options=s}addText(s){s!==""&&this.add(s)}startScope(s){this.openNode(s)}endScope(){this.closeNode()}__addSublanguage(s,r){const a=s.root;r&&(a.scope=`language:${r}`),this.add(a)}toHTML(){return new pc(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}};function Dt(e){return e?typeof e=="string"?e:e.source:null}function wn(e){return lt("(?=",e,")")}function vc(e){return lt("(?:",e,")*")}function jc(e){return lt("(?:",e,")?")}function lt(...e){return e.map(r=>Dt(r)).join("")}function kc(e){const s=e[e.length-1];return typeof s=="object"&&s.constructor===Object?(e.splice(e.length-1,1),s):{}}function Ir(...e){return"("+(kc(e).capture?"":"?:")+e.map(a=>Dt(a)).join("|")+")"}function Nn(e){return new RegExp(e.toString()+"|").exec("").length-1}function wc(e,s){const r=e&&e.exec(s);return r&&r.index===0}const Nc=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function Or(e,{joinWith:s}){let r=0;return e.map(a=>{r+=1;const n=r;let o=Dt(a),i="";for(;o.length>0;){const l=Nc.exec(o);if(!l){i+=o;break}i+=o.substring(0,l.index),o=o.substring(l.index+l[0].length),l[0][0]==="\\"&&l[1]?i+="\\"+String(Number(l[1])+n):(i+=l[0],l[0]==="("&&r++)}return i}).map(a=>`(${a})`).join(s)}const Ec=/\b\B/,En="[a-zA-Z]\\w*",Pr="[a-zA-Z_]\\w*",Sn="\\b\\d+(\\.\\d+)?",Cn="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",An="\\b(0b[01]+)",Sc="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",Cc=(e={})=>{const s=/^#![ ]*\//;return e.binary&&(e.begin=lt(s,/.*\b/,e.binary,/\b.*/)),Ge({scope:"meta",begin:s,end:/$/,relevance:0,"on:begin":(r,a)=>{r.index!==0&&a.ignoreMatch()}},e)},Ut={begin:"\\\\[\\s\\S]",relevance:0},Ac={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[Ut]},Rc={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[Ut]},Mc={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Bs=function(e,s,r={}){const a=Ge({scope:"comment",begin:e,end:s,contains:[]},r);a.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const n=Ir("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return a.contains.push({begin:lt(/[ ]+/,"(",n,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),a},Tc=Bs("//","$"),Lc=Bs("/\\*","\\*/"),Ic=Bs("#","$"),Oc={scope:"number",begin:Sn,relevance:0},Pc={scope:"number",begin:Cn,relevance:0},_c={scope:"number",begin:An,relevance:0},zc={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[Ut,{begin:/\[/,end:/\]/,relevance:0,contains:[Ut]}]},$c={scope:"title",begin:En,relevance:0},Dc={scope:"title",begin:Pr,relevance:0},Uc={begin:"\\.\\s*"+Pr,relevance:0},Hc=function(e){return Object.assign(e,{"on:begin":(s,r)=>{r.data._beginMatch=s[1]},"on:end":(s,r)=>{r.data._beginMatch!==s[1]&&r.ignoreMatch()}})};var Qt=Object.freeze({__proto__:null,APOS_STRING_MODE:Ac,BACKSLASH_ESCAPE:Ut,BINARY_NUMBER_MODE:_c,BINARY_NUMBER_RE:An,COMMENT:Bs,C_BLOCK_COMMENT_MODE:Lc,C_LINE_COMMENT_MODE:Tc,C_NUMBER_MODE:Pc,C_NUMBER_RE:Cn,END_SAME_AS_BEGIN:Hc,HASH_COMMENT_MODE:Ic,IDENT_RE:En,MATCH_NOTHING_RE:Ec,METHOD_GUARD:Uc,NUMBER_MODE:Oc,NUMBER_RE:Sn,PHRASAL_WORDS_MODE:Mc,QUOTE_STRING_MODE:Rc,REGEXP_MODE:zc,RE_STARTERS_RE:Sc,SHEBANG:Cc,TITLE_MODE:$c,UNDERSCORE_IDENT_RE:Pr,UNDERSCORE_TITLE_MODE:Dc});function Bc(e,s){e.input[e.index-1]==="."&&s.ignoreMatch()}function Wc(e,s){e.className!==void 0&&(e.scope=e.className,delete e.className)}function Fc(e,s){s&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=Bc,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function Kc(e,s){Array.isArray(e.illegal)&&(e.illegal=Ir(...e.illegal))}function qc(e,s){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function Gc(e,s){e.relevance===void 0&&(e.relevance=1)}const Vc=(e,s)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const r=Object.assign({},e);Object.keys(e).forEach(a=>{delete e[a]}),e.keywords=r.keywords,e.begin=lt(r.beforeMatch,wn(r.begin)),e.starts={relevance:0,contains:[Object.assign(r,{endsParent:!0})]},e.relevance=0,delete r.beforeMatch},Jc=["of","and","for","in","not","or","if","then","parent","list","value"],Xc="keyword";function Rn(e,s,r=Xc){const a=Object.create(null);return typeof e=="string"?n(r,e.split(" ")):Array.isArray(e)?n(r,e):Object.keys(e).forEach(function(o){Object.assign(a,Rn(e[o],s,o))}),a;function n(o,i){s&&(i=i.map(l=>l.toLowerCase())),i.forEach(function(l){const c=l.split("|");a[c[0]]=[o,Qc(c[0],c[1])]})}}function Qc(e,s){return s?Number(s):Zc(e)?0:1}function Zc(e){return Jc.includes(e.toLowerCase())}const Na={},at=e=>{console.error(e)},Ea=(e,...s)=>{console.log(`WARN: ${e}`,...s)},ct=(e,s)=>{Na[`${e}/${s}`]||(console.log(`Deprecated as of ${e}. ${s}`),Na[`${e}/${s}`]=!0)},Ps=new Error;function Mn(e,s,{key:r}){let a=0;const n=e[r],o={},i={};for(let l=1;l<=s.length;l++)i[l+a]=n[l],o[l+a]=!0,a+=Nn(s[l-1]);e[r]=i,e[r]._emit=o,e[r]._multi=!0}function Yc(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw at("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),Ps;if(typeof e.beginScope!="object"||e.beginScope===null)throw at("beginScope must be object"),Ps;Mn(e,e.begin,{key:"beginScope"}),e.begin=Or(e.begin,{joinWith:""})}}function ed(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw at("skip, excludeEnd, returnEnd not compatible with endScope: {}"),Ps;if(typeof e.endScope!="object"||e.endScope===null)throw at("endScope must be object"),Ps;Mn(e,e.end,{key:"endScope"}),e.end=Or(e.end,{joinWith:""})}}function td(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function sd(e){td(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),Yc(e),ed(e)}function rd(e){function s(i,l){return new RegExp(Dt(i),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(l?"g":""))}class r{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(l,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,l]),this.matchAt+=Nn(l)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const l=this.regexes.map(c=>c[1]);this.matcherRe=s(Or(l,{joinWith:"|"}),!0),this.lastIndex=0}exec(l){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(l);if(!c)return null;const g=c.findIndex((x,p)=>p>0&&x!==void 0),u=this.matchIndexes[g];return c.splice(0,g),Object.assign(c,u)}}class a{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(l){if(this.multiRegexes[l])return this.multiRegexes[l];const c=new r;return this.rules.slice(l).forEach(([g,u])=>c.addRule(g,u)),c.compile(),this.multiRegexes[l]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(l,c){this.rules.push([l,c]),c.type==="begin"&&this.count++}exec(l){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let g=c.exec(l);if(this.resumingScanAtSamePosition()&&!(g&&g.index===this.lastIndex)){const u=this.getMatcher(0);u.lastIndex=this.lastIndex+1,g=u.exec(l)}return g&&(this.regexIndex+=g.position+1,this.regexIndex===this.count&&this.considerAll()),g}}function n(i){const l=new a;return i.contains.forEach(c=>l.addRule(c.begin,{rule:c,type:"begin"})),i.terminatorEnd&&l.addRule(i.terminatorEnd,{type:"end"}),i.illegal&&l.addRule(i.illegal,{type:"illegal"}),l}function o(i,l){const c=i;if(i.isCompiled)return c;[Wc,qc,sd,Vc].forEach(u=>u(i,l)),e.compilerExtensions.forEach(u=>u(i,l)),i.__beforeBegin=null,[Fc,Kc,Gc].forEach(u=>u(i,l)),i.isCompiled=!0;let g=null;return typeof i.keywords=="object"&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),g=i.keywords.$pattern,delete i.keywords.$pattern),g=g||/\w+/,i.keywords&&(i.keywords=Rn(i.keywords,e.case_insensitive)),c.keywordPatternRe=s(g,!0),l&&(i.begin||(i.begin=/\B|\b/),c.beginRe=s(c.begin),!i.end&&!i.endsWithParent&&(i.end=/\B|\b/),i.end&&(c.endRe=s(c.end)),c.terminatorEnd=Dt(c.end)||"",i.endsWithParent&&l.terminatorEnd&&(c.terminatorEnd+=(i.end?"|":"")+l.terminatorEnd)),i.illegal&&(c.illegalRe=s(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(u){return ad(u==="self"?i:u)})),i.contains.forEach(function(u){o(u,c)}),i.starts&&o(i.starts,l),c.matcher=n(c),c}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=Ge(e.classNameAliases||{}),o(e)}function Tn(e){return e?e.endsWithParent||Tn(e.starts):!1}function ad(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(s){return Ge(e,{variants:null},s)})),e.cachedVariants?e.cachedVariants:Tn(e)?Ge(e,{starts:e.starts?Ge(e.starts):null}):Object.isFrozen(e)?Ge(e):e}var nd="11.10.0";let id=class extends Error{constructor(s,r){super(s),this.name="HTMLInjectionError",this.html=r}};const Zs=jn,Sa=Ge,Ca=Symbol("nomatch"),ld=7,Ln=function(e){const s=Object.create(null),r=Object.create(null),a=[];let n=!0;const o="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let l={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:bc};function c(f){return l.noHighlightRe.test(f)}function g(f){let j=f.className+" ";j+=f.parentNode?f.parentNode.className:"";const b=l.languageDetectRe.exec(j);if(b){const S=B(b[1]);return S||(Ea(o.replace("{}",b[1])),Ea("Falling back to no-highlight mode for this block.",f)),S?b[1]:"no-highlight"}return j.split(/\s+/).find(S=>c(S)||B(S))}function u(f,j,b){let S="",I="";typeof j=="object"?(S=f,b=j.ignoreIllegals,I=j.language):(ct("10.7.0","highlight(lang, code, ...args) has been deprecated."),ct("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),I=f,S=j),b===void 0&&(b=!0);const K={code:S,language:I};ce("before:highlight",K);const J=K.result?K.result:x(K.language,K.code,b);return J.code=K.code,ce("after:highlight",J),J}function x(f,j,b,S){const I=Object.create(null);function K(h,y){return h.keywords[y]}function J(){if(!F.keywords){U.addText(Q);return}let h=0;F.keywordPatternRe.lastIndex=0;let y=F.keywordPatternRe.exec(Q),v="";for(;y;){v+=Q.substring(h,y.index);const O=X.case_insensitive?y[0].toLowerCase():y[0],q=K(F,O);if(q){const[V,Ee]=q;if(U.addText(v),v="",I[O]=(I[O]||0)+1,I[O]<=ld&&(Ne+=Ee),V.startsWith("_"))v+=y[0];else{const et=X.classNameAliases[V]||V;oe(y[0],et)}}else v+=y[0];h=F.keywordPatternRe.lastIndex,y=F.keywordPatternRe.exec(Q)}v+=Q.substring(h),U.addText(v)}function C(){if(Q==="")return;let h=null;if(typeof F.subLanguage=="string"){if(!s[F.subLanguage]){U.addText(Q);return}h=x(F.subLanguage,Q,!0,re[F.subLanguage]),re[F.subLanguage]=h._top}else h=m(Q,F.subLanguage.length?F.subLanguage:null);F.relevance>0&&(Ne+=h.relevance),U.__addSublanguage(h._emitter,h.language)}function ne(){F.subLanguage!=null?C():J(),Q=""}function oe(h,y){h!==""&&(U.startScope(y),U.addText(h),U.endScope())}function ke(h,y){let v=1;const O=y.length-1;for(;v<=O;){if(!h._emit[v]){v++;continue}const q=X.classNameAliases[h[v]]||h[v],V=y[v];q?oe(V,q):(Q=V,J(),Q=""),v++}}function de(h,y){return h.scope&&typeof h.scope=="string"&&U.openNode(X.classNameAliases[h.scope]||h.scope),h.beginScope&&(h.beginScope._wrap?(oe(Q,X.classNameAliases[h.beginScope._wrap]||h.beginScope._wrap),Q=""):h.beginScope._multi&&(ke(h.beginScope,y),Q="")),F=Object.create(h,{parent:{value:F}}),F}function ge(h,y,v){let O=wc(h.endRe,v);if(O){if(h["on:end"]){const q=new ja(h);h["on:end"](y,q),q.isMatchIgnored&&(O=!1)}if(O){for(;h.endsParent&&h.parent;)h=h.parent;return h}}if(h.endsWithParent)return ge(h.parent,y,v)}function we(h){return F.matcher.regexIndex===0?(Q+=h[0],1):(P=!0,0)}function be(h){const y=h[0],v=h.rule,O=new ja(v),q=[v.__beforeBegin,v["on:begin"]];for(const V of q)if(V&&(V(h,O),O.isMatchIgnored))return we(y);return v.skip?Q+=y:(v.excludeBegin&&(Q+=y),ne(),!v.returnBegin&&!v.excludeBegin&&(Q=y)),de(v,h),v.returnBegin?0:y.length}function Oe(h){const y=h[0],v=j.substring(h.index),O=ge(F,h,v);if(!O)return Ca;const q=F;F.endScope&&F.endScope._wrap?(ne(),oe(y,F.endScope._wrap)):F.endScope&&F.endScope._multi?(ne(),ke(F.endScope,h)):q.skip?Q+=y:(q.returnEnd||q.excludeEnd||(Q+=y),ne(),q.excludeEnd&&(Q=y));do F.scope&&U.closeNode(),!F.skip&&!F.subLanguage&&(Ne+=F.relevance),F=F.parent;while(F!==O.parent);return O.starts&&de(O.starts,h),q.returnEnd?0:y.length}function Pe(){const h=[];for(let y=F;y!==X;y=y.parent)y.scope&&h.unshift(y.scope);h.forEach(y=>U.openNode(y))}let H={};function W(h,y){const v=y&&y[0];if(Q+=h,v==null)return ne(),0;if(H.type==="begin"&&y.type==="end"&&H.index===y.index&&v===""){if(Q+=j.slice(y.index,y.index+1),!n){const O=new Error(`0 width match regex (${f})`);throw O.languageName=f,O.badRule=H.rule,O}return 1}if(H=y,y.type==="begin")return be(y);if(y.type==="illegal"&&!b){const O=new Error('Illegal lexeme "'+v+'" for mode "'+(F.scope||"<unnamed>")+'"');throw O.mode=F,O}else if(y.type==="end"){const O=Oe(y);if(O!==Ca)return O}if(y.type==="illegal"&&v==="")return 1;if(_e>1e5&&_e>y.index*3)throw new Error("potential infinite loop, way more iterations than matches");return Q+=v,v.length}const X=B(f);if(!X)throw at(o.replace("{}",f)),new Error('Unknown language: "'+f+'"');const ie=rd(X);let L="",F=S||ie;const re={},U=new l.__emitter(l);Pe();let Q="",Ne=0,Se=0,_e=0,P=!1;try{if(X.__emitTokens)X.__emitTokens(j,U);else{for(F.matcher.considerAll();;){_e++,P?P=!1:F.matcher.considerAll(),F.matcher.lastIndex=Se;const h=F.matcher.exec(j);if(!h)break;const y=j.substring(Se,h.index),v=W(y,h);Se=h.index+v}W(j.substring(Se))}return U.finalize(),L=U.toHTML(),{language:f,value:L,relevance:Ne,illegal:!1,_emitter:U,_top:F}}catch(h){if(h.message&&h.message.includes("Illegal"))return{language:f,value:Zs(j),illegal:!0,relevance:0,_illegalBy:{message:h.message,index:Se,context:j.slice(Se-100,Se+100),mode:h.mode,resultSoFar:L},_emitter:U};if(n)return{language:f,value:Zs(j),illegal:!1,relevance:0,errorRaised:h,_emitter:U,_top:F};throw h}}function p(f){const j={value:Zs(f),illegal:!1,relevance:0,_top:i,_emitter:new l.__emitter(l)};return j._emitter.addText(f),j}function m(f,j){j=j||l.languages||Object.keys(s);const b=p(f),S=j.filter(B).filter(Y).map(ne=>x(ne,f,!1));S.unshift(b);const I=S.sort((ne,oe)=>{if(ne.relevance!==oe.relevance)return oe.relevance-ne.relevance;if(ne.language&&oe.language){if(B(ne.language).supersetOf===oe.language)return 1;if(B(oe.language).supersetOf===ne.language)return-1}return 0}),[K,J]=I,C=K;return C.secondBest=J,C}function w(f,j,b){const S=j&&r[j]||b;f.classList.add("hljs"),f.classList.add(`language-${S}`)}function T(f){let j=null;const b=g(f);if(c(b))return;if(ce("before:highlightElement",{el:f,language:b}),f.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",f);return}if(f.children.length>0&&(l.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(f)),l.throwUnescapedHTML))throw new id("One of your code blocks includes unescaped HTML.",f.innerHTML);j=f;const S=j.textContent,I=b?u(S,{language:b,ignoreIllegals:!0}):m(S);f.innerHTML=I.value,f.dataset.highlighted="yes",w(f,b,I.language),f.result={language:I.language,re:I.relevance,relevance:I.relevance},I.secondBest&&(f.secondBest={language:I.secondBest.language,relevance:I.secondBest.relevance}),ce("after:highlightElement",{el:f,result:I,text:S})}function N(f){l=Sa(l,f)}const A=()=>{R(),ct("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function E(){R(),ct("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let z=!1;function R(){if(document.readyState==="loading"){z=!0;return}document.querySelectorAll(l.cssSelector).forEach(T)}function D(){z&&R()}typeof window<"u"&&window.addEventListener&&window.addEventListener("DOMContentLoaded",D,!1);function _(f,j){let b=null;try{b=j(e)}catch(S){if(at("Language definition for '{}' could not be registered.".replace("{}",f)),n)at(S);else throw S;b=i}b.name||(b.name=f),s[f]=b,b.rawDefinition=j.bind(null,e),b.aliases&&se(b.aliases,{languageName:f})}function G(f){delete s[f];for(const j of Object.keys(r))r[j]===f&&delete r[j]}function Z(){return Object.keys(s)}function B(f){return f=(f||"").toLowerCase(),s[f]||s[r[f]]}function se(f,{languageName:j}){typeof f=="string"&&(f=[f]),f.forEach(b=>{r[b.toLowerCase()]=j})}function Y(f){const j=B(f);return j&&!j.disableAutodetect}function te(f){f["before:highlightBlock"]&&!f["before:highlightElement"]&&(f["before:highlightElement"]=j=>{f["before:highlightBlock"](Object.assign({block:j.el},j))}),f["after:highlightBlock"]&&!f["after:highlightElement"]&&(f["after:highlightElement"]=j=>{f["after:highlightBlock"](Object.assign({block:j.el},j))})}function ue(f){te(f),a.push(f)}function le(f){const j=a.indexOf(f);j!==-1&&a.splice(j,1)}function ce(f,j){const b=f;a.forEach(function(S){S[b]&&S[b](j)})}function k(f){return ct("10.7.0","highlightBlock will be removed entirely in v12.0"),ct("10.7.0","Please use highlightElement now."),T(f)}Object.assign(e,{highlight:u,highlightAuto:m,highlightAll:R,highlightElement:T,highlightBlock:k,configure:N,initHighlighting:A,initHighlightingOnLoad:E,registerLanguage:_,unregisterLanguage:G,listLanguages:Z,getLanguage:B,registerAliases:se,autoDetection:Y,inherit:Sa,addPlugin:ue,removePlugin:le}),e.debugMode=function(){n=!1},e.safeMode=function(){n=!0},e.versionString=nd,e.regex={concat:lt,lookahead:wn,either:Ir,optional:jc,anyNumberOfTimes:vc};for(const f in Qt)typeof Qt[f]=="object"&&vn(Qt[f]);return Object.assign(e,Qt),e},yt=Ln({});yt.newInstance=()=>Ln({});var od=yt;yt.HighlightJS=yt;yt.default=yt;var cd=xc(od);function In(e,s=[]){return e.map(r=>{const a=[...s,...r.properties?r.properties.className:[]];return r.children?In(r.children,a):{text:r.value,classes:a}}).flat()}function Aa(e){return e.value||e.children||[]}function dd(e){return!!cd.getLanguage(e)}function Ra({doc:e,name:s,lowlight:r,defaultLanguage:a}){const n=[];return tr(e,o=>o.type.name===s).forEach(o=>{var i;let l=o.pos+1;const c=o.node.attrs.language||a,g=r.listLanguages(),u=c&&(g.includes(c)||dd(c)||!((i=r.registered)===null||i===void 0)&&i.call(r,c))?Aa(r.highlight(c,o.node.textContent)):Aa(r.highlightAuto(o.node.textContent));In(u).forEach(x=>{const p=l+x.text.length;if(x.classes.length){const m=oi.inline(l,p,{class:x.classes.join(" ")});n.push(m)}l=p})}),ci.create(e,n)}function ud(e){return typeof e=="function"}function hd({name:e,lowlight:s,defaultLanguage:r}){if(!["highlight","highlightAuto","listLanguages"].every(n=>ud(s[n])))throw Error("You should provide an instance of lowlight to use the code-block-lowlight extension");const a=new zs({key:new $s("lowlight"),state:{init:(n,{doc:o})=>Ra({doc:o,name:e,lowlight:s,defaultLanguage:r}),apply:(n,o,i,l)=>{const c=i.selection.$head.parent.type.name,g=l.selection.$head.parent.type.name,u=tr(i.doc,p=>p.type.name===e),x=tr(l.doc,p=>p.type.name===e);return n.docChanged&&([c,g].includes(e)||x.length!==u.length||n.steps.some(p=>p.from!==void 0&&p.to!==void 0&&u.some(m=>m.pos>=p.from&&m.pos+m.node.nodeSize<=p.to)))?Ra({doc:n.doc,name:e,lowlight:s,defaultLanguage:r}):o.map(n.mapping,n.doc)}},props:{decorations(n){return a.getState(n)}}});return a}const gd=li.extend({addOptions(){var e;return{...(e=this.parent)===null||e===void 0?void 0:e.call(this),lowlight:{},languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},addProseMirrorPlugins(){var e;return[...((e=this.parent)===null||e===void 0?void 0:e.call(this))||[],hd({name:this.name,lowlight:this.options.lowlight,defaultLanguage:this.options.defaultLanguage})]}});function On(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(s=>{const r=e[s],a=typeof r;(a==="object"||a==="function")&&!Object.isFrozen(r)&&On(r)}),e}class Ma{constructor(s){s.data===void 0&&(s.data={}),this.data=s.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function Pn(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function Ve(e,...s){const r=Object.create(null);for(const a in e)r[a]=e[a];return s.forEach(function(a){for(const n in a)r[n]=a[n]}),r}const xd="</span>",Ta=e=>!!e.scope,fd=(e,{prefix:s})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const r=e.split(".");return[`${s}${r.shift()}`,...r.map((a,n)=>`${a}${"_".repeat(n+1)}`)].join(" ")}return`${s}${e}`};class md{constructor(s,r){this.buffer="",this.classPrefix=r.classPrefix,s.walk(this)}addText(s){this.buffer+=Pn(s)}openNode(s){if(!Ta(s))return;const r=fd(s.scope,{prefix:this.classPrefix});this.span(r)}closeNode(s){Ta(s)&&(this.buffer+=xd)}value(){return this.buffer}span(s){this.buffer+=`<span class="${s}">`}}const La=(e={})=>{const s={children:[]};return Object.assign(s,e),s};class _r{constructor(){this.rootNode=La(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(s){this.top.children.push(s)}openNode(s){const r=La({scope:s});this.add(r),this.stack.push(r)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(s){return this.constructor._walk(s,this.rootNode)}static _walk(s,r){return typeof r=="string"?s.addText(r):r.children&&(s.openNode(r),r.children.forEach(a=>this._walk(s,a)),s.closeNode(r)),s}static _collapse(s){typeof s!="string"&&s.children&&(s.children.every(r=>typeof r=="string")?s.children=[s.children.join("")]:s.children.forEach(r=>{_r._collapse(r)}))}}class pd extends _r{constructor(s){super(),this.options=s}addText(s){s!==""&&this.add(s)}startScope(s){this.openNode(s)}endScope(){this.closeNode()}__addSublanguage(s,r){const a=s.root;r&&(a.scope=`language:${r}`),this.add(a)}toHTML(){return new md(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function Ht(e){return e?typeof e=="string"?e:e.source:null}function _n(e){return ot("(?=",e,")")}function yd(e){return ot("(?:",e,")*")}function bd(e){return ot("(?:",e,")?")}function ot(...e){return e.map(r=>Ht(r)).join("")}function vd(e){const s=e[e.length-1];return typeof s=="object"&&s.constructor===Object?(e.splice(e.length-1,1),s):{}}function zr(...e){return"("+(vd(e).capture?"":"?:")+e.map(a=>Ht(a)).join("|")+")"}function zn(e){return new RegExp(e.toString()+"|").exec("").length-1}function jd(e,s){const r=e&&e.exec(s);return r&&r.index===0}const kd=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function $r(e,{joinWith:s}){let r=0;return e.map(a=>{r+=1;const n=r;let o=Ht(a),i="";for(;o.length>0;){const l=kd.exec(o);if(!l){i+=o;break}i+=o.substring(0,l.index),o=o.substring(l.index+l[0].length),l[0][0]==="\\"&&l[1]?i+="\\"+String(Number(l[1])+n):(i+=l[0],l[0]==="("&&r++)}return i}).map(a=>`(${a})`).join(s)}const wd=/\b\B/,$n="[a-zA-Z]\\w*",Dr="[a-zA-Z_]\\w*",Dn="\\b\\d+(\\.\\d+)?",Un="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Hn="\\b(0b[01]+)",Nd="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",Ed=(e={})=>{const s=/^#![ ]*\//;return e.binary&&(e.begin=ot(s,/.*\b/,e.binary,/\b.*/)),Ve({scope:"meta",begin:s,end:/$/,relevance:0,"on:begin":(r,a)=>{r.index!==0&&a.ignoreMatch()}},e)},Bt={begin:"\\\\[\\s\\S]",relevance:0},Sd={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[Bt]},Cd={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[Bt]},Ad={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Ws=function(e,s,r={}){const a=Ve({scope:"comment",begin:e,end:s,contains:[]},r);a.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const n=zr("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return a.contains.push({begin:ot(/[ ]+/,"(",n,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),a},Rd=Ws("//","$"),Md=Ws("/\\*","\\*/"),Td=Ws("#","$"),Ld={scope:"number",begin:Dn,relevance:0},Id={scope:"number",begin:Un,relevance:0},Od={scope:"number",begin:Hn,relevance:0},Pd={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[Bt,{begin:/\[/,end:/\]/,relevance:0,contains:[Bt]}]},_d={scope:"title",begin:$n,relevance:0},zd={scope:"title",begin:Dr,relevance:0},$d={begin:"\\.\\s*"+Dr,relevance:0},Dd=function(e){return Object.assign(e,{"on:begin":(s,r)=>{r.data._beginMatch=s[1]},"on:end":(s,r)=>{r.data._beginMatch!==s[1]&&r.ignoreMatch()}})};var Zt=Object.freeze({__proto__:null,APOS_STRING_MODE:Sd,BACKSLASH_ESCAPE:Bt,BINARY_NUMBER_MODE:Od,BINARY_NUMBER_RE:Hn,COMMENT:Ws,C_BLOCK_COMMENT_MODE:Md,C_LINE_COMMENT_MODE:Rd,C_NUMBER_MODE:Id,C_NUMBER_RE:Un,END_SAME_AS_BEGIN:Dd,HASH_COMMENT_MODE:Td,IDENT_RE:$n,MATCH_NOTHING_RE:wd,METHOD_GUARD:$d,NUMBER_MODE:Ld,NUMBER_RE:Dn,PHRASAL_WORDS_MODE:Ad,QUOTE_STRING_MODE:Cd,REGEXP_MODE:Pd,RE_STARTERS_RE:Nd,SHEBANG:Ed,TITLE_MODE:_d,UNDERSCORE_IDENT_RE:Dr,UNDERSCORE_TITLE_MODE:zd});function Ud(e,s){e.input[e.index-1]==="."&&s.ignoreMatch()}function Hd(e,s){e.className!==void 0&&(e.scope=e.className,delete e.className)}function Bd(e,s){s&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=Ud,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function Wd(e,s){Array.isArray(e.illegal)&&(e.illegal=zr(...e.illegal))}function Fd(e,s){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function Kd(e,s){e.relevance===void 0&&(e.relevance=1)}const qd=(e,s)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const r=Object.assign({},e);Object.keys(e).forEach(a=>{delete e[a]}),e.keywords=r.keywords,e.begin=ot(r.beforeMatch,_n(r.begin)),e.starts={relevance:0,contains:[Object.assign(r,{endsParent:!0})]},e.relevance=0,delete r.beforeMatch},Gd=["of","and","for","in","not","or","if","then","parent","list","value"],Vd="keyword";function Bn(e,s,r=Vd){const a=Object.create(null);return typeof e=="string"?n(r,e.split(" ")):Array.isArray(e)?n(r,e):Object.keys(e).forEach(function(o){Object.assign(a,Bn(e[o],s,o))}),a;function n(o,i){s&&(i=i.map(l=>l.toLowerCase())),i.forEach(function(l){const c=l.split("|");a[c[0]]=[o,Jd(c[0],c[1])]})}}function Jd(e,s){return s?Number(s):Xd(e)?0:1}function Xd(e){return Gd.includes(e.toLowerCase())}const Ia={},nt=e=>{console.error(e)},Oa=(e,...s)=>{console.log(`WARN: ${e}`,...s)},dt=(e,s)=>{Ia[`${e}/${s}`]||(console.log(`Deprecated as of ${e}. ${s}`),Ia[`${e}/${s}`]=!0)},_s=new Error;function Wn(e,s,{key:r}){let a=0;const n=e[r],o={},i={};for(let l=1;l<=s.length;l++)i[l+a]=n[l],o[l+a]=!0,a+=zn(s[l-1]);e[r]=i,e[r]._emit=o,e[r]._multi=!0}function Qd(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw nt("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),_s;if(typeof e.beginScope!="object"||e.beginScope===null)throw nt("beginScope must be object"),_s;Wn(e,e.begin,{key:"beginScope"}),e.begin=$r(e.begin,{joinWith:""})}}function Zd(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw nt("skip, excludeEnd, returnEnd not compatible with endScope: {}"),_s;if(typeof e.endScope!="object"||e.endScope===null)throw nt("endScope must be object"),_s;Wn(e,e.end,{key:"endScope"}),e.end=$r(e.end,{joinWith:""})}}function Yd(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function eu(e){Yd(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),Qd(e),Zd(e)}function tu(e){function s(i,l){return new RegExp(Ht(i),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(l?"g":""))}class r{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(l,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,l]),this.matchAt+=zn(l)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const l=this.regexes.map(c=>c[1]);this.matcherRe=s($r(l,{joinWith:"|"}),!0),this.lastIndex=0}exec(l){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(l);if(!c)return null;const g=c.findIndex((x,p)=>p>0&&x!==void 0),u=this.matchIndexes[g];return c.splice(0,g),Object.assign(c,u)}}class a{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(l){if(this.multiRegexes[l])return this.multiRegexes[l];const c=new r;return this.rules.slice(l).forEach(([g,u])=>c.addRule(g,u)),c.compile(),this.multiRegexes[l]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(l,c){this.rules.push([l,c]),c.type==="begin"&&this.count++}exec(l){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let g=c.exec(l);if(this.resumingScanAtSamePosition()&&!(g&&g.index===this.lastIndex)){const u=this.getMatcher(0);u.lastIndex=this.lastIndex+1,g=u.exec(l)}return g&&(this.regexIndex+=g.position+1,this.regexIndex===this.count&&this.considerAll()),g}}function n(i){const l=new a;return i.contains.forEach(c=>l.addRule(c.begin,{rule:c,type:"begin"})),i.terminatorEnd&&l.addRule(i.terminatorEnd,{type:"end"}),i.illegal&&l.addRule(i.illegal,{type:"illegal"}),l}function o(i,l){const c=i;if(i.isCompiled)return c;[Hd,Fd,eu,qd].forEach(u=>u(i,l)),e.compilerExtensions.forEach(u=>u(i,l)),i.__beforeBegin=null,[Bd,Wd,Kd].forEach(u=>u(i,l)),i.isCompiled=!0;let g=null;return typeof i.keywords=="object"&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),g=i.keywords.$pattern,delete i.keywords.$pattern),g=g||/\w+/,i.keywords&&(i.keywords=Bn(i.keywords,e.case_insensitive)),c.keywordPatternRe=s(g,!0),l&&(i.begin||(i.begin=/\B|\b/),c.beginRe=s(c.begin),!i.end&&!i.endsWithParent&&(i.end=/\B|\b/),i.end&&(c.endRe=s(c.end)),c.terminatorEnd=Ht(c.end)||"",i.endsWithParent&&l.terminatorEnd&&(c.terminatorEnd+=(i.end?"|":"")+l.terminatorEnd)),i.illegal&&(c.illegalRe=s(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(u){return su(u==="self"?i:u)})),i.contains.forEach(function(u){o(u,c)}),i.starts&&o(i.starts,l),c.matcher=n(c),c}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=Ve(e.classNameAliases||{}),o(e)}function Fn(e){return e?e.endsWithParent||Fn(e.starts):!1}function su(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(s){return Ve(e,{variants:null},s)})),e.cachedVariants?e.cachedVariants:Fn(e)?Ve(e,{starts:e.starts?Ve(e.starts):null}):Object.isFrozen(e)?Ve(e):e}var ru="11.11.1";class au extends Error{constructor(s,r){super(s),this.name="HTMLInjectionError",this.html=r}}const Ys=Pn,Pa=Ve,_a=Symbol("nomatch"),nu=7,Kn=function(e){const s=Object.create(null),r=Object.create(null),a=[];let n=!0;const o="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let l={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:pd};function c(k){return l.noHighlightRe.test(k)}function g(k){let f=k.className+" ";f+=k.parentNode?k.parentNode.className:"";const j=l.languageDetectRe.exec(f);if(j){const b=Z(j[1]);return b||(Oa(o.replace("{}",j[1])),Oa("Falling back to no-highlight mode for this block.",k)),b?j[1]:"no-highlight"}return f.split(/\s+/).find(b=>c(b)||Z(b))}function u(k,f,j){let b="",S="";typeof f=="object"?(b=k,j=f.ignoreIllegals,S=f.language):(dt("10.7.0","highlight(lang, code, ...args) has been deprecated."),dt("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),S=k,b=f),j===void 0&&(j=!0);const I={code:b,language:S};le("before:highlight",I);const K=I.result?I.result:x(I.language,I.code,j);return K.code=I.code,le("after:highlight",K),K}function x(k,f,j,b){const S=Object.create(null);function I(P,h){return P.keywords[h]}function K(){if(!L.keywords){re.addText(U);return}let P=0;L.keywordPatternRe.lastIndex=0;let h=L.keywordPatternRe.exec(U),y="";for(;h;){y+=U.substring(P,h.index);const v=W.case_insensitive?h[0].toLowerCase():h[0],O=I(L,v);if(O){const[q,V]=O;if(re.addText(y),y="",S[v]=(S[v]||0)+1,S[v]<=nu&&(Q+=V),q.startsWith("_"))y+=h[0];else{const Ee=W.classNameAliases[q]||q;ne(h[0],Ee)}}else y+=h[0];P=L.keywordPatternRe.lastIndex,h=L.keywordPatternRe.exec(U)}y+=U.substring(P),re.addText(y)}function J(){if(U==="")return;let P=null;if(typeof L.subLanguage=="string"){if(!s[L.subLanguage]){re.addText(U);return}P=x(L.subLanguage,U,!0,F[L.subLanguage]),F[L.subLanguage]=P._top}else P=m(U,L.subLanguage.length?L.subLanguage:null);L.relevance>0&&(Q+=P.relevance),re.__addSublanguage(P._emitter,P.language)}function C(){L.subLanguage!=null?J():K(),U=""}function ne(P,h){P!==""&&(re.startScope(h),re.addText(P),re.endScope())}function oe(P,h){let y=1;const v=h.length-1;for(;y<=v;){if(!P._emit[y]){y++;continue}const O=W.classNameAliases[P[y]]||P[y],q=h[y];O?ne(q,O):(U=q,K(),U=""),y++}}function ke(P,h){return P.scope&&typeof P.scope=="string"&&re.openNode(W.classNameAliases[P.scope]||P.scope),P.beginScope&&(P.beginScope._wrap?(ne(U,W.classNameAliases[P.beginScope._wrap]||P.beginScope._wrap),U=""):P.beginScope._multi&&(oe(P.beginScope,h),U="")),L=Object.create(P,{parent:{value:L}}),L}function de(P,h,y){let v=jd(P.endRe,y);if(v){if(P["on:end"]){const O=new Ma(P);P["on:end"](h,O),O.isMatchIgnored&&(v=!1)}if(v){for(;P.endsParent&&P.parent;)P=P.parent;return P}}if(P.endsWithParent)return de(P.parent,h,y)}function ge(P){return L.matcher.regexIndex===0?(U+=P[0],1):(_e=!0,0)}function we(P){const h=P[0],y=P.rule,v=new Ma(y),O=[y.__beforeBegin,y["on:begin"]];for(const q of O)if(q&&(q(P,v),v.isMatchIgnored))return ge(h);return y.skip?U+=h:(y.excludeBegin&&(U+=h),C(),!y.returnBegin&&!y.excludeBegin&&(U=h)),ke(y,P),y.returnBegin?0:h.length}function be(P){const h=P[0],y=f.substring(P.index),v=de(L,P,y);if(!v)return _a;const O=L;L.endScope&&L.endScope._wrap?(C(),ne(h,L.endScope._wrap)):L.endScope&&L.endScope._multi?(C(),oe(L.endScope,P)):O.skip?U+=h:(O.returnEnd||O.excludeEnd||(U+=h),C(),O.excludeEnd&&(U=h));do L.scope&&re.closeNode(),!L.skip&&!L.subLanguage&&(Q+=L.relevance),L=L.parent;while(L!==v.parent);return v.starts&&ke(v.starts,P),O.returnEnd?0:h.length}function Oe(){const P=[];for(let h=L;h!==W;h=h.parent)h.scope&&P.unshift(h.scope);P.forEach(h=>re.openNode(h))}let Pe={};function H(P,h){const y=h&&h[0];if(U+=P,y==null)return C(),0;if(Pe.type==="begin"&&h.type==="end"&&Pe.index===h.index&&y===""){if(U+=f.slice(h.index,h.index+1),!n){const v=new Error(`0 width match regex (${k})`);throw v.languageName=k,v.badRule=Pe.rule,v}return 1}if(Pe=h,h.type==="begin")return we(h);if(h.type==="illegal"&&!j){const v=new Error('Illegal lexeme "'+y+'" for mode "'+(L.scope||"<unnamed>")+'"');throw v.mode=L,v}else if(h.type==="end"){const v=be(h);if(v!==_a)return v}if(h.type==="illegal"&&y==="")return U+=`
`,1;if(Se>1e5&&Se>h.index*3)throw new Error("potential infinite loop, way more iterations than matches");return U+=y,y.length}const W=Z(k);if(!W)throw nt(o.replace("{}",k)),new Error('Unknown language: "'+k+'"');const X=tu(W);let ie="",L=b||X;const F={},re=new l.__emitter(l);Oe();let U="",Q=0,Ne=0,Se=0,_e=!1;try{if(W.__emitTokens)W.__emitTokens(f,re);else{for(L.matcher.considerAll();;){Se++,_e?_e=!1:L.matcher.considerAll(),L.matcher.lastIndex=Ne;const P=L.matcher.exec(f);if(!P)break;const h=f.substring(Ne,P.index),y=H(h,P);Ne=P.index+y}H(f.substring(Ne))}return re.finalize(),ie=re.toHTML(),{language:k,value:ie,relevance:Q,illegal:!1,_emitter:re,_top:L}}catch(P){if(P.message&&P.message.includes("Illegal"))return{language:k,value:Ys(f),illegal:!0,relevance:0,_illegalBy:{message:P.message,index:Ne,context:f.slice(Ne-100,Ne+100),mode:P.mode,resultSoFar:ie},_emitter:re};if(n)return{language:k,value:Ys(f),illegal:!1,relevance:0,errorRaised:P,_emitter:re,_top:L};throw P}}function p(k){const f={value:Ys(k),illegal:!1,relevance:0,_top:i,_emitter:new l.__emitter(l)};return f._emitter.addText(k),f}function m(k,f){f=f||l.languages||Object.keys(s);const j=p(k),b=f.filter(Z).filter(se).map(C=>x(C,k,!1));b.unshift(j);const S=b.sort((C,ne)=>{if(C.relevance!==ne.relevance)return ne.relevance-C.relevance;if(C.language&&ne.language){if(Z(C.language).supersetOf===ne.language)return 1;if(Z(ne.language).supersetOf===C.language)return-1}return 0}),[I,K]=S,J=I;return J.secondBest=K,J}function w(k,f,j){const b=f&&r[f]||j;k.classList.add("hljs"),k.classList.add(`language-${b}`)}function T(k){let f=null;const j=g(k);if(c(j))return;if(le("before:highlightElement",{el:k,language:j}),k.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",k);return}if(k.children.length>0&&(l.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(k)),l.throwUnescapedHTML))throw new au("One of your code blocks includes unescaped HTML.",k.innerHTML);f=k;const b=f.textContent,S=j?u(b,{language:j,ignoreIllegals:!0}):m(b);k.innerHTML=S.value,k.dataset.highlighted="yes",w(k,j,S.language),k.result={language:S.language,re:S.relevance,relevance:S.relevance},S.secondBest&&(k.secondBest={language:S.secondBest.language,relevance:S.secondBest.relevance}),le("after:highlightElement",{el:k,result:S,text:b})}function N(k){l=Pa(l,k)}const A=()=>{R(),dt("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function E(){R(),dt("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let z=!1;function R(){function k(){R()}if(document.readyState==="loading"){z||window.addEventListener("DOMContentLoaded",k,!1),z=!0;return}document.querySelectorAll(l.cssSelector).forEach(T)}function D(k,f){let j=null;try{j=f(e)}catch(b){if(nt("Language definition for '{}' could not be registered.".replace("{}",k)),n)nt(b);else throw b;j=i}j.name||(j.name=k),s[k]=j,j.rawDefinition=f.bind(null,e),j.aliases&&B(j.aliases,{languageName:k})}function _(k){delete s[k];for(const f of Object.keys(r))r[f]===k&&delete r[f]}function G(){return Object.keys(s)}function Z(k){return k=(k||"").toLowerCase(),s[k]||s[r[k]]}function B(k,{languageName:f}){typeof k=="string"&&(k=[k]),k.forEach(j=>{r[j.toLowerCase()]=f})}function se(k){const f=Z(k);return f&&!f.disableAutodetect}function Y(k){k["before:highlightBlock"]&&!k["before:highlightElement"]&&(k["before:highlightElement"]=f=>{k["before:highlightBlock"](Object.assign({block:f.el},f))}),k["after:highlightBlock"]&&!k["after:highlightElement"]&&(k["after:highlightElement"]=f=>{k["after:highlightBlock"](Object.assign({block:f.el},f))})}function te(k){Y(k),a.push(k)}function ue(k){const f=a.indexOf(k);f!==-1&&a.splice(f,1)}function le(k,f){const j=k;a.forEach(function(b){b[j]&&b[j](f)})}function ce(k){return dt("10.7.0","highlightBlock will be removed entirely in v12.0"),dt("10.7.0","Please use highlightElement now."),T(k)}Object.assign(e,{highlight:u,highlightAuto:m,highlightAll:R,highlightElement:T,highlightBlock:ce,configure:N,initHighlighting:A,initHighlightingOnLoad:E,registerLanguage:D,unregisterLanguage:_,listLanguages:G,getLanguage:Z,registerAliases:B,autoDetection:se,inherit:Pa,addPlugin:te,removePlugin:ue}),e.debugMode=function(){n=!1},e.safeMode=function(){n=!0},e.versionString=ru,e.regex={concat:ot,lookahead:_n,either:zr,optional:bd,anyNumberOfTimes:yd};for(const k in Zt)typeof Zt[k]=="object"&&On(Zt[k]);return Object.assign(e,Zt),e},bt=Kn({});bt.newInstance=()=>Kn({});var iu=bt;bt.HighlightJS=bt;bt.default=bt;const lu=Xn(iu),za={},ou="hljs-";function cu(e){const s=lu.newInstance();return e&&o(e),{highlight:r,highlightAuto:a,listLanguages:n,register:o,registerAlias:i,registered:l};function r(c,g,u){const x=u||za,p=typeof x.prefix=="string"?x.prefix:ou;if(!s.getLanguage(c))throw new Error("Unknown language: `"+c+"` is not registered");s.configure({__emitter:du,classPrefix:p});const m=s.highlight(g,{ignoreIllegals:!0,language:c});if(m.errorRaised)throw new Error("Could not highlight with `Highlight.js`",{cause:m.errorRaised});const w=m._emitter.root,T=w.data;return T.language=m.language,T.relevance=m.relevance,w}function a(c,g){const x=(g||za).subset||n();let p=-1,m=0,w;for(;++p<x.length;){const T=x[p];if(!s.getLanguage(T))continue;const N=r(T,c,g);N.data&&N.data.relevance!==void 0&&N.data.relevance>m&&(m=N.data.relevance,w=N)}return w||{type:"root",children:[],data:{language:void 0,relevance:m}}}function n(){return s.listLanguages()}function o(c,g){if(typeof c=="string")s.registerLanguage(c,g);else{let u;for(u in c)Object.hasOwn(c,u)&&s.registerLanguage(u,c[u])}}function i(c,g){if(typeof c=="string")s.registerAliases(typeof g=="string"?g:[...g],{languageName:c});else{let u;for(u in c)if(Object.hasOwn(c,u)){const x=c[u];s.registerAliases(typeof x=="string"?x:[...x],{languageName:u})}}}function l(c){return!!s.getLanguage(c)}}class du{constructor(s){this.options=s,this.root={type:"root",children:[],data:{language:void 0,relevance:0}},this.stack=[this.root]}addText(s){if(s==="")return;const r=this.stack[this.stack.length-1],a=r.children[r.children.length-1];a&&a.type==="text"?a.value+=s:r.children.push({type:"text",value:s})}startScope(s){this.openNode(String(s))}endScope(){this.closeNode()}__addSublanguage(s,r){const a=this.stack[this.stack.length-1],n=s.root.children;r?a.children.push({type:"element",tagName:"span",properties:{className:[r]},children:n}):a.children.push(...n)}openNode(s){const r=this,a=s.split(".").map(function(i,l){return l?i+"_".repeat(l):r.options.classPrefix+i}),n=this.stack[this.stack.length-1],o={type:"element",tagName:"span",properties:{className:a},children:[]};n.children.push(o),this.stack.push(o)}closeNode(){this.stack.pop()}finalize(){}toHTML(){return""}}const uu=cu();function me({onClick:e,isActive:s,disabled:r,loading:a,title:n,children:o,variant:i="default"}){const l="p-2 rounded-md transition-all duration-200 flex items-center justify-center",c={default:s?"bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300":"hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-400",primary:"bg-blue-600 hover:bg-blue-700 text-white",secondary:"bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300"};return t.jsx("button",{onClick:e,disabled:r||a,title:n,className:`${l} ${c[i]} ${r?"opacity-50 cursor-not-allowed":""}`,children:a?t.jsx(mt,{size:16,className:"animate-spin"}):o})}function hu({content:e,onChange:s,placeholder:r="开始写作...",coverImage:a,onCoverImageChange:n,autoSave:o=!1,onAutoSave:i}){const[l,c]=d.useState(!1),[g,u]=d.useState(!1),[x,p]=d.useState(!1),[m,w]=d.useState(!1),[T,N]=d.useState(!1),[A,E]=d.useState([]),[z,R]=d.useState(!1),[D,_]=d.useState(!1),[G,Z]=d.useState(!1),[B,se]=d.useState(!1),[Y,te]=d.useState(!1),[ue,le]=d.useState(!1),[ce,k]=d.useState(0),[f,j]=d.useState(null),[b,S]=d.useState(!1),I=Nr(),K=d.useCallback(async H=>{if(!(!o||!i||b))try{S(!0),await i(H),j(new Date)}catch(W){console.error("自动保存失败:",W)}finally{S(!1)}},[o,i,b]);d.useEffect(()=>{if(!e||!o)return;const H=setTimeout(()=>{K(e)},3e3);return()=>clearTimeout(H)},[e,K,o]),d.useEffect(()=>{if(e){const H=e.replace(/<[^>]*>/g,"");k(H.length)}else k(0)},[e]);const J=async H=>{const W=new FormData;W.append("file",H);try{const X=await Te(fe.uploadImage,{method:"POST",body:W,headers:{}});return(await ve(X)).url}catch(X){throw console.error("图片上传失败:",X),new Error("图片上传失败")}},C=di({extensions:[ui,Fo.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),gc.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-600 dark:text-blue-400 underline"}}),gd.configure({lowlight:uu,HTMLAttributes:{class:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"}})],content:e,onUpdate:({editor:H})=>{s(H.getHTML())},editorProps:{attributes:{class:`prose prose-gray dark:prose-dark max-w-none focus:outline-none transition-all duration-200 ${Y?"min-h-[calc(100vh-200px)]":"min-h-[400px]"} p-6`,"data-placeholder":r,spellcheck:"false"},handleKeyDown:(H,W)=>{if(W.ctrlKey||W.metaKey)switch(W.key){case"s":return W.preventDefault(),o&&i&&K(e),!0;case"b":return W.preventDefault(),C==null||C.chain().focus().toggleBold().run(),!0;case"i":return W.preventDefault(),C==null||C.chain().focus().toggleItalic().run(),!0;case"k":return W.preventDefault(),ge(),!0;case"Enter":if(W.shiftKey)return W.preventDefault(),C==null||C.chain().focus().setHardBreak().run(),!0;break}return!1},handlePaste:(H,W,X)=>{var F;const L=Array.from(((F=W.clipboardData)==null?void 0:F.items)||[]).filter(re=>re.type.indexOf("image")===0);return L.length>0?(W.preventDefault(),L.forEach(async re=>{const U=re.getAsFile();if(U)try{p(!0);const Q=await J(U);C==null||C.chain().focus().setImage({src:Q}).run(),I.success("图片上传成功","图片已插入到文章中")}catch(Q){console.error("图片上传失败:",Q),I.error("图片上传失败","请检查网络连接后重试")}finally{p(!1)}}),!0):!1},handleDrop:(H,W,X,ie)=>{var re;const F=Array.from(((re=W.dataTransfer)==null?void 0:re.files)||[]).filter(U=>U.type.indexOf("image")===0);return F.length>0?(W.preventDefault(),F.forEach(async U=>{try{p(!0);const Q=await J(U);C==null||C.chain().focus().setImage({src:Q}).run(),I.success("图片上传成功","图片已插入到文章中")}catch(Q){console.error("图片上传失败:",Q),I.error("图片上传失败","请检查网络连接后重试")}finally{p(!1)}}),!0):!1}}}),ne=async()=>{if(!C)return;const H=C.state.selection.empty?"现代科技风格的文章配图":C.state.doc.textBetween(C.state.selection.from,C.state.selection.to),W=window.prompt(`AI智能配图 - 请描述您想要的配图风格和内容：

提示：可以描述场景、颜色、风格等
例如：蓝色科技风格、自然风光、商务办公等`,H);if(W===null)return;const X=W.trim()||H;c(!0);const ie=I.loading("AI图片生成中...","通常需要15-45秒，请耐心等待");try{console.log("开始生成AI配图，提示词:",X),console.log("API端点:",fe.aiImage);const L=new AbortController,F=setTimeout(()=>L.abort(),6e4),re=await Te(fe.aiImage,{method:"POST",body:JSON.stringify({prompt:X,style:"tech"}),signal:L.signal});clearTimeout(F),console.log("API响应状态:",re.status),console.log("API响应头:",Object.fromEntries(re.headers.entries()));const U=await ve(re);if(console.log("API响应数据:",U),U.success&&U.images&&U.images.length>0){const Q=U.images[0].url;console.log("插入AI生成图片URL:",Q),C.chain().focus().setImage({src:Q}).run(),I.update(ie,{type:"success",title:"AI配图生成成功！",message:"图片已插入到文章中",duration:3e3}),w(!1)}else throw console.error("API响应数据格式错误:",U),new Error("AI配图生成失败：没有返回图片")}catch(L){console.error("AI配图生成错误:",L),console.error("错误详情:",{name:L==null?void 0:L.name,message:L==null?void 0:L.message,stack:L==null?void 0:L.stack}),(L==null?void 0:L.name)==="AbortError"?I.update(ie,{type:"warning",title:"AI生成超时",message:"AI图片生成通常需要15-45秒，请稍后重试",duration:5e3}):L instanceof ls?I.update(ie,{type:"error",title:"AI配图生成失败",message:L.message,duration:5e3}):I.update(ie,{type:"error",title:"AI配图生成失败",message:(L==null?void 0:L.message)||"未知错误",duration:5e3})}finally{c(!1)}},oe=async()=>{if(!C)return;const H=window.confirm(`选择图片类型：

确定 = 一般图片（适合所有场景）
取消 = R18图片（仅限成人内容）

注意：所有图片均来自Pixiv艺术创作平台`);if(H===null)return;const W=H?"safe":"r18";u(!0);try{console.log("开始获取Mossia API图片，类型:",W);const X=await Te(fe.aiImage,{method:"POST",body:JSON.stringify({type:"mossia",source:W,num:9})}),ie=await ve(X);if(console.log("Mossia API响应数据:",ie),ie.success&&ie.images&&ie.images.length>0){const L=ie.images.filter(U=>U.url&&U.url.trim()!=="");if(L.length===0)throw new Error("没有可用的图片");E(L),N(!0),w(!1);const F=ie.cached?"（来自缓存，快速加载）":"（实时获取）",re=W==="r18"?"R18":"一般";console.log(`获取到 ${L.length} 张${re}图片${F}`)}else throw new Error("Mossia API没有返回图片")}catch(X){console.error("Mossia图片获取错误:",X),I.error("配图获取失败",(X==null?void 0:X.message)||"未知错误")}finally{u(!1)}},ke=H=>{console.log("选择图片URL:",H),B?(n==null||n(H),N(!1),E([]),R(!1),se(!1),I.success("封面图片设置成功！","封面已更新")):C&&(C.chain().focus().setImage({src:H}).run(),N(!1),I.success("配图添加成功！","图片已插入到文章中"))},de=()=>{N(!1),E([]),B&&(se(!1),R(!1))},ge=()=>{if(!C)return;const H=window.prompt("请输入链接地址:");H&&C.chain().focus().setLink({href:H}).run()},we=()=>{if(!C)return;const H=window.prompt("请输入图片地址:");H&&C.chain().focus().setImage({src:H}).run()},be=async()=>{const H=window.confirm(`选择封面图片类型：

确定 = 一般图片（适合所有场景）
取消 = R18图片（仅限成人内容）

注意：所有图片均来自Pixiv艺术创作平台`);if(H===null)return;const W=H?"safe":"r18";try{_(!0),console.log("开始获取封面图片，类型:",W);const X=await Te(fe.aiImage,{method:"POST",body:JSON.stringify({type:"mossia",source:W,num:9})}),ie=await ve(X);if(console.log("封面图片API响应数据:",ie),ie.success&&ie.images&&ie.images.length>0){const L=ie.images.filter(U=>U.url&&U.url.trim()!=="");if(console.log("有效图片数量:",L.length),L.length===0)throw new Error("没有可用的图片");E(L),se(!0),N(!0);const F=ie.cached?"（来自缓存）":"（实时获取）",re=W==="r18"?"R18":"一般";console.log(`获取到${L.length}张${re}封面图片${F}`)}else throw console.error("API响应格式错误:",ie),new Error("API没有返回可用的图片")}catch(X){console.error("获取精美封面失败:",X),alert(`获取精美封面失败: ${(X==null?void 0:X.message)||"未知错误"}`)}finally{_(!1)}},Oe=async()=>{const H=window.prompt(`生成文章封面图 - 请描述封面图的风格和内容：

提示：可以描述主题、颜色、风格等
例如：现代科技、蓝色调、商务风格`,"现代简约风格的文章封面");if(H!==null)try{Z(!0);const W=new AbortController,X=setTimeout(()=>W.abort(),6e4),ie=await Te(fe.aiImage,{method:"POST",body:JSON.stringify({prompt:H.trim()||"现代简约风格的文章封面",style:"tech"}),signal:W.signal});clearTimeout(X);const L=await ve(ie);L.url&&(n==null||n(L.url),R(!1),alert("AI封面生成成功！"))}catch(W){console.error("AI生成封面失败:",W),W instanceof ls?alert(`AI生成封面失败: ${W.message}`):alert("AI生成封面失败，请重试")}finally{Z(!1)}},Pe=()=>{n==null||n("")};return C?t.jsxs("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[t.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:[t.jsxs("div",{className:"flex items-center justify-between mb-3",children:[t.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"文章封面图"}),a&&t.jsx("button",{onClick:Pe,className:"text-red-600 hover:text-red-700 text-sm",title:"删除封面图",children:"删除封面"})]}),a?t.jsxs("div",{className:"relative",children:[t.jsx("img",{src:a,alt:"文章封面",className:"w-full h-32 object-cover rounded-lg",onError:H=>{const W=H.target;W.style.display="none"}}),t.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center",children:t.jsx("div",{className:"opacity-0 hover:opacity-100 flex space-x-2",children:t.jsx("button",{onClick:()=>R(!z),className:"px-3 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"更换封面"})})})]}):t.jsxs("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center",children:[t.jsx(Yt,{size:32,className:"mx-auto text-gray-400 mb-2"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4",children:"添加文章封面图，让您的文章更吸引人"}),t.jsx("button",{onClick:()=>R(!z),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"添加封面图"})]}),z&&t.jsx("div",{className:"mt-4 p-4 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600",children:t.jsxs("div",{className:"flex flex-wrap gap-3 items-center",children:[t.jsxs("button",{onClick:be,disabled:D,className:"flex items-center space-x-2 px-3 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 disabled:opacity-50 transition-colors",children:[D?t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):t.jsx(Yt,{size:16}),t.jsx("span",{children:D?"获取中":"精美图片"})]}),t.jsxs("button",{onClick:Oe,disabled:G||D,className:"flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 transition-colors",children:[G?t.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):t.jsx(Ct,{size:16}),t.jsx("span",{children:G?"AI生成中":"AI生成"})]}),t.jsx("button",{onClick:()=>R(!1),className:"px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors",children:"取消"})]})})]}),t.jsxs("div",{className:"flex flex-wrap items-center justify-between gap-2 p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:[t.jsxs("div",{className:"flex flex-wrap items-center gap-1",children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>C.chain().focus().toggleBold().run(),isActive:C.isActive("bold"),title:"粗体 (Ctrl+B)",children:t.jsx(Nl,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleItalic().run(),isActive:C.isActive("italic"),title:"斜体 (Ctrl+I)",children:t.jsx($l,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleStrike().run(),isActive:C.isActive("strike"),title:"删除线",children:t.jsx(no,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleCode().run(),isActive:C.isActive("code"),title:"行内代码",children:t.jsx(Cl,{size:16})})]}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>C.chain().focus().toggleHeading({level:1}).run(),isActive:C.isActive("heading",{level:1}),title:"标题1",children:t.jsx(Ol,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleHeading({level:2}).run(),isActive:C.isActive("heading",{level:2}),title:"标题2",children:t.jsx(Pl,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleHeading({level:3}).run(),isActive:C.isActive("heading",{level:3}),title:"标题3",children:t.jsx(_l,{size:16})})]}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>C.chain().focus().toggleBulletList().run(),isActive:C.isActive("bulletList"),title:"无序列表",children:t.jsx(tn,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleOrderedList().run(),isActive:C.isActive("orderedList"),title:"有序列表",children:t.jsx(Ul,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().toggleBlockquote().run(),isActive:C.isActive("blockquote"),title:"引用",children:t.jsx(Ql,{size:16})})]}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:ge,title:"插入链接 (Ctrl+K)",children:t.jsx(Dl,{size:16})}),t.jsx(me,{onClick:we,title:"插入图片",children:t.jsx(Yt,{size:16})}),t.jsx(me,{onClick:()=>{const H=document.createElement("input");H.type="file",H.accept="image/*",H.onchange=async W=>{var ie;const X=(ie=W.target.files)==null?void 0:ie[0];if(X)try{p(!0);const L=await J(X);C.chain().focus().setImage({src:L}).run(),I.success("图片上传成功","图片已插入到文章中")}catch(L){console.error("图片上传失败:",L),I.error("图片上传失败","请检查网络连接后重试")}finally{p(!1)}},H.click()},loading:x,title:"上传图片",children:t.jsx(uo,{size:16})})]})]}),t.jsxs("div",{className:"flex items-center gap-2",children:[m?t.jsxs("div",{className:"flex items-center gap-1 p-1 bg-gray-100 dark:bg-gray-700 rounded-lg",children:[t.jsx(me,{onClick:ne,disabled:l||g,loading:l,variant:"primary",title:l?"AI生成中...":"AI智能生成",children:t.jsx(Ct,{size:14})}),t.jsx(me,{onClick:oe,disabled:l||g,loading:g,variant:"secondary",title:g?"获取中...":"精美图片",children:t.jsx(Vl,{size:14})}),t.jsx(me,{onClick:()=>w(!1),title:"关闭",children:"×"})]}):t.jsx(me,{onClick:()=>w(!0),disabled:l||g,variant:"primary",title:"文章配图 - AI生成或精美图片",children:t.jsx(Ct,{size:16})}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>C.chain().focus().undo().run(),disabled:!C.can().undo(),title:"撤销 (Ctrl+Z)",children:t.jsx(co,{size:16})}),t.jsx(me,{onClick:()=>C.chain().focus().redo().run(),disabled:!C.can().redo(),title:"重做 (Ctrl+Y)",children:t.jsx(Zl,{size:16})})]}),t.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600"}),t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx(me,{onClick:()=>le(!ue),isActive:ue,title:"预览模式",children:ue?t.jsx(kr,{size:16}):t.jsx(Xe,{size:16})}),t.jsx(me,{onClick:()=>te(!Y),isActive:Y,title:"全屏模式",children:Y?t.jsx(Fl,{size:16}):t.jsx(rn,{size:16})})]}),t.jsxs("div",{className:"flex items-center gap-3 text-sm text-gray-500 dark:text-gray-400 ml-2",children:[t.jsxs("span",{children:[ce," 字"]}),o&&t.jsx("div",{className:"flex items-center gap-1",children:b?t.jsxs(t.Fragment,{children:[t.jsx(mt,{size:12,className:"animate-spin"}),t.jsx("span",{children:"保存中"})]}):f?t.jsxs("span",{children:["已保存 ",f.toLocaleTimeString()]}):t.jsx("span",{children:"未保存"})})]})]})]}),t.jsx("div",{className:`relative ${Y?"min-h-[calc(100vh-200px)]":"min-h-[400px]"}`,children:ue?t.jsx("div",{className:"p-6 prose prose-gray dark:prose-dark max-w-none",children:t.jsx("div",{dangerouslySetInnerHTML:{__html:e||'<p class="text-gray-400">开始写作以查看预览...</p>'}})}):t.jsxs("div",{className:"relative",children:[t.jsx(hi,{editor:C}),x&&t.jsx("div",{className:"absolute inset-0 bg-blue-50 dark:bg-blue-900/20 border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg flex items-center justify-center",children:t.jsxs("div",{className:"text-center",children:[t.jsx(mt,{size:32,className:"animate-spin text-blue-600 mx-auto mb-2"}),t.jsx("p",{className:"text-blue-600 dark:text-blue-400 font-medium",children:"图片上传中..."})]})})]})}),T&&t.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:t.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto",children:[t.jsxs("div",{className:"flex justify-between items-center mb-4",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:B?`选择封面图片 (${A.length} 张)`:`选择文章配图 (${A.length} 张)`}),t.jsx("button",{onClick:de,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",title:"关闭画廊",children:"✕"})]}),t.jsx("div",{className:"grid grid-cols-3 gap-4",children:A.map((H,W)=>t.jsxs("div",{className:"relative group cursor-pointer border-2 border-transparent hover:border-blue-500 rounded-lg overflow-hidden",onClick:()=>ke(H.url),children:[t.jsx("img",{src:H.url,alt:H.title||"配图选项",className:"w-full h-32 object-cover",loading:"lazy",onError:X=>{console.error("图片加载失败:",H.url),X.currentTarget.style.display="none"},onLoad:()=>{console.log("图片加载成功:",H.url)}}),t.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center",children:t.jsx("span",{className:"text-white opacity-0 group-hover:opacity-100 font-medium",children:"选择"})}),H.title&&t.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-2",children:[t.jsx("p",{className:"truncate",title:H.title,children:H.title}),H.author&&t.jsxs("p",{className:"truncate text-gray-300",title:`作者: ${H.author}`,children:["作者: ",H.author]})]})]},`${H.url}-${W}`))}),t.jsxs("div",{className:"mt-4 flex justify-between",children:[t.jsx("button",{onClick:()=>{N(!1),B?be():oe()},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",children:"换一批图片"}),t.jsx("button",{onClick:de,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors",children:"取消选择"})]})]})})]}):t.jsx("div",{children:"编辑器加载中..."})}function $a(){const e=Ft(),{slug:s}=Kt(),{isAdmin:r,isLoading:a}=qt(),n=!!s,o=Nr(),[i,l]=d.useState(""),[c,g]=d.useState({title:"",excerpt:"",tags:[],category:"",featured:!1,imageUrl:""}),[u,x]=d.useState(!1),[p,m]=d.useState(!1),[w,T]=d.useState("idle"),[N,A]=d.useState(""),[E,z]=d.useState(!1),[R,D]=d.useState(!1),[_,G]=d.useState([]),[Z,B]=d.useState([]),[se,Y]=d.useState(!1),[te,ue]=d.useState(!1);d.useEffect(()=>{!a&&r&&(async()=>{try{console.log("开始加载分类和标签...");const I=(await je.getPosts(1,100)).posts||[];console.log("获取到文章数量:",I.length);const K=I.filter(oe=>oe.category).map(oe=>oe.category),J=Array.from(new Set(K));G(J),console.log("可用分类:",J);const C=I.flatMap(oe=>oe.tags||[]),ne=Array.from(new Set(C));B(ne),console.log("可用标签:",ne)}catch(S){console.error("加载分类和标签失败:",S),G([]),B([])}})()},[a,r]),d.useEffect(()=>{n&&s&&(async()=>{z(!0);try{const S=await je.getPost(s);l(S.content||""),g({title:S.title,excerpt:S.excerpt,tags:S.tags||[],category:S.category||"",featured:S.featured||!1,imageUrl:S.imageUrl||""})}catch(S){console.error("加载文章失败:",S),o.error("加载文章失败","请检查文章是否存在，即将返回首页"),setTimeout(()=>e("/"),2e3)}finally{z(!1)}})()},[n,s,e]),d.useEffect(()=>{const b=S=>{const I=S.target;I.closest(".category-dropdown")||Y(!1),I.closest(".tag-dropdown")||ue(!1)};return document.addEventListener("mousedown",b),()=>document.removeEventListener("mousedown",b)},[]);const le=async(b=0)=>{if(!c.title.trim()){o.warning("请输入文章标题","标题是必填项");return}if(!i.trim()){o.warning("请输入文章内容","内容不能为空");return}m(!0),T("saving"),A(b>0?`正在重试保存... (${b+1}/3)`:"正在保存文章...");const S=o.loading(b>0?"重试保存中...":"保存文章中...",b>0?`第 ${b+1} 次尝试`:"请稍候");try{const I={...c,content:i};if(console.log("开始保存文章:",{isEditing:n,slug:s,postData:{...I,content:i.substring(0,100)+"..."}}),n)await je.updatePost(s,I),console.log("文章更新成功"),T("success"),A("文章更新成功！"),o.update(S,{type:"success",title:"文章更新成功！",message:"即将跳转到文章页面",duration:3e3}),setTimeout(()=>e(`/post/${s}`),1e3);else{const K=await je.createPost(I);console.log("文章创建成功:",K),T("success"),A("文章创建成功！"),o.update(S,{type:"success",title:"文章创建成功！",message:"即将跳转到文章页面",duration:3e3}),setTimeout(()=>e(`/post/${K.slug}`),1e3)}}catch(I){console.error("保存失败:",I);let K="保存失败，请重试",J=!0;if(I.status===400?(K="请检查文章内容格式是否正确",J=!1):I.status===401?(K="登录已过期，请重新登录",J=!1):I.status===409?(K="文章标题已存在，请修改标题",J=!1):I.status===413?(K="文章内容过大，请减少内容长度",J=!1):I.status>=500?K="服务器错误，正在重试...":I.name==="AbortError"?K="请求超时，正在重试...":I.message&&(K=`保存失败: ${I.message}`),T("error"),A(K),o.update(S,{type:"error",title:"保存失败",message:K,duration:J?0:5e3,action:J?{label:"重试",onClick:()=>{o.remove(S),le(0)}}:void 0}),J&&b<2){console.log(`第 ${b+1} 次重试保存...`),setTimeout(()=>{o.remove(S),le(b+1)},1e3*(b+1));return}J||setTimeout(()=>{T("idle"),A("")},3e3)}finally{m(!1)}},ce=b=>{b&&!c.tags.includes(b)&&g(S=>({...S,tags:[...S.tags,b]}))},k=b=>{g(S=>({...S,tags:S.tags.filter(I=>I!==b)}))},f=b=>{g(S=>({...S,imageUrl:b}))},j=async b=>{if(!c.title.trim())return;const S=`draft_${n?s:"new"}_${Date.now()}`,I={...c,content:b,lastSaved:new Date().toISOString()};try{localStorage.setItem(S,JSON.stringify(I)),console.log("草稿已自动保存")}catch(K){console.error("草稿保存失败:",K)}};return a?t.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"检查权限中..."})]}):r?E?t.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载文章中..."})]}):t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"flex items-center justify-between mb-8",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:n?"编辑文章":"写新文章"}),t.jsxs("div",{className:"flex items-center space-x-4",children:[t.jsxs("button",{onClick:()=>x(!u),title:u?"切换到编辑模式":"切换到预览模式","aria-label":u?"切换到编辑模式":"切换到预览模式",className:"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[u?t.jsx(kr,{size:16}):t.jsx(Xe,{size:16}),t.jsx("span",{children:u?"编辑":"预览"})]}),t.jsxs("button",{onClick:()=>le(0),disabled:p,title:p?"正在保存文章":"保存文章","aria-label":p?"正在保存文章":"保存文章",className:`flex items-center space-x-2 px-6 py-2 rounded-lg transition-all duration-300 ${w==="success"?"bg-green-600 hover:bg-green-700 text-white":w==="error"?"bg-red-600 hover:bg-red-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"}`,children:[w==="saving"&&t.jsx(mt,{size:16,className:"animate-spin"}),w==="success"&&t.jsx(lr,{size:16}),w==="error"&&t.jsx(_t,{size:16}),w==="idle"&&t.jsx(to,{size:16}),t.jsx("span",{children:w==="saving"?"保存中...":w==="success"?"已保存":w==="error"?"保存失败":"保存"})]})]})]}),N&&t.jsx("div",{className:`mb-6 p-4 rounded-lg border transition-all duration-300 ${w==="success"?"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200":w==="error"?"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200":"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200"}`,children:t.jsxs("div",{className:"flex items-center space-x-2",children:[w==="saving"&&t.jsx(mt,{size:16,className:"animate-spin"}),w==="success"&&t.jsx(lr,{size:16}),w==="error"&&t.jsx(_t,{size:16}),t.jsx("span",{children:N})]})}),t.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[t.jsx("div",{className:"lg:col-span-2",children:t.jsx("div",{className:"card p-6",children:u?t.jsxs("div",{className:"prose prose-gray dark:prose-dark max-w-none",children:[t.jsx("h1",{children:c.title}),t.jsx("div",{dangerouslySetInnerHTML:{__html:i}})]}):t.jsx(hu,{content:i,onChange:l,placeholder:"开始写作...",coverImage:c.imageUrl,onCoverImageChange:f,autoSave:!0,onAutoSave:j})})}),t.jsx("div",{className:"lg:col-span-1",children:t.jsxs("div",{className:"card p-6",children:[t.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"文章设置"}),t.jsxs("div",{className:"space-y-4",children:[t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标题"}),t.jsx("input",{type:"text",value:c.title,onChange:b=>g(S=>({...S,title:b.target.value})),className:"input w-full",placeholder:"输入文章标题"})]}),t.jsxs("div",{children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"摘要"}),t.jsx("textarea",{value:c.excerpt,onChange:b=>g(S=>({...S,excerpt:b.target.value})),className:"input w-full h-20 resize-none",placeholder:"输入文章摘要"})]}),t.jsxs("div",{className:"relative category-dropdown",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"分类"}),t.jsxs("button",{onClick:()=>Y(!se),className:"w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[t.jsx("span",{className:c.category?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:c.category||"选择或输入分类"}),t.jsx(gt,{size:16})]}),se&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[t.jsx("div",{className:"px-3 py-2 border-b border-gray-100 dark:border-gray-700",children:t.jsx("input",{type:"text",placeholder:"输入新分类",className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onKeyPress:b=>{if(b.key==="Enter"){const S=b.currentTarget.value.trim();S&&(g(I=>({...I,category:S})),Y(!1),b.currentTarget.value="")}}})}),_.map(b=>t.jsx("button",{onClick:()=>{g(S=>({...S,category:b})),Y(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:b},b)),_.length===0&&t.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 dark:text-gray-400",children:"暂无现有分类"})]})}),c.category&&t.jsx("button",{onClick:()=>g(b=>({...b,category:""})),className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:t.jsx(Ze,{size:14})})]}),t.jsxs("div",{className:"relative tag-dropdown",children:[t.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标签"}),t.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:c.tags.map(b=>t.jsxs("span",{className:"inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full",children:[b,t.jsx("button",{onClick:()=>k(b),title:`移除标签: ${b}`,"aria-label":`移除标签: ${b}`,className:"ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200",children:"×"})]},b))}),t.jsxs("button",{onClick:()=>ue(!te),className:"w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[t.jsx("span",{className:"text-gray-500 dark:text-gray-400",children:"选择或输入标签"}),t.jsx(gt,{size:16})]}),te&&t.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:t.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[t.jsx("div",{className:"px-3 py-2 border-b border-gray-100 dark:border-gray-700",children:t.jsx("input",{type:"text",placeholder:"输入新标签后按回车",className:"w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white",onKeyPress:b=>{if(b.key==="Enter"){const S=b.currentTarget.value.trim();S&&(ce(S),b.currentTarget.value="",ue(!1))}}})}),Z.filter(b=>!c.tags.includes(b)).map(b=>t.jsx("button",{onClick:()=>{ce(b),ue(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:b},b)),Z.filter(b=>!c.tags.includes(b)).length===0&&t.jsx("div",{className:"px-3 py-2 text-sm text-gray-500 dark:text-gray-400",children:Z.length===0?"暂无现有标签":"所有标签已选择"})]})})]}),t.jsx("div",{children:t.jsxs("label",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",checked:c.featured,onChange:b=>g(S=>({...S,featured:b.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),t.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"设为特色文章"})]})})]})]})})]})]}):t.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[t.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:t.jsx(xt,{className:"text-red-600 dark:text-red-400",size:32})}),t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"需要管理员权限"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"只有管理员才能创建和编辑文章"}),t.jsxs("button",{onClick:()=>D(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[t.jsx(xt,{size:20}),t.jsx("span",{children:"管理员登录"})]}),t.jsx(cn,{isOpen:R,onClose:()=>D(!1)})]})}function Da(){const{category:e}=Kt(),[s,r]=d.useState([]),[a,n]=d.useState([]),[o,i]=d.useState(!0),[l,c]=d.useState(null);d.useEffect(()=>{g()},[e]);const g=async()=>{i(!0),c(null);try{if(e){const u=await je.getPosts(1,20,e);r(u.posts)}else{const u=await je.getPosts(1,100);r(u.posts);const x=new Map;u.posts.forEach(m=>{m.category&&x.set(m.category,(x.get(m.category)||0)+1)}),x.set("General",u.posts.length);const p=Array.from(x.entries()).map(([m,w])=>({name:m,count:w})).sort((m,w)=>m.name==="General"?-1:w.name==="General"?1:w.count-m.count);n(p)}}catch(u){console.error("加载分类数据失败:",u),c("加载失败，请重试")}finally{i(!1)}};return o?t.jsxs("div",{className:"text-center py-12",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载中..."})]}):l?t.jsxs("div",{className:"text-center py-12",children:[t.jsx("p",{className:"text-red-600 dark:text-red-400",children:l}),t.jsx("button",{onClick:g,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]}):e?t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-6",children:t.jsxs(ae,{to:"/categories",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[t.jsx(ns,{size:16}),t.jsx("span",{children:"返回所有分类"})]})}),t.jsxs("div",{className:"mb-8",children:[t.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[t.jsx(kt,{size:24,className:"text-blue-600"}),t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e})]}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共找到 ",s.length," 篇文章"]})]}),s.length>0?t.jsx("div",{className:"space-y-6",children:s.map(u=>t.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[t.jsx("h2",{className:"text-xl font-semibold mb-3",children:t.jsx(ae,{to:`/post/${u.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:u.title})}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:u.excerpt}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Qe,{size:14}),t.jsx("span",{children:u.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:u.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:14}),t.jsxs("span",{children:[u.readTime," 分钟阅读"]})]}),u.category&&t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(kt,{size:14}),t.jsx("span",{className:"px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded",children:u.category})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:u.tags.map(x=>t.jsxs(ae,{to:`/tag/${x}`,className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:["#",x]},x))})]},u.id))}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx(kt,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无相关文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"该分类下还没有文章"}),t.jsx(ae,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]}):t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"所有分类"}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共 ",a.length," 个分类"]})]}),a.length>0?t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:a.map(({name:u,count:x})=>t.jsxs(ae,{to:`/category/${u}`,className:"group p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 hover:shadow-lg transition-all",children:[t.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[t.jsx(kt,{size:20,className:"text-blue-600"}),t.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:u})]}),t.jsxs("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:[x," 篇文章"]})]},u))}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx(kt,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无分类"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"还没有任何分类"}),t.jsx(ae,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})]})}function Ua(){const{tag:e}=Kt(),[s,r]=d.useState([]),[a,n]=d.useState([]),[o,i]=d.useState(!0),[l,c]=d.useState(null);d.useEffect(()=>{g()},[e]);const g=async()=>{i(!0),c(null);try{if(e){const u=await je.getPosts(1,20,void 0,e);r(u.posts)}else{const u=await je.getPosts(1,100);r(u.posts);const x=new Map;u.posts.forEach(m=>{m.tags.forEach(w=>{x.set(w,(x.get(w)||0)+1)})});const p=Array.from(x.entries()).map(([m,w])=>({name:m,count:w})).sort((m,w)=>w.count-m.count);n(p)}}catch(u){console.error("加载标签数据失败:",u),c("加载失败，请重试")}finally{i(!1)}};return o?t.jsxs("div",{className:"text-center py-12",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载中..."})]}):l?t.jsxs("div",{className:"text-center py-12",children:[t.jsx("p",{className:"text-red-600 dark:text-red-400",children:l}),t.jsx("button",{onClick:g,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]}):e?t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-6",children:t.jsxs(ae,{to:"/tags",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[t.jsx(ns,{size:16}),t.jsx("span",{children:"返回所有标签"})]})}),t.jsxs("div",{className:"mb-8",children:[t.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[t.jsx(ft,{size:24,className:"text-blue-600"}),t.jsxs("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["#",e]})]}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共找到 ",s.length," 篇文章"]})]}),s.length>0?t.jsx("div",{className:"space-y-6",children:s.map(u=>t.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[t.jsx("h2",{className:"text-xl font-semibold mb-3",children:t.jsx(ae,{to:`/post/${u.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:u.title})}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:u.excerpt}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Qe,{size:14}),t.jsx("span",{children:u.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:u.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:14}),t.jsxs("span",{children:[u.readTime," 分钟阅读"]})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:u.tags.map(x=>t.jsxs(ae,{to:`/tag/${x}`,className:`px-2 py-1 text-xs rounded transition-colors ${x===e?"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:["#",x]},x))})]},u.id))}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx(ft,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无相关文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"该标签下还没有文章"}),t.jsx(ae,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]}):t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsxs("div",{className:"mb-8",children:[t.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"所有标签"}),t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共 ",a.length," 个标签"]})]}),a.length>0?t.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:a.map(({name:u,count:x})=>t.jsx(ae,{to:`/tag/${u}`,className:"group p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 hover:shadow-lg transition-all",children:t.jsxs("div",{className:"flex items-center justify-between",children:[t.jsxs("span",{className:"font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:["#",u]}),t.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:x})]})},u))}):t.jsxs("div",{className:"text-center py-12",children:[t.jsx(ft,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无标签"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"还没有任何标签"}),t.jsx(ae,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})]})}function gu(){const[e,s]=vl(),[r,a]=d.useState(e.get("q")||""),[n,o]=d.useState([]),[i,l]=d.useState(!1),[c,g]=d.useState(!1);d.useEffect(()=>{const m=e.get("q");m&&(a(m),u(m))},[e]);const u=async m=>{if(m.trim()){l(!0),g(!0);try{const w=await je.searchPosts(m);o(w)}catch(w){console.error("搜索失败:",w),o([])}finally{l(!1)}}},x=m=>{m.preventDefault(),r.trim()&&s({q:r.trim()})},p=m=>{a(m.target.value)};return t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"mb-8",children:t.jsxs("form",{onSubmit:x,className:"relative",children:[t.jsxs("div",{className:"relative",children:[t.jsx(De,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),t.jsx("input",{type:"text",value:r,onChange:p,placeholder:"搜索文章...",className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),t.jsx("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-4 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"搜索"})]})}),t.jsxs("div",{children:[i&&t.jsxs("div",{className:"text-center py-8",children:[t.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),t.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"搜索中..."})]}),!i&&c&&t.jsx("div",{className:"mb-6",children:t.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:[n.length>0?`找到 ${n.length} 篇文章`:"没有找到相关文章",r&&` 关于 "${r}"`]})}),!i&&n.length>0&&t.jsx("div",{className:"space-y-6",children:n.map(m=>t.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[t.jsx("h2",{className:"text-xl font-semibold mb-3",children:t.jsx(ae,{to:`/post/${m.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:m.title})}),t.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:m.excerpt}),t.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Qe,{size:14}),t.jsx("span",{children:m.author})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ae,{size:14}),t.jsx("span",{children:m.date})]}),t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(Ie,{size:14}),t.jsxs("span",{children:[m.readTime," 分钟阅读"]})]}),m.category&&t.jsxs("div",{className:"flex items-center space-x-1",children:[t.jsx(ft,{size:14}),t.jsx("span",{children:m.category})]})]}),t.jsx("div",{className:"flex flex-wrap gap-2",children:m.tags.map(w=>t.jsxs(ae,{to:`/tag/${w}`,className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:["#",w]},w))})]},m.id))}),!i&&c&&n.length===0&&t.jsxs("div",{className:"text-center py-12",children:[t.jsx(De,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),t.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"没有找到相关文章"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"尝试使用不同的关键词或查看所有文章"}),t.jsx(ae,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]})]})}function xu(){return t.jsx("div",{className:"max-w-4xl mx-auto",children:t.jsxs("div",{className:"card p-8",children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-6",children:"关于我"}),t.jsxs("div",{className:"prose prose-gray dark:prose-dark max-w-none",children:[t.jsx("p",{children:"欢迎来到我的个人博客！这里是我分享技术见解、开发经验和生活感悟的地方。"}),t.jsx("h2",{children:"技术栈"}),t.jsxs("ul",{children:[t.jsx("li",{children:"前端：React, TypeScript, Tailwind CSS"}),t.jsx("li",{children:"后端：Cloudflare Workers, Node.js"}),t.jsx("li",{children:"数据库：Cloudflare KV, MongoDB"}),t.jsx("li",{children:"部署：Cloudflare Pages, Vercel"})]}),t.jsx("h2",{children:"联系方式"}),t.jsx("p",{children:"如果您想与我交流或有任何问题，请通过以下方式联系我："}),t.jsxs("ul",{children:[t.jsx("li",{children:"<EMAIL>"}),t.jsx("li",{children:"https://github.com/ajkdfe2e2e"}),t.jsx("li",{children:"https://x.com/x2a1HRjxs552213"})]})]})]})})}function fu(){const e=d.useRef(null),s=()=>{var r;(r=e.current)==null||r.toggleVisibility()};return t.jsxs(bo,{children:[t.jsx(Eo,{onMusicToggle:s,children:t.jsxs(ol,{children:[t.jsx(Le,{path:"/",element:t.jsx(Uo,{})}),t.jsx(Le,{path:"/post/:slug",element:t.jsx(Bo,{})}),t.jsx(Le,{path:"/write",element:t.jsx($a,{})}),t.jsx(Le,{path:"/write/:slug",element:t.jsx($a,{})}),t.jsx(Le,{path:"/categories",element:t.jsx(Da,{})}),t.jsx(Le,{path:"/category/:category",element:t.jsx(Da,{})}),t.jsx(Le,{path:"/tags",element:t.jsx(Ua,{})}),t.jsx(Le,{path:"/tag/:tag",element:t.jsx(Ua,{})}),t.jsx(Le,{path:"/search",element:t.jsx(gu,{})}),t.jsx(Le,{path:"/about",element:t.jsx(xu,{})})]})}),t.jsx(So,{ref:e})]})}ur.setupNetworkListener();sr.createRoot(document.getElementById("root")).render(t.jsx(Qn.StrictMode,{children:t.jsx(ml,{children:t.jsx(Do,{children:t.jsx(fu,{})})})}));
//# sourceMappingURL=index-67066099.js.map
