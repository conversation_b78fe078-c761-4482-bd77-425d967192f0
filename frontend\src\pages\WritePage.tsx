import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Save, Eye, EyeOff, Shield, Loader2, CheckCircle, AlertCircle, ChevronDown, X } from 'lucide-react'
import { createToastHelpers } from '../components/Toast'
import { BlogEditor } from '../components/BlogEditor'
import { BlogService } from '../services/blogService'
import { useAuth } from '../contexts/AuthContext'
import { AdminLogin } from '../components/AdminLogin'

interface PostMetadata {
  title: string
  excerpt: string
  tags: string[]
  category: string
  featured: boolean
  imageUrl?: string
}

export function WritePage() {
  const navigate = useNavigate()
  const { slug } = useParams()
  const { isAdmin, isLoading } = useAuth()
  const isEditing = Boolean(slug)
  const toast = createToastHelpers()
  
  const [content, setContent] = useState('')
  const [metadata, setMetadata] = useState<PostMetadata>({
    title: '',
    excerpt: '',
    tags: [],
    category: '',
    featured: false,
    imageUrl: ''
  })
  const [isPreview, setIsPreview] = useState(false)
  const [saving, setSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle')
  const [saveMessage, setSaveMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const [showAdminLogin, setShowAdminLogin] = useState(false)
  const [availableCategories, setAvailableCategories] = useState<string[]>([])
  const [availableTags, setAvailableTags] = useState<string[]>([])
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false)
  const [showTagDropdown, setShowTagDropdown] = useState(false)


  // 加载现有分类和标签
  useEffect(() => {
    // 只在用户已经通过权限检查后才加载
    if (!isLoading && isAdmin) {
      const loadCategoriesAndTags = async () => {
        try {
          console.log('开始加载分类和标签...')
          const response = await BlogService.getPosts(1, 100) // 获取所有文章来提取分类和标签
          const posts = response.posts || []
          console.log('获取到文章数量:', posts.length)

          // 提取分类
          const categories = posts
            .filter(post => post.category)
            .map(post => post.category!)
          const uniqueCategories = Array.from(new Set(categories))
          setAvailableCategories(uniqueCategories)
          console.log('可用分类:', uniqueCategories)

          // 提取标签
          const tags = posts.flatMap(post => post.tags || [])
          const uniqueTags = Array.from(new Set(tags))
          setAvailableTags(uniqueTags)
          console.log('可用标签:', uniqueTags)
        } catch (error) {
          console.error('加载分类和标签失败:', error)
          // 设置为空数组，避免界面错误
          setAvailableCategories([])
          setAvailableTags([])
        }
      }

      loadCategoriesAndTags()
    }
  }, [isLoading, isAdmin])

  // 在编辑模式下加载已有文章内容
  useEffect(() => {
    if (isEditing && slug) {
      const loadPost = async () => {
        setLoading(true)
        try {
          const post = await BlogService.getPost(slug)
          setContent(post.content || '')
          setMetadata({
            title: post.title,
            excerpt: post.excerpt,
            tags: post.tags || [],
            category: post.category || '',
            featured: post.featured || false,
            imageUrl: post.imageUrl || ''
          })
        } catch (error) {
          console.error('加载文章失败:', error)
          toast.error('加载文章失败', '请检查文章是否存在，即将返回首页')
          setTimeout(() => navigate('/'), 2000)
        } finally {
          setLoading(false)
        }
      }
      loadPost()
    }
  }, [isEditing, slug, navigate])

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest('.category-dropdown')) {
        setShowCategoryDropdown(false)
      }
      if (!target.closest('.tag-dropdown')) {
        setShowTagDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  const handleSave = async (retryCount = 0) => {
    if (!metadata.title.trim()) {
      toast.warning('请输入文章标题', '标题是必填项')
      return
    }

    if (!content.trim()) {
      toast.warning('请输入文章内容', '内容不能为空')
      return
    }

    setSaving(true)
    setSaveStatus('saving')
    setSaveMessage(retryCount > 0 ? `正在重试保存... (${retryCount + 1}/3)` : '正在保存文章...')

    // 显示保存中的Toast
    const loadingToastId = toast.loading(
      retryCount > 0 ? '重试保存中...' : '保存文章中...',
      retryCount > 0 ? `第 ${retryCount + 1} 次尝试` : '请稍候'
    )

    try {
      const postData = {
        ...metadata,
        content
      }

      console.log('开始保存文章:', { isEditing, slug, postData: { ...postData, content: content.substring(0, 100) + '...' } })

      if (isEditing) {
        await BlogService.updatePost(slug!, postData)
        console.log('文章更新成功')
        setSaveStatus('success')
        setSaveMessage('文章更新成功！')

        // 更新Toast为成功状态
        toast.update(loadingToastId, {
          type: 'success',
          title: '文章更新成功！',
          message: '即将跳转到文章页面',
          duration: 3000
        })

        setTimeout(() => navigate(`/post/${slug}`), 1000)
      } else {
        const result = await BlogService.createPost(postData)
        console.log('文章创建成功:', result)
        setSaveStatus('success')
        setSaveMessage('文章创建成功！')

        // 更新Toast为成功状态
        toast.update(loadingToastId, {
          type: 'success',
          title: '文章创建成功！',
          message: '即将跳转到文章页面',
          duration: 3000
        })

        setTimeout(() => navigate(`/post/${result.slug}`), 1000)
      }
    } catch (error: any) {
      console.error('保存失败:', error)

      // 详细的错误处理
      let errorMessage = '保存失败，请重试'
      let canRetry = true

      if (error.status === 400) {
        errorMessage = '请检查文章内容格式是否正确'
        canRetry = false
      } else if (error.status === 401) {
        errorMessage = '登录已过期，请重新登录'
        canRetry = false
      } else if (error.status === 409) {
        errorMessage = '文章标题已存在，请修改标题'
        canRetry = false
      } else if (error.status === 413) {
        errorMessage = '文章内容过大，请减少内容长度'
        canRetry = false
      } else if (error.status >= 500) {
        errorMessage = '服务器错误，正在重试...'
      } else if (error.name === 'AbortError') {
        errorMessage = '请求超时，正在重试...'
      } else if (error.message) {
        errorMessage = `保存失败: ${error.message}`
      }

      setSaveStatus('error')
      setSaveMessage(errorMessage)

      // 更新Toast为错误状态
      toast.update(loadingToastId, {
        type: 'error',
        title: '保存失败',
        message: errorMessage,
        duration: canRetry ? 0 : 5000, // 可重试时不自动消失
        action: canRetry ? {
          label: '重试',
          onClick: () => {
            toast.remove(loadingToastId)
            handleSave(0)
          }
        } : undefined
      })

      // 自动重试逻辑
      if (canRetry && retryCount < 2) {
        console.log(`第 ${retryCount + 1} 次重试保存...`)
        setTimeout(() => {
          toast.remove(loadingToastId)
          handleSave(retryCount + 1)
        }, 1000 * (retryCount + 1)) // 递增延迟
        return
      }

      // 重置状态
      if (!canRetry) {
        setTimeout(() => {
          setSaveStatus('idle')
          setSaveMessage('')
        }, 3000)
      }
    } finally {
      setSaving(false)
    }
  }

  const addTag = (tag: string) => {
    if (tag && !metadata.tags.includes(tag)) {
      setMetadata(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }))
    }
  }

  const removeTag = (tagToRemove: string) => {
    setMetadata(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleCoverImageChange = (imageUrl: string) => {
    setMetadata(prev => ({
      ...prev,
      imageUrl
    }))
  }

  // 自动保存草稿
  const handleAutoSave = async (content: string) => {
    if (!metadata.title.trim()) return // 没有标题时不保存草稿

    const draftKey = `draft_${isEditing ? slug : 'new'}_${Date.now()}`
    const draftData = {
      ...metadata,
      content,
      lastSaved: new Date().toISOString()
    }

    try {
      localStorage.setItem(draftKey, JSON.stringify(draftData))
      console.log('草稿已自动保存')
    } catch (error) {
      console.error('草稿保存失败:', error)
    }
  }

  // 权限检查
  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">检查权限中...</p>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <Shield className="text-red-600 dark:text-red-400" size={32} />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          需要管理员权限
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          只有管理员才能创建和编辑文章
        </p>
        <button
          onClick={() => setShowAdminLogin(true)}
          className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Shield size={20} />
          <span>管理员登录</span>
        </button>

        <AdminLogin 
          isOpen={showAdminLogin} 
          onClose={() => setShowAdminLogin(false)} 
        />
      </div>
    )
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">加载文章中...</p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {isEditing ? '编辑文章' : '写新文章'}
        </h1>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setIsPreview(!isPreview)}
            title={isPreview ? '切换到编辑模式' : '切换到预览模式'}
            aria-label={isPreview ? '切换到编辑模式' : '切换到预览模式'}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {isPreview ? <EyeOff size={16} /> : <Eye size={16} />}
            <span>{isPreview ? '编辑' : '预览'}</span>
          </button>
          
          <button
            onClick={() => handleSave(0)}
            disabled={saving}
            title={saving ? '正在保存文章' : '保存文章'}
            aria-label={saving ? '正在保存文章' : '保存文章'}
            className={`flex items-center space-x-2 px-6 py-2 rounded-lg transition-all duration-300 ${
              saveStatus === 'success'
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : saveStatus === 'error'
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50'
            }`}
          >
            {saveStatus === 'saving' && <Loader2 size={16} className="animate-spin" />}
            {saveStatus === 'success' && <CheckCircle size={16} />}
            {saveStatus === 'error' && <AlertCircle size={16} />}
            {saveStatus === 'idle' && <Save size={16} />}
            <span>
              {saveStatus === 'saving' ? '保存中...' :
               saveStatus === 'success' ? '已保存' :
               saveStatus === 'error' ? '保存失败' : '保存'}
            </span>
          </button>
        </div>
      </div>

      {/* 保存状态消息 */}
      {saveMessage && (
        <div className={`mb-6 p-4 rounded-lg border transition-all duration-300 ${
          saveStatus === 'success'
            ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200'
            : saveStatus === 'error'
            ? 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200'
            : 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200'
        }`}>
          <div className="flex items-center space-x-2">
            {saveStatus === 'saving' && <Loader2 size={16} className="animate-spin" />}
            {saveStatus === 'success' && <CheckCircle size={16} />}
            {saveStatus === 'error' && <AlertCircle size={16} />}
            <span>{saveMessage}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主编辑区域 */}
        <div className="lg:col-span-2">
          <div className="card p-6">
            {!isPreview ? (
              <BlogEditor
                content={content}
                onChange={setContent}
                placeholder="开始写作..."
                coverImage={metadata.imageUrl}
                onCoverImageChange={handleCoverImageChange}
                autoSave={true}
                onAutoSave={handleAutoSave}

              />
            ) : (
              <div className="prose prose-gray dark:prose-dark max-w-none">
                <h1>{metadata.title}</h1>
                <div dangerouslySetInnerHTML={{ __html: content }} />
              </div>
            )}
          </div>
        </div>

        {/* 元数据面板 */}
        <div className="lg:col-span-1">
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              文章设置
            </h3>
            
            <div className="space-y-4">
              {/* 标题 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  标题
                </label>
                <input
                  type="text"
                  value={metadata.title}
                  onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
                  className="input w-full"
                  placeholder="输入文章标题"
                />
              </div>

              {/* 摘要 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  摘要
                </label>
                <textarea
                  value={metadata.excerpt}
                  onChange={(e) => setMetadata(prev => ({ ...prev, excerpt: e.target.value }))}
                  className="input w-full h-20 resize-none"
                  placeholder="输入文章摘要"
                />
              </div>

              {/* 分类 */}
              <div className="relative category-dropdown">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  分类
                </label>
                <button
                  onClick={() => setShowCategoryDropdown(!showCategoryDropdown)}
                  className="w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <span className={metadata.category ? 'text-gray-900 dark:text-white' : 'text-gray-500 dark:text-gray-400'}>
                    {metadata.category || '选择或输入分类'}
                  </span>
                  <ChevronDown size={16} />
                </button>

                {showCategoryDropdown && (
                  <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
                    <div className="py-1 max-h-60 overflow-auto">
                      {/* 手动输入选项 */}
                      <div className="px-3 py-2 border-b border-gray-100 dark:border-gray-700">
                        <input
                          type="text"
                          placeholder="输入新分类"
                          className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              const value = e.currentTarget.value.trim()
                              if (value) {
                                setMetadata(prev => ({ ...prev, category: value }))
                                setShowCategoryDropdown(false)
                                e.currentTarget.value = ''
                              }
                            }
                          }}
                        />
                      </div>

                      {/* 现有分类选项 */}
                      {availableCategories.map((category) => (
                        <button
                          key={category}
                          onClick={() => {
                            setMetadata(prev => ({ ...prev, category }))
                            setShowCategoryDropdown(false)
                          }}
                          className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          {category}
                        </button>
                      ))}

                      {availableCategories.length === 0 && (
                        <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                          暂无现有分类
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {metadata.category && (
                  <button
                    onClick={() => setMetadata(prev => ({ ...prev, category: '' }))}
                    className="absolute right-8 top-9 text-gray-400 hover:text-gray-600"
                  >
                    <X size={14} />
                  </button>
                )}
              </div>

              {/* 标签 */}
              <div className="relative tag-dropdown">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  标签
                </label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {metadata.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                    >
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        title={`移除标签: ${tag}`}
                        aria-label={`移除标签: ${tag}`}
                        className="ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>

                <button
                  onClick={() => setShowTagDropdown(!showTagDropdown)}
                  className="w-full flex items-center justify-between px-3 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <span className="text-gray-500 dark:text-gray-400">
                    选择或输入标签
                  </span>
                  <ChevronDown size={16} />
                </button>

                {showTagDropdown && (
                  <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
                    <div className="py-1 max-h-60 overflow-auto">
                      {/* 手动输入选项 */}
                      <div className="px-3 py-2 border-b border-gray-100 dark:border-gray-700">
                        <input
                          type="text"
                          placeholder="输入新标签后按回车"
                          className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                          onKeyPress={(e) => {
                            if (e.key === 'Enter') {
                              const value = e.currentTarget.value.trim()
                              if (value) {
                                addTag(value)
                                e.currentTarget.value = ''
                                setShowTagDropdown(false)
                              }
                            }
                          }}
                        />
                      </div>

                      {/* 现有标签选项 */}
                      {availableTags
                        .filter(tag => !metadata.tags.includes(tag)) // 过滤已选择的标签
                        .map((tag) => (
                        <button
                          key={tag}
                          onClick={() => {
                            addTag(tag)
                            setShowTagDropdown(false)
                          }}
                          className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
                        >
                          {tag}
                        </button>
                      ))}

                      {availableTags.filter(tag => !metadata.tags.includes(tag)).length === 0 && (
                        <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400">
                          {availableTags.length === 0 ? '暂无现有标签' : '所有标签已选择'}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* 特色文章 */}
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={metadata.featured}
                    onChange={(e) => setMetadata(prev => ({ ...prev, featured: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    设为特色文章
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 