import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { Save, Eye, EyeOff, Shield, Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { BlogEditor } from '../components/BlogEditor'
import { BlogService } from '../services/blogService'
import { useAuth } from '../contexts/AuthContext'
import { AdminLogin } from '../components/AdminLogin'

interface PostMetadata {
  title: string
  excerpt: string
  tags: string[]
  category: string
  featured: boolean
  imageUrl?: string
}

export function WritePage() {
  const navigate = useNavigate()
  const { slug } = useParams()
  const { isAdmin, isLoading } = useAuth()
  const isEditing = Boolean(slug)
  
  const [content, setContent] = useState('')
  const [metadata, setMetadata] = useState<PostMetadata>({
    title: '',
    excerpt: '',
    tags: [],
    category: '',
    featured: false,
    imageUrl: ''
  })
  const [isPreview, setIsPreview] = useState(false)
  const [saving, setSaving] = useState(false)
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle')
  const [saveMessage, setSaveMessage] = useState('')
  const [loading, setLoading] = useState(false)
  const [showAdminLogin, setShowAdminLogin] = useState(false)

  // 在编辑模式下加载已有文章内容
  useEffect(() => {
    if (isEditing && slug) {
      const loadPost = async () => {
        setLoading(true)
        try {
          const post = await BlogService.getPost(slug)
          setContent(post.content || '')
          setMetadata({
            title: post.title,
            excerpt: post.excerpt,
            tags: post.tags || [],
            category: post.category || '',
            featured: post.featured || false,
            imageUrl: post.imageUrl || ''
          })
        } catch (error) {
          console.error('加载文章失败:', error)
          alert('加载文章失败，请检查文章是否存在')
          navigate('/')
        } finally {
          setLoading(false)
        }
      }
      loadPost()
    }
  }, [isEditing, slug, navigate])

  const handleSave = async (retryCount = 0) => {
    if (!metadata.title.trim()) {
      alert('请输入文章标题')
      return
    }

    if (!content.trim()) {
      alert('请输入文章内容')
      return
    }

    setSaving(true)
    setSaveStatus('saving')
    setSaveMessage(retryCount > 0 ? `正在重试保存... (${retryCount + 1}/3)` : '正在保存文章...')

    try {
      const postData = {
        ...metadata,
        content
      }

      console.log('开始保存文章:', { isEditing, slug, postData: { ...postData, content: content.substring(0, 100) + '...' } })

      if (isEditing) {
        await BlogService.updatePost(slug!, postData)
        console.log('文章更新成功')
        setSaveStatus('success')
        setSaveMessage('文章更新成功！')
        setTimeout(() => navigate(`/post/${slug}`), 1000)
      } else {
        const result = await BlogService.createPost(postData)
        console.log('文章创建成功:', result)
        setSaveStatus('success')
        setSaveMessage('文章创建成功！')
        setTimeout(() => navigate(`/post/${result.slug}`), 1000)
      }
    } catch (error: any) {
      console.error('保存失败:', error)

      // 详细的错误处理
      let errorMessage = '保存失败，请重试'
      let canRetry = true

      if (error.status === 400) {
        errorMessage = '请检查文章内容格式是否正确'
        canRetry = false
      } else if (error.status === 401) {
        errorMessage = '登录已过期，请重新登录'
        canRetry = false
      } else if (error.status === 409) {
        errorMessage = '文章标题已存在，请修改标题'
        canRetry = false
      } else if (error.status === 413) {
        errorMessage = '文章内容过大，请减少内容长度'
        canRetry = false
      } else if (error.status >= 500) {
        errorMessage = '服务器错误，正在重试...'
      } else if (error.name === 'AbortError') {
        errorMessage = '请求超时，正在重试...'
      } else if (error.message) {
        errorMessage = `保存失败: ${error.message}`
      }

      setSaveStatus('error')
      setSaveMessage(errorMessage)

      // 自动重试逻辑
      if (canRetry && retryCount < 2) {
        console.log(`第 ${retryCount + 1} 次重试保存...`)
        setTimeout(() => {
          handleSave(retryCount + 1)
        }, 1000 * (retryCount + 1)) // 递增延迟
        return
      }

      // 显示错误信息和手动重试选项
      if (canRetry) {
        setTimeout(() => {
          const shouldRetry = confirm(`${errorMessage}\n\n是否手动重试？`)
          if (shouldRetry) {
            handleSave(0) // 重置重试计数
          } else {
            setSaveStatus('idle')
            setSaveMessage('')
          }
        }, 2000)
      } else {
        setTimeout(() => {
          setSaveStatus('idle')
          setSaveMessage('')
        }, 3000)
      }
    } finally {
      setSaving(false)
    }
  }

  const addTag = (tag: string) => {
    if (tag && !metadata.tags.includes(tag)) {
      setMetadata(prev => ({
        ...prev,
        tags: [...prev.tags, tag]
      }))
    }
  }

  const removeTag = (tagToRemove: string) => {
    setMetadata(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleCoverImageChange = (imageUrl: string) => {
    setMetadata(prev => ({
      ...prev,
      imageUrl
    }))
  }

  // 权限检查
  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">检查权限中...</p>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <Shield className="text-red-600 dark:text-red-400" size={32} />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          需要管理员权限
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          只有管理员才能创建和编辑文章
        </p>
        <button
          onClick={() => setShowAdminLogin(true)}
          className="inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Shield size={20} />
          <span>管理员登录</span>
        </button>

        <AdminLogin 
          isOpen={showAdminLogin} 
          onClose={() => setShowAdminLogin(false)} 
        />
      </div>
    )
  }

  if (loading) {
    return (
      <div className="max-w-4xl mx-auto text-center py-12">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p className="mt-2 text-gray-600 dark:text-gray-400">加载文章中...</p>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          {isEditing ? '编辑文章' : '写新文章'}
        </h1>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setIsPreview(!isPreview)}
            title={isPreview ? '切换到编辑模式' : '切换到预览模式'}
            aria-label={isPreview ? '切换到编辑模式' : '切换到预览模式'}
            className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
          >
            {isPreview ? <EyeOff size={16} /> : <Eye size={16} />}
            <span>{isPreview ? '编辑' : '预览'}</span>
          </button>
          
          <button
            onClick={() => handleSave(0)}
            disabled={saving}
            title={saving ? '正在保存文章' : '保存文章'}
            aria-label={saving ? '正在保存文章' : '保存文章'}
            className={`flex items-center space-x-2 px-6 py-2 rounded-lg transition-all duration-300 ${
              saveStatus === 'success'
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : saveStatus === 'error'
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50'
            }`}
          >
            {saveStatus === 'saving' && <Loader2 size={16} className="animate-spin" />}
            {saveStatus === 'success' && <CheckCircle size={16} />}
            {saveStatus === 'error' && <AlertCircle size={16} />}
            {saveStatus === 'idle' && <Save size={16} />}
            <span>
              {saveStatus === 'saving' ? '保存中...' :
               saveStatus === 'success' ? '已保存' :
               saveStatus === 'error' ? '保存失败' : '保存'}
            </span>
          </button>
        </div>
      </div>

      {/* 保存状态消息 */}
      {saveMessage && (
        <div className={`mb-6 p-4 rounded-lg border transition-all duration-300 ${
          saveStatus === 'success'
            ? 'bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200'
            : saveStatus === 'error'
            ? 'bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200'
            : 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200'
        }`}>
          <div className="flex items-center space-x-2">
            {saveStatus === 'saving' && <Loader2 size={16} className="animate-spin" />}
            {saveStatus === 'success' && <CheckCircle size={16} />}
            {saveStatus === 'error' && <AlertCircle size={16} />}
            <span>{saveMessage}</span>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 主编辑区域 */}
        <div className="lg:col-span-2">
          <div className="card p-6">
            {!isPreview ? (
              <BlogEditor
                content={content}
                onChange={setContent}
                placeholder="开始写作..."
                coverImage={metadata.imageUrl}
                onCoverImageChange={handleCoverImageChange}
              />
            ) : (
              <div className="prose prose-gray dark:prose-dark max-w-none">
                <h1>{metadata.title}</h1>
                <div dangerouslySetInnerHTML={{ __html: content }} />
              </div>
            )}
          </div>
        </div>

        {/* 元数据面板 */}
        <div className="lg:col-span-1">
          <div className="card p-6">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              文章设置
            </h3>
            
            <div className="space-y-4">
              {/* 标题 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  标题
                </label>
                <input
                  type="text"
                  value={metadata.title}
                  onChange={(e) => setMetadata(prev => ({ ...prev, title: e.target.value }))}
                  className="input w-full"
                  placeholder="输入文章标题"
                />
              </div>

              {/* 摘要 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  摘要
                </label>
                <textarea
                  value={metadata.excerpt}
                  onChange={(e) => setMetadata(prev => ({ ...prev, excerpt: e.target.value }))}
                  className="input w-full h-20 resize-none"
                  placeholder="输入文章摘要"
                />
              </div>

              {/* 分类 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  分类
                </label>
                <input
                  type="text"
                  value={metadata.category}
                  onChange={(e) => setMetadata(prev => ({ ...prev, category: e.target.value }))}
                  className="input w-full"
                  placeholder="输入分类"
                />
              </div>

              {/* 标签 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  标签
                </label>
                <div className="flex flex-wrap gap-2 mb-2">
                  {metadata.tags.map((tag) => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full"
                    >
                      {tag}
                      <button
                        onClick={() => removeTag(tag)}
                        title={`移除标签: ${tag}`}
                        aria-label={`移除标签: ${tag}`}
                        className="ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
                <input
                  type="text"
                  className="input w-full"
                  placeholder="输入标签后按回车"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      addTag(e.currentTarget.value.trim())
                      e.currentTarget.value = ''
                    }
                  }}
                />
              </div>

              {/* 特色文章 */}
              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={metadata.featured}
                    onChange={(e) => setMetadata(prev => ({ ...prev, featured: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    设为特色文章
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 