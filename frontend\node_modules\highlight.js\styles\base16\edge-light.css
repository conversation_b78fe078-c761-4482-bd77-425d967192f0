pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Edge Light
  Author: cjayross (https://github.com/cjayross)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme edge-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #fafafa  Default Background
base01  #7c9f4b  Lighter Background (Used for status bars, line number and folding marks)
base02  #d69822  Selection Background
base03  #5e646f  Comments, Invisibles, Line Highlighting
base04  #6587bf  Dark Foreground (Used for status bars)
base05  #5e646f  Default Foreground, Caret, Delimiters, Operators
base06  #b870ce  Light Foreground (Not often used)
base07  #5e646f  Light Background (Not often used)
base08  #db7070  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #db7070  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #d69822  Classes, Markup Bold, Search Text Background
base0B  #7c9f4b  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #509c93  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #6587bf  Functions, Methods, Attribute IDs, Headings
base0E  #b870ce  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #509c93  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #5e646f;
  background: #fafafa
}
.hljs::selection,
.hljs ::selection {
  background-color: #d69822;
  color: #5e646f
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #5e646f -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #5e646f
}
/* base04 - #6587bf -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #6587bf
}
/* base05 - #5e646f -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #5e646f
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #db7070
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #db7070
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #d69822
}
.hljs-strong {
  font-weight: bold;
  color: #d69822
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7c9f4b
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #509c93
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #6587bf
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #b870ce
}
.hljs-emphasis {
  color: #b870ce;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #509c93
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}