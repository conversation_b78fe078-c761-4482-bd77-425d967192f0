/**
 * 文章操作组件
 * 提供编辑、删除等管理功能
 */
import { useState } from 'react'
import { createPortal } from 'react-dom'
import { Edit, Trash2, MoreVertical } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { BlogService } from '../services/blogService'
import { useAuth } from '../contexts/AuthContext'

interface PostActionsProps {
  slug: string
  title: string
  onDeleted?: () => void
  showEdit?: boolean
  showDelete?: boolean
}

export function PostActions({ 
  slug, 
  title, 
  onDeleted, 
  showEdit = true, 
  showDelete = true 
}: PostActionsProps) {
  const { isAdmin } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const navigate = useNavigate()

  const handleEdit = () => {
    navigate(`/write/${slug}`)
    setIsMenuOpen(false)
  }

  const handleDeleteClick = () => {
    console.log('Delete button clicked, showing modal')
    setShowDeleteConfirm(true)
    setIsMenuOpen(false)
  }

  const handleDeleteConfirm = async () => {
    if (isDeleting) return

    try {
      setIsDeleting(true)
      await BlogService.deletePost(slug)
      
      // 通知父组件文章已删除
      onDeleted?.()
      
      // 如果是在文章详情页，返回首页
      if (window.location.pathname.includes(`/post/${slug}`)) {
        navigate('/')
      }
    } catch (error) {
      console.error('删除文章失败:', error)
      alert('删除文章失败，请稍后重试')
    } finally {
      setIsDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  const handleDeleteCancel = () => {
    console.log('Cancel button clicked, hiding modal')
    setShowDeleteConfirm(false)
  }

  // 如果不是管理员，不显示操作按钮
  if (!isAdmin || (!showEdit && !showDelete)) {
    return null
  }

  // 删除确认对话框组件
  const DeleteConfirmModal = () => (
    <div className="fixed inset-0 z-[99999] overflow-y-auto" style={{ zIndex: 99999 }}>
      {/* 背景遮罩 */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleDeleteCancel}
        style={{ zIndex: 99999 }}
      />

      {/* 弹窗容器 */}
      <div className="flex min-h-full items-center justify-center p-4 text-center" style={{ zIndex: 100000 }}>
        <div
          className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"
          onClick={(e) => e.stopPropagation()}
          style={{ zIndex: 100001 }}
        >
          {/* 弹窗内容 */}
          <div className="bg-white dark:bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div className="sm:flex sm:items-start">
              <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20 sm:mx-0 sm:h-10 sm:w-10">
                <Trash2 className="h-6 w-6 text-red-600 dark:text-red-400" />
              </div>
              <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                <h3 className="text-base font-semibold leading-6 text-gray-900 dark:text-white">
                  确认删除文章
                </h3>
                <div className="mt-2">
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    你确定要删除文章 <span className="font-medium text-gray-900 dark:text-white">"{title}"</span> 吗？此操作无法撤销。
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button
              type="button"
              onClick={handleDeleteConfirm}
              disabled={isDeleting}
              className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isDeleting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  删除中...
                </>
              ) : (
                '确认删除'
              )}
            </button>
            <button
              type="button"
              onClick={handleDeleteCancel}
              disabled={isDeleting}
              className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-600 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500 sm:mt-0 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
            >
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  )

  return (
    <>
      <div className="relative">
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          title="文章操作"
        >
          <MoreVertical size={18} />
        </button>

        {isMenuOpen && (
          <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-[9999]">
            {showEdit && (
              <button
                onClick={handleEdit}
                className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 first:rounded-t-lg"
              >
                <Edit size={16} />
                <span>编辑文章</span>
              </button>
            )}
            {showDelete && (
              <button
                onClick={handleDeleteClick}
                className="w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2 last:rounded-b-lg"
              >
                <Trash2 size={16} />
                <span>删除文章</span>
              </button>
            )}
          </div>
        )}

        {/* 点击外部关闭菜单 */}
        {isMenuOpen && (
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsMenuOpen(false)}
          />
        )}
      </div>

      {/* 删除确认对话框 - 使用Portal渲染到body */}
      {showDeleteConfirm && (
        console.log('Rendering delete confirm modal, showDeleteConfirm:', showDeleteConfirm),
        createPortal(
          <DeleteConfirmModal />,
          document.body
        )
      )}
    </>
  )
} 