/**
 * 文章操作组件
 * 提供编辑、删除等管理功能
 */
import { useState } from 'react'
import { Edit, Trash2, MoreVertical } from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { BlogService } from '../services/blogService'
import { useAuth } from '../contexts/AuthContext'
import { DeleteConfirmModal } from './DeleteConfirmModal'

interface PostActionsProps {
  slug: string
  title: string
  onDeleted?: () => void
  showEdit?: boolean
  showDelete?: boolean
}

export function PostActions({ 
  slug, 
  title, 
  onDeleted, 
  showEdit = true, 
  showDelete = true 
}: PostActionsProps) {
  const { isAdmin } = useAuth()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const navigate = useNavigate()

  const handleEdit = () => {
    navigate(`/write/${slug}`)
    setIsMenuOpen(false)
  }

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true)
    setIsMenuOpen(false)
  }

  const handleDeleteConfirm = async () => {
    if (isDeleting) return

    try {
      setIsDeleting(true)
      await BlogService.deletePost(slug)
      
      // 通知父组件文章已删除
      onDeleted?.()
      
      // 如果是在文章详情页，返回首页
      if (window.location.pathname.includes(`/post/${slug}`)) {
        navigate('/')
      }
    } catch (error) {
      console.error('删除文章失败:', error)
      alert('删除文章失败，请稍后重试')
    } finally {
      setIsDeleting(false)
      setShowDeleteConfirm(false)
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false)
  }

  // 如果不是管理员，不显示操作按钮
  if (!isAdmin || (!showEdit && !showDelete)) {
    return null
  }



  return (
    <>
      <div className="relative">
        <button
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          title="文章操作"
        >
          <MoreVertical size={18} />
        </button>

        {isMenuOpen && (
          <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-[9999]">
            {showEdit && (
              <button
                onClick={handleEdit}
                className="w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 first:rounded-t-lg"
              >
                <Edit size={16} />
                <span>编辑文章</span>
              </button>
            )}
            {showDelete && (
              <button
                onClick={handleDeleteClick}
                className="w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2 last:rounded-b-lg"
              >
                <Trash2 size={16} />
                <span>删除文章</span>
              </button>
            )}
          </div>
        )}

        {/* 点击外部关闭菜单 */}
        {isMenuOpen && (
          <div
            className="fixed inset-0 z-40"
            onClick={() => setIsMenuOpen(false)}
          />
        )}
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmModal
        isOpen={showDeleteConfirm}
        title={title}
        isDeleting={isDeleting}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
      />
    </>
  )
} 