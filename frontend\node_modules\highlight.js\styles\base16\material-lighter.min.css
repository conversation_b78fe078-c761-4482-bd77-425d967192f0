/*!
  Theme: Material Lighter
  Author: <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#80cbc4;background:#fafafa}.hljs ::selection,.hljs::selection{background-color:#cceae7;color:#80cbc4}.hljs-comment{color:#ccd7da}.hljs-tag{color:#8796b0}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#80cbc4}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#ff5370}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#f76d47}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#ffb62c}.hljs-strong{font-weight:700;color:#ffb62c}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#91b859}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#39adb5}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#6182b8}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#7c4dff}.hljs-emphasis{color:#7c4dff;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#e53935}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}