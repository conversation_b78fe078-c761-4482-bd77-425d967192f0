pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: One Light
  Author: <PERSON> (http://github.com/purpleKarrot)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme one-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #fafafa  Default Background
base01  #f0f0f1  Lighter Background (Used for status bars, line number and folding marks)
base02  #e5e5e6  Selection Background
base03  #a0a1a7  Comments, Invisibles, Line Highlighting
base04  #696c77  Dark Foreground (Used for status bars)
base05  #383a42  Default Foreground, Caret, Delimiters, Operators
base06  #202227  Light Foreground (Not often used)
base07  #090a0b  Light Background (Not often used)
base08  #ca1243  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #d75f00  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #c18401  Classes, Markup Bold, Search Text Background
base0B  #50a14f  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #0184bc  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #4078f2  Functions, Methods, Attribute IDs, Headings
base0E  #a626a4  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #986801  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #383a42;
  background: #fafafa
}
.hljs::selection,
.hljs ::selection {
  background-color: #e5e5e6;
  color: #383a42
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #a0a1a7 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #a0a1a7
}
/* base04 - #696c77 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #696c77
}
/* base05 - #383a42 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #383a42
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ca1243
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #d75f00
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #c18401
}
.hljs-strong {
  font-weight: bold;
  color: #c18401
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #50a14f
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #0184bc
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #4078f2
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #a626a4
}
.hljs-emphasis {
  color: #a626a4;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #986801
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}