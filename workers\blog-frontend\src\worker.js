/**
 * 统一的博客前端Worker - 整合所有功能
 * 1. 前端静态文件服务（代理到Pages）
 * 2. 博客API（KV存储）
 * 3. 音乐API（7个音源聚合）
 * 4. AI图像API
 * 5. 权限管理系统
 */

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-API-Key',
  'Access-Control-Max-Age': '86400',
}

// 权限验证中间件
function verifyAdminPermission(request, env) {
  const apiKey = request.headers.get('X-API-Key') || request.headers.get('Authorization')?.replace('Bearer ', '')
  const adminKey = env.ADMIN_API_KEY || env.SHYKEY || 'ybw' // 支持多种环境变量配置
  
  console.log(`[Auth] 请求API密钥: ${apiKey ? '***' : '无'}, 系统密钥: ${adminKey ? '***' : '无'}`)
  
  return apiKey === adminKey
}

function createUnauthorizedResponse() {
  return new Response(JSON.stringify({ 
    error: 'Unauthorized', 
    message: '需要管理员权限才能执行此操作' 
  }), {
    status: 401,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

const MUSIC_SOURCES = {
  wyy: { name: '网易云音乐', type: 'wyy', priority: 1, enabled: true },
  qq: { name: 'QQ音乐', type: 'qq', priority: 2, enabled: true },
  kg: { name: '酷狗音乐', type: 'kg', priority: 3, enabled: true },
  kw: { name: '酷我音乐', type: 'kw', priority: 4, enabled: true },
  xmla: { name: '喜马拉雅', type: 'xmla', priority: 5, enabled: true },
  qishui: { name: '汽水音乐', type: 'qishui', priority: 6, enabled: true },
  qt: { name: '蜻蜓FM', type: 'qt', priority: 7, enabled: true }
}

const SHYBOT_BASE_URL = 'http://shybot.top/v2/music/api'
const SHYBOT_HEADERS = {
  'Accept': '*/*',
  'Accept-Language': 'zh-CN,zh;q=0.9',
  'X-Requested-With': 'XMLHttpRequest',
  'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
}

/**
 * 智能专辑图片提取器 - 支持多音源数据格式
 */
class AlbumPictureExtractor {
  constructor() {
    // 各音源的图片字段映射表
    this.fieldMappings = {
      // 通用字段（优先级最高）
      common: ['picUrl', 'pic', 'cover', 'image', 'img', 'albumPic', 'artwork', 'albumCover'],
      
      // QQ音乐特有字段
      qq: ['album.picUrl', 'albumpic', 'pic', 'cover'],
      
      // 网易云音乐特有字段  
      wyy: ['al.picUrl', 'album.picUrl', 'pic', 'picUrl'],
      
      // 酷狗音乐特有字段
      kg: ['imgUrl', 'pic', 'singerpic', 'album_img'],
      
      // 酷我音乐特有字段
      kw: ['pic', 'artist_pic', 'album_pic'],
      
      // 其他音源
      xmla: ['cover_url', 'pic', 'cover'],
      qishui: ['pic', 'cover'],
      qt: ['pic', 'cover_url']
    }
    
    // 图片URL构建规则
    this.urlBuilders = {
      qq: (song) => {
        // QQ音乐图片URL构建规则
        if (song.albummid || song.album_mid) {
          const mid = song.albummid || song.album_mid;
          return `https://y.gtimg.cn/music/photo_new/T002R800x800M000${mid}.jpg?max_age=2592000`;
        }
        if (song.mid) {
          return `https://y.gtimg.cn/music/photo_new/T002R800x800M000${song.mid}.jpg?max_age=2592000`;
        }
        return null;
      },
      
      wyy: (song) => {
        // 网易云音乐图片URL构建规则
        const albumId = song.al?.id || song.album_id || song.albumId;
        if (albumId) {
          return `https://p1.music.126.net/6y-UleORITEDbvrOLV0Q2g==/${albumId}.jpg?param=800y800`;
        }
        return null;
      },
      
      kg: (song) => {
        // 酷狗音乐通常在API响应中直接包含图片URL
        // 如果没有，可以尝试通过hash构建，但这通常不可靠
        return null;
      }
    }
  }
  
  /**
   * 从歌曲数据中提取专辑图片URL
   * @param {Object} songData - 歌曲数据对象
   * @param {string} sourceType - 音源类型 (qq, wyy, kg, etc.)
   * @returns {string|null} - 图片URL或null
   */
  extractPictureUrl(songData, sourceType = 'common') {
    console.log(`[AlbumPicture] 开始提取图片URL，音源: ${sourceType}，歌曲: ${songData.name}`);
    console.log(`[AlbumPicture] 原始歌曲数据:`, JSON.stringify(songData, null, 2));
    
    // 1. 首先尝试音源特定的字段
    const sourceFields = this.fieldMappings[sourceType] || [];
    for (const field of sourceFields) {
      const url = this.getNestedProperty(songData, field);
      if (this.isValidImageUrl(url)) {
        console.log(`[AlbumPicture] 通过音源特定字段 "${field}" 找到图片: ${url}`);
        return this.normalizeImageUrl(url);
      }
    }
    
    // 2. 尝试通用字段
    for (const field of this.fieldMappings.common) {
      const url = this.getNestedProperty(songData, field);
      if (this.isValidImageUrl(url)) {
        console.log(`[AlbumPicture] 通过通用字段 "${field}" 找到图片: ${url}`);
        return this.normalizeImageUrl(url);
      }
    }
    
    // 3. 尝试深度搜索所有可能的图片字段
    const foundUrls = this.deepSearchImageFields(songData);
    if (foundUrls.length > 0) {
      console.log(`[AlbumPicture] 通过深度搜索找到图片: ${foundUrls[0]}`);
      return this.normalizeImageUrl(foundUrls[0]);
    }
    
    // 4. 尝试通过音源特定规则构建URL
    if (this.urlBuilders[sourceType]) {
      const builtUrl = this.urlBuilders[sourceType](songData);
      if (builtUrl) {
        console.log(`[AlbumPicture] 通过构建规则生成图片: ${builtUrl}`);
        return builtUrl;
      }
    }
    
    console.log(`[AlbumPicture] 未找到有效的图片URL，歌曲: ${songData.name}`);
    return null;
  }
  
  /**
   * 获取嵌套属性值
   * @param {Object} obj - 对象
   * @param {string} path - 属性路径，如 'album.picUrl'
   * @returns {any} - 属性值
   */
  getNestedProperty(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }
  
  /**
   * 深度搜索对象中的所有图片字段
   * @param {Object} obj - 要搜索的对象
   * @returns {Array} - 找到的图片URL数组
   */
  deepSearchImageFields(obj, visited = new Set()) {
    const imageUrls = [];
    const imageKeywords = ['pic', 'img', 'cover', 'image', 'photo', 'artwork', 'avatar', 'thumb'];
    
    const search = (current, path = '') => {
      if (!current || typeof current !== 'object' || visited.has(current)) {
        return;
      }
      visited.add(current);
      
      for (const [key, value] of Object.entries(current)) {
        const currentPath = path ? `${path}.${key}` : key;
        
        // 检查是否是图片字段
        const lowerKey = key.toLowerCase();
        if (imageKeywords.some(keyword => lowerKey.includes(keyword))) {
          if (this.isValidImageUrl(value)) {
            imageUrls.push(value);
          }
        }
        
        // 递归搜索嵌套对象
        if (typeof value === 'object' && value !== null) {
          search(value, currentPath);
        }
      }
    };
    
    search(obj);
    return imageUrls;
  }
  
  /**
   * 验证是否为有效的图片URL
   * @param {any} url - 要验证的URL
   * @returns {boolean} - 是否有效
   */
  isValidImageUrl(url) {
    if (typeof url !== 'string' || !url.trim()) {
      return false;
    }
    
    // 检查是否为HTTP/HTTPS URL
    if (!url.match(/^https?:\/\//)) {
      return false;
    }
    
    // 检查是否包含常见的图片格式
    const imageExtensions = /\.(jpg|jpeg|png|gif|webp|bmp)(\?.*)?$/i;
    const hasImageExtension = imageExtensions.test(url);
    
    // 检查是否为已知的音乐平台图片域名
    const musicImageDomains = [
      'music.126.net',      // 网易云音乐
      'p1.music.126.net',   // 网易云音乐
      'p2.music.126.net',   // 网易云音乐  
      'p3.music.126.net',   // 网易云音乐
      'p4.music.126.net',   // 网易云音乐
      'y.gtimg.cn',         // QQ音乐
      'qpic.y.qq.com',      // QQ音乐
      'imgcache.qq.com',    // QQ音乐
      'singerimg.kugou.com', // 酷狗音乐
      'imge.kugou.com',     // 酷狗音乐
      'star.kuwo.cn',       // 酷我音乐
      'qukufile2.qianqian.com' // 百度音乐
    ];
    
    const hasMusicDomain = musicImageDomains.some(domain => url.includes(domain));
    
    return hasImageExtension || hasMusicDomain;
  }
  
  /**
   * 标准化图片URL
   * @param {string} url - 原始URL
   * @returns {string} - 标准化后的URL
   */
  normalizeImageUrl(url) {
    if (!url) return url;
    
    // 强制使用HTTPS
    if (url.startsWith('http://')) {
      url = url.replace('http://', 'https://');
    }
    
    // 网易云音乐图片URL优化
    if (url.includes('music.126.net')) {
      // 确保使用高清参数
      if (!url.includes('param=')) {
        url += url.includes('?') ? '&param=800y800' : '?param=800y800';
      }
    }
    
    // QQ音乐图片URL优化
    if (url.includes('y.gtimg.cn') || url.includes('qpic.y.qq.com')) {
      // 确保使用高清尺寸
      if (url.includes('T002R') && !url.includes('T002R800x800')) {
        url = url.replace(/T002R\d+x\d+/, 'T002R800x800');
      }
    }
    
    return url;
  }
}

// 全局实例
const albumPictureExtractor = new AlbumPictureExtractor();

/**
 * 增强的歌曲数据处理器
 */
function processSongData(rawSong, sourceType) {
  console.log(`[DataProcessor] 处理歌曲数据，音源: ${sourceType}，歌曲: ${rawSong.name}`);
  
  // 提取专辑图片
  const albumPicUrl = albumPictureExtractor.extractPictureUrl(rawSong, sourceType);
  
  // 构建标准化的歌曲对象
  const processedSong = {
    id: rawSong.id || rawSong.songid || rawSong.hash || generateTempId(rawSong),
    name: rawSong.name || rawSong.songname || rawSong.title || '未知歌曲',
    artists: extractArtists(rawSong),
    album: {
      id: extractAlbumId(rawSong),
      name: extractAlbumName(rawSong),
      picUrl: albumPicUrl || '/default-album.jpg'
    },
    duration: extractDuration(rawSong),
    sourceType: sourceType,
    sourceName: MUSIC_SOURCES[sourceType]?.name || '未知音源',
    // 保留原始字段用于播放URL生成
    originalData: {
      mid: rawSong.mid,
      media_mid: rawSong.media_mid,
      albummid: rawSong.albummid || rawSong.album_mid,
      vid: rawSong.vid,
      hash: rawSong.hash,
      album_id: rawSong.album_id,
      album_audio_id: rawSong.album_audio_id,
      pay: rawSong.pay,
      privilege: rawSong.privilege
    }
  };
  
  console.log(`[DataProcessor] 处理完成，专辑图片: ${processedSong.album.picUrl}`);
  return processedSong;
}

// 辅助函数
function extractArtists(song) {
  if (song.singer) return [{ name: song.singer }];
  if (song.artists && Array.isArray(song.artists)) return song.artists;
  if (song.ar && Array.isArray(song.ar)) return song.ar;
  if (song.artist) return [{ name: song.artist }];
  if (song.singername) return [{ name: song.singername }];
  return [{ name: '未知艺术家' }];
}

function extractAlbumId(song) {
  return song.album?.id || song.albumid || song.al?.id || song.album_id || null;
}

function extractAlbumName(song) {
  return song.album?.name || song.album || song.al?.name || song.albumname || '未知专辑';
}

function extractDuration(song) {
  if (song.duration) return song.duration;
  if (song.dt) return song.dt;
  if (song.interval) return song.interval * 1000;
  if (song.time) return song.time * 1000;
  return 0;
}

function generateTempId(song) {
  // 生成临时ID，基于歌曲名和艺术家
  const name = song.name || song.songname || 'unknown';
  const artist = song.singer || song.artist || song.singername || 'unknown';
  return btoa(`${name}-${artist}-${Date.now()}`).replace(/[^a-zA-Z0-9]/g, '').substring(0, 16);
}

export default {
  async fetch(request, env, ctx) {
    // 从环境变量获取shykey，如果没有设置则使用用户提供的正确值
    const SHYKEY = env.SHYKEY || '333dd95934d98caae0f7903e615f95fe5b4a69316e45cb87d0edb50518f67786'
    
    // 为了在其他函数中使用，将SHYKEY添加到全局
    globalThis.CURRENT_SHYKEY = SHYKEY
    const url = new URL(request.url)
    const pathname = url.pathname

    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 200, headers: corsHeaders })
    }

    try {
      // API 路由分发
      if (pathname.startsWith('/api/')) {
        return handleApiRequest(request, env, pathname)
      }
      if (pathname.startsWith('/music/')) {
        return handleMusicRequest(request, pathname)
      }
      if (pathname.startsWith('/ai/')) {
        return handleAiRequest(request, pathname)
      }
      if (pathname.startsWith('/weather')) {
        return handleWeatherRequest(request, pathname)
      }

      // 静态文件代理到Pages
      return handleStaticRequest(pathname)

    } catch (error) {
      return new Response(JSON.stringify({
        error: 'Internal server error',
        message: error.message
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  }
}

// 静态文件处理 - 代理到Pages项目
async function handleStaticRequest(pathname) {
  // 为了避免循环代理，直接返回静态资源或使用备用Pages URL
  const pagesUrl = `https://429e8830.modern-blog-35d.pages.dev${pathname}`

  try {
    const response = await fetch(pagesUrl)

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}`)
    }

    // 获取正确的 Content-Type
    let contentType = response.headers.get('Content-Type') || ''

    // 根据文件扩展名设置正确的 MIME 类型
    if (pathname.endsWith('.css')) {
      contentType = 'text/css; charset=utf-8'
    } else if (pathname.endsWith('.js') || pathname.endsWith('.mjs')) {
      contentType = 'application/javascript; charset=utf-8'
    } else if (pathname.endsWith('.json')) {
      contentType = 'application/json; charset=utf-8'
    } else if (pathname.endsWith('.svg')) {
      contentType = 'image/svg+xml'
    } else if (pathname.endsWith('.png')) {
      contentType = 'image/png'
    } else if (pathname.endsWith('.jpg') || pathname.endsWith('.jpeg')) {
      contentType = 'image/jpeg'
    } else if (pathname.endsWith('.ico')) {
      contentType = 'image/x-icon'
    } else if (pathname === '/' || pathname.endsWith('.html')) {
      contentType = 'text/html; charset=utf-8'
    }

    // 创建新的响应头，确保包含正确的 Content-Type
    const newHeaders = new Headers(response.headers)
    newHeaders.set('Content-Type', contentType)

    // 根据文件类型设置不同的缓存策略
    if (pathname === '/' || pathname.endsWith('.html')) {
      newHeaders.set('Cache-Control', 'public, max-age=300') // HTML 缓存5分钟
    } else if (pathname.endsWith('.css') || pathname.endsWith('.js') || pathname.endsWith('.mjs')) {
      newHeaders.set('Cache-Control', 'public, max-age=86400') // CSS/JS 缓存1天
    } else {
      newHeaders.set('Cache-Control', 'public, max-age=31536000') // 其他静态资源缓存1年
    }

    return new Response(response.body, {
      status: response.status,
      headers: newHeaders
    })

  } catch (error) {
    console.error(`[静态文件] 获取失败: ${pathname}`, error)

    if (pathname === '/' || pathname === '/index.html') {
      return new Response(`
        <!DOCTYPE html>
        <html>
        <head><title>博客系统</title><meta charset="utf-8"></head>
        <body><h1>博客系统</h1><p>前端加载中...</p></body>
        </html>
      `, {
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
      })
    }
    return new Response('File not found', {
      status: 404,
      headers: { 'Content-Type': 'text/plain' }
    })
  }
}

// 博客API处理
async function handleApiRequest(request, env, pathname) {
  if (pathname === '/api/posts') {
    if (request.method === 'GET') {
      return getPosts(request, env)
    } else if (request.method === 'POST') {
      // 创建文章需要管理员权限
      if (!verifyAdminPermission(request, env)) {
        return createUnauthorizedResponse()
      }
      return createPost(request, env)
    }
  }
  
  // 单篇文章处理
  if (pathname.startsWith('/api/posts/')) {
    const pathParts = pathname.split('/')
    const slug = pathParts[3]
    
    // 评论相关API
    if (pathParts.length === 5 && pathParts[4] === 'comments') {
      if (request.method === 'GET') {
        return getComments(env, slug)
      } else if (request.method === 'POST') {
        return createComment(request, env, slug)
      }
    }
    
    // 单条评论操作
    if (pathParts.length === 6 && pathParts[4] === 'comments') {
      const commentId = pathParts[5]
      if (request.method === 'DELETE') {
        // 删除评论需要管理员权限
        if (!verifyAdminPermission(request, env)) {
          return createUnauthorizedResponse()
        }
        return deleteComment(env, slug, commentId)
      }
    }
    
    // 文章操作
    if (pathParts.length === 4) {
      if (request.method === 'GET') {
        return getPost(env, slug)
      } else if (request.method === 'PUT') {
        // 更新文章需要管理员权限
        if (!verifyAdminPermission(request, env)) {
          return createUnauthorizedResponse()
        }
        return updatePost(request, env, slug)
      } else if (request.method === 'DELETE') {
        // 删除文章需要管理员权限
        if (!verifyAdminPermission(request, env)) {
          return createUnauthorizedResponse()
        }
        return deletePost(env, slug)
      }
    }
  }
  
  // 分类列表
  if (pathname === '/api/categories') {
    return getCategories(env)
  }
  
  // 标签列表  
  if (pathname === '/api/tags') {
    return getTags(env)
  }
  
  // 图片上传
  if (pathname === '/api/upload-image' && request.method === 'POST') {
    return uploadImage(request, env)
  }

  // 图片优化代理
  if (pathname.startsWith('/api/image-proxy/')) {
    return imageProxy(request, env)
  }
  
  // 搜索功能
  if (pathname === '/api/search') {
    return searchPosts(request, env)
  }

  // 权限验证API
  if (pathname === '/api/auth/verify' && request.method === 'POST') {
    return verifyAuth(request, env)
  }
  
  // 数据一致性检查和修复API（管理员专用）
  if (pathname === '/api/admin/data-integrity') {
    if (!verifyAdminPermission(request, env)) {
      return createUnauthorizedResponse()
    }
    if (request.method === 'GET') {
      return await checkDataIntegrity(env)
    } else if (request.method === 'POST') {
      return await repairDataIntegrity(request, env)
    }
  }
  
  return new Response(JSON.stringify({ error: 'API endpoint not found' }), {
    status: 404,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function getPosts(request, env) {
  try {
    // 解析URL参数
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10')
    const category = url.searchParams.get('category')
    const tag = url.searchParams.get('tag')

    console.log(`[getPosts] 请求参数:`, { page, pageSize, category, tag })

    const postsIndex = await env.POSTS.get('posts_index', 'json') || []

    let posts = await Promise.all(
      postsIndex.map(async (postMeta) => {
        const post = await env.POSTS.get(`post:${postMeta.slug}`, 'json')
        if (post) {
          // 确保文章有有效的slug
          if (!post.slug || post.slug.trim() === '') {
            post.slug = post.id || `post-${Date.now()}`
          }

          // 清理slug格式，移除异常后缀
          if (post.slug && post.slug.includes(':')) {
            post.slug = post.slug.split(':')[0]
          }

          // 保存原始slug用于验证
          const originalSlug = post.slug

          // 确保slug只包含有效字符，但保留中文字符
          post.slug = post.slug.replace(/[^\u4e00-\u9fa5a-zA-Z0-9\-_]/g, '')

          // 如果清理后的slug为空或太短，使用文章ID
          if (!post.slug || post.slug.length < 2) {
            post.slug = post.id || `post-${Date.now()}`
          }

          return post
        }
        return postMeta
      })
    )

    // 过滤掉无效的文章
    posts = posts.filter(post => post && post.title && post.slug)

    // 应用分类筛选
    if (category && category !== 'general') {
      posts = posts.filter(post => post.category === category)
      console.log(`[getPosts] 按分类 "${category}" 筛选后: ${posts.length} 篇文章`)
    }

    // 应用标签筛选
    if (tag) {
      posts = posts.filter(post => post.tags && post.tags.includes(tag))
      console.log(`[getPosts] 按标签 "${tag}" 筛选后: ${posts.length} 篇文章`)
    }

    console.log(`[getPosts] 筛选后共 ${posts.length} 篇文章`)

    // 智能排序
    posts.sort((a, b) => {
      // 提取系列名称和课程编号的函数
      const extractSeriesInfo = (title) => {
        // 更全面的匹配模式
        const patterns = [
          // 深度学习-第X课
          /^(.+?)-第(\d+)课/,
          // 深度学习 第X课
          /^(.+?)\s+第(\d+)课/,
          // 第X课（独立格式）
          /^第(\d+)课/,
          // 深度学习-课程X
          /^(.+?)-课程(\d+)/,
          // 深度学习-X
          /^(.+?)-(\d+)/,
          // 深度学习 X
          /^(.+?)\s+(\d+)/,
          // 番外、特殊课程
          /^(.+?)-?(番外|特别|实战|项目)(\d*)/,
          /^(.+?)\s+(番外|特别|实战|项目)(\d*)/
        ]

        for (let i = 0; i < patterns.length; i++) {
          const pattern = patterns[i]
          const match = title.match(pattern)
          if (match) {
            if (i === 2) { // 第X课（独立格式）
              return {
                series: '深度学习', // 默认归类到深度学习
                number: parseInt(match[1]),
                hasNumber: true,
                isSpecial: false
              }
            } else if (i >= 6) { // 番外等特殊课程
              return {
                series: match[1].trim() || '深度学习',
                number: match[3] ? parseInt(match[3]) : 999, // 番外排在最后
                hasNumber: true,
                isSpecial: true
              }
            } else {
              return {
                series: match[1] ? match[1].trim() : '深度学习',
                number: parseInt(match[2]),
                hasNumber: true,
                isSpecial: false
              }
            }
          }
        }

        return {
          series: title,
          number: 0,
          hasNumber: false,
          isSpecial: false
        }
      }

      const aInfo = extractSeriesInfo(a.title)
      const bInfo = extractSeriesInfo(b.title)

      console.log(`[排序] ${a.title} -> 系列: ${aInfo.series}, 编号: ${aInfo.number}, 特殊: ${aInfo.isSpecial}`)
      console.log(`[排序] ${b.title} -> 系列: ${bInfo.series}, 编号: ${bInfo.number}, 特殊: ${bInfo.isSpecial}`)

      // 如果是同一系列且都有编号
      if (aInfo.series === bInfo.series && aInfo.hasNumber && bInfo.hasNumber) {
        // 特殊课程（番外）排在最后
        if (aInfo.isSpecial && !bInfo.isSpecial) return 1
        if (!aInfo.isSpecial && bInfo.isSpecial) return -1
        // 都是特殊课程或都是普通课程，按编号排序
        return aInfo.number - bInfo.number
      }

      // 如果是同一系列但编号情况不同，有编号的排在前面
      if (aInfo.series === bInfo.series) {
        if (aInfo.hasNumber && !bInfo.hasNumber) return -1
        if (!aInfo.hasNumber && bInfo.hasNumber) return 1
      }

      // 不同系列或都没有编号，按日期排序（新的在前）
      return new Date(b.date).getTime() - new Date(a.date).getTime()
    })

    // 分页
    const totalPosts = posts.length
    const totalPages = Math.ceil(totalPosts / pageSize)
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedPosts = posts.slice(startIndex, endIndex)

    return new Response(JSON.stringify({
      posts: paginatedPosts,
      pagination: {
        page,
        pageSize,
        totalPosts,
        totalPages
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('获取文章列表失败:', error)
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch posts',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 单篇文章获取
async function getPost(env, slug) {
  try {
    console.log(`[getPost] 开始查找文章: ${slug}`)

    // URL解码处理
    let decodedSlug = slug
    try {
      decodedSlug = decodeURIComponent(slug)
      console.log(`[getPost] URL解码后: ${decodedSlug}`)
    } catch (e) {
      console.log(`[getPost] URL解码失败，使用原始slug: ${slug}`)
    }

    // 查找策略数组，按优先级排序
    const searchStrategies = [
      // 1. 直接使用原始slug查找
      () => env.POSTS.get(`post:${slug}`, 'json'),

      // 2. 使用解码后的slug查找
      () => decodedSlug !== slug ? env.POSTS.get(`post:${decodedSlug}`, 'json') : null,

      // 3. 从posts_index中通过slug查找（原始）
      async () => {
        const postsIndex = await env.POSTS.get('posts_index', 'json') || []
        const postMeta = postsIndex.find(p => p.slug === slug)
        return postMeta ? env.POSTS.get(`post:${postMeta.slug}`, 'json') : null
      },

      // 4. 从posts_index中通过slug查找（解码后）
      async () => {
        const postsIndex = await env.POSTS.get('posts_index', 'json') || []
        const postMeta = postsIndex.find(p => p.slug === decodedSlug)
        return postMeta ? env.POSTS.get(`post:${postMeta.slug}`, 'json') : null
      },

      // 5. 从posts_index中通过ID查找
      async () => {
        const postsIndex = await env.POSTS.get('posts_index', 'json') || []
        const postMeta = postsIndex.find(p => p.id === slug || p.id === decodedSlug)
        return postMeta ? env.POSTS.get(`post:${postMeta.slug}`, 'json') : null
      },

      // 6. 通过标题模糊匹配
      async () => {
        const postsIndex = await env.POSTS.get('posts_index', 'json') || []
        const postMeta = postsIndex.find(p =>
          p.title === decodedSlug ||
          p.title === slug ||
          p.slug?.includes(decodedSlug) ||
          p.slug?.includes(slug) ||
          decodedSlug.includes(p.title) ||
          slug.includes(p.title)
        )
        return postMeta ? env.POSTS.get(`post:${postMeta.slug}`, 'json') : null
      }
    ]

    // 依次尝试各种查找策略
    let post = null
    for (let i = 0; i < searchStrategies.length; i++) {
      try {
        post = await searchStrategies[i]()
        if (post) {
          console.log(`[getPost] 策略 ${i + 1} 找到文章: ${post.title}`)
          break
        }
      } catch (error) {
        console.warn(`[getPost] 策略 ${i + 1} 查找失败:`, error.message)
      }
    }
    
    if (!post) {
      console.log(`[getPost] 所有策略都未找到文章: ${slug}`)
      
      // 输出调试信息
      const postsIndex = await env.POSTS.get('posts_index', 'json') || []
      console.log(`[getPost] 当前索引中的文章:`, postsIndex.map(p => ({ id: p.id, slug: p.slug, title: p.title })))
      
      return new Response(JSON.stringify({ 
        error: 'Post not found',
        slug: slug,
        message: `文章 ${slug} 不存在`,
        debug: {
          indexedPosts: postsIndex.length,
          searchedSlug: slug
        }
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    // 验证文章数据完整性
    if (!post.title || !post.slug) {
      console.warn(`[getPost] 文章数据不完整:`, post)
    }
    
    console.log(`[getPost] 成功获取文章: ${post.title}`)
    return new Response(JSON.stringify(post), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error(`[getPost] 获取文章失败: ${error.message}`)
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch post',
      message: error.message,
      slug: slug
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 分类列表
async function getCategories(env) {
  try {
    const postsIndex = await env.POSTS.get('posts_index', 'json') || []
    const categories = [...new Set(postsIndex.map(post => post.category).filter(Boolean))]
    
    return new Response(JSON.stringify({
      categories: categories.map(cat => ({ name: cat, count: postsIndex.filter(p => p.category === cat).length }))
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch categories',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 标签列表
async function getTags(env) {
  try {
    const postsIndex = await env.POSTS.get('posts_index', 'json') || []
    const allTags = postsIndex.flatMap(post => post.tags || [])
    const uniqueTags = [...new Set(allTags)]
    
    return new Response(JSON.stringify({
      tags: uniqueTags.map(tag => ({ name: tag, count: allTags.filter(t => t === tag).length }))
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch tags',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 图片上传
async function uploadImage(request, env) {
  try {
    console.log('[图片上传] 开始处理图片上传请求')

    // 检查是否有 R2 绑定
    if (!env.IMAGE_BUCKET) {
      console.error('[图片上传] R2 存储桶未配置')
      return new Response(JSON.stringify({
        error: 'Storage not configured',
        message: 'R2存储桶未配置'
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 解析 multipart/form-data
    const formData = await request.formData()
    const file = formData.get('file')

    if (!file || !(file instanceof File)) {
      console.error('[图片上传] 未找到有效的文件')
      return new Response(JSON.stringify({
        error: 'No file provided',
        message: '请选择要上传的图片文件'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      console.error('[图片上传] 不支持的文件类型:', file.type)
      return new Response(JSON.stringify({
        error: 'Invalid file type',
        message: '仅支持 JPEG、PNG、GIF、WebP 格式的图片'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 验证文件大小 (最大 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      console.error('[图片上传] 文件过大:', file.size)
      return new Response(JSON.stringify({
        error: 'File too large',
        message: '图片文件大小不能超过 10MB'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 生成唯一的文件名
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 15)
    const fileExtension = file.name.split('.').pop() || 'jpg'
    const fileName = `images/${timestamp}-${randomStr}.${fileExtension}`

    console.log('[图片上传] 生成文件名:', fileName)
    console.log('[图片上传] 文件信息:', {
      name: file.name,
      type: file.type,
      size: file.size
    })

    // 处理图片压缩和格式转换
    let processedBuffer = await file.arrayBuffer()
    let finalContentType = file.type
    let finalFileName = fileName

    try {
      // 如果是JPEG或PNG，尝试压缩
      if (file.type === 'image/jpeg' || file.type === 'image/png') {
        console.log('[图片处理] 开始压缩图片')

        // 这里可以添加图片压缩逻辑
        // 由于Cloudflare Workers环境限制，我们先保持原始格式
        // 在实际部署时可以考虑使用Cloudflare Images服务

        // 生成WebP版本的文件名
        const webpFileName = fileName.replace(/\.(jpg|jpeg|png)$/i, '.webp')

        // 保存原始图片
        await env.IMAGE_BUCKET.put(fileName, processedBuffer, {
          httpMetadata: {
            contentType: finalContentType,
            cacheControl: 'public, max-age=31536000', // 缓存1年
          },
          customMetadata: {
            originalName: file.name,
            uploadTime: new Date().toISOString(),
            fileSize: file.size.toString(),
            imageType: 'original'
          }
        })

        console.log('[图片处理] 原始图片上传完成')
      } else {
        // 其他格式直接上传
        await env.IMAGE_BUCKET.put(fileName, processedBuffer, {
          httpMetadata: {
            contentType: finalContentType,
            cacheControl: 'public, max-age=31536000', // 缓存1年
          },
          customMetadata: {
            originalName: file.name,
            uploadTime: new Date().toISOString(),
            fileSize: file.size.toString()
          }
        })
      }
    } catch (compressionError) {
      console.error('[图片处理] 压缩失败，使用原始图片:', compressionError)

      // 压缩失败时使用原始图片
      await env.IMAGE_BUCKET.put(fileName, processedBuffer, {
        httpMetadata: {
          contentType: finalContentType,
          cacheControl: 'public, max-age=31536000', // 缓存1年
        },
        customMetadata: {
          originalName: file.name,
          uploadTime: new Date().toISOString(),
          fileSize: file.size.toString()
        }
      })
    }

    // 构建图片URL
    const imageUrl = `https://pub-a1a2.r2.dev/${finalFileName}`

    console.log('[图片上传] 上传成功:', imageUrl)

    return new Response(JSON.stringify({
      success: true,
      url: imageUrl,
      fileName: fileName,
      originalName: file.name,
      size: file.size,
      type: file.type
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })

  } catch (error) {
    console.error('[图片上传] 上传失败:', error)
    return new Response(JSON.stringify({
      error: 'Upload failed',
      message: `图片上传失败: ${error.message}`
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 图片优化代理
async function imageProxy(request, env) {
  try {
    const url = new URL(request.url)
    const imagePath = url.pathname.replace('/api/image-proxy/', '')
    const searchParams = url.searchParams

    // 解析优化参数
    const width = searchParams.get('w') ? parseInt(searchParams.get('w')) : null
    const height = searchParams.get('h') ? parseInt(searchParams.get('h')) : null
    const quality = searchParams.get('q') ? parseInt(searchParams.get('q')) : 85
    const format = searchParams.get('f') || 'auto'

    console.log('[图片代理] 请求参数:', { imagePath, width, height, quality, format })

    // 构建原始图片URL
    const originalUrl = `https://pub-a1a2.r2.dev/${imagePath}`

    // 检查浏览器是否支持WebP
    const acceptHeader = request.headers.get('Accept') || ''
    const supportsWebP = acceptHeader.includes('image/webp')
    const supportsAVIF = acceptHeader.includes('image/avif')

    // 确定最佳格式
    let targetFormat = format
    if (format === 'auto') {
      if (supportsAVIF) {
        targetFormat = 'avif'
      } else if (supportsWebP) {
        targetFormat = 'webp'
      } else {
        targetFormat = 'jpeg'
      }
    }

    // 构建缓存键
    const cacheKey = `image-cache:${imagePath}:${width || 'auto'}x${height || 'auto'}:${quality}:${targetFormat}`

    // 尝试从缓存获取
    const cached = await env.POSTS.get(cacheKey, 'arrayBuffer')
    if (cached) {
      console.log('[图片代理] 返回缓存图片')
      return new Response(cached, {
        headers: {
          ...corsHeaders,
          'Content-Type': `image/${targetFormat}`,
          'Cache-Control': 'public, max-age=31536000',
          'X-Cache': 'HIT'
        }
      })
    }

    // 获取原始图片
    const originalResponse = await fetch(originalUrl)
    if (!originalResponse.ok) {
      return new Response('Image not found', {
        status: 404,
        headers: corsHeaders
      })
    }

    const originalBuffer = await originalResponse.arrayBuffer()

    // 在实际环境中，这里可以使用图片处理库进行压缩和格式转换
    // 目前直接返回原始图片，但设置适当的缓存头

    // 缓存处理后的图片
    try {
      await env.POSTS.put(cacheKey, originalBuffer, {
        expirationTtl: 86400 * 30 // 30天过期
      })
    } catch (cacheError) {
      console.warn('[图片代理] 缓存失败:', cacheError)
    }

    return new Response(originalBuffer, {
      headers: {
        ...corsHeaders,
        'Content-Type': originalResponse.headers.get('Content-Type') || `image/${targetFormat}`,
        'Cache-Control': 'public, max-age=31536000',
        'X-Cache': 'MISS'
      }
    })

  } catch (error) {
    console.error('[图片代理] 处理失败:', error)
    return new Response('Image processing failed', {
      status: 500,
      headers: corsHeaders
    })
  }
}

// 搜索功能
async function searchPosts(request, env) {
  try {
    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    
    if (!query) {
      return new Response(JSON.stringify({ error: 'Search query is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    const postsIndex = await env.POSTS.get('posts_index', 'json') || []
    const results = postsIndex.filter(post => 
      post.title?.toLowerCase().includes(query.toLowerCase()) ||
      post.content?.toLowerCase().includes(query.toLowerCase()) ||
      post.excerpt?.toLowerCase().includes(query.toLowerCase())
    )
    
    return new Response(JSON.stringify({
      query,
      results,
      total: results.length
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Search failed',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 权限验证API
async function verifyAuth(request, env) {
  try {
    const isAdmin = verifyAdminPermission(request, env)
    return new Response(JSON.stringify({
      isAdmin,
      message: isAdmin ? '管理员身份验证成功' : '权限不足'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Auth verification failed',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 创建文章
async function createPost(request, env) {
  try {
    const postData = await request.json()
    
    // 验证必需字段
    if (!postData.title || !postData.content) {
      return new Response(JSON.stringify({ 
        error: 'Missing required fields',
        message: '标题和内容不能为空' 
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 生成唯一slug
    let slug = postData.slug || generateSlug(postData.title)

    // 检查slug是否已存在，如果存在则生成唯一的slug
    let existingPost = await env.POSTS.get(`post:${slug}`)
    let counter = 1
    const originalSlug = slug

    while (existingPost) {
      slug = `${originalSlug}-${counter}`
      existingPost = await env.POSTS.get(`post:${slug}`)
      counter++

      // 防止无限循环，最多尝试100次
      if (counter > 100) {
        // 使用时间戳作为最后的备选方案
        slug = `${originalSlug}-${Date.now()}`
        break
      }
    }

    console.log('[创建文章] 最终使用的slug:', slug)

    // 创建文章对象
    const post = {
      id: generateId(),
      slug,
      title: postData.title,
      content: postData.content,
      excerpt: postData.excerpt || postData.content.substring(0, 200) + '...',
      author: postData.author || 'Admin',
      date: new Date().toISOString().split('T')[0],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      category: postData.category || '',
      tags: postData.tags || [],
      featured: postData.featured || false,
      imageUrl: postData.imageUrl || '', // 添加封面图片字段
      readTime: Math.ceil(postData.content.length / 500) // 估算阅读时间
    }

    // 保存文章
    await env.POSTS.put(`post:${slug}`, JSON.stringify(post))
    
    // 更新文章索引
    const postsIndex = await env.POSTS.get('posts_index', 'json') || []
    const postMeta = {
      id: post.id,
      slug: post.slug,
      title: post.title,
      excerpt: post.excerpt,
      author: post.author,
      date: post.date,
      category: post.category,
      tags: post.tags,
      featured: post.featured,
      imageUrl: post.imageUrl, // 添加封面图片到索引
      readTime: post.readTime
    }
    postsIndex.unshift(postMeta)
    await env.POSTS.put('posts_index', JSON.stringify(postsIndex))

    return new Response(JSON.stringify(post), {
      status: 201,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Failed to create post',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 更新文章
async function updatePost(request, env, slug) {
  try {
    const postData = await request.json()
    
    // 获取现有文章
    const existingPost = await env.POSTS.get(`post:${slug}`, 'json')
    if (!existingPost) {
      return new Response(JSON.stringify({ 
        error: 'Post not found',
        message: '文章不存在' 
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 更新文章对象
    const updatedPost = {
      ...existingPost,
      title: postData.title || existingPost.title,
      content: postData.content || existingPost.content,
      excerpt: postData.excerpt || postData.content?.substring(0, 200) + '...' || existingPost.excerpt,
      category: postData.category !== undefined ? postData.category : existingPost.category,
      tags: postData.tags || existingPost.tags,
      featured: postData.featured !== undefined ? postData.featured : existingPost.featured,
      imageUrl: postData.imageUrl !== undefined ? postData.imageUrl : existingPost.imageUrl, // 添加封面图片更新
      updatedAt: new Date().toISOString(),
      readTime: postData.content ? Math.ceil(postData.content.length / 500) : existingPost.readTime
    }

    // 保存更新的文章
    await env.POSTS.put(`post:${slug}`, JSON.stringify(updatedPost))
    
    // 更新文章索引
    const postsIndex = await env.POSTS.get('posts_index', 'json') || []
    const postIndex = postsIndex.findIndex(p => p.slug === slug)
    if (postIndex !== -1) {
      postsIndex[postIndex] = {
        id: updatedPost.id,
        slug: updatedPost.slug,
        title: updatedPost.title,
        excerpt: updatedPost.excerpt,
        author: updatedPost.author,
        date: updatedPost.date,
        category: updatedPost.category,
        tags: updatedPost.tags,
        featured: updatedPost.featured,
        imageUrl: updatedPost.imageUrl, // 添加封面图片到索引更新
        readTime: updatedPost.readTime
      }
      await env.POSTS.put('posts_index', JSON.stringify(postsIndex))
    }

    return new Response(JSON.stringify(updatedPost), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Failed to update post',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 删除文章
async function deletePost(env, slug) {
  try {
    console.log(`[deletePost] 开始删除文章: ${slug}`)
    
    // 使用增强的查找逻辑获取完整的文章数据
    const getPostResponse = await getPost(env, slug)
    if (getPostResponse.status === 404) {
      console.log(`[deletePost] 文章不存在: ${slug}`)
      return new Response(JSON.stringify({ 
        error: 'Post not found',
        message: '文章不存在',
        slug: slug
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    const postData = await getPostResponse.json()
    console.log(`[deletePost] 找到文章: ${postData.title}`)
    
    // 记录要删除的图片URL（用于后续清理）
    const imageUrls = []
    if (postData.imageUrl) {
      imageUrls.push(postData.imageUrl)
      console.log(`[deletePost] 文章包含封面图片: ${postData.imageUrl}`)
    }

    // 查找所有可能的存储键并删除
    const keysToDelete = [
      `post:${slug}`,
      `post:${postData.slug}`,
      `post:${postData.id}`
    ]
    
    // 去重并删除文章数据
    const uniqueKeys = [...new Set(keysToDelete)]
    for (const key of uniqueKeys) {
      try {
        await env.POSTS.delete(key)
        console.log(`[deletePost] 删除键: ${key}`)
      } catch (error) {
        console.warn(`[deletePost] 删除键失败 ${key}:`, error.message)
      }
    }
    
    // 从索引中移除（支持多种匹配方式）
    const postsIndex = await env.POSTS.get('posts_index', 'json') || []
    const originalLength = postsIndex.length
    const filteredIndex = postsIndex.filter(p => 
      p.slug !== slug && 
      p.slug !== postData.slug && 
      p.id !== slug && 
      p.id !== postData.id
    )
    
    if (filteredIndex.length < originalLength) {
      await env.POSTS.put('posts_index', JSON.stringify(filteredIndex))
      console.log(`[deletePost] 从索引中删除文章，剩余 ${filteredIndex.length} 篇文章`)
    } else {
      console.warn(`[deletePost] 未能从索引中找到要删除的文章`)
    }

    // 删除相关评论
    const commentKeysToDelete = [
      `comments_index:${slug}`,
      `comments_index:${postData.slug}`
    ]
    
    for (const commentKey of commentKeysToDelete) {
      try {
        const commentsIndex = await env.POSTS.get(commentKey, 'json') || []
        
        // 删除所有评论
        for (const commentId of commentsIndex) {
          await env.POSTS.delete(`comment:${slug}:${commentId}`)
          await env.POSTS.delete(`comment:${postData.slug}:${commentId}`)
        }
        
        // 删除评论索引
        await env.POSTS.delete(commentKey)
        console.log(`[deletePost] 删除评论索引: ${commentKey}，包含 ${commentsIndex.length} 条评论`)
      } catch (error) {
        console.warn(`[deletePost] 删除评论失败:`, error.message)
      }
    }

    // TODO: 清理图片资源（需要R2权限配置）
    if (imageUrls.length > 0) {
      console.log(`[deletePost] 注意：文章包含 ${imageUrls.length} 个图片资源，请手动清理:`, imageUrls)
      // 这里可以添加R2图片删除逻辑，需要相应的权限配置
    }

    console.log(`[deletePost] 文章删除完成: ${postData.title}`)
    return new Response(JSON.stringify({ 
      message: '文章删除成功',
      slug: slug,
      title: postData.title,
      deletedImages: imageUrls.length,
      debug: {
        deletedKeys: uniqueKeys,
        originalSlug: postData.slug,
        originalId: postData.id
      }
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    console.error(`[deletePost] 删除文章失败: ${error.message}`)
    return new Response(JSON.stringify({ 
      error: 'Failed to delete post',
      message: error.message,
      slug: slug
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 获取评论
async function getComments(env, slug) {
  try {
    const commentsIndex = await env.POSTS.get(`comments_index:${slug}`, 'json') || []
    
    const comments = await Promise.all(
      commentsIndex.map(async (commentId) => {
        const comment = await env.POSTS.get(`comment:${slug}:${commentId}`, 'json')
        return comment
      })
    )
    
    // 按时间排序，最新的在前
    const sortedComments = comments
      .filter(Boolean)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())

    return new Response(JSON.stringify({ 
      comments: sortedComments 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Failed to fetch comments',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 创建评论
async function createComment(request, env, slug) {
  try {
    const commentData = await request.json()
    
    // 验证必需字段
    if (!commentData.content || !commentData.author) {
      return new Response(JSON.stringify({ 
        error: 'Missing required fields',
        message: '评论内容和姓名不能为空' 
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 验证字段长度
    if (commentData.content.length > 1000) {
      return new Response(JSON.stringify({ 
        error: 'Content too long',
        message: '评论内容过长，最多1000个字符' 
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    if (commentData.author.length > 50) {
      return new Response(JSON.stringify({ 
        error: 'Author name too long',
        message: '姓名过长，最多50个字符' 
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 验证邮箱格式
    if (commentData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(commentData.email)) {
      return new Response(JSON.stringify({ 
        error: 'Invalid email',
        message: '请输入有效的邮箱地址' 
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 创建评论对象
    const commentId = generateId()
    const comment = {
      id: commentId,
      postSlug: slug,
      content: commentData.content.trim(),
      author: commentData.author.trim(),
      email: commentData.email?.trim() || '',
      website: commentData.website?.trim() || '',
      createdAt: new Date().toISOString(),
      ip: request.headers.get('CF-Connecting-IP') || 'unknown'
    }

    // 保存评论
    await env.POSTS.put(`comment:${slug}:${commentId}`, JSON.stringify(comment))
    
    // 更新评论索引
    const commentsIndex = await env.POSTS.get(`comments_index:${slug}`, 'json') || []
    commentsIndex.unshift(commentId)
    await env.POSTS.put(`comments_index:${slug}`, JSON.stringify(commentsIndex))

    return new Response(JSON.stringify(comment), {
      status: 201,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Failed to create comment',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 删除评论
async function deleteComment(env, slug, commentId) {
  try {
    // 检查评论是否存在
    const existingComment = await env.POSTS.get(`comment:${slug}:${commentId}`)
    if (!existingComment) {
      return new Response(JSON.stringify({ 
        error: 'Comment not found',
        message: '评论不存在' 
      }), {
        status: 404,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    // 删除评论
    await env.POSTS.delete(`comment:${slug}:${commentId}`)
    
    // 从索引中移除
    const commentsIndex = await env.POSTS.get(`comments_index:${slug}`, 'json') || []
    const filteredIndex = commentsIndex.filter(id => id !== commentId)
    await env.POSTS.put(`comments_index:${slug}`, JSON.stringify(filteredIndex))

    return new Response(JSON.stringify({ 
      message: '评论删除成功',
      commentId 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  } catch (error) {
    return new Response(JSON.stringify({ 
      error: 'Failed to delete comment',
      message: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 工具函数
function generateSlug(title) {
  // 处理中文和特殊字符
  let slug = title
    .toLowerCase()
    .trim()
    // 保留中文字符、英文字母、数字、空格和连字符
    .replace(/[^\u4e00-\u9fa5\w\s-]/g, '')
    // 将空格和下划线替换为连字符
    .replace(/[\s_]+/g, '-')
    // 移除多余的连字符
    .replace(/-+/g, '-')
    // 移除开头和结尾的连字符
    .replace(/^-+|-+$/g, '')
    // 限制长度
    .substring(0, 50)

  // 如果slug为空或太短，使用时间戳
  if (!slug || slug.length < 2) {
    slug = `post-${Date.now().toString(36)}`
  }

  return slug
}

function generateId() {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 音乐API处理
async function handleMusicRequest(request, pathname) {
  const url = new URL(request.url)
  
  if (pathname === '/music/sources') {
    // 返回可用的音乐源列表
    const sources = Object.entries(MUSIC_SOURCES)
      .filter(([_, config]) => config.enabled)
      .map(([type, config]) => ({
        type,
        name: config.name,
        priority: config.priority,
        enabled: config.enabled
      }))
      .sort((a, b) => a.priority - b.priority)

    return new Response(JSON.stringify({
      code: 200,
      sources: sources,
      total: sources.length,
      message: '获取音乐源列表成功'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
  
  if (pathname === '/music/search') {
    const keywords = url.searchParams.get('keywords')
    const source = url.searchParams.get('source') // 获取指定音源参数
    const page = parseInt(url.searchParams.get('page') || '1')
    
    if (!keywords) {
      return new Response(JSON.stringify({ error: 'keywords is required' }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }

    const result = await searchMusic(keywords, page, source)
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
  
  if (pathname === '/music/url') {
    const source = url.searchParams.get('source') || 'wyy'
    let songData = {}
    
    // 根据音源类型收集所需参数
    switch (source) {
      case 'qq':
        songData = {
          mid: url.searchParams.get('mid'),
          media_mid: url.searchParams.get('media_mid'),
          vid: url.searchParams.get('vid')
        }
        if (!songData.mid || !songData.media_mid || !songData.vid) {
          return new Response(JSON.stringify({ 
            error: 'QQ音乐需要mid, media_mid, vid参数' 
          }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          })
        }
        break
      
      case 'kg':
        songData = {
          hash: url.searchParams.get('hash'),
          album_id: url.searchParams.get('album_id'),
          album_audio_id: url.searchParams.get('album_audio_id')
        }
        if (!songData.hash || !songData.album_id || !songData.album_audio_id) {
          return new Response(JSON.stringify({ 
            error: '酷狗音乐需要hash, album_id, album_audio_id参数' 
          }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          })
        }
        break
      
      default:
        // 标准格式音源使用id参数
        const id = url.searchParams.get('id')
        if (!id) {
          return new Response(JSON.stringify({ error: 'id is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          })
        }
        songData = id
        break
    }

    const result = await getMusicUrl(songData, source)
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
  
  if (pathname === '/music/lyric') {
    const source = url.searchParams.get('source') || 'wyy'
    let songData = {}
    
    // 根据音源类型收集所需参数
    switch (source) {
      case 'qq':
        // QQ音乐歌词使用id参数
        const qqId = url.searchParams.get('id')
        if (!qqId) {
          return new Response(JSON.stringify({ error: 'QQ音乐歌词需要id参数' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          })
        }
        songData = qqId
        break
      
      case 'kg':
        // 酷狗音乐歌词使用hash参数
        const kgHash = url.searchParams.get('hash')
        if (!kgHash) {
          return new Response(JSON.stringify({ error: '酷狗音乐歌词需要hash参数' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          })
        }
        songData = kgHash
        break
      
      default:
        // 标准格式音源使用id参数
        const id = url.searchParams.get('id')
        if (!id) {
          return new Response(JSON.stringify({ error: 'id is required' }), {
            status: 400,
            headers: { ...corsHeaders, 'Content-Type': 'application/json' }
          })
        }
        songData = id
        break
    }

    const result = await getMusicLyric(songData, source)
    return new Response(JSON.stringify(result), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
  
  return new Response(JSON.stringify({ error: 'Music API endpoint not found' }), {
    status: 404,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

async function searchMusic(keywords, page, specificSource) {
  console.log(`[Music API] 搜索参数: 关键词="${keywords}", 页数=${page}, 指定音源="${specificSource}"`)
  
  // 如果指定了音源，只搜索该音源
  if (specificSource && specificSource !== 'auto' && MUSIC_SOURCES[specificSource]) {
    const config = MUSIC_SOURCES[specificSource]
    try {
      const searchUrl = `${SHYBOT_BASE_URL}/?type=${specificSource}&name=${encodeURIComponent(keywords)}&page=${page}&shykey=${globalThis.CURRENT_SHYKEY}`
      console.log(`[Music API] 指定音源搜索: ${config.name}, URL: ${searchUrl}`)
      
      const response = await fetch(searchUrl, {
        method: 'GET',
        headers: SHYBOT_HEADERS
      })

      if (response.ok) {
        // 先获取ArrayBuffer，然后使用UTF-8解码，防止乱码
        const buffer = await response.arrayBuffer();
        const text = new TextDecoder('utf-8').decode(buffer);
        
        // 临时日志，用于诊断API响应
        console.log(`[Music API Debug] 搜索关键词 "${keywords}" 的原始响应:`, text);
        
        try {
          const data = JSON.parse(text);
          
          console.log(`[Music API] ${config.name} 返回数据:`, data)
          
          if (Array.isArray(data) && data.length > 0) {
            // 使用新的数据处理器
            const songs = data.map(song => processSongData(song, specificSource));

            return {
              code: 200,
              result: { songs: songs },
              source: specificSource,
              sourceName: config.name,
              message: `搜索成功 - 来源: ${config.name}`
            }
          }
        } catch (parseError) {
          console.error(`[Music API] 解析响应失败:`, parseError);
          return {
            code: 500,
            result: { songs: [] },
            source: specificSource,
            sourceName: config.name,
            message: `${config.name} 返回数据格式错误: ${parseError.message}`,
            searchKeywords: keywords,
            searchPage: page
          }
        }
      }
      
      console.log(`[Music API] ${config.name} 搜索无结果`)
      return {
        code: 404,
        result: { songs: [] },
        source: specificSource,
        sourceName: config.name,
        message: `${config.name} 未找到相关歌曲`,
        searchKeywords: keywords,
        searchPage: page
      }
      
    } catch (error) {
      console.error(`[Music API] ${config.name} 搜索异常:`, error)
      return {
        code: 500,
        result: { songs: [] },
        source: specificSource,
        sourceName: config.name,
        message: `${config.name} 搜索失败: ${error.message}`,
        searchKeywords: keywords,
        searchPage: page
      }
    }
  }
  
  // 自动多源搜索（默认行为）
  console.log('[Music API] 开始多源智能搜索')
  const enabledSources = Object.entries(MUSIC_SOURCES)
    .filter(([_, config]) => config.enabled)
    .sort((a, b) => a[1].priority - b[1].priority)

  for (const [sourceType, config] of enabledSources) {
    try {
      const searchUrl = `${SHYBOT_BASE_URL}/?type=${sourceType}&name=${encodeURIComponent(keywords)}&page=${page}&shykey=${globalThis.CURRENT_SHYKEY}`
      console.log(`[Music API] 尝试 ${config.name}: ${searchUrl}`)
      
      const response = await fetch(searchUrl, {
        method: 'GET',
        headers: SHYBOT_HEADERS
      })

      if (response.ok) {
        // 先获取ArrayBuffer，然后使用UTF-8解码，防止乱码
        const buffer = await response.arrayBuffer();
        const text = new TextDecoder('utf-8').decode(buffer);
        
        // 临时日志，用于诊断API响应
        console.log(`[Music API Debug] 搜索关键词 "${keywords}" 从源 "${sourceType}" 的原始响应:`, text);
        
        try {
          const data = JSON.parse(text);
          
          console.log(`[Music API] ${config.name} 返回数据:`, data)
          
          // shybot API 直接返回歌曲数组
          if (Array.isArray(data) && data.length > 0) {
            console.log(`[Music API] ${config.name} 搜索成功，找到 ${data.length} 首歌曲`)
            
            // 使用新的数据处理器
            const songs = data.map(song => processSongData(song, sourceType));

            return {
              code: 200,
              result: { songs: songs },
              source: sourceType,
              sourceName: config.name,
              message: `搜索成功 - 来源: ${config.name}`
            }
          }
        } catch (parseError) {
          console.error(`[Music API] 解析 ${config.name} 响应失败:`, parseError);
          continue;
        }
      }
      
    } catch (error) {
      console.error(`[Music API] ${config.name} 搜索异常:`, error)
      continue
    }
  }

  console.log('[Music API] 所有音源都无结果')
  return {
    code: 404,
    result: { songs: [] },
    source: 'multi',
    message: '所有音源都未找到相关歌曲',
    searchKeywords: keywords,
    searchPage: page
  }
}

// 获取音乐播放链接 - 支持不同音源的参数格式
async function getMusicUrl(songData, source) {
  try {
    const sourceType = source || 'wyy' // 默认使用网易云
    let urlEndpoint
    
    // 根据不同音源构建URL - 基于抓包的真实格式
    switch (sourceType) {
      case 'qq':
        // QQ音乐需要 mid, media_mid, vid 参数
        if (songData.mid && songData.media_mid && songData.vid) {
          urlEndpoint = `${SHYBOT_BASE_URL}/?type=qq_url&mid=${songData.mid}&media_mid=${songData.media_mid}&vid=${songData.vid}&shykey=${globalThis.CURRENT_SHYKEY}`
        } else {
          console.error('[Music URL API] QQ音乐缺少必要参数:', songData)
          return { code: 400, data: null, source: sourceType, message: 'QQ音乐缺少mid/media_mid/vid参数' }
        }
        break
      
      case 'kg':
        // 酷狗音乐需要 hash, album_id, album_audio_id 参数
        if (songData.hash && songData.album_id && songData.album_audio_id) {
          urlEndpoint = `${SHYBOT_BASE_URL}/?type=kg_url&hash=${songData.hash}&album_id=${songData.album_id}&album_audio_id=${songData.album_audio_id}&shykey=${globalThis.CURRENT_SHYKEY}`
        } else {
          console.error('[Music URL API] 酷狗音乐缺少必要参数:', songData)
          return { code: 400, data: null, source: sourceType, message: '酷狗音乐缺少hash/album_id/album_audio_id参数' }
        }
        break
      
      default:
        // 标准格式音源：wyy, kw, xmla, qishui, qt - 使用id参数
        const id = songData.id || songData
        urlEndpoint = `${SHYBOT_BASE_URL}/?type=${sourceType}_url&id=${id}&shykey=${globalThis.CURRENT_SHYKEY}`
        break
    }
    
    console.log(`[Music URL API] 调用URL获取接口: ${urlEndpoint}`)
    
    const response = await fetch(urlEndpoint, {
      method: 'GET',
      headers: SHYBOT_HEADERS
    })

    console.log(`[Music URL API] API响应状态: ${response.status}`)

    if (response.ok) {
      const responseText = await response.text()
      console.log(`[Music URL API] API返回原始数据:`, responseText)
      
      // 尝试解析为JSON
      try {
        const data = JSON.parse(responseText)
        console.log(`[Music URL API] 解析为JSON成功:`, data)
        
        // 检查返回数据格式
        if (data && data.url) {
          // 将HTTP链接升级为HTTPS
          let secureUrl = data.url
          if (secureUrl.startsWith('http://')) {
            // 处理网易云音乐特殊的音频URL格式 (m7.music.126.net等)
            if (secureUrl.match(/http:\/\/m\d+\.music\.126\.net/)) {
              secureUrl = secureUrl.replace(/^http:\/\/m(\d+)\.music\.126\.net/, 'https://m$1.music.126.net');
            } 
            // 处理QQ音乐特殊的音频URL格式 (isure.stream.qqmusic.qq.com等)
            else if (secureUrl.match(/http:\/\/isure\.stream\.qqmusic\.qq\.com/)) {
              secureUrl = secureUrl.replace(/^http:\/\//, 'https://');
            }
            else {
              secureUrl = 'https://' + secureUrl.substring(7)
            }
          }
          
          return {
            code: 200,
            data: [{ url: secureUrl }], // 包装成前端期望的格式
            source: sourceType,
            message: '获取播放链接成功'
          }
        } else if (Array.isArray(data) && data.length > 0 && data[0].url) {
          // 将所有URL从HTTP升级为HTTPS
          const secureData = data.map(item => {
            if (item.url && item.url.startsWith('http://')) {
              // 处理网易云音乐特殊的音频URL格式 (m7.music.126.net等)
              if (item.url.match(/http:\/\/m\d+\.music\.126\.net/)) {
                item.url = item.url.replace(/^http:\/\/m(\d+)\.music\.126\.net/, 'https://m$1.music.126.net');
              } 
              // 处理QQ音乐特殊的音频URL格式 (isure.stream.qqmusic.qq.com等)
              else if (item.url.match(/http:\/\/isure\.stream\.qqmusic\.qq\.com/)) {
                item.url = item.url.replace(/^http:\/\//, 'https://');
              }
              else {
                item.url = 'https://' + item.url.substring(7)
              }
            }
            return item
          })
          
          return {
            code: 200,
            data: secureData,
            source: sourceType,
            message: '获取播放链接成功'
          }
        } else {
          console.error(`[Music URL API] JSON格式数据错误:`, data)
          return {
            code: 404,
            data: null,
            source: sourceType,
            message: '播放链接不可用'
          }
        }
      } catch (jsonError) {
        // 如果不是JSON，可能直接返回URL字符串
        console.log(`[Music URL API] 不是JSON格式，尝试作为URL字符串处理:`, responseText)
        
        // 检查是否是有效的URL
        if (responseText && (responseText.startsWith('http://') || responseText.startsWith('https://'))) {
          // 将HTTP链接升级为HTTPS
          let secureUrl = responseText.trim()
          if (secureUrl.startsWith('http://')) {
            // 处理网易云音乐特殊的音频URL格式 (m7.music.126.net等)
            if (secureUrl.match(/http:\/\/m\d+\.music\.126\.net/)) {
              secureUrl = secureUrl.replace(/^http:\/\/m(\d+)\.music\.126\.net/, 'https://m$1.music.126.net');
            } 
            // 处理QQ音乐特殊的音频URL格式 (isure.stream.qqmusic.qq.com等)
            else if (secureUrl.match(/http:\/\/isure\.stream\.qqmusic\.qq\.com/)) {
              secureUrl = secureUrl.replace(/^http:\/\//, 'https://');
            }
            else {
              secureUrl = 'https://' + secureUrl.substring(7)
            }
          }
          
          return {
            code: 200,
            data: [{ url: secureUrl }],
            source: sourceType,
            message: '获取播放链接成功'
          }
        } else {
          console.error(`[Music URL API] 返回内容既不是JSON也不是有效URL:`, responseText)
          return {
            code: 404,
            data: null,
            source: sourceType,
            message: '播放链接格式错误'
          }
        }
      }
    }
    
    const errorText = await response.text()
    console.error(`[Music URL API] HTTP错误: ${response.status} - ${errorText}`)
    
    return {
      code: response.status,
      data: null,
      source: sourceType,
      message: `获取播放链接失败: ${response.status}`
    }
  } catch (error) {
    console.error('[Music URL API] 获取播放链接异常:', error)
    return {
      code: 500,
      data: null,
      message: '服务器错误: ' + error.message
    }
  }
}

// 获取歌词 - 支持不同音源的参数格式
async function getMusicLyric(songData, source) {
  try {
    const sourceType = source || 'wyy' // 默认使用网易云
    let lyricEndpoint
    
    // 根据不同音源构建URL - 基于抓包的真实格式
    switch (sourceType) {
      case 'qq':
        // QQ音乐歌词使用id参数
        const qqId = songData.id || songData
        lyricEndpoint = `${SHYBOT_BASE_URL}/?type=qq_lrc&id=${qqId}&shykey=${globalThis.CURRENT_SHYKEY}`
        break
      
      case 'kg':
        // 酷狗音乐歌词需要 f=1 和 hash 参数
        const kgHash = songData.hash || songData
        lyricEndpoint = `${SHYBOT_BASE_URL}/?type=kg_lrc&f=1&hash=${kgHash}&shykey=${globalThis.CURRENT_SHYKEY}`
        break
      
      case 'xmla':
      case 'qishui':
      case 'qt':
        // 这些音源可能不支持歌词，先尝试标准格式
        const id1 = songData.id || songData
        lyricEndpoint = `${SHYBOT_BASE_URL}/?type=${sourceType}_lrc&id=${id1}&shykey=${globalThis.CURRENT_SHYKEY}`
        break
      
      default:
        // 标准格式音源：wyy, kw - 使用id参数
        const id = songData.id || songData
        lyricEndpoint = `${SHYBOT_BASE_URL}/?type=${sourceType}_lrc&id=${id}&shykey=${globalThis.CURRENT_SHYKEY}`
        break
    }
    
    console.log(`[Music Lyric API] 调用歌词获取接口: ${lyricEndpoint}`)
    
    const response = await fetch(lyricEndpoint, {
      method: 'GET',
      headers: SHYBOT_HEADERS
    })

    console.log(`[Music Lyric API] API响应状态: ${response.status}`)

    if (response.ok) {
      const data = await response.json()
      console.log(`[Music Lyric API] API返回数据:`, JSON.stringify(data, null, 2))
      
      return {
        code: 200,
        lrc: data,
        source: sourceType,
        message: '获取歌词成功'
      }
    }
    
    const errorText = await response.text()
    console.error(`[Music Lyric API] HTTP错误: ${response.status} - ${errorText}`)
    
    return {
      code: response.status,
      lrc: null,
      source: sourceType,
      message: `获取歌词失败: ${response.status}`
    }
  } catch (error) {
    console.error('[Music Lyric API] 获取歌词异常:', error)
    return {
      code: 500,
      lrc: null,
      message: '服务器错误: ' + error.message
    }
  }
}

// AI API处理
async function handleAiRequest(request, pathname) {
  // 预加载图片缓存的管理接口
  if (pathname === '/ai/preload' && request.method === 'POST') {
    try {
      const result = await preloadMossiaImages()
      return new Response(JSON.stringify(result), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      console.error('[AI API] 预加载错误:', error)
      return new Response(JSON.stringify({
        success: false,
        error: '预加载失败',
        message: error.message
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  }

  if (pathname === '/ai/generate' && request.method === 'POST') {
    try {
      const requestData = await request.json()
      const { prompt, style = 'tech', type, source, num = 1 } = requestData
      
      // 如果是Mossia API请求
      if (type === 'mossia') {
        const result = await getMossiaImage(source, num)
        return new Response(JSON.stringify(result), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }
      
      // 原有的AI图像生成逻辑
      if (!prompt) {
        return new Response(JSON.stringify({
          success: false,
          error: 'Prompt is required'
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        })
      }
      
      // 调用 Minimax API 生成图像
      const result = await generateMinimaxImage(prompt, style)
      
      return new Response(JSON.stringify(result), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
      
    } catch (error) {
      console.error('[AI API] 图像生成错误:', error)
      return new Response(JSON.stringify({
        success: false,
        error: 'AI图像生成失败',
        message: error.message
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  }
  
  return new Response(JSON.stringify({ error: 'AI API endpoint not found' }), {
    status: 404,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

// Mossia API 图片获取 - 简化版本（暂时不使用缓存）
async function getMossiaImage(source = 'safe', num = 1) {
  try {
    console.log('[Mossia API] 开始获取图片，类型:', source, '数量:', num)
    
    // 直接调用API获取图片
    console.log('[Mossia API] 调用API获取图片')
    let apiUrl
    const params = new URLSearchParams({
      num: Math.max(num, 10).toString() // 获取更多图片用于补充缓存
    })
    
    if (source === 'r18') {
      // R18内容
      params.append('r18Type', '1') // 获取R18内容
      params.append('aiType', '1')  // 优先非AI作品
      apiUrl = `https://api.mossia.top/duckMo?${params}`
    } else {
      // 非R18内容（默认）
      params.append('r18Type', '0') // 只获取非R18内容
      params.append('aiType', '1')  // 优先非AI作品
      apiUrl = `https://api.mossia.top/duckMo?${params}`
    }
    
    console.log('[Mossia API] 请求URL:', apiUrl)
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'User-Agent': 'Blog-Image-Service/1.0',
        'Accept': 'application/json'
      }
    })
    
    console.log('[Mossia API] 响应状态:', response.status)
    
    if (!response.ok) {
      throw new Error(`Mossia API 请求失败: ${response.status}`)
    }
    
    const data = await response.json()
    
    if (!data.success || !data.data || data.data.length === 0) {
      throw new Error('Mossia API 没有返回图片数据')
    }
    
    // 处理返回的图片数据
    const images = data.data.map(item => {
      // 统一处理Pixiv图片，优化URL选择
      let imageUrl = null
      
      // 按优先级选择最佳图片URL
      if (item.urlsList && item.urlsList.length > 0) {
        // 优先选择regular尺寸（平衡质量和加载速度）
        const regularUrl = item.urlsList.find(u => u.urlSize === 'regular')
        const originalUrl = item.urlsList.find(u => u.urlSize === 'original')
        const smallUrl = item.urlsList.find(u => u.urlSize === 'small')
        
        imageUrl = (regularUrl || originalUrl || smallUrl)?.url
      }
      
      // 如果没有找到合适的URL，跳过这张图片
      if (!imageUrl) {
        console.warn('[Mossia API] 图片URL无效，跳过:', item.pid)
        return null
      }
      
      return {
        url: imageUrl,
        title: item.title || '精美插画',
        author: item.author || '未知作者',
        source: 'Pixiv',
        pid: item.pid,
        uid: item.uid,
        tags: item.tagsList?.map(tag => tag.tagName).slice(0, 5).join(', '), // 限制标签数量
        createDate: item.pcreateDate,
        r18: source === 'r18',
        originalData: item
      }
    }).filter(Boolean) // 过滤掉null值
    
    // 暂时不使用缓存机制
    console.log('[Mossia API] 图片处理完成，共', images.length, '张图片')
    
    // 返回请求数量的图片
    const resultImages = images.slice(0, num)
    
    return {
      success: true,
      images: resultImages,
      source: source,
      total: resultImages.length,
      cached: false
    }
    
  } catch (error) {
    console.error('[Mossia API] 获取图片失败:', error)
    
    return {
      success: false,
      error: error.message,
      message: `获取${source === 'r18' ? 'R18' : '一般'}图片失败，请稍后重试`
    }
  }
}

// 预加载图片到缓存的管理接口
async function preloadMossiaImages() {
  try {
    console.log('[预加载] 开始预加载图片到缓存')
    
    // 预加载非R18图片
    const safeResult = await getMossiaImage('safe', 20)
    console.log('[预加载] 非R18图片预加载结果:', safeResult.success ? '成功' : '失败')
    
    // 预加载R18图片
    const r18Result = await getMossiaImage('r18', 20)
    console.log('[预加载] R18图片预加载结果:', r18Result.success ? '成功' : '失败')
    
    return {
      success: true,
      safe: safeResult.success,
      r18: r18Result.success,
      message: '预加载完成'
    }
    
  } catch (error) {
    console.error('[预加载] 预加载失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// Minimax AI 图像生成
async function generateMinimaxImage(prompt, style = 'tech') {
  // 使用提供的 API Key
  const API_KEY = '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
  
  try {
    // 根据风格优化提示词
    const stylePrompts = {
      tech: 'technology-themed, modern, clean, blue tone, sci-fi style',
      nature: 'natural, fresh, green, landscape painting',
      business: 'business, professional, minimalist, modern office',
      art: 'artistic, creative, colorful, abstract'
    }
    
    const enhancedPrompt = `${prompt}, ${stylePrompts[style] || stylePrompts.tech}, high quality, high resolution`
    
    console.log('[AI API] 正在调用 Minimax API 生成图像...')
    console.log('[AI API] Enhanced prompt:', enhancedPrompt)
    
    const response = await fetch('https://api.minimaxi.com/v1/image_generation', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: 'image-01',
        prompt: enhancedPrompt,
        aspect_ratio: '1:1',
        response_format: 'url',
        n: 1,
        prompt_optimizer: true
      })
    })
    
    console.log('[AI API] API 响应状态:', response.status)
    
    if (!response.ok) {
      const errorText = await response.text()
      console.error('[AI API] Minimax API 错误:', response.status, errorText)
      throw new Error(`Minimax API 错误: ${response.status} - ${errorText}`)
    }
    
    const data = await response.json()
    console.log('[AI API] API 响应数据:', JSON.stringify(data, null, 2))
    
    // 按照新的API响应格式处理
    if (data.base_resp && data.base_resp.status_code === 0 && data.data && data.data.image_urls && data.data.image_urls.length > 0) {
      return {
        success: true,
        images: data.data.image_urls.map(url => ({
          url: url
        })),
        metadata: data.metadata || {},
        id: data.id
      }
    } else {
      throw new Error(`图像生成失败: ${data.base_resp ? data.base_resp.status_msg : '未知错误'}`)
    }
    
  } catch (error) {
    console.error('[AI API] Minimax 图像生成失败:', error)
    
    // 返回真实的错误信息，不再使用占位符
    return {
      success: false,
      error: error.message,
      message: 'AI图像生成失败，请检查API配置或稍后重试'
    }
  }
}

// 天气API处理
async function handleWeatherRequest(request, pathname) {
  const url = new URL(request.url)
  
  if (pathname === '/weather' && request.method === 'GET') {
    const lat = url.searchParams.get('lat')
    const lon = url.searchParams.get('lon')
    
    if (!lat || !lon) {
      return new Response(JSON.stringify({
        error: '缺少经纬度参数',
        message: '请提供lat和lon参数'
      }), {
        status: 400,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
    
    try {
      const weatherData = await getWeatherByCoords(lat, lon)
      return new Response(JSON.stringify(weatherData), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    } catch (error) {
      console.error('[Weather API] 获取天气信息失败:', error)
      return new Response(JSON.stringify({
        error: '获取天气信息失败',
        message: error.message
      }), {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      })
    }
  }
  
  return new Response(JSON.stringify({ error: 'Weather API endpoint not found' }), {
    status: 404,
    headers: { ...corsHeaders, 'Content-Type': 'application/json' }
  })
}

// 使用高德地图API获取天气信息
async function getWeatherByCoords(lat, lon) {
  const AMAP_KEY = '31280bbb46f491bd85df213f90d01f31'
  
  try {
    console.log('[Weather API] 正在获取位置信息...', { lat, lon })
    
    // 1. 先通过逆地理编码获取城市信息
    const geoResponse = await fetch(
      `https://restapi.amap.com/v3/geocode/regeo?key=${AMAP_KEY}&location=${lon},${lat}&extensions=all&batch=false&roadlevel=0`
    )
    
    if (!geoResponse.ok) {
      throw new Error(`地理编码API错误: ${geoResponse.status}`)
    }
    
    const geoData = await geoResponse.json()
    console.log('[Weather API] 地理编码响应:', JSON.stringify(geoData, null, 2))
    
    if (geoData.status !== '1' || !geoData.regeocode) {
      throw new Error(`地理编码失败: ${geoData.info || '未知错误'}`)
    }
    
    const addressComponent = geoData.regeocode.addressComponent
    const adcode = addressComponent.adcode || addressComponent.citycode
    
    // 构建详细的位置信息：省市区
    const province = addressComponent.province || ''
    const city = addressComponent.city || ''  
    const district = addressComponent.district || ''
    
    // 组合位置名称，优先显示区，然后是市
    let locationName = ''
    if (district && district !== city) {
      locationName = `${city}${district}`
    } else if (city) {
      locationName = city
    } else if (province) {
      locationName = province
    } else {
      locationName = '未知位置'
    }
    
    console.log('[Weather API] 位置解析:', { province, city, district, locationName })
    
    if (!adcode) {
      throw new Error('无法获取城市编码')
    }
    
    console.log('[Weather API] 城市信息:', { adcode, locationName })
    
    // 2. 使用城市编码获取天气信息
    const weatherResponse = await fetch(
      `https://restapi.amap.com/v3/weather/weatherInfo?key=${AMAP_KEY}&city=${adcode}&extensions=base`
    )
    
    if (!weatherResponse.ok) {
      throw new Error(`天气API错误: ${weatherResponse.status}`)
    }
    
    const weatherData = await weatherResponse.json()
    console.log('[Weather API] 天气响应:', JSON.stringify(weatherData, null, 2))
    
    if (weatherData.status !== '1' || !weatherData.lives || !weatherData.lives[0]) {
      throw new Error(`天气查询失败: ${weatherData.info || '未知错误'}`)
    }
    
    const live = weatherData.lives[0]
    
    // 3. 格式化响应数据，符合前端WeatherData接口
    const formattedData = {
      location: locationName || live.city,
      temperature: `${live.temperature}°C`,
      weather: live.weather,
      windDirection: live.winddirection,
      windPower: `${live.windpower}级`,
      humidity: `${live.humidity}%`,
      reportTime: live.reporttime,
      icon: getWeatherIcon(live.weather)
    }
    
    console.log('[Weather API] 格式化数据:', formattedData)
    return formattedData
    
  } catch (error) {
    console.error('[Weather API] 获取天气信息异常:', error)
    throw error
  }
}

// 根据天气状况返回对应的图标
function getWeatherIcon(weather) {
  const iconMap = {
    '晴': '☀️',
    '多云': '⛅',
    '阴': '☁️',
    '小雨': '🌦️',
    '中雨': '🌧️',
    '大雨': '⛈️',
    '雷阵雨': '⛈️',
    '雪': '❄️',
    '雾': '🌫️',
    '霾': '😷',
    '沙尘': '🌪️'
  }
  
  // 尝试精确匹配
  if (iconMap[weather]) {
    return iconMap[weather]
  }
  
  // 模糊匹配
  for (const [key, icon] of Object.entries(iconMap)) {
    if (weather.includes(key)) {
      return icon
    }
  }
  
  // 默认图标
  return '🌤️'
}

// 数据一致性检查函数
async function checkDataIntegrity(env) {
  try {
    console.log('[checkDataIntegrity] 开始数据一致性检查')
    
    const postsIndex = await env.POSTS.get('posts_index', 'json') || []
    const issues = []
    const stats = {
      totalIndexed: postsIndex.length,
      validPosts: 0,
      missingPosts: 0,
      corruptedPosts: 0,
      orphanedPosts: [],
      duplicates: []
    }
    
    // 检查索引中的文章
    for (const postMeta of postsIndex) {
      try {
        const post = await env.POSTS.get(`post:${postMeta.slug}`, 'json')
        
        if (!post) {
          stats.missingPosts++
          issues.push({
            type: 'missing_post',
            slug: postMeta.slug,
            id: postMeta.id,
            title: postMeta.title,
            message: `索引中存在但实际文章缺失: ${postMeta.slug}`
          })
        } else if (!post.title || !post.slug || !post.content) {
          stats.corruptedPosts++
          issues.push({
            type: 'corrupted_post',
            slug: postMeta.slug,
            id: postMeta.id,
            title: postMeta.title,
            message: `文章数据不完整: ${postMeta.slug}`,
            missing_fields: {
              title: !post.title,
              slug: !post.slug,
              content: !post.content
            }
          })
        } else {
          stats.validPosts++
        }
      } catch (error) {
        stats.corruptedPosts++
        issues.push({
          type: 'read_error',
          slug: postMeta.slug,
          id: postMeta.id,
          title: postMeta.title,
          message: `读取文章失败: ${error.message}`
        })
      }
    }
    
    // 检查重复的slug
    const slugCounts = {}
    postsIndex.forEach(post => {
      if (post.slug) {
        slugCounts[post.slug] = (slugCounts[post.slug] || 0) + 1
      }
    })
    
    Object.entries(slugCounts).forEach(([slug, count]) => {
      if (count > 1) {
        stats.duplicates.push({ slug, count })
        issues.push({
          type: 'duplicate_slug',
          slug,
          count,
          message: `重复的slug: ${slug} (${count}次)`
        })
      }
    })
    
    const isHealthy = issues.length === 0
    const summary = {
      healthy: isHealthy,
      timestamp: new Date().toISOString(),
      stats,
      issues: issues.slice(0, 50), // 限制返回的问题数量
      recommendations: []
    }
    
    // 生成修复建议
    if (stats.missingPosts > 0) {
      summary.recommendations.push('清理索引中的缺失文章引用')
    }
    if (stats.corruptedPosts > 0) {
      summary.recommendations.push('修复损坏的文章数据')
    }
    if (stats.duplicates.length > 0) {
      summary.recommendations.push('解决重复的slug问题')
    }
    
    console.log(`[checkDataIntegrity] 检查完成，发现 ${issues.length} 个问题`)
    
    return new Response(JSON.stringify(summary), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('[checkDataIntegrity] 检查失败:', error.message)
    return new Response(JSON.stringify({
      error: 'Data integrity check failed',
      message: error.message
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
}

// 数据一致性修复函数
async function repairDataIntegrity(request, env) {
  try {
    const repairOptions = await request.json()
    console.log('[repairDataIntegrity] 开始数据修复:', repairOptions)
    
    const repairs = []
    const errors = []
    
    // 修复缺失的文章引用
    if (repairOptions.fixMissingPosts) {
      try {
        const postsIndex = await env.POSTS.get('posts_index', 'json') || []
        const validIndex = []
        
        for (const postMeta of postsIndex) {
          const post = await env.POSTS.get(`post:${postMeta.slug}`, 'json')
          if (post) {
            validIndex.push(postMeta)
          } else {
            repairs.push(`删除缺失文章的索引: ${postMeta.slug}`)
          }
        }
        
        await env.POSTS.put('posts_index', JSON.stringify(validIndex))
        repairs.push(`清理索引：从 ${postsIndex.length} 减少到 ${validIndex.length}`)
        
      } catch (error) {
        errors.push(`修复缺失文章失败: ${error.message}`)
      }
    }
    
    // 修复重复的slug
    if (repairOptions.fixDuplicates) {
      try {
        const postsIndex = await env.POSTS.get('posts_index', 'json') || []
        const seenSlugs = new Set()
        const uniqueIndex = []
        
        for (const postMeta of postsIndex) {
          if (!seenSlugs.has(postMeta.slug)) {
            seenSlugs.add(postMeta.slug)
            uniqueIndex.push(postMeta)
          } else {
            repairs.push(`删除重复的slug: ${postMeta.slug}`)
          }
        }
        
        if (uniqueIndex.length < postsIndex.length) {
          await env.POSTS.put('posts_index', JSON.stringify(uniqueIndex))
          repairs.push(`去重索引：从 ${postsIndex.length} 减少到 ${uniqueIndex.length}`)
        }
        
      } catch (error) {
        errors.push(`修复重复slug失败: ${error.message}`)
      }
    }
    
    // 重建索引
    if (repairOptions.rebuildIndex) {
      try {
        const postsIndex = await env.POSTS.get('posts_index', 'json') || []
        const rebuiltIndex = []
        
        for (const postMeta of postsIndex) {
          const post = await env.POSTS.get(`post:${postMeta.slug}`, 'json')
          if (post && post.title && post.slug) {
            rebuiltIndex.push({
              id: post.id || postMeta.id,
              slug: post.slug,
              title: post.title,
              excerpt: post.excerpt || '',
              author: post.author || 'Admin',
              date: post.date || new Date().toISOString().split('T')[0],
              category: post.category || '',
              tags: post.tags || [],
              featured: post.featured || false,
              imageUrl: post.imageUrl || '',
              readTime: post.readTime || 1
            })
          }
        }
        
        await env.POSTS.put('posts_index', JSON.stringify(rebuiltIndex))
        repairs.push(`重建索引：包含 ${rebuiltIndex.length} 篇文章`)
        
      } catch (error) {
        errors.push(`重建索引失败: ${error.message}`)
      }
    }
    
    console.log(`[repairDataIntegrity] 修复完成，成功 ${repairs.length} 项，失败 ${errors.length} 项`)
    
    return new Response(JSON.stringify({
      success: errors.length === 0,
      timestamp: new Date().toISOString(),
      repairs,
      errors,
      message: errors.length === 0 ? '数据修复完成' : '数据修复部分完成，存在错误'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
    
  } catch (error) {
    console.error('[repairDataIntegrity] 修复失败:', error.message)
    return new Response(JSON.stringify({
      error: 'Data repair failed',
      message: error.message
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    })
  }
} 