pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

GML Theme - Meseta <<EMAIL>>

*/
.hljs {
  background: #222222;
  color: #C0C0C0
}
.hljs-keyword {
  color: #FFB871;
  font-weight: bold
}
.hljs-built_in {
  color: #FFB871
}
.hljs-literal {
  color: #FF8080
}
.hljs-symbol {
  color: #58E55A
}
.hljs-comment {
  color: #5B995B
}
.hljs-string {
  color: #FFFF00
}
.hljs-number {
  color: #FF8080
}
.hljs-attribute,
.hljs-selector-tag,
.hljs-doctag,
.hljs-name,
.hljs-bullet,
.hljs-code,
.hljs-addition,
.hljs-regexp,
.hljs-variable,
.hljs-template-variable,
.hljs-link,
.hljs-selector-attr,
.hljs-selector-pseudo,
.hljs-type,
.hljs-selector-id,
.hljs-selector-class,
.hljs-quote,
.hljs-template-tag,
.hljs-deletion,
.hljs-title,
.hljs-section,
.hljs-function,
.hljs-meta .hljs-keyword,
.hljs-meta,
.hljs-subst {
  color: #C0C0C0
}
.hljs-emphasis {
  font-style: italic
}
.hljs-strong {
  font-weight: bold
}