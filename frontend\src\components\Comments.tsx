/**
 * 自定义评论组件
 * 基于自己的评论API实现的评论系统
 */
import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { MessageCircle, Send, User, Globe, Mail, Trash2, Calendar } from 'lucide-react'
import { CommentService, Comment, CreateCommentData } from '../services/commentService'
import { useAuth } from '../contexts/AuthContext'

interface CommentsProps {
  slug: string
}

export function SimpleComments({ slug }: CommentsProps) {
  const { isAdmin } = useAuth()
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [deletingId, setDeletingId] = useState<string | null>(null)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null)
  
  // 表单数据
  const [formData, setFormData] = useState<CreateCommentData>({
    content: '',
    author: '',
    email: '',
    website: ''
  })

  // 加载评论
  const loadComments = async () => {
    try {
      setLoading(true)
      setError(null)
      const commentsList = await CommentService.getComments(slug)
      setComments(Array.isArray(commentsList) ? commentsList : [])
    } catch (error) {
      console.error('加载评论失败:', error)
      setError('加载评论失败，请稍后重试')
      setComments([]) // 确保在错误时设置为空数组
    } finally {
      setLoading(false)
    }
  }

  // 提交评论
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // 验证表单数据
    const validationError = CommentService.validateComment(formData)
    if (validationError) {
      setError(validationError)
      return
    }

    try {
      setSubmitting(true)
      setError(null)
      
      const newComment = await CommentService.createComment(slug, formData)
      
      // 添加新评论到列表开头
      setComments(prev => [newComment, ...(Array.isArray(prev) ? prev : [])])
      
      // 重置表单
      setFormData({
        content: '',
        author: '',
        email: '',
        website: ''
      })
      setShowForm(false)
      
    } catch (error: any) {
      console.error('提交评论失败:', error)
      setError(error.message || '提交评论失败，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  // 删除评论
  const handleDeleteClick = (commentId: string) => {
    setShowDeleteConfirm(commentId)
  }

  const handleDeleteConfirm = async (commentId: string) => {
    if (deletingId) return

    try {
      setDeletingId(commentId)
      await CommentService.deleteComment(slug, commentId)
      setComments(prev => Array.isArray(prev) ? prev.filter(c => c.id !== commentId) : [])
      setShowDeleteConfirm(null)
    } catch (error: any) {
      console.error('删除评论失败:', error)
      setError(error.message || '删除评论失败，请稍后重试')
    } finally {
      setDeletingId(null)
    }
  }

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(null)
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return '今天'
    } else if (diffDays === 1) {
      return '昨天'
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  useEffect(() => {
    loadComments()
  }, [slug])

  return (
    <div className="mt-12">
      <div className="card p-8">
        {/* 标题 */}
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white flex items-center space-x-2">
            <MessageCircle size={20} />
            <span>评论讨论 ({comments?.length || 0})</span>
          </h3>
          
          {!showForm && (
            <button
              onClick={() => setShowForm(true)}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
            >
              <MessageCircle size={16} />
              <span>发表评论</span>
            </button>
          )}
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
          </div>
        )}

        {/* 评论表单 */}
        {showForm && (
          <form onSubmit={handleSubmit} className="mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="space-y-4">
              <div>
                <textarea
                  value={formData.content}
                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                  placeholder="写下您的评论..."
                  rows={4}
                  className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  required
                />
                <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {formData.content.length}/1000 字符
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="relative">
                  <User className="absolute left-3 top-3 text-gray-400" size={16} />
                  <input
                    type="text"
                    value={formData.author}
                    onChange={(e) => setFormData(prev => ({ ...prev, author: e.target.value }))}
                    placeholder="您的姓名 *"
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    required
                  />
                </div>
                
                <div className="relative">
                  <Mail className="absolute left-3 top-3 text-gray-400" size={16} />
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="邮箱 (可选)"
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
                
                <div className="relative">
                  <Globe className="absolute left-3 top-3 text-gray-400" size={16} />
                  <input
                    type="url"
                    value={formData.website}
                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                    placeholder="网站 (可选)"
                    className="w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  />
                </div>
              </div>
            </div>
            
            <div className="flex items-center justify-between mt-4">
              <p className="text-xs text-gray-500 dark:text-gray-400">
                评论将会被公开显示，请文明发言
              </p>
              
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setShowForm(false)}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={submitting}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                >
                  <Send size={16} />
                  <span>{submitting ? '发表中...' : '发表评论'}</span>
                </button>
              </div>
            </div>
          </form>
        )}

        {/* 评论列表 */}
        <div className="space-y-6">
          {loading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="flex space-x-3">
                    <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (comments?.length || 0) === 0 ? (
            <div className="text-center py-8">
              <MessageCircle className="mx-auto mb-4 text-gray-400" size={48} />
              <p className="text-gray-500 dark:text-gray-400">还没有评论，来发表第一条吧！</p>
            </div>
          ) : (
            (comments || []).map((comment) => (
              <div key={comment.id} className="border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0 last:pb-0">
                <div className="flex items-start space-x-3">
                  {/* 头像 */}
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                    {comment.author.charAt(0).toUpperCase()}
                  </div>
                  
                  <div className="flex-1">
                    {/* 用户信息 */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <h4 className="font-medium text-gray-900 dark:text-white">
                          {comment.website ? (
                            <a 
                              href={comment.website.startsWith('http') ? comment.website : `https://${comment.website}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                            >
                              {comment.author}
                            </a>
                          ) : (
                            comment.author
                          )}
                        </h4>
                        <div className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
                          <Calendar size={14} />
                          <span>{formatDate(comment.createdAt)}</span>
                        </div>
                      </div>
                      
                      {/* 删除按钮（仅管理员可见） */}
                      {isAdmin && (
                      <button
                        onClick={() => handleDeleteClick(comment.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors p-1 rounded"
                        title="删除评论"
                      >
                        <Trash2 size={14} />
                      </button>
                      )}
                    </div>
                    
                    {/* 评论内容 */}
                    <div className="prose prose-sm dark:prose-dark max-w-none">
                      <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                        {comment.content}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* 删除确认对话框 - 使用Portal渲染到body */}
      {showDeleteConfirm && createPortal(
        <div className="fixed inset-0 z-[99999] overflow-y-auto" style={{ zIndex: 99999 }}>
          {/* 背景遮罩 */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={handleDeleteCancel}
            style={{ zIndex: 99999 }}
          />

          {/* 弹窗容器 */}
          <div className="flex min-h-full items-center justify-center p-4 text-center" style={{ zIndex: 100000 }}>
            <div
              className="relative transform overflow-hidden rounded-lg bg-white dark:bg-gray-800 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"
              onClick={(e) => e.stopPropagation()}
              style={{ zIndex: 100001 }}
            >
              {/* 弹窗内容 */}
              <div className="bg-white dark:bg-gray-800 px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 dark:bg-red-900/20 sm:mx-0 sm:h-10 sm:w-10">
                    <Trash2 className="h-6 w-6 text-red-600 dark:text-red-400" />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <h3 className="text-base font-semibold leading-6 text-gray-900 dark:text-white">
                      确认删除评论
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        你确定要删除这条评论吗？此操作无法撤销。
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 按钮区域 */}
              <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                <button
                  type="button"
                  onClick={() => handleDeleteConfirm(showDeleteConfirm)}
                  disabled={deletingId === showDeleteConfirm}
                  className="inline-flex w-full justify-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 sm:ml-3 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {deletingId === showDeleteConfirm ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      删除中...
                    </>
                  ) : (
                    '确认删除'
                  )}
                </button>
                <button
                  type="button"
                  onClick={handleDeleteCancel}
                  disabled={deletingId === showDeleteConfirm}
                  className="mt-3 inline-flex w-full justify-center rounded-md bg-white dark:bg-gray-600 px-3 py-2 text-sm font-semibold text-gray-900 dark:text-gray-200 shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-500 hover:bg-gray-50 dark:hover:bg-gray-500 sm:mt-0 sm:w-auto disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  )
}

// 保留原有的 Comments 组件作为别名
export const Comments = SimpleComments 