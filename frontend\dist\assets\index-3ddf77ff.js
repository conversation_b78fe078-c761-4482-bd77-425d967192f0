var ei=Object.defineProperty;var ti=(e,t,s)=>t in e?ei(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s;var Vr=(e,t,s)=>(ti(e,typeof t!="symbol"?t+"":t,s),s);import{r as g,b as ri,c as si,g as ni,R as ai}from"./vendor-5f6cd04d.js";import{N as ii,m as as,n as oi,M as li,a as ci,P as Ur,b as Hr,c as di,g as ui,f as hi,d as gi,e as fi,C as xi,h as is,D as pi,i as mi,u as yi,S as bi,E as vi}from"./editor-cda9e262.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function s(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(a){if(a.ep)return;a.ep=!0;const o=s(a);fetch(a.href,o)}})();var Jn={exports:{}},Br={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ki=g,ji=Symbol.for("react.element"),wi=Symbol.for("react.fragment"),Ni=Object.prototype.hasOwnProperty,Ei=ki.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Si={key:!0,ref:!0,__self:!0,__source:!0};function Qn(e,t,s){var n,a={},o=null,i=null;s!==void 0&&(o=""+s),t.key!==void 0&&(o=""+t.key),t.ref!==void 0&&(i=t.ref);for(n in t)Ni.call(t,n)&&!Si.hasOwnProperty(n)&&(a[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps,t)a[n]===void 0&&(a[n]=t[n]);return{$$typeof:ji,type:e,key:o,ref:i,props:a,_owner:Ei.current}}Br.Fragment=wi;Br.jsx=Qn;Br.jsxs=Qn;Jn.exports=Br;var r=Jn.exports,os={},tn=ri;os.createRoot=tn.createRoot,os.hydrateRoot=tn.hydrateRoot;/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Ot(){return Ot=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Ot.apply(this,arguments)}var Be;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Be||(Be={}));const rn="popstate";function Ci(e){e===void 0&&(e={});function t(n,a){let{pathname:o,search:i,hash:l}=n.location;return ls("",{pathname:o,search:i,hash:l},a.state&&a.state.usr||null,a.state&&a.state.key||"default")}function s(n,a){return typeof a=="string"?a:ar(a)}return Ri(t,s,null,e)}function de(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function Xn(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function Ai(){return Math.random().toString(36).substr(2,8)}function sn(e,t){return{usr:e.state,key:e.key,idx:t}}function ls(e,t,s,n){return s===void 0&&(s=null),Ot({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?pt(t):t,{state:s,key:t&&t.key||n||Ai()})}function ar(e){let{pathname:t="/",search:s="",hash:n=""}=e;return s&&s!=="?"&&(t+=s.charAt(0)==="?"?s:"?"+s),n&&n!=="#"&&(t+=n.charAt(0)==="#"?n:"#"+n),t}function pt(e){let t={};if(e){let s=e.indexOf("#");s>=0&&(t.hash=e.substr(s),e=e.substr(0,s));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}function Ri(e,t,s,n){n===void 0&&(n={});let{window:a=document.defaultView,v5Compat:o=!1}=n,i=a.history,l=Be.Pop,c=null,u=d();u==null&&(u=0,i.replaceState(Ot({},i.state,{idx:u}),""));function d(){return(i.state||{idx:null}).idx}function f(){l=Be.Pop;let m=d(),w=m==null?null:m-u;u=m,c&&c({action:l,location:R.location,delta:w})}function b(m,w){l=Be.Push;let j=ls(R.location,m,w);s&&s(j,m),u=d()+1;let L=sn(j,u),H=R.createHref(j);try{i.pushState(L,"",H)}catch(K){if(K instanceof DOMException&&K.name==="DataCloneError")throw K;a.location.assign(H)}o&&c&&c({action:l,location:R.location,delta:1})}function x(m,w){l=Be.Replace;let j=ls(R.location,m,w);s&&s(j,m),u=d();let L=sn(j,u),H=R.createHref(j);i.replaceState(L,"",H),o&&c&&c({action:l,location:R.location,delta:0})}function k(m){let w=a.location.origin!=="null"?a.location.origin:a.location.href,j=typeof m=="string"?m:ar(m);return j=j.replace(/ $/,"%20"),de(w,"No window.location.(origin|href) available to create URL for href: "+j),new URL(j,w)}let R={get action(){return l},get location(){return e(a,i)},listen(m){if(c)throw new Error("A history only accepts one active listener");return a.addEventListener(rn,f),c=m,()=>{a.removeEventListener(rn,f),c=null}},createHref(m){return t(a,m)},createURL:k,encodeLocation(m){let w=k(m);return{pathname:w.pathname,search:w.search,hash:w.hash}},push:b,replace:x,go(m){return i.go(m)}};return R}var nn;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(nn||(nn={}));function Mi(e,t,s){return s===void 0&&(s="/"),Ti(e,t,s,!1)}function Ti(e,t,s,n){let a=typeof t=="string"?pt(t):t,o=Cs(a.pathname||"/",s);if(o==null)return null;let i=Zn(e);Li(i);let l=null;for(let c=0;l==null&&c<i.length;++c){let u=Wi(o);l=Hi(i[c],u,n)}return l}function Zn(e,t,s,n){t===void 0&&(t=[]),s===void 0&&(s=[]),n===void 0&&(n="");let a=(o,i,l)=>{let c={relativePath:l===void 0?o.path||"":l,caseSensitive:o.caseSensitive===!0,childrenIndex:i,route:o};c.relativePath.startsWith("/")&&(de(c.relativePath.startsWith(n),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+n+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(n.length));let u=Ke([n,c.relativePath]),d=s.concat(c);o.children&&o.children.length>0&&(de(o.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),Zn(o.children,t,d,u)),!(o.path==null&&!o.index)&&t.push({path:u,score:zi(u,o.index),routesMeta:d})};return e.forEach((o,i)=>{var l;if(o.path===""||!((l=o.path)!=null&&l.includes("?")))a(o,i);else for(let c of Yn(o.path))a(o,i,c)}),t}function Yn(e){let t=e.split("/");if(t.length===0)return[];let[s,...n]=t,a=s.endsWith("?"),o=s.replace(/\?$/,"");if(n.length===0)return a?[o,""]:[o];let i=Yn(n.join("/")),l=[];return l.push(...i.map(c=>c===""?o:[o,c].join("/"))),a&&l.push(...i),l.map(c=>e.startsWith("/")&&c===""?"/":c)}function Li(e){e.sort((t,s)=>t.score!==s.score?s.score-t.score:Ui(t.routesMeta.map(n=>n.childrenIndex),s.routesMeta.map(n=>n.childrenIndex)))}const Ii=/^:[\w-]+$/,Oi=3,Pi=2,_i=1,$i=10,Di=-2,an=e=>e==="*";function zi(e,t){let s=e.split("/"),n=s.length;return s.some(an)&&(n+=Di),t&&(n+=Pi),s.filter(a=>!an(a)).reduce((a,o)=>a+(Ii.test(o)?Oi:o===""?_i:$i),n)}function Ui(e,t){return e.length===t.length&&e.slice(0,-1).every((n,a)=>n===t[a])?e[e.length-1]-t[t.length-1]:0}function Hi(e,t,s){s===void 0&&(s=!1);let{routesMeta:n}=e,a={},o="/",i=[];for(let l=0;l<n.length;++l){let c=n[l],u=l===n.length-1,d=o==="/"?t:t.slice(o.length)||"/",f=on({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},d),b=c.route;if(!f&&u&&s&&!n[n.length-1].route.index&&(f=on({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},d)),!f)return null;Object.assign(a,f.params),i.push({params:a,pathname:Ke([o,f.pathname]),pathnameBase:qi(Ke([o,f.pathnameBase])),route:b}),f.pathnameBase!=="/"&&(o=Ke([o,f.pathnameBase]))}return i}function on(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[s,n]=Bi(e.path,e.caseSensitive,e.end),a=t.match(s);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),l=a.slice(1);return{params:n.reduce((u,d,f)=>{let{paramName:b,isOptional:x}=d;if(b==="*"){let R=l[f]||"";i=o.slice(0,o.length-R.length).replace(/(.)\/+$/,"$1")}const k=l[f];return x&&!k?u[b]=void 0:u[b]=(k||"").replace(/%2F/g,"/"),u},{}),pathname:o,pathnameBase:i,pattern:e}}function Bi(e,t,s){t===void 0&&(t=!1),s===void 0&&(s=!0),Xn(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let n=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,l,c)=>(n.push({paramName:l,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(n.push({paramName:"*"}),a+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):s?a+="\\/*$":e!==""&&e!=="/"&&(a+="(?:(?=\\/|$))"),[new RegExp(a,t?void 0:"i"),n]}function Wi(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return Xn(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Cs(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let s=t.endsWith("/")?t.length-1:t.length,n=e.charAt(s);return n&&n!=="/"?null:e.slice(s)||"/"}function Fi(e,t){t===void 0&&(t="/");let{pathname:s,search:n="",hash:a=""}=typeof e=="string"?pt(e):e;return{pathname:s?s.startsWith("/")?s:Ki(s,t):t,search:Vi(n),hash:Ji(a)}}function Ki(e,t){let s=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(a=>{a===".."?s.length>1&&s.pop():a!=="."&&s.push(a)}),s.length>1?s.join("/"):"/"}function Jr(e,t,s,n){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the ")+("`to."+s+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Gi(e){return e.filter((t,s)=>s===0||t.route.path&&t.route.path.length>0)}function ea(e,t){let s=Gi(e);return t?s.map((n,a)=>a===s.length-1?n.pathname:n.pathnameBase):s.map(n=>n.pathnameBase)}function ta(e,t,s,n){n===void 0&&(n=!1);let a;typeof e=="string"?a=pt(e):(a=Ot({},e),de(!a.pathname||!a.pathname.includes("?"),Jr("?","pathname","search",a)),de(!a.pathname||!a.pathname.includes("#"),Jr("#","pathname","hash",a)),de(!a.search||!a.search.includes("#"),Jr("#","search","hash",a)));let o=e===""||a.pathname==="",i=o?"/":a.pathname,l;if(i==null)l=s;else{let f=t.length-1;if(!n&&i.startsWith("..")){let b=i.split("/");for(;b[0]==="..";)b.shift(),f-=1;a.pathname=b.join("/")}l=f>=0?t[f]:"/"}let c=Fi(a,l),u=i&&i!=="/"&&i.endsWith("/"),d=(o||i===".")&&s.endsWith("/");return!c.pathname.endsWith("/")&&(u||d)&&(c.pathname+="/"),c}const Ke=e=>e.join("/").replace(/\/\/+/g,"/"),qi=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),Vi=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Ji=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Qi(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ra=["post","put","patch","delete"];new Set(ra);const Xi=["get",...ra];new Set(Xi);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Pt(){return Pt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},Pt.apply(this,arguments)}const As=g.createContext(null),Zi=g.createContext(null),nt=g.createContext(null),Wr=g.createContext(null),qe=g.createContext({outlet:null,matches:[],isDataRoute:!1}),sa=g.createContext(null);function Yi(e,t){let{relative:s}=t===void 0?{}:t;Wt()||de(!1);let{basename:n,navigator:a}=g.useContext(nt),{hash:o,pathname:i,search:l}=aa(e,{relative:s}),c=i;return n!=="/"&&(c=i==="/"?n:Ke([n,i])),a.createHref({pathname:c,search:l,hash:o})}function Wt(){return g.useContext(Wr)!=null}function mt(){return Wt()||de(!1),g.useContext(Wr).location}function na(e){g.useContext(nt).static||g.useLayoutEffect(e)}function Ft(){let{isDataRoute:e}=g.useContext(qe);return e?go():eo()}function eo(){Wt()||de(!1);let e=g.useContext(As),{basename:t,future:s,navigator:n}=g.useContext(nt),{matches:a}=g.useContext(qe),{pathname:o}=mt(),i=JSON.stringify(ea(a,s.v7_relativeSplatPath)),l=g.useRef(!1);return na(()=>{l.current=!0}),g.useCallback(function(u,d){if(d===void 0&&(d={}),!l.current)return;if(typeof u=="number"){n.go(u);return}let f=ta(u,JSON.parse(i),o,d.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:Ke([t,f.pathname])),(d.replace?n.replace:n.push)(f,d.state,d)},[t,n,i,o,e])}function Kt(){let{matches:e}=g.useContext(qe),t=e[e.length-1];return t?t.params:{}}function aa(e,t){let{relative:s}=t===void 0?{}:t,{future:n}=g.useContext(nt),{matches:a}=g.useContext(qe),{pathname:o}=mt(),i=JSON.stringify(ea(a,n.v7_relativeSplatPath));return g.useMemo(()=>ta(e,JSON.parse(i),o,s==="path"),[e,i,o,s])}function to(e,t){return ro(e,t)}function ro(e,t,s,n){Wt()||de(!1);let{navigator:a}=g.useContext(nt),{matches:o}=g.useContext(qe),i=o[o.length-1],l=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let u=mt(),d;if(t){var f;let m=typeof t=="string"?pt(t):t;c==="/"||(f=m.pathname)!=null&&f.startsWith(c)||de(!1),d=m}else d=u;let b=d.pathname||"/",x=b;if(c!=="/"){let m=c.replace(/^\//,"").split("/");x="/"+b.replace(/^\//,"").split("/").slice(m.length).join("/")}let k=Mi(e,{pathname:x}),R=oo(k&&k.map(m=>Object.assign({},m,{params:Object.assign({},l,m.params),pathname:Ke([c,a.encodeLocation?a.encodeLocation(m.pathname).pathname:m.pathname]),pathnameBase:m.pathnameBase==="/"?c:Ke([c,a.encodeLocation?a.encodeLocation(m.pathnameBase).pathname:m.pathnameBase])})),o,s,n);return t&&R?g.createElement(Wr.Provider,{value:{location:Pt({pathname:"/",search:"",hash:"",state:null,key:"default"},d),navigationType:Be.Pop}},R):R}function so(){let e=ho(),t=Qi(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),s=e instanceof Error?e.stack:null,a={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},o=null;return g.createElement(g.Fragment,null,g.createElement("h2",null,"Unexpected Application Error!"),g.createElement("h3",{style:{fontStyle:"italic"}},t),s?g.createElement("pre",{style:a},s):null,o)}const no=g.createElement(so,null);class ao extends g.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,s){return s.location!==t.location||s.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:s.error,location:s.location,revalidation:t.revalidation||s.revalidation}}componentDidCatch(t,s){console.error("React Router caught the following error during render",t,s)}render(){return this.state.error!==void 0?g.createElement(qe.Provider,{value:this.props.routeContext},g.createElement(sa.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function io(e){let{routeContext:t,match:s,children:n}=e,a=g.useContext(As);return a&&a.static&&a.staticContext&&(s.route.errorElement||s.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=s.route.id),g.createElement(qe.Provider,{value:t},n)}function oo(e,t,s,n){var a;if(t===void 0&&(t=[]),s===void 0&&(s=null),n===void 0&&(n=null),e==null){var o;if(!s)return null;if(s.errors)e=s.matches;else if((o=n)!=null&&o.v7_partialHydration&&t.length===0&&!s.initialized&&s.matches.length>0)e=s.matches;else return null}let i=e,l=(a=s)==null?void 0:a.errors;if(l!=null){let d=i.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id])!==void 0);d>=0||de(!1),i=i.slice(0,Math.min(i.length,d+1))}let c=!1,u=-1;if(s&&n&&n.v7_partialHydration)for(let d=0;d<i.length;d++){let f=i[d];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=d),f.route.id){let{loaderData:b,errors:x}=s,k=f.route.loader&&b[f.route.id]===void 0&&(!x||x[f.route.id]===void 0);if(f.route.lazy||k){c=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((d,f,b)=>{let x,k=!1,R=null,m=null;s&&(x=l&&f.route.id?l[f.route.id]:void 0,R=f.route.errorElement||no,c&&(u<0&&b===0?(fo("route-fallback",!1),k=!0,m=null):u===b&&(k=!0,m=f.route.hydrateFallbackElement||null)));let w=t.concat(i.slice(0,b+1)),j=()=>{let L;return x?L=R:k?L=m:f.route.Component?L=g.createElement(f.route.Component,null):f.route.element?L=f.route.element:L=d,g.createElement(io,{match:f,routeContext:{outlet:d,matches:w,isDataRoute:s!=null},children:L})};return s&&(f.route.ErrorBoundary||f.route.errorElement||b===0)?g.createElement(ao,{location:s.location,revalidation:s.revalidation,component:R,error:x,children:j(),routeContext:{outlet:null,matches:w,isDataRoute:!0}}):j()},null)}var ia=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ia||{}),ir=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ir||{});function lo(e){let t=g.useContext(As);return t||de(!1),t}function co(e){let t=g.useContext(Zi);return t||de(!1),t}function uo(e){let t=g.useContext(qe);return t||de(!1),t}function oa(e){let t=uo(),s=t.matches[t.matches.length-1];return s.route.id||de(!1),s.route.id}function ho(){var e;let t=g.useContext(sa),s=co(ir.UseRouteError),n=oa(ir.UseRouteError);return t!==void 0?t:(e=s.errors)==null?void 0:e[n]}function go(){let{router:e}=lo(ia.UseNavigateStable),t=oa(ir.UseNavigateStable),s=g.useRef(!1);return na(()=>{s.current=!0}),g.useCallback(function(a,o){o===void 0&&(o={}),s.current&&(typeof a=="number"?e.navigate(a):e.navigate(a,Pt({fromRouteId:t},o)))},[e,t])}const ln={};function fo(e,t,s){!t&&!ln[e]&&(ln[e]=!0)}function xo(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function Se(e){de(!1)}function po(e){let{basename:t="/",children:s=null,location:n,navigationType:a=Be.Pop,navigator:o,static:i=!1,future:l}=e;Wt()&&de(!1);let c=t.replace(/^\/*/,"/"),u=g.useMemo(()=>({basename:c,navigator:o,static:i,future:Pt({v7_relativeSplatPath:!1},l)}),[c,l,o,i]);typeof n=="string"&&(n=pt(n));let{pathname:d="/",search:f="",hash:b="",state:x=null,key:k="default"}=n,R=g.useMemo(()=>{let m=Cs(d,c);return m==null?null:{location:{pathname:m,search:f,hash:b,state:x,key:k},navigationType:a}},[c,d,f,b,x,k,a]);return R==null?null:g.createElement(nt.Provider,{value:u},g.createElement(Wr.Provider,{children:s,value:R}))}function mo(e){let{children:t,location:s}=e;return to(cs(t),s)}new Promise(()=>{});function cs(e,t){t===void 0&&(t=[]);let s=[];return g.Children.forEach(e,(n,a)=>{if(!g.isValidElement(n))return;let o=[...t,a];if(n.type===g.Fragment){s.push.apply(s,cs(n.props.children,o));return}n.type!==Se&&de(!1),!n.props.index||!n.props.children||de(!1);let i={id:n.props.id||o.join("-"),caseSensitive:n.props.caseSensitive,element:n.props.element,Component:n.props.Component,index:n.props.index,path:n.props.path,loader:n.props.loader,action:n.props.action,errorElement:n.props.errorElement,ErrorBoundary:n.props.ErrorBoundary,hasErrorBoundary:n.props.ErrorBoundary!=null||n.props.errorElement!=null,shouldRevalidate:n.props.shouldRevalidate,handle:n.props.handle,lazy:n.props.lazy};n.props.children&&(i.children=cs(n.props.children,o)),s.push(i)}),s}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ds(){return ds=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e[n]=s[n])}return e},ds.apply(this,arguments)}function yo(e,t){if(e==null)return{};var s={},n=Object.keys(e),a,o;for(o=0;o<n.length;o++)a=n[o],!(t.indexOf(a)>=0)&&(s[a]=e[a]);return s}function bo(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function vo(e,t){return e.button===0&&(!t||t==="_self")&&!bo(e)}function us(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,s)=>{let n=e[s];return t.concat(Array.isArray(n)?n.map(a=>[s,a]):[[s,n]])},[]))}function ko(e,t){let s=us(e);return t&&t.forEach((n,a)=>{s.has(a)||t.getAll(a).forEach(o=>{s.append(a,o)})}),s}const jo=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],wo="6";try{window.__reactRouterVersion=wo}catch{}const No="startTransition",cn=si[No];function Eo(e){let{basename:t,children:s,future:n,window:a}=e,o=g.useRef();o.current==null&&(o.current=Ci({window:a,v5Compat:!0}));let i=o.current,[l,c]=g.useState({action:i.action,location:i.location}),{v7_startTransition:u}=n||{},d=g.useCallback(f=>{u&&cn?cn(()=>c(f)):c(f)},[c,u]);return g.useLayoutEffect(()=>i.listen(d),[i,d]),g.useEffect(()=>xo(n),[n]),g.createElement(po,{basename:t,children:s,location:l.location,navigationType:l.action,navigator:i,future:n})}const So=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Co=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Y=g.forwardRef(function(t,s){let{onClick:n,relative:a,reloadDocument:o,replace:i,state:l,target:c,to:u,preventScrollReset:d,viewTransition:f}=t,b=yo(t,jo),{basename:x}=g.useContext(nt),k,R=!1;if(typeof u=="string"&&Co.test(u)&&(k=u,So))try{let L=new URL(window.location.href),H=u.startsWith("//")?new URL(L.protocol+u):new URL(u),K=Cs(H.pathname,x);H.origin===L.origin&&K!=null?u=K+H.search+H.hash:R=!0}catch{}let m=Yi(u,{relative:a}),w=Ao(u,{replace:i,state:l,target:c,preventScrollReset:d,relative:a,viewTransition:f});function j(L){n&&n(L),L.defaultPrevented||w(L)}return g.createElement("a",ds({},b,{href:k||m,onClick:R||o?n:j,ref:s,target:c}))});var dn;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(dn||(dn={}));var un;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(un||(un={}));function Ao(e,t){let{target:s,replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:l}=t===void 0?{}:t,c=Ft(),u=mt(),d=aa(e,{relative:i});return g.useCallback(f=>{if(vo(f,s)){f.preventDefault();let b=n!==void 0?n:ar(u)===ar(d);c(e,{replace:b,state:a,preventScrollReset:o,relative:i,viewTransition:l})}},[u,c,d,n,a,s,e,o,i,l])}function Ro(e){let t=g.useRef(us(e)),s=g.useRef(!1),n=mt(),a=g.useMemo(()=>ko(n.search,s.current?null:t.current),[n.search]),o=Ft(),i=g.useCallback((l,c)=>{const u=us(typeof l=="function"?l(a):l);s.current=!0,o("?"+u,c)},[o,a]);return[a,i]}let Mo={data:""},To=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||Mo,Lo=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Io=/\/\*[^]*?\*\/|  +/g,hn=/\n+/g,Ue=(e,t)=>{let s="",n="",a="";for(let o in e){let i=e[o];o[0]=="@"?o[1]=="i"?s=o+" "+i+";":n+=o[1]=="f"?Ue(i,o):o+"{"+Ue(i,o[1]=="k"?"":t)+"}":typeof i=="object"?n+=Ue(i,t?t.replace(/([^,])+/g,l=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,l):l?l+" "+c:c)):o):i!=null&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=Ue.p?Ue.p(o,i):o+":"+i+";")}return s+(t&&a?t+"{"+a+"}":a)+n},Te={},la=e=>{if(typeof e=="object"){let t="";for(let s in e)t+=s+la(e[s]);return t}return e},Oo=(e,t,s,n,a)=>{let o=la(e),i=Te[o]||(Te[o]=(c=>{let u=0,d=11;for(;u<c.length;)d=101*d+c.charCodeAt(u++)>>>0;return"go"+d})(o));if(!Te[i]){let c=o!==e?e:(u=>{let d,f,b=[{}];for(;d=Lo.exec(u.replace(Io,""));)d[4]?b.shift():d[3]?(f=d[3].replace(hn," ").trim(),b.unshift(b[0][f]=b[0][f]||{})):b[0][d[1]]=d[2].replace(hn," ").trim();return b[0]})(e);Te[i]=Ue(a?{["@keyframes "+i]:c}:c,s?"":"."+i)}let l=s&&Te.g?Te.g:null;return s&&(Te.g=Te[i]),((c,u,d,f)=>{f?u.data=u.data.replace(f,c):u.data.indexOf(c)===-1&&(u.data=d?c+u.data:u.data+c)})(Te[i],t,n,l),i},Po=(e,t,s)=>e.reduce((n,a,o)=>{let i=t[o];if(i&&i.call){let l=i(s),c=l&&l.props&&l.props.className||/^go/.test(l)&&l;i=c?"."+c:l&&typeof l=="object"?l.props?"":Ue(l,""):l===!1?"":l}return n+a+(i??"")},"");function Fr(e){let t=this||{},s=e.call?e(t.p):e;return Oo(s.unshift?s.raw?Po(s,[].slice.call(arguments,1),t.p):s.reduce((n,a)=>Object.assign(n,a&&a.call?a(t.p):a),{}):s,To(t.target),t.g,t.o,t.k)}let ca,hs,gs;Fr.bind({g:1});let Pe=Fr.bind({k:1});function _o(e,t,s,n){Ue.p=t,ca=e,hs=s,gs=n}function Ve(e,t){let s=this||{};return function(){let n=arguments;function a(o,i){let l=Object.assign({},o),c=l.className||a.className;s.p=Object.assign({theme:hs&&hs()},l),s.o=/ *go\d+/.test(c),l.className=Fr.apply(s,n)+(c?" "+c:""),t&&(l.ref=i);let u=e;return e[0]&&(u=l.as||e,delete l.as),gs&&u[0]&&gs(l),ca(u,l)}return t?t(a):a}}var $o=e=>typeof e=="function",or=(e,t)=>$o(e)?e(t):e,Do=(()=>{let e=0;return()=>(++e).toString()})(),da=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),zo=20,ua=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,zo)};case 1:return{...e,toasts:e.toasts.map(o=>o.id===t.toast.id?{...o,...t.toast}:o)};case 2:let{toast:s}=t;return ua(e,{type:e.toasts.find(o=>o.id===s.id)?1:0,toast:s});case 3:let{toastId:n}=t;return{...e,toasts:e.toasts.map(o=>o.id===n||n===void 0?{...o,dismissed:!0,visible:!1}:o)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(o=>o.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let a=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(o=>({...o,pauseDuration:o.pauseDuration+a}))}}},tr=[],Ye={toasts:[],pausedAt:void 0},at=e=>{Ye=ua(Ye,e),tr.forEach(t=>{t(Ye)})},Uo={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Ho=(e={})=>{let[t,s]=g.useState(Ye),n=g.useRef(Ye);g.useEffect(()=>(n.current!==Ye&&s(Ye),tr.push(s),()=>{let o=tr.indexOf(s);o>-1&&tr.splice(o,1)}),[]);let a=t.toasts.map(o=>{var i,l,c;return{...e,...e[o.type],...o,removeDelay:o.removeDelay||((i=e[o.type])==null?void 0:i.removeDelay)||(e==null?void 0:e.removeDelay),duration:o.duration||((l=e[o.type])==null?void 0:l.duration)||(e==null?void 0:e.duration)||Uo[o.type],style:{...e.style,...(c=e[o.type])==null?void 0:c.style,...o.style}}});return{...t,toasts:a}},Bo=(e,t="blank",s)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...s,id:(s==null?void 0:s.id)||Do()}),Gt=e=>(t,s)=>{let n=Bo(t,e,s);return at({type:2,toast:n}),n.id},ke=(e,t)=>Gt("blank")(e,t);ke.error=Gt("error");ke.success=Gt("success");ke.loading=Gt("loading");ke.custom=Gt("custom");ke.dismiss=e=>{at({type:3,toastId:e})};ke.remove=e=>at({type:4,toastId:e});ke.promise=(e,t,s)=>{let n=ke.loading(t.loading,{...s,...s==null?void 0:s.loading});return typeof e=="function"&&(e=e()),e.then(a=>{let o=t.success?or(t.success,a):void 0;return o?ke.success(o,{id:n,...s,...s==null?void 0:s.success}):ke.dismiss(n),a}).catch(a=>{let o=t.error?or(t.error,a):void 0;o?ke.error(o,{id:n,...s,...s==null?void 0:s.error}):ke.dismiss(n)}),e};var Wo=(e,t)=>{at({type:1,toast:{id:e,height:t}})},Fo=()=>{at({type:5,time:Date.now()})},St=new Map,Ko=1e3,Go=(e,t=Ko)=>{if(St.has(e))return;let s=setTimeout(()=>{St.delete(e),at({type:4,toastId:e})},t);St.set(e,s)},qo=e=>{let{toasts:t,pausedAt:s}=Ho(e);g.useEffect(()=>{if(s)return;let o=Date.now(),i=t.map(l=>{if(l.duration===1/0)return;let c=(l.duration||0)+l.pauseDuration-(o-l.createdAt);if(c<0){l.visible&&ke.dismiss(l.id);return}return setTimeout(()=>ke.dismiss(l.id),c)});return()=>{i.forEach(l=>l&&clearTimeout(l))}},[t,s]);let n=g.useCallback(()=>{s&&at({type:6,time:Date.now()})},[s]),a=g.useCallback((o,i)=>{let{reverseOrder:l=!1,gutter:c=8,defaultPosition:u}=i||{},d=t.filter(x=>(x.position||u)===(o.position||u)&&x.height),f=d.findIndex(x=>x.id===o.id),b=d.filter((x,k)=>k<f&&x.visible).length;return d.filter(x=>x.visible).slice(...l?[b+1]:[0,b]).reduce((x,k)=>x+(k.height||0)+c,0)},[t]);return g.useEffect(()=>{t.forEach(o=>{if(o.dismissed)Go(o.id,o.removeDelay);else{let i=St.get(o.id);i&&(clearTimeout(i),St.delete(o.id))}})},[t]),{toasts:t,handlers:{updateHeight:Wo,startPause:Fo,endPause:n,calculateOffset:a}}},Vo=Pe`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,Jo=Pe`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Qo=Pe`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Xo=Ve("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Vo} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${Jo} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Qo} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Zo=Pe`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Yo=Ve("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Zo} 1s linear infinite;
`,el=Pe`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,tl=Pe`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,rl=Ve("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${el} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${tl} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,sl=Ve("div")`
  position: absolute;
`,nl=Ve("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,al=Pe`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,il=Ve("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${al} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,ol=({toast:e})=>{let{icon:t,type:s,iconTheme:n}=e;return t!==void 0?typeof t=="string"?g.createElement(il,null,t):t:s==="blank"?null:g.createElement(nl,null,g.createElement(Yo,{...n}),s!=="loading"&&g.createElement(sl,null,s==="error"?g.createElement(Xo,{...n}):g.createElement(rl,{...n})))},ll=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,cl=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,dl="0%{opacity:0;} 100%{opacity:1;}",ul="0%{opacity:1;} 100%{opacity:0;}",hl=Ve("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,gl=Ve("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,fl=(e,t)=>{let s=e.includes("top")?1:-1,[n,a]=da()?[dl,ul]:[ll(s),cl(s)];return{animation:t?`${Pe(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Pe(a)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},xl=g.memo(({toast:e,position:t,style:s,children:n})=>{let a=e.height?fl(e.position||t||"top-center",e.visible):{opacity:0},o=g.createElement(ol,{toast:e}),i=g.createElement(gl,{...e.ariaProps},or(e.message,e));return g.createElement(hl,{className:e.className,style:{...a,...s,...e.style}},typeof n=="function"?n({icon:o,message:i}):g.createElement(g.Fragment,null,o,i))});_o(g.createElement);var pl=({id:e,className:t,style:s,onHeightUpdate:n,children:a})=>{let o=g.useCallback(i=>{if(i){let l=()=>{let c=i.getBoundingClientRect().height;n(e,c)};l(),new MutationObserver(l).observe(i,{subtree:!0,childList:!0,characterData:!0})}},[e,n]);return g.createElement("div",{ref:o,className:t,style:s},a)},ml=(e,t)=>{let s=e.includes("top"),n=s?{top:0}:{bottom:0},a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:da()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(s?1:-1)}px)`,...n,...a}},yl=Fr`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,Vt=16,bl=({reverseOrder:e,position:t="top-center",toastOptions:s,gutter:n,children:a,containerStyle:o,containerClassName:i})=>{let{toasts:l,handlers:c}=qo(s);return g.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:Vt,left:Vt,right:Vt,bottom:Vt,pointerEvents:"none",...o},className:i,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(u=>{let d=u.position||t,f=c.calculateOffset(u,{reverseOrder:e,gutter:n,defaultPosition:t}),b=ml(d,f);return g.createElement(pl,{id:u.id,key:u.id,onHeightUpdate:c.updateHeight,className:u.visible?yl:"",style:b},u.type==="custom"?or(u.message,u):a?a(u):g.createElement(xl,{toast:u,position:d}))}))};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var vl={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kl=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),_=(e,t)=>{const s=g.forwardRef(({color:n="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:i,className:l="",children:c,...u},d)=>g.createElement("svg",{ref:d,...vl,width:a,height:a,stroke:n,strokeWidth:i?Number(o)*24/Number(a):o,className:["lucide",`lucide-${kl(e)}`,l].join(" "),...u},[...t.map(([f,b])=>g.createElement(f,b)),...Array.isArray(c)?c:[c]]));return s.displayName=`${e}`,s};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gn=_("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const lr=_("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jl=_("Bold",[["path",{d:"M14 12a4 4 0 0 0 0-8H6v8",key:"v2sylx"}],["path",{d:"M15 20a4 4 0 0 0 0-8H6v8Z",key:"1ef5ya"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ge=_("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fn=_("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rr=_("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wl=_("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const st=_("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nl=_("Cloud",[["path",{d:"M17.5 19H9a7 7 0 1 1 6.71-9h1.79a4.5 4.5 0 1 1 0 9Z",key:"p7xjir"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const El=_("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sl=_("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ha=_("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ga=_("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cl=_("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kt=_("Folder",[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Al=_("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rl=_("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ml=_("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tl=_("Heading2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ll=_("Headphones",[["path",{d:"M3 14h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-7a9 9 0 0 1 18 0v7a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3",key:"1xhozi"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Il=_("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xn=_("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jt=_("Image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ol=_("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pn=_("Key",[["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["path",{d:"m15.5 7.5 3 3L22 7l-3-3",key:"1rn1fs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pl=_("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _l=_("ListOrdered",[["line",{x1:"10",x2:"21",y1:"6",y2:"6",key:"76qw6h"}],["line",{x1:"10",x2:"21",y1:"12",y2:"12",key:"16nom4"}],["line",{x1:"10",x2:"21",y1:"18",y2:"18",key:"u3jurt"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=_("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mn=_("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Dl=_("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zl=_("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fa=_("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yn=_("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ul=_("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Qr=_("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Hl=_("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Bl=_("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Nt=_("Music",[["path",{d:"M9 18V5l12-2v13",key:"1jmyc2"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["circle",{cx:"18",cy:"16",r:"3",key:"1hluhg"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Wl=_("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fs=_("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Fl=_("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Kl=_("Quote",[["path",{d:"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z",key:"4rm80e"}],["path",{d:"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z",key:"10za9r"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Gl=_("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ql=_("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _t=_("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Vl=_("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ht=_("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Jl=_("SkipBack",[["polygon",{points:"19 20 9 12 19 4 19 20",key:"o2sva"}],["line",{x1:"5",x2:"5",y1:"19",y2:"5",key:"1ocqjk"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ql=_("SkipForward",[["polygon",{points:"5 4 15 12 5 20 5 4",key:"16p6eg"}],["line",{x1:"19",x2:"19",y1:"5",y2:"19",key:"futhcm"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bn=_("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Xl=_("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zl=_("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ct=_("Tag",[["path",{d:"M12 2H2v10l9.29 9.29c.94.94 2.48.94 3.42 0l6.58-6.58c.94-.94.94-2.48 0-3.42L12 2Z",key:"14b2ls"}],["path",{d:"M7 7h.01",key:"7u93v4"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Yl=_("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xs=_("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ec=_("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const tc=_("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yt=_("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rc=_("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sc=_("VolumeX",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cr=_("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);function nc(){const[e,t]=g.useState(()=>{const n=localStorage.getItem("theme");return n||(window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light")});return g.useEffect(()=>{const n=document.documentElement;n.classList.remove("light","dark"),n.classList.add(e),localStorage.setItem("theme",e)},[e]),{theme:e,toggleTheme:()=>{t(n=>n==="light"?"dark":"light")},setTheme:t,isDark:e==="dark"}}const ac={weather:"https://blog.fddfffff.site",aiImage:"https://blog.fddfffff.site",blog:"https://blog.fddfffff.site",music:"https://blog.fddfffff.site",r2Storage:"https://pub-02490a32db742d596d4d7c00aec.r2.dev"},ue=ac,le={weather:`${ue.weather}/weather`,aiImage:`${ue.aiImage}/ai/generate`,posts:`${ue.blog}/api/posts`,post:e=>`${ue.blog}/api/posts/${e}`,comments:e=>`${ue.blog}/api/posts/${e}/comments`,comment:(e,t)=>`${ue.blog}/api/posts/${e}/comments/${t}`,uploadImage:`${ue.blog}/api/upload-image`,auth:{verify:`${ue.blog}/api/auth/verify`},music:{search:`${ue.music}/music/search`,songUrl:`${ue.music}/music/url`,lyric:`${ue.music}/music/lyric`,songDetail:`${ue.music}/music/song/detail`,playlist:`${ue.music}/music/playlist/detail`},r2Images:`${ue.r2Storage}`,categories:`${ue.blog}/api/categories`,tags:`${ue.blog}/api/tags`,search:`${ue.blog}/api/search`},xa={"Content-Type":"application/json",Accept:"application/json"},pa=6e4;function ic(){return localStorage.getItem("admin_api_key")}const Ee=(e,t={})=>{const s=new AbortController,n=setTimeout(()=>s.abort(),pa);return fetch(e,{headers:{...xa,...t.headers},signal:s.signal,...t}).finally(()=>{clearTimeout(n)})},sr=(e,t={})=>{const s=ic(),n={...xa,...t.headers};s&&(n["X-API-Key"]=s);const a=new AbortController,o=setTimeout(()=>a.abort(),pa);return fetch(e,{headers:n,signal:a.signal,...t}).finally(()=>{clearTimeout(o)})};class dr extends Error{constructor(t,s,n){super(n||`API Error: ${t} ${s}`),this.status=t,this.statusText=s,this.name="ApiError"}}const ge=async e=>{if(!e.ok){let t;try{const s=await e.json();t=s.error||s.message||e.statusText}catch{t=e.statusText}throw new dr(e.status,e.statusText,t)}return e.json()},ma=g.createContext(void 0);function qt(){const e=g.useContext(ma);if(e===void 0)throw new Error("useAuth must be used within an AuthProvider");return e}function oc({children:e}){const[t,s]=g.useState(!1),[n,a]=g.useState(null),[o,i]=g.useState(!0);g.useEffect(()=>{const f=localStorage.getItem("admin_api_key");f?l(f):i(!1)},[]);const l=async f=>{try{return(await(await fetch(le.auth.verify,{method:"POST",headers:{"Content-Type":"application/json","X-API-Key":f}})).json()).isAdmin?(s(!0),a(f),localStorage.setItem("admin_api_key",f),!0):(s(!1),a(null),localStorage.removeItem("admin_api_key"),!1)}catch(b){return console.error("API密钥验证失败:",b),s(!1),a(null),localStorage.removeItem("admin_api_key"),!1}finally{i(!1)}},d={isAdmin:t,apiKey:n,login:async f=>(i(!0),await l(f)),logout:()=>{s(!1),a(null),localStorage.removeItem("admin_api_key")},isLoading:o};return r.jsx(ma.Provider,{value:d,children:e})}function ya({isOpen:e,onClose:t}){const[s,n]=g.useState(""),[a,o]=g.useState(!1),[i,l]=g.useState(""),[c,u]=g.useState(!1),{login:d}=qt();if(!e)return null;const f=async x=>{if(x.preventDefault(),!s.trim()){l("请输入API密钥");return}u(!0),l("");try{await d(s.trim())?(n(""),t()):l("API密钥无效，请检查后重试")}catch{l("登录失败，请稍后重试")}finally{u(!1)}},b=()=>{n(""),l(""),o(!1),t()};return r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:[r.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(ht,{className:"text-white",size:20})}),r.jsxs("div",{children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"管理员登录"}),r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"输入API密钥以获取管理权限"})]})]}),r.jsx("button",{onClick:b,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",title:"关闭",children:r.jsx(cr,{size:20})})]}),r.jsxs("form",{onSubmit:f,className:"p-6",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:[r.jsx(pn,{size:16,className:"inline mr-2"}),"API密钥"]}),r.jsxs("div",{className:"relative",children:[r.jsx("input",{type:a?"text":"password",value:s,onChange:x=>n(x.target.value),placeholder:"请输入管理员API密钥",className:"w-full pr-10 pl-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",disabled:c}),r.jsx("button",{type:"button",onClick:()=>o(!a),className:"absolute right-3 top-2.5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors",disabled:c,title:a?"隐藏密钥":"显示密钥",children:a?r.jsx(ha,{size:16}):r.jsx(ga,{size:16})})]})]}),i&&r.jsx("div",{className:"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:r.jsxs("p",{className:"text-red-700 dark:text-red-300 text-sm flex items-center",children:[r.jsx(Dl,{size:16,className:"mr-2"}),i]})})]}),r.jsxs("div",{className:"flex space-x-3 mt-6",children:[r.jsx("button",{type:"button",onClick:b,className:"flex-1 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",disabled:c,children:"取消"}),r.jsx("button",{type:"submit",disabled:c||!s.trim(),className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2",children:c?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"验证中..."})]}):r.jsxs(r.Fragment,{children:[r.jsx(pn,{size:16}),r.jsx("span",{children:"登录"})]})})]})]})]})})}class ye{static async getPosts(t=1,s=10,n,a){var l,c,u;const o=new URLSearchParams({page:t.toString(),pageSize:s.toString()});n&&o.append("category",n),a&&o.append("tag",a);const i=`${le.posts}?${o.toString()}`;try{const d=await Ee(i),f=await ge(d);return{posts:f.posts||[],total:((l=f.pagination)==null?void 0:l.totalPosts)||0,page:((c=f.pagination)==null?void 0:c.page)||1,pageSize:((u=f.pagination)==null?void 0:u.pageSize)||10}}catch(d){throw console.error("获取文章列表失败:",d),d}}static async getPost(t){try{const s=await Ee(le.post(t));return await ge(s)}catch(s){throw console.error("获取文章失败:",s),s}}static async createPost(t){try{const s=await sr(le.posts,{method:"POST",body:JSON.stringify(t)});return await ge(s)}catch(s){throw console.error("创建文章失败:",s),s}}static async updatePost(t,s){try{const n=await sr(le.post(t),{method:"PUT",body:JSON.stringify(s)});return await ge(n)}catch(n){throw console.error("更新文章失败:",n),n}}static async deletePost(t){try{const s=await sr(le.post(t),{method:"DELETE"});await ge(s)}catch(s){throw console.error("删除文章失败:",s),s}}static async searchPosts(t){const s=new URLSearchParams({q:t}),n=`${le.search}?${s.toString()}`;try{const a=await Ee(n);return(await ge(a)).posts}catch(a){throw console.error("搜索文章失败:",a),a}}}function lc({onMusicToggle:e}){const[t,s]=g.useState(!1),[n,a]=g.useState(!1),[o,i]=g.useState(!1),[l,c]=g.useState(""),[u,d]=g.useState([]),[f,b]=g.useState(!1),{theme:x,toggleTheme:k}=nc(),{isAdmin:R,logout:m}=qt(),w=Ft(),j=g.useRef(null),L=g.useRef(null),H=[{name:"首页",href:"/"},{name:"分类",href:"/categories"},{name:"标签",href:"/tags"},{name:"关于",href:"/about"}],K=async z=>{if(!z.trim()){d([]);return}b(!0);try{const ee=await ye.searchPosts(z.trim());d(ee.slice(0,5))}catch(ee){console.error("搜索失败:",ee),d([])}finally{b(!1)}};return g.useEffect(()=>{const z=ee=>{(ee.ctrlKey||ee.metaKey)&&ee.key==="k"&&(ee.preventDefault(),i(re=>{const $=!re;return $?setTimeout(()=>{var S;return(S=L.current)==null?void 0:S.focus()},100):(c(""),d([])),$})),ee.key==="Escape"&&(i(!1),c(""),d([]))};return document.addEventListener("keydown",z),()=>document.removeEventListener("keydown",z)},[]),g.useEffect(()=>{const z=ee=>{j.current&&!j.current.contains(ee.target)&&(i(!1),c(""),d([]))};if(o)return document.addEventListener("mousedown",z),()=>document.removeEventListener("mousedown",z)},[o]),g.useEffect(()=>{const z=setTimeout(()=>{K(l)},300);return()=>clearTimeout(z)},[l]),r.jsxs("header",{className:"sticky top-0 z-50 bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border-b border-gray-200 dark:border-gray-700",children:[r.jsxs("div",{className:"container mx-auto px-4",children:[r.jsxs("div",{className:"flex items-center justify-between h-16",children:[r.jsxs(Y,{to:"/",className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center",children:r.jsx("span",{className:"text-white font-bold text-sm",children:"B"})}),r.jsx("span",{className:"font-semibold text-xl text-gray-900 dark:text-white",children:"个人博客"})]}),r.jsx("nav",{className:"hidden md:flex items-center space-x-8",children:H.map(z=>r.jsx(Y,{to:z.href,className:"text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:z.name},z.name))}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("div",{className:"relative",ref:j,children:[r.jsx("button",{onClick:()=>i(!o),title:"快捷搜索 (Ctrl+K)","aria-label":"快捷搜索",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(_t,{size:20})}),o&&r.jsx("div",{className:"absolute top-full mt-2 right-0 w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50",children:r.jsxs("div",{className:"p-4",children:[r.jsxs("div",{className:"flex items-center space-x-2 mb-3",children:[r.jsx(_t,{size:16,className:"text-gray-400"}),r.jsx("input",{ref:L,type:"text",placeholder:"搜索文章...",value:l,onChange:z=>c(z.target.value),className:"flex-1 outline-none bg-transparent text-sm",onKeyDown:z=>{z.key==="Enter"&&l.trim()&&(w(`/search?q=${encodeURIComponent(l.trim())}`),i(!1),c(""))}}),r.jsxs("kbd",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 rounded text-gray-500 dark:text-gray-400",children:[r.jsx(Sl,{size:12,className:"inline mr-1"}),"K"]})]}),f&&r.jsx("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:"搜索中..."}),u.length>0&&!f&&r.jsxs("div",{className:"space-y-2",children:[r.jsx("div",{className:"text-xs text-gray-500 dark:text-gray-400 mb-2",children:"搜索结果"}),u.map(z=>r.jsxs(Y,{to:`/post/${z.slug}`,onClick:()=>{i(!1),c("")},className:"block p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-sm",children:[r.jsx("div",{className:"font-medium text-gray-900 dark:text-white truncate",children:z.title}),r.jsx("div",{className:"text-gray-500 dark:text-gray-400 text-xs truncate mt-1",children:z.excerpt})]},z.slug)),l.trim()&&r.jsx(Y,{to:`/search?q=${encodeURIComponent(l.trim())}`,onClick:()=>{i(!1),c("")},className:"block p-2 text-center text-blue-600 dark:text-blue-400 hover:bg-gray-50 dark:hover:bg-gray-700 rounded text-sm",children:"查看所有结果 →"})]}),l.trim()&&u.length===0&&!f&&r.jsxs("div",{className:"text-center py-4 text-gray-500 dark:text-gray-400 text-sm",children:["未找到相关文章",r.jsx(Y,{to:`/search?q=${encodeURIComponent(l.trim())}`,onClick:()=>{i(!1),c("")},className:"block mt-2 text-blue-600 dark:text-blue-400 hover:underline",children:"在搜索页面查看 →"})]})]})})]}),r.jsx("button",{onClick:e,title:"音乐播放器","aria-label":"打开音乐播放器",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors",children:r.jsx(Nt,{size:20})}),R?r.jsx("button",{onClick:m,title:"管理员已登录 - 点击退出","aria-label":"管理员退出",className:"p-2 text-green-600 dark:text-green-400 hover:text-red-600 dark:hover:text-red-400 transition-colors",children:r.jsx(ht,{size:20})}):r.jsx("button",{onClick:()=>a(!0),title:"管理员登录","aria-label":"管理员登录",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(ht,{size:20})}),R&&r.jsxs(Y,{to:"/write",title:"写文章",className:"hidden md:flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[r.jsx(fs,{size:16}),r.jsx("span",{children:"写文章"})]}),r.jsx("button",{onClick:k,title:x==="dark"?"切换到浅色模式":"切换到深色模式","aria-label":x==="dark"?"切换到浅色模式":"切换到深色模式",className:"p-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:x==="dark"?r.jsx(Zl,{size:20}):r.jsx(Hl,{size:20})}),r.jsx("button",{onClick:()=>s(!t),title:t?"关闭菜单":"打开菜单","aria-label":t?"关闭菜单":"打开菜单","aria-expanded":t,className:"md:hidden p-2 text-gray-600 dark:text-gray-400",children:t?r.jsx(cr,{size:20}):r.jsx(Ul,{size:20})})]})]}),t&&r.jsx("div",{className:"md:hidden py-4 border-t border-gray-200 dark:border-gray-700",children:r.jsxs("nav",{className:"space-y-2",children:[H.map(z=>r.jsx(Y,{to:z.href,className:"block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors",onClick:()=>s(!1),children:z.name},z.name)),R?r.jsxs(r.Fragment,{children:[r.jsxs(Y,{to:"/write",className:"flex items-center space-x-2 px-4 py-2 text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors",onClick:()=>s(!1),children:[r.jsx(fs,{size:16}),r.jsx("span",{children:"写文章"})]}),r.jsxs("button",{onClick:()=>{m(),s(!1)},className:"flex items-center space-x-2 px-4 py-2 text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left",children:[r.jsx(zl,{size:16}),r.jsx("span",{children:"管理员退出"})]})]}):r.jsxs("button",{onClick:()=>{a(!0),s(!1)},className:"flex items-center space-x-2 px-4 py-2 text-green-600 dark:text-green-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors w-full text-left",children:[r.jsx(ht,{size:16}),r.jsx("span",{children:"管理员登录"})]})]})})]}),r.jsx(ya,{isOpen:n,onClose:()=>a(!1)})]})}function cc(){return r.jsx("footer",{className:"bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700",children:r.jsxs("div",{className:"container mx-auto px-4 py-8",children:[r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"个人博客"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"基于 Cloudflare Workers 构建的现代个人博客系统，集成 AI 配图、天气显示等功能。"})]}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"快速链接"}),r.jsxs("ul",{className:"space-y-2 text-sm",children:[r.jsx("li",{children:r.jsx("a",{href:"/",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"首页"})}),r.jsx("li",{children:r.jsx("a",{href:"/about",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"关于我"})}),r.jsx("li",{children:r.jsx("a",{href:"/write",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:"写文章"})})]})]}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"联系方式"}),r.jsxs("div",{className:"flex space-x-4",children:[r.jsx("a",{href:"https://github.com/ajkdfe2e2e",target:"_blank",rel:"noopener noreferrer",title:"GitHub","aria-label":"访问 GitHub 主页",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(Al,{size:20})}),r.jsx("a",{href:"https://x.com/x2a1HRjxs552213",target:"_blank",rel:"noopener noreferrer",title:"Twitter","aria-label":"访问 Twitter 主页",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(ec,{size:20})}),r.jsx("a",{href:"mailto:<EMAIL>",title:"邮箱","aria-label":"发送邮件联系我",className:"text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:r.jsx(fa,{size:20})})]})]})]}),r.jsx("div",{className:"border-t border-gray-200 dark:border-gray-700 mt-8 pt-8 text-center",children:r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"© 2025 个人博客. 使用 Cloudflare Workers 强力驱动"})})]})})}class vn{static async getCurrentWeather(){try{let t;try{t=await this.getCurrentPosition()}catch(o){console.warn("无法获取当前位置，使用默认位置（北京）:",o),t={latitude:39.9042,longitude:116.4074}}const s=new URLSearchParams({lat:t.latitude.toString(),lon:t.longitude.toString()}),n=`${le.weather}?${s.toString()}`,a=await Ee(n);return await ge(a)}catch(t){throw console.error("获取天气信息失败:",t),t}}static async getWeatherByLocation(t,s){try{const n=new URLSearchParams({lat:t.toString(),lon:s.toString()}),a=`${le.weather}?${n.toString()}`,o=await Ee(a);return await ge(o)}catch(n){throw console.error("获取天气信息失败:",n),n}}static getCurrentPosition(){return new Promise((t,s)=>{if(!navigator.geolocation){s(new Error("该浏览器不支持地理定位"));return}const n={enableHighAccuracy:!0,timeout:5e3,maximumAge:3e5};navigator.geolocation.getCurrentPosition(a=>{t({latitude:a.coords.latitude,longitude:a.coords.longitude})},a=>{let o="获取位置失败";switch(a.code){case a.PERMISSION_DENIED:o="用户拒绝了位置请求";break;case a.POSITION_UNAVAILABLE:o="位置信息不可用";break;case a.TIMEOUT:o="获取位置超时";break}s(new Error(o))},n)})}static isGeolocationSupported(){return"geolocation"in navigator}}function dc(){const[e,t]=g.useState(null),[s,n]=g.useState(!0),[a,o]=g.useState(null);return g.useEffect(()=>{(async()=>{try{if(n(!0),o(null),!vn.isGeolocationSupported())throw new Error("浏览器不支持地理定位");const l=await vn.getCurrentWeather();t(l)}catch(l){console.error("获取天气信息失败:",l),o(l instanceof Error?l.message:"获取天气信息失败")}finally{n(!1)}})()},[]),s?r.jsx("div",{className:"card p-4",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"animate-spin",children:r.jsx(Nl,{size:20,className:"text-gray-400"})}),r.jsxs("div",{children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-1"}),r.jsx("div",{className:"h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"})]})]})}):a||!e?r.jsx("div",{className:"card p-4",children:r.jsxs("div",{className:"flex items-center space-x-3 text-gray-500 dark:text-gray-400",children:[r.jsx(yn,{size:20}),r.jsxs("div",{children:[r.jsx("p",{className:"text-sm",children:"位置信息"}),r.jsx("p",{className:"text-xs",children:"获取失败"})]})]})}):r.jsx("div",{className:"card p-4",children:r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center",children:r.jsx(Yl,{size:20,className:"text-white"})})}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(yn,{size:14,className:"text-gray-500 dark:text-gray-400"}),r.jsx("p",{className:"text-sm font-medium text-gray-900 dark:text-gray-100 truncate",children:e.location})]}),r.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[r.jsx("span",{className:"text-lg font-semibold text-blue-600 dark:text-blue-400",children:e.temperature}),r.jsx("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.weather})]})]})]})})}function uc(){const[e,t]=g.useState([]),[s,n]=g.useState([]),[a,o]=g.useState(!0);return g.useEffect(()=>{(async()=>{try{o(!0);const l=await ye.getPosts(1,5);t(l.posts);const c=await ye.getPosts(1,100),u=new Set;c.posts.forEach(d=>{d.tags.forEach(f=>u.add(f))}),n(Array.from(u))}catch(l){console.error("加载侧边栏数据失败:",l),t([]),n([])}finally{o(!1)}})()},[]),r.jsxs("div",{className:"sticky top-8 space-y-6",children:[r.jsx(dc,{}),r.jsxs("div",{className:"card p-4",children:[r.jsx("h3",{className:"font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"最新文章"}),r.jsx("div",{className:"space-y-2",children:a?Array.from({length:3}).map((i,l)=>r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"},l)):e.length>0?e.map(i=>r.jsx(Y,{to:`/post/${i.slug}`,className:"block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors line-clamp-2",title:i.title,children:i.title},i.id)):r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"暂无文章"})})]}),r.jsxs("div",{className:"card p-4",children:[r.jsx("h3",{className:"font-semibold mb-3 text-gray-900 dark:text-gray-100",children:"标签"}),r.jsx("div",{className:"flex flex-wrap gap-2",children:a?Array.from({length:6}).map((i,l)=>r.jsx("div",{className:"h-6 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"},l)):s.length>0?s.map(i=>r.jsx(Y,{to:`/tag/${encodeURIComponent(i)}`,className:"px-2 py-1 text-xs bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-blue-600 hover:text-white transition-colors cursor-pointer",children:i},i)):r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-500",children:"暂无标签"})})]})]})}function hc(){const e=mt(),t=Kt(),[s,n]=g.useState([]),[a,o]=g.useState(null);return g.useEffect(()=>{(async()=>{if(e.pathname.startsWith("/post/")&&t.slug)try{const l=await ye.getPost(t.slug);o({title:l.title,category:l.category})}catch(l){console.error("获取文章详情失败:",l),o({title:"文章详情",category:void 0})}})()},[e.pathname,t.slug]),g.useEffect(()=>{n((()=>{const l=e.pathname,c=[{label:"首页",href:"/"}];return l==="/"?[{label:"首页"}]:(l==="/categories"?c.push({label:"分类"}):l.startsWith("/category/")?(c.push({label:"分类",href:"/categories"}),c.push({label:decodeURIComponent(t.category||"")})):l==="/tags"?c.push({label:"标签"}):l.startsWith("/tag/")?(c.push({label:"标签",href:"/tags"}),c.push({label:decodeURIComponent(t.tag||"")})):l==="/search"?c.push({label:"搜索"}):l==="/about"?c.push({label:"关于"}):l==="/write"?c.push({label:"写文章"}):l.startsWith("/write/")?(c.push({label:"写文章",href:"/write"}),c.push({label:"编辑文章"})):l.startsWith("/post/")&&(a?(a.category&&(c.push({label:"分类",href:"/categories"}),c.push({label:a.category,href:`/category/${encodeURIComponent(a.category)}`})),c.push({label:a.title})):c.push({label:"文章详情"})),c)})())},[e.pathname,t,a]),e.pathname==="/"?null:r.jsx("nav",{"aria-label":"面包屑导航",className:"mb-6",children:r.jsx("ol",{className:"flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400",children:s.map((i,l)=>r.jsxs("li",{className:"flex items-center",children:[l>0&&r.jsx(wl,{size:16,className:"mr-2 text-gray-400"}),i.href?r.jsxs(Y,{to:i.href,className:"flex items-center hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[l===0&&r.jsx(xn,{size:16,className:"mr-1"}),r.jsx("span",{className:"truncate max-w-[200px]",title:i.label,children:i.label})]}):r.jsxs("span",{className:"flex items-center text-gray-900 dark:text-gray-100",children:[l===0&&r.jsx(xn,{size:16,className:"mr-1"}),r.jsx("span",{className:"truncate max-w-[200px]",title:i.label,children:i.label})]})]},l))})})}function gc({children:e,onMusicToggle:t}){return r.jsxs("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors",children:[r.jsx(lc,{onMusicToggle:t}),r.jsx("main",{className:"container mx-auto px-4 py-8 max-w-6xl",children:r.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[r.jsxs("div",{className:"lg:col-span-3",children:[r.jsx(hc,{}),e]}),r.jsx("aside",{className:"lg:col-span-1",children:r.jsx(uc,{})})]})}),r.jsx(cc,{})]})}const kn=(e,t,s)=>{if(console.log(`[Music Player] 处理图片URL: ${e} (歌曲: ${t})`),!e)return console.log("[Music Player] 图片URL为空，使用默认图片"),"/default-album.jpg";if(s!=null&&s.has(e))return console.log(`[Music Player] URL之前加载失败过，使用默认图片: ${e}`),"/default-album.jpg";let n=e;return n.startsWith("http:")&&(console.log(`[Music Player] 转换HTTP到HTTPS: ${n}`),n.match(/http:\/\/p\d+\.music\.126\.net/)?n=n.replace(/^http:\/\/p(\d+)\.music\.126\.net/,"https://p$1.music.126.net"):n.match(/http:\/\/y\.gtimg\.cn/)?n=n.replace(/^http:\/\//,"https://"):n=n.replace("http:","https:"),console.log(`[Music Player] 转换后URL: ${n}`)),!n||n==="null"||n==="undefined"?(console.log(`[Music Player] URL无效，使用默认图片: ${n}`),"/default-album.jpg"):(console.log(`[Music Player] 最终图片URL: ${n}`),n)},fc=g.forwardRef((e,t)=>{var Ae,T;const[s,n]=g.useState(!1),[a,o]=g.useState(null),[i,l]=g.useState([]),[c,u]=g.useState(0),[d,f]=g.useState(!1),[b,x]=g.useState(!1),[k,R]=g.useState(0),[m,w]=g.useState(0),[j,L]=g.useState(.7),[H,K]=g.useState(!1),[z,ee]=g.useState(""),[re,$]=g.useState([]),[S,V]=g.useState(!1),[X,ce]=g.useState([]),[se,he]=g.useState(-1),[N,p]=g.useState([]),[E,I]=g.useState("auto"),[A,P]=g.useState(!1),[B,O]=g.useState(""),D=g.useRef(null),J=g.useRef(null),[Z,ae]=g.useState(new Set);g.useImperativeHandle(t,()=>({toggleVisibility:()=>{f(!d),x(!0)}})),g.useEffect(()=>{(async()=>{try{const v=await(await fetch(`${le.music.search.replace("/search","/sources")}`)).json();if(v&&v.code===200&&Array.isArray(v.sources))p(v.sources);else throw new Error("Invalid response format")}catch(y){console.error("Error fetching music sources:",y),p([{type:"wyy",name:"网易云音乐",enabled:!0,priority:1},{type:"qq",name:"QQ音乐",enabled:!0,priority:2},{type:"kg",name:"酷狗音乐",enabled:!0,priority:3},{type:"kw",name:"酷我音乐",enabled:!0,priority:4},{type:"xmla",name:"喜马拉雅",enabled:!0,priority:5},{type:"qishui",name:"汽水音乐",enabled:!0,priority:6},{type:"qt",name:"蜻蜓FM",enabled:!0,priority:7}])}})()},[]),g.useEffect(()=>{let h=window.innerWidth;const y=()=>{const v=window.innerWidth;if(Math.abs(v-h)<50)return;const M=v<640;h<640!==M&&(M&&!a&&d&&!b?f(!1):!M&&!d&&f(!0)),h=v};return window.addEventListener("resize",y),()=>window.removeEventListener("resize",y)},[a,d]),g.useEffect(()=>{const h=D.current;if(!h)return;const y=()=>R(h.currentTime),v=()=>w(h.duration),M=()=>Ce();return h.addEventListener("timeupdate",y),h.addEventListener("durationchange",v),h.addEventListener("ended",M),()=>{h.removeEventListener("timeupdate",y),h.removeEventListener("durationchange",v),h.removeEventListener("ended",M)}},[]),g.useEffect(()=>{D.current&&(D.current.volume=H?0:j)},[j,H]),g.useEffect(()=>{var y;if(!X.length)return;const h=X.findIndex((v,M)=>{const U=X[M+1];return k>=v.time&&(!U||k<U.time)});if(h!==-1&&h!==se){he(h);const v=(y=J.current)==null?void 0:y.children[h];v&&v.scrollIntoView({behavior:"smooth",block:"center"})}},[k,X,se]);const fe=async h=>{if(h.trim()){V(!0);try{let y=`${le.music.search}?keywords=${encodeURIComponent(h)}`;E!=="auto"&&(y+=`&source=${E}`);const v=await fetch(y);if(!v.ok)throw new Error(`HTTP error! status: ${v.status}`);const M=await v.json();if(console.log("[Music Player] 后端返回的原始数据:",M),M.code===200&&M.result&&Array.isArray(M.result.songs)){const U=M.result.songs.map(F=>{var Qe,Fs,Ks,Gs,qs,Vs,Js,Qs,Xs,Zs,Ys,en;console.log("[Music Player] 处理单首歌曲数据:",F);const me={id:((Qe=F.album)==null?void 0:Qe.id)||((Fs=F.al)==null?void 0:Fs.id)||0,name:((Ks=F.album)==null?void 0:Ks.name)||((Gs=F.al)==null?void 0:Gs.name)||"未知专辑",picUrl:((qs=F.album)==null?void 0:qs.picUrl)||((Vs=F.al)==null?void 0:Vs.picUrl)||"/default-album.jpg"};return console.log("[Music Player] 提取的专辑信息:",me),{id:F.id,name:F.name||"未知歌曲",artists:Array.isArray(F.artists)?F.artists:Array.isArray(F.ar)?F.ar:[{id:0,name:"未知艺术家"}],album:me,duration:F.duration||F.dt||0,sourceType:F.sourceType||M.source,sourceName:F.sourceName||M.sourceName,mid:((Js=F.originalData)==null?void 0:Js.mid)||F.mid,media_mid:((Qs=F.originalData)==null?void 0:Qs.media_mid)||F.media_mid,vid:((Xs=F.originalData)==null?void 0:Xs.vid)||F.vid,hash:((Zs=F.originalData)==null?void 0:Zs.hash)||F.hash,album_id:((Ys=F.originalData)==null?void 0:Ys.album_id)||F.album_id,album_audio_id:((en=F.originalData)==null?void 0:en.album_audio_id)||F.album_audio_id}});if($(U),M.sourceName)O(`搜索来源：${M.sourceName}`);else if(E!=="auto"){const F=N.find(me=>me.type===E);O(`搜索来源：${(F==null?void 0:F.name)||E}`)}else O("多源智能搜索")}else $([]),O("搜索无结果")}catch(y){console.error("Error searching music:",y),$([]),O("搜索失败，请检查网络连接")}finally{V(!1)}}},lt=async h=>{var y,v;try{let M=`${le.music.songUrl}?`;const U=new URLSearchParams;switch(h.sourceType&&U.append("source",h.sourceType),h.sourceType){case"qq":if(h.mid&&h.media_mid&&h.vid)U.append("mid",h.mid),U.append("media_mid",h.media_mid),U.append("vid",h.vid);else return console.error("QQ音乐缺少必要参数:",h),null;break;case"kg":if(h.hash&&h.album_id&&h.album_audio_id)U.append("hash",h.hash),U.append("album_id",h.album_id),U.append("album_audio_id",h.album_audio_id);else return console.error("酷狗音乐缺少必要参数:",h),null;break;default:U.append("id",h.id.toString());break}M+=U.toString(),console.log("调用URL获取接口:",M);const me=await(await fetch(M)).json();return console.log("URL API响应:",me),me.code===200&&((v=(y=me.data)==null?void 0:y[0])!=null&&v.url)?me.data[0].url:null}catch(M){return console.error("Error fetching song URL:",M),null}},bt=async h=>{try{let y=`${le.music.lyric}?`;const v=new URLSearchParams;switch(h.sourceType&&v.append("source",h.sourceType),h.sourceType){case"qq":v.append("id",h.id.toString());break;case"kg":if(h.hash)v.append("hash",h.hash);else{console.error("酷狗音乐歌词缺少hash参数:",h),ce([]);return}break;default:v.append("id",h.id.toString());break}y+=v.toString(),console.log("调用歌词获取接口:",y);const U=await(await fetch(y)).json();if(console.log("歌词API响应:",U),U.code===200&&U.lrc&&U.lrc.lyric){const F=vt(U.lrc.lyric);ce(F)}else ce([])}catch(y){console.error("Error fetching lyrics:",y),ce([])}},vt=h=>h.split(`
`).map(y=>{const v=y.match(/\[(\d{2}):(\d{2})\.(\d{2,3})\](.*)/);if(!v)return null;const M=parseInt(v[1]),U=parseInt(v[2]),F=parseInt(v[3].padEnd(3,"0")),me=M*60+U+F/1e3,Qe=v[4].trim();return Qe?{time:me,text:Qe}:null}).filter(Boolean),Je=async(h,y)=>{if(console.log("Playing song:",h),y!==void 0)u(y),l(re);else{const v=re.findIndex(M=>M.id===h.id&&M.sourceType===h.sourceType);v!==-1?(u(v),l(re)):(l([h]),u(0))}o(h);try{const v=await lt(h);if(v){if(D.current){let M=v;v.startsWith("http://")&&(v.match(/http:\/\/m\d+\.music\.126\.net/)?M=v.replace(/^http:\/\/m(\d+)\.music\.126\.net/,"https://m$1.music.126.net"):v.match(/http:\/\/isure\.stream\.qqmusic\.qq\.com/)?M=v.replace(/^http:\/\//,"https://"):M="https://"+v.substring(7)),D.current.src=M,D.current.play(),n(!0),bt(h),h.sourceName&&O(`播放来源：${h.sourceName}`)}}else console.error("Failed to get song URL"),O("播放链接获取失败")}catch(v){console.error("Error playing song:",v),O("播放失败")}},_e=()=>{D.current&&(s?D.current.pause():D.current.play(),n(!s))},Ce=()=>{if(i.length===0)return;const h=(c+1)%i.length;Je(i[h],h)},xe=()=>{if(i.length===0)return;const h=(c-1+i.length)%i.length;Je(i[h],h)},be=h=>{const y=parseFloat(h.target.value);R(y),D.current&&(D.current.currentTime=y)},$e=()=>{D.current&&D.current.removeEventListener("timeupdate",()=>R(D.current.currentTime))},W=()=>{D.current&&D.current.addEventListener("timeupdate",()=>R(D.current.currentTime))},G=h=>{const y=parseFloat(h.target.value);L(y),K(!1)},ie=()=>{K(!H)},q=h=>{const y=Math.floor(h/60),v=Math.floor(h%60);return`${y}:${v.toString().padStart(2,"0")}`},te=()=>{P(!A)},pe=h=>{if(I(h),P(!1),h==="auto")O("多源智能搜索");else{const y=N.find(v=>v.type===h);O(`当前音源：${(y==null?void 0:y.name)||h}`)}},ve=(h,y)=>{const v=h.currentTarget,M=v.src;console.error("[Music Player] 图片加载失败:",{songName:y.name,originalUrl:y.album.picUrl,currentSrc:M,sourceName:y.sourceName}),v.onerror=null,ae(U=>new Set(U).add(M)),M.includes("default-album.jpg")?(console.error(`[Music Player] 连默认图片都加载失败了: ${y.name}`),v.style.backgroundColor="#4b5563",v.style.display="flex",v.style.alignItems="center",v.style.justifyContent="center"):(console.log(`[Music Player] 回退到默认图片: ${y.name}`),v.src="/default-album.jpg")};return r.jsxs(r.Fragment,{children:[r.jsx("audio",{ref:D}),r.jsx("div",{className:`fixed bottom-0 right-0 z-50 transition-transform duration-300 ${d?"translate-y-0":"translate-y-full"}`,children:r.jsxs("div",{className:"w-screen sm:w-[450px] sm:right-4 sm:bottom-4 sm:mb-0 bg-gray-800/95 backdrop-blur-md text-white sm:rounded-lg shadow-lg flex flex-col h-[60vh] sm:h-[500px] sm:max-h-[80vh]",children:[r.jsxs("div",{className:"p-3 sm:p-4 flex justify-between items-center border-b border-gray-700",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsxs("h2",{className:"text-base sm:text-lg font-bold flex items-center",children:[r.jsx(Nt,{className:"mr-2",size:20})," 音乐播放器"]}),B&&r.jsx("span",{className:"text-xs text-purple-400 bg-purple-500/20 px-2 py-1 rounded-full",children:B})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsxs("div",{className:"relative",children:[r.jsx("button",{onClick:te,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"音源设置",children:r.jsx(Ll,{size:18})}),A&&r.jsx("div",{className:"absolute top-full right-0 mt-1 bg-gray-700 rounded-lg shadow-lg border border-gray-600 z-10 min-w-48",children:r.jsxs("div",{className:"p-2",children:[r.jsx("div",{className:"text-xs text-gray-400 mb-2",children:"选择音源"}),r.jsx("button",{onClick:()=>pe("auto"),className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${E==="auto"?"bg-purple-600 text-white":"hover:bg-gray-600"}`,children:"🔄 智能多源 (推荐)"}),r.jsx("div",{className:"border-t border-gray-600 my-2"}),N.map(h=>r.jsxs("button",{onClick:()=>pe(h.type),disabled:!h.enabled,className:`w-full text-left px-3 py-2 rounded-md text-sm transition-colors ${E===h.type?"bg-purple-600 text-white":h.enabled?"hover:bg-gray-600":"text-gray-500 cursor-not-allowed"}`,children:[h.name,!h.enabled&&" (禁用)"]},h.type))]})})]}),r.jsx("button",{onClick:()=>{f(!1),x(!0)},className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"收起播放器",children:r.jsx(rr,{size:20})})]})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row flex-grow overflow-hidden",children:[r.jsxs("div",{className:"w-full sm:w-1/2 flex flex-col border-b sm:border-b-0 sm:border-r border-gray-700",children:[r.jsxs("div",{className:"p-3 flex-shrink-0",children:[r.jsxs("form",{onSubmit:h=>{h.preventDefault(),fe(z)},className:"flex",children:[r.jsx("input",{type:"text",value:z,onChange:h=>ee(h.target.value),placeholder:"搜索歌曲、歌手...",className:"flex-grow bg-gray-700/80 border border-gray-600 rounded-l-md px-3 py-2.5 sm:py-1.5 focus:outline-none focus:ring-1 focus:ring-purple-500 text-sm touch-target"}),r.jsx("button",{type:"submit",className:"bg-purple-600 hover:bg-purple-700 px-4 sm:px-3 py-2.5 sm:py-1.5 rounded-r-md touch-target",title:"搜索",children:r.jsx(_t,{size:18})})]}),r.jsx("div",{className:"mt-2 text-xs text-gray-400",children:E==="auto"?r.jsx("span",{children:"🔄 多源智能搜索"}):r.jsxs("span",{children:["🎵 ",((Ae=N.find(h=>h.type===E))==null?void 0:Ae.name)||E]})})]}),r.jsx("div",{className:"flex-grow overflow-y-auto px-1 mb-2",style:{maxHeight:"calc(60vh - 200px)"},children:S?r.jsx("p",{className:"p-4 text-center text-gray-400",children:"正在搜索..."}):re.length>0?re.map((h,y)=>r.jsxs("div",{className:"p-2 flex items-center gap-3 hover:bg-white/10 rounded-lg cursor-pointer transition-colors",onClick:()=>Je(h,y),children:[r.jsx("img",{src:kn(h.album.picUrl,h.name,Z),alt:h.album.name||"专辑封面",className:"w-12 h-12 rounded-md object-cover bg-gray-700",onError:v=>ve(v,h)}),r.jsxs("div",{className:"flex-grow overflow-hidden",children:[r.jsx("p",{className:"font-semibold text-sm truncate",children:h.name}),r.jsxs("p",{className:"text-xs text-gray-400 truncate",children:[Array.isArray(h.artists)?h.artists.map(v=>v.name).join(" / "):"未知艺术家",r.jsxs("span",{className:"text-purple-400 ml-2",children:["• ",h.sourceName]})]})]})]},`${h.id}-${h.sourceType}-${y}`)):z?r.jsx("p",{className:"p-4 text-center text-gray-400",children:"没有找到相关歌曲"}):r.jsx("div",{className:"p-4 text-center text-gray-500",children:r.jsx("p",{className:"text-sm",children:"开始搜索音乐"})})})]}),r.jsx("div",{className:"w-full sm:w-1/2 flex flex-col p-3 min-h-0",children:a?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"text-center pb-3 border-b border-gray-700 mb-3 flex-shrink-0",children:r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsx("img",{src:kn(a.album.picUrl,a.name,Z),alt:a.album.name,className:"w-16 h-16 rounded-lg shadow-lg",onError:h=>ve(h,a)}),r.jsxs("div",{className:"flex flex-col",children:[r.jsx("h3",{className:"text-lg font-bold",children:a.name||"未知歌曲"}),r.jsx("p",{className:"text-sm text-gray-400",children:Array.isArray(a.artists)&&a.artists.length>0?a.artists.map(h=>h.name).join(" / "):"未知艺术家"})]})]})}),r.jsx("div",{ref:J,className:"flex-grow overflow-y-auto text-center space-y-2 text-gray-300 px-2",style:{maxHeight:"calc(60vh - 240px)"},children:X.length>0?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"sm:hidden",children:r.jsx("p",{className:"text-sm text-purple-400 font-medium text-center py-2",children:se>=0&&X[se]?X[se].text:((T=X[0])==null?void 0:T.text)||"♪ ♪"})}),r.jsx("div",{className:"hidden sm:block",children:X.map((h,y)=>r.jsx("p",{className:`transition-all duration-300 text-sm leading-relaxed ${y===se?"text-purple-400 font-bold scale-105":"hover:text-gray-200"}`,children:h.text},y))})]}):r.jsx("p",{className:"text-gray-500 text-sm",children:a?"暂无歌词":"♪ ♪"})})]}):r.jsxs("div",{className:"flex-grow flex flex-col items-center justify-center text-gray-500 px-4",children:[r.jsx(Nt,{size:48,className:"mb-4 opacity-50 hidden sm:block"}),r.jsx(Nt,{size:32,className:"mb-2 opacity-50 sm:hidden"}),r.jsx("p",{className:"text-center text-sm sm:text-base",children:"选择音乐开始播放"}),r.jsx("p",{className:"text-xs mt-2 text-gray-600 text-center hidden sm:block",children:"歌词和音乐信息将在此显示"}),r.jsx("p",{className:"text-xs mt-1 text-gray-600 text-center sm:hidden",children:"歌词将在此显示"})]})})]}),r.jsxs("div",{className:"p-3 sm:p-3 border-t border-gray-700 bg-gray-800/95",children:[r.jsxs("div",{className:"mb-3",children:[r.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-400 mb-1",children:[r.jsx("span",{children:q(k)}),r.jsx("span",{children:q(m)})]}),r.jsx("input",{type:"range",value:k,max:m||0,onChange:be,onTouchStart:$e,onMouseDown:$e,onTouchEnd:W,onMouseUp:W,className:"w-full music-slider progress-slider",title:"播放进度",style:{"--progress":`${k/(m||1)*100}%`}})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsx("button",{onClick:ie,className:"p-2 hover:bg-white/10 rounded-full transition-colors",children:H||j===0?r.jsx(sc,{size:20}):r.jsx(rc,{size:20})}),r.jsx("input",{type:"range",min:"0",max:"1",step:"0.01",value:H?0:j,onChange:G,className:"volume-slider w-24","aria-label":"音量控制"})]}),r.jsxs("div",{className:"flex items-center space-x-3 sm:space-x-4",children:[r.jsx("button",{onClick:xe,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"上一首",children:r.jsx(Jl,{size:20})}),r.jsx("button",{onClick:_e,className:"bg-purple-600 hover:bg-purple-700 rounded-full p-3 text-white touch-target transition-colors",title:s?"暂停":"播放",children:s?r.jsx(Wl,{size:24}):r.jsx(Fl,{size:24})}),r.jsx("button",{onClick:Ce,className:"p-2 hover:bg-gray-700/50 rounded-full touch-target",title:"下一首",children:r.jsx(Ql,{size:20})})]}),r.jsx("div",{className:"w-24 sm:w-20"})]})]})]})}),!d&&r.jsx("div",{className:"fixed bottom-4 right-4 z-40",children:r.jsx("button",{onClick:()=>{f(!0),x(!0)},className:"bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 hover:scale-110 touch-target",title:"打开音乐播放器",children:r.jsx(Nt,{size:24})})})]})});function ps({slug:e,title:t,onDeleted:s,showEdit:n=!0,showDelete:a=!0}){const{isAdmin:o}=qt(),[i,l]=g.useState(!1),[c,u]=g.useState(!1),[d,f]=g.useState(!1),b=Ft(),x=()=>{b(`/write/${e}`),l(!1)},k=()=>{f(!0),l(!1)},R=async()=>{if(!c)try{u(!0),await ye.deletePost(e),s==null||s(),window.location.pathname.includes(`/post/${e}`)&&b("/")}catch(w){console.error("删除文章失败:",w),alert("删除文章失败，请稍后重试")}finally{u(!1),f(!1)}},m=()=>{f(!1)};return!o||!n&&!a?null:r.jsxs(r.Fragment,{children:[r.jsxs("div",{className:"relative",children:[r.jsx("button",{onClick:()=>l(!i),className:"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",title:"文章操作",children:r.jsx(Bl,{size:18})}),i&&r.jsxs("div",{className:"absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-50",children:[n&&r.jsxs("button",{onClick:x,className:"w-full px-4 py-2 text-left text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2 first:rounded-t-lg",children:[r.jsx(fs,{size:16}),r.jsx("span",{children:"编辑文章"})]}),a&&r.jsxs("button",{onClick:k,className:"w-full px-4 py-2 text-left text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2 last:rounded-b-lg",children:[r.jsx(xs,{size:16}),r.jsx("span",{children:"删除文章"})]})]}),i&&r.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>l(!1)})]}),d&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"确认删除文章"}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:["你确定要删除文章 ",r.jsxs("strong",{children:['"',t,'"']})," 吗？此操作无法撤销。"]}),r.jsxs("div",{className:"flex space-x-3 justify-end",children:[r.jsx("button",{onClick:m,disabled:c,className:"px-4 py-2 text-sm text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors disabled:opacity-50",children:"取消"}),r.jsx("button",{onClick:R,disabled:c,className:"px-4 py-2 text-sm text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors disabled:opacity-50 flex items-center space-x-2",children:c?r.jsxs(r.Fragment,{children:[r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),r.jsx("span",{children:"删除中..."})]}):r.jsxs(r.Fragment,{children:[r.jsx(xs,{size:16}),r.jsx("span",{children:"确认删除"})]})})]})]})})]})}function xc({onFilterChange:e,availableCategories:t,availableTags:s}){var m;const[n,a]=g.useState({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),[o,i]=g.useState(!1),[l,c]=g.useState(!1),[u,d]=g.useState(!1),f=[{value:"date",label:"发布时间",icon:Ge},{value:"readTime",label:"阅读时长",icon:st},{value:"featured",label:"推荐程度",icon:Il}];g.useEffect(()=>{e(n)},[n,e]);const b=(w,j)=>{a(L=>({...L,[w]:j}))},x=w=>{const j=w==="sortBy"?"date":w==="sortOrder"?"desc":"";a(L=>({...L,[w]:j}))},k=()=>{a({category:"",tag:"",sortBy:"date",sortOrder:"desc"})},R=n.category||n.tag||n.sortBy!=="date"||n.sortOrder!=="desc";return r.jsxs("div",{className:"bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 mb-6",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(Cl,{size:20,className:"text-gray-500 dark:text-gray-400"}),r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"筛选文章"})]}),R&&r.jsx("button",{onClick:k,className:"text-sm text-blue-600 dark:text-blue-400 hover:underline",children:"清除所有筛选"})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"分类"}),r.jsxs("button",{onClick:()=>i(!o),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[r.jsx("span",{className:n.category?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:n.category||"选择分类"}),r.jsx(rr,{size:16})]}),o&&r.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:r.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[r.jsx("button",{onClick:()=>{b("category",""),i(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"全部分类"}),t.map(w=>r.jsx("button",{onClick:()=>{b("category",w),i(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:w},w))]})}),n.category&&r.jsx("button",{onClick:()=>x("category"),"aria-label":"清除分类筛选",className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:r.jsx(cr,{size:14})})]}),r.jsxs("div",{className:"relative",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标签"}),r.jsxs("button",{onClick:()=>c(!l),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[r.jsx("span",{className:n.tag?"text-gray-900 dark:text-white":"text-gray-500 dark:text-gray-400",children:n.tag||"选择标签"}),r.jsx(rr,{size:16})]}),l&&r.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:r.jsxs("div",{className:"py-1 max-h-60 overflow-auto",children:[r.jsx("button",{onClick:()=>{b("tag",""),c(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"全部标签"}),s.map(w=>r.jsx("button",{onClick:()=>{b("tag",w),c(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:w},w))]})}),n.tag&&r.jsx("button",{onClick:()=>x("tag"),"aria-label":"清除标签筛选",className:"absolute right-8 top-9 text-gray-400 hover:text-gray-600",children:r.jsx(cr,{size:14})})]}),r.jsxs("div",{className:"relative",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"排序方式"}),r.jsxs("button",{onClick:()=>d(!u),className:"w-full flex items-center justify-between px-3 py-2 bg-gray-50 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md text-sm",children:[r.jsx("span",{className:"text-gray-900 dark:text-white",children:(m=f.find(w=>w.value===n.sortBy))==null?void 0:m.label}),r.jsx(rr,{size:16})]}),u&&r.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg",children:r.jsx("div",{className:"py-1",children:f.map(w=>{const j=w.icon;return r.jsxs("button",{onClick:()=>{b("sortBy",w.value),d(!1)},className:"w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center space-x-2",children:[r.jsx(j,{size:16}),r.jsx("span",{children:w.label})]},w.value)})})})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"排序顺序"}),r.jsxs("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>b("sortOrder","desc"),className:`flex-1 px-3 py-2 text-sm rounded-md border ${n.sortOrder==="desc"?"bg-blue-600 text-white border-blue-600":"bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"}`,children:"倒序"}),r.jsx("button",{onClick:()=>b("sortOrder","asc"),className:`flex-1 px-3 py-2 text-sm rounded-md border ${n.sortOrder==="asc"?"bg-blue-600 text-white border-blue-600":"bg-gray-50 dark:bg-gray-700 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600"}`,children:"正序"})]})]})]})]})}function pc({src:e,alt:t,className:s="",containerClassName:n="",maxHeight:a=400,minHeight:o=200,priority:i="auto",lazy:l=!0,preload:c=!1,quality:u=85,width:d,height:f,onError:b,onLoad:x}){const[k,R]=g.useState("cover"),[m,w]=g.useState(!1),[j,L]=g.useState(!l),[H,K]=g.useState(""),z=g.useRef(null),ee=g.useRef(null),re=g.useCallback(V=>{if(!V.includes("pub-a1a2.r2.dev")||V.includes("/api/image-proxy/"))return V;const X=V.split("pub-a1a2.r2.dev/");if(X.length<2)return V;const ce=X[1],se=new URLSearchParams;return d&&se.set("w",d.toString()),f&&se.set("h",f.toString()),u!==85&&se.set("q",u.toString()),se.set("f","auto"),`${window.location.origin.includes("localhost")?"http://localhost:8787":"https://blog.fddfffff.site"}/api/image-proxy/${ce}?${se.toString()}`},[d,f,u]);g.useEffect(()=>{if(!l||j)return;const V=new IntersectionObserver(X=>{X.forEach(ce=>{ce.isIntersecting&&(L(!0),V.disconnect())})},{rootMargin:"50px",threshold:.1});return ee.current&&V.observe(ee.current),()=>V.disconnect()},[l,j]),g.useEffect(()=>{if(j&&e){const V=re(e);if(K(V),c){const X=new Image;X.src=V}}},[j,e,re,c]);const $=V=>{const X=V.currentTarget;if(w(!0),i==="auto"){const ce=X.naturalWidth/X.naturalHeight;ce>2.5||ce<.4?R("contain"):R("cover")}else R(i==="contain"?"contain":"cover");x==null||x()},S=()=>{console.warn("图片加载失败:",H),H!==e?K(e):b==null||b()};return r.jsxs("div",{ref:ee,className:`relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 ${n}`,style:{minHeight:`${o}px`,maxHeight:`${a}px`,height:k==="contain"?"auto":`${a}px`},children:[(!m||!j)&&r.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}),j&&H&&r.jsx("img",{ref:z,src:H,alt:t,className:`w-full transition-all duration-500 ${k==="contain"?"h-auto object-contain":"h-full object-cover"} ${s} ${m?"opacity-100":"opacity-0"}`,loading:l?"lazy":"eager",onLoad:$,onError:S,style:{maxHeight:`${a}px`,minHeight:k==="contain"?`${o}px`:"auto"}}),k==="contain"&&m&&r.jsx("div",{className:"absolute inset-0 border border-gray-200/50 dark:border-gray-700/50 rounded-inherit pointer-events-none"})]})}const Ze=class Ze{constructor(){Vr(this,"serviceWorkerRegistration",null);this.initServiceWorker()}static getInstance(){return Ze.instance||(Ze.instance=new Ze),Ze.instance}async initServiceWorker(){if("serviceWorker"in navigator)try{this.serviceWorkerRegistration=await navigator.serviceWorker.register("/sw.js"),console.log("[缓存管理] Service Worker 注册成功"),this.serviceWorkerRegistration.addEventListener("updatefound",()=>{console.log("[缓存管理] Service Worker 更新可用")})}catch(t){console.error("[缓存管理] Service Worker 注册失败:",t)}}async preloadImages(t){if(!this.serviceWorkerRegistration||!this.serviceWorkerRegistration.active)return console.warn("[缓存管理] Service Worker 未激活，使用浏览器预加载"),this.browserPreloadImages(t);try{const s=new MessageChannel,n=new Promise(a=>{s.port1.onmessage=o=>{a(o.data.success)}});return this.serviceWorkerRegistration.active.postMessage({type:"PRELOAD_IMAGES",urls:t},[s.port2]),await n}catch(s){return console.error("[缓存管理] 预加载失败:",s),!1}}async browserPreloadImages(t){try{const s=t.map(n=>new Promise((a,o)=>{const i=new Image;i.onload=()=>a(),i.onerror=()=>o(new Error(`Failed to load ${n}`)),i.src=n}));return await Promise.allSettled(s),console.log("[缓存管理] 浏览器预加载完成"),!0}catch(s){return console.error("[缓存管理] 浏览器预加载失败:",s),!1}}async clearImageCache(){if(!this.serviceWorkerRegistration||!this.serviceWorkerRegistration.active)return console.warn("[缓存管理] Service Worker 未激活，无法清理缓存"),!1;try{const t=new MessageChannel,s=new Promise(n=>{t.port1.onmessage=a=>{n(a.data.success)}});return this.serviceWorkerRegistration.active.postMessage({type:"CLEAR_IMAGE_CACHE"},[t.port2]),await s}catch(t){return console.error("[缓存管理] 清理缓存失败:",t),!1}}async getCacheSize(){if("storage"in navigator&&"estimate"in navigator.storage)try{return(await navigator.storage.estimate()).usage||0}catch(t){return console.error("[缓存管理] 获取缓存大小失败:",t),0}return 0}async checkCacheQuota(){if("storage"in navigator&&"estimate"in navigator.storage)try{const t=await navigator.storage.estimate(),s=t.usage||0,n=t.quota||0,a=n-s,o=n>0?s/n*100:0;return{used:s,available:a,percentage:o}}catch(t){console.error("[缓存管理] 检查缓存配额失败:",t)}return{used:0,available:0,percentage:0}}async smartPreload(t){try{const s=[],n=this.getFeaturedImages();s.push(...n),s.length>0&&(console.log("[缓存管理] 开始智能预加载:",s.length,"张图片"),await this.preloadImages(s))}catch(s){console.error("[缓存管理] 智能预加载失败:",s)}}getFeaturedImages(){try{const t=localStorage.getItem("featured_images");return t?JSON.parse(t):[]}catch{return[]}}cacheFeaturedImages(t){try{localStorage.setItem("featured_images",JSON.stringify(t))}catch(s){console.error("[缓存管理] 缓存特色图片列表失败:",s)}}adaptToNetworkCondition(){if("connection"in navigator){const t=navigator.connection;if(t){const{effectiveType:s,downlink:n}=t;if(s==="slow-2g"||s==="2g"||n<.5)return console.log("[缓存管理] 检测到慢速网络，减少预加载"),"conservative";if(s==="4g"&&n>2)return console.log("[缓存管理] 检测到快速网络，增加预加载"),"aggressive"}}return"normal"}setupNetworkListener(){if("connection"in navigator){const t=navigator.connection;t&&t.addEventListener("change",()=>{const s=this.adaptToNetworkCondition();console.log("[缓存管理] 网络状态变化，调整策略为:",s)})}window.addEventListener("online",()=>{console.log("[缓存管理] 网络已连接")}),window.addEventListener("offline",()=>{console.log("[缓存管理] 网络已断开")})}};Vr(Ze,"instance");let ms=Ze;const ys=ms.getInstance();function mc(){const[e,t]=g.useState([]),[s,n]=g.useState(!0),[a,o]=g.useState(null),[i,l]=g.useState({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),c=async()=>{try{n(!0),o(null),console.log("开始加载文章列表...");const m=await ye.getPosts(1,10);if(console.log("获取文章响应:",m),t(m.posts||[]),!m.posts||m.posts.length===0)console.warn("API返回空文章列表");else{const w=m.posts.map(j=>j.imageUrl).filter(Boolean);w.length>0&&(ys.cacheFeaturedImages(w),ys.smartPreload().catch(j=>{console.warn("智能预加载失败:",j)}))}}catch(m){console.error("加载文章失败详情:",m),m instanceof dr?o(`API错误 (${m.status}): ${m.message}`):m instanceof Error?o(`网络错误: ${m.message}`):o("未知错误，请检查网络连接"),t([])}finally{n(!1)}};g.useEffect(()=>{c()},[]);const u=()=>{c()},d=g.useCallback(m=>{l(m)},[]),f=g.useMemo(()=>{const m=e.filter(w=>w.category).map(w=>w.category);return Array.from(new Set(m))},[e]),b=g.useMemo(()=>{const m=e.flatMap(w=>w.tags);return Array.from(new Set(m))},[e]),x=g.useMemo(()=>{let m=[...e];return i.category&&(m=m.filter(w=>w.category===i.category)),i.tag&&(m=m.filter(w=>w.tags.includes(i.tag))),m.sort((w,j)=>{let L=0;switch(i.sortBy){case"date":L=new Date(w.date).getTime()-new Date(j.date).getTime();break;case"readTime":L=w.readTime-j.readTime;break;case"featured":L=(w.featured?1:0)-(j.featured?1:0);break}return i.sortOrder==="desc"?-L:L}),m},[e,i]),k=x.find(m=>m.featured),R=x.filter(m=>!m.featured);return s?r.jsxs("div",{className:"space-y-8",children:[r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto mb-4 animate-pulse"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"})]}),r.jsxs("div",{className:"card p-8 animate-pulse",children:[r.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"})]})]}):r.jsxs("div",{className:"space-y-12",children:[r.jsxs("div",{className:"text-center py-12",children:[r.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4",children:"欢迎来到我的博客"}),r.jsx("p",{className:"text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto",children:"分享技术见解、开发经验和生活感悟的个人空间"})]}),a&&r.jsx("div",{className:"card p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800",children:r.jsxs("div",{className:"flex items-start justify-between",children:[r.jsxs("div",{className:"flex-1",children:[r.jsx("h3",{className:"text-red-800 dark:text-red-200 font-medium mb-2",children:"文章加载失败"}),r.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm mb-4",children:a}),r.jsx("div",{className:"text-xs text-red-600 dark:text-red-400 mb-4",children:"请打开浏览器开发者工具的控制台查看详细错误信息"})]}),r.jsx("button",{onClick:c,className:"ml-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm",disabled:s,children:s?"重试中...":"重试"})]})}),e.length>0&&r.jsx(xc,{onFilterChange:d,availableCategories:f,availableTags:b}),!s&&e.length===0&&r.jsx("div",{className:"text-center py-16",children:r.jsxs("div",{className:"card p-8 max-w-md mx-auto",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"还没有文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"开始写作，分享你的想法和经验吧！"}),r.jsx(Y,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})}),!s&&e.length>0&&x.length===0&&r.jsx("div",{className:"text-center py-16",children:r.jsxs("div",{className:"card p-8 max-w-md mx-auto",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-4",children:"没有找到匹配的文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"试试调整筛选条件，或者浏览全部文章"}),r.jsx("button",{onClick:()=>l({category:"",tag:"",sortBy:"date",sortOrder:"desc"}),className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"清除筛选条件"})]})}),k&&r.jsxs("div",{children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"✨ 特色文章"}),r.jsxs("div",{className:"card p-8 hover:shadow-lg transition-all duration-300 relative group",children:[r.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity",children:r.jsx(ps,{slug:k.slug,title:k.title,onDeleted:u})}),r.jsxs(Y,{to:`/post/${k.slug}`,className:"block",children:[k.imageUrl&&r.jsx("div",{className:"mb-6 rounded-xl overflow-hidden shadow-lg",children:r.jsx(pc,{src:k.imageUrl,alt:k.title,className:"group-hover:scale-105 transition-transform duration-500",maxHeight:400,minHeight:200,priority:"auto",lazy:!1,preload:!0,quality:90,width:800,height:400,onError:()=>{console.warn("特色文章封面加载失败:",k.imageUrl)}})}),r.jsx("h3",{className:"text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 pr-12 leading-tight",children:k.title}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 leading-relaxed mb-4",children:k.excerpt}),r.jsxs("div",{className:"flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(yt,{size:16}),r.jsx("span",{children:k.author})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:16}),r.jsx("span",{children:k.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(st,{size:16}),r.jsxs("span",{children:[k.readTime," 分钟阅读"]})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2 mt-4",children:k.tags.map(m=>r.jsx("span",{className:"px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full",children:m},m))})]})]})]}),R.length>0&&r.jsxs("div",{children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-6",children:"📚 最新文章"}),r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:R.map(m=>r.jsxs("div",{className:"card p-0 hover:shadow-lg transition-all duration-300 group h-full relative overflow-hidden rounded-lg",children:[r.jsx("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10",children:r.jsx(ps,{slug:m.slug,title:m.title,onDeleted:u})}),r.jsxs(Y,{to:`/post/${m.slug}`,className:"block h-full",children:[m.imageUrl&&r.jsx("div",{className:"w-full h-48 overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900",children:r.jsx("img",{src:m.imageUrl,alt:m.title,className:"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500",loading:"lazy",onError:w=>{console.warn("文章封面加载失败:",m.imageUrl);const L=w.target.parentElement;L&&(L.style.display="none")}})}),r.jsxs("div",{className:"p-6 flex flex-col h-full",children:[r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 line-clamp-2",children:m.title}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3 flex-grow",children:m.excerpt}),r.jsxs("div",{className:"mt-auto",children:[r.jsxs("div",{className:"flex flex-wrap gap-2 mb-3",children:[m.tags.slice(0,3).map(w=>r.jsx("span",{className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full",children:w},w)),m.tags.length>3&&r.jsxs("span",{className:"px-2 py-1 text-xs text-gray-400 dark:text-gray-500",children:["+",m.tags.length-3]})]}),r.jsxs("div",{className:"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:12}),r.jsx("span",{children:m.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(st,{size:12}),r.jsxs("span",{children:[m.readTime," 分钟"]})]})]})]})]})]})]},m.id))})]}),e.length>0&&!k&&R.length===0&&r.jsx("div",{className:"text-center py-8",children:r.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"文章加载中..."})})]})}class Jt{static async getComments(t){try{const s=await Ee(le.comments(t));return(await ge(s)).comments||[]}catch(s){return console.error("获取评论失败:",s),[]}}static async createComment(t,s){try{const n=await Ee(le.comments(t),{method:"POST",body:JSON.stringify(s)});return await ge(n)}catch(n){throw console.error("创建评论失败:",n),n}}static async deleteComment(t,s){try{const n=await sr(le.comment(t,s),{method:"DELETE"});await ge(n)}catch(n){throw console.error("删除评论失败:",n),n}}static validateComment(t){var s,n;return(s=t.content)!=null&&s.trim()?t.content.length>1e3?"评论内容过长，最多1000个字符":(n=t.author)!=null&&n.trim()?t.author.length>50?"姓名过长，最多50个字符":t.email&&!this.isValidEmail(t.email)?"请输入有效的邮箱地址":t.website&&!this.isValidUrl(t.website)?"请输入有效的网站地址":null:"请输入您的姓名":"评论内容不能为空"}static isValidEmail(t){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(t)}static isValidUrl(t){try{return new URL(t.startsWith("http")?t:`https://${t}`),!0}catch{return!1}}}function yc({slug:e}){const{isAdmin:t}=qt(),[s,n]=g.useState([]),[a,o]=g.useState(!0),[i,l]=g.useState(!1),[c,u]=g.useState(null),[d,f]=g.useState(!1),[b,x]=g.useState({content:"",author:"",email:"",website:""}),k=async()=>{try{o(!0),u(null);const j=await Jt.getComments(e);n(Array.isArray(j)?j:[])}catch(j){console.error("加载评论失败:",j),u("加载评论失败，请稍后重试"),n([])}finally{o(!1)}},R=async j=>{j.preventDefault();const L=Jt.validateComment(b);if(L){u(L);return}try{l(!0),u(null);const H=await Jt.createComment(e,b);n(K=>[H,...Array.isArray(K)?K:[]]),x({content:"",author:"",email:"",website:""}),f(!1)}catch(H){console.error("提交评论失败:",H),u(H.message||"提交评论失败，请稍后重试")}finally{l(!1)}},m=async j=>{if(confirm("确定要删除这条评论吗？"))try{await Jt.deleteComment(e,j),n(L=>Array.isArray(L)?L.filter(H=>H.id!==j):[])}catch(L){console.error("删除评论失败:",L),u(L.message||"删除评论失败，请稍后重试")}},w=j=>{const L=new Date(j),K=new Date().getTime()-L.getTime(),z=Math.floor(K/(1e3*60*60*24));return z===0?"今天":z===1?"昨天":z<7?`${z}天前`:L.toLocaleDateString("zh-CN")};return g.useEffect(()=>{k()},[e]),r.jsx("div",{className:"mt-12",children:r.jsxs("div",{className:"card p-8",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white flex items-center space-x-2",children:[r.jsx(Qr,{size:20}),r.jsxs("span",{children:["评论讨论 (",(s==null?void 0:s.length)||0,")"]})]}),!d&&r.jsxs("button",{onClick:()=>f(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2",children:[r.jsx(Qr,{size:16}),r.jsx("span",{children:"发表评论"})]})]}),c&&r.jsx("div",{className:"mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:r.jsx("p",{className:"text-red-700 dark:text-red-300 text-sm",children:c})}),d&&r.jsxs("form",{onSubmit:R,className:"mb-8 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("textarea",{value:b.content,onChange:j=>x(L=>({...L,content:j.target.value})),placeholder:"写下您的评论...",rows:4,className:"w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0}),r.jsxs("div",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:[b.content.length,"/1000 字符"]})]}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[r.jsxs("div",{className:"relative",children:[r.jsx(yt,{className:"absolute left-3 top-3 text-gray-400",size:16}),r.jsx("input",{type:"text",value:b.author,onChange:j=>x(L=>({...L,author:j.target.value})),placeholder:"您的姓名 *",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white",required:!0})]}),r.jsxs("div",{className:"relative",children:[r.jsx(fa,{className:"absolute left-3 top-3 text-gray-400",size:16}),r.jsx("input",{type:"email",value:b.email,onChange:j=>x(L=>({...L,email:j.target.value})),placeholder:"邮箱 (可选)",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]}),r.jsxs("div",{className:"relative",children:[r.jsx(Rl,{className:"absolute left-3 top-3 text-gray-400",size:16}),r.jsx("input",{type:"url",value:b.website,onChange:j=>x(L=>({...L,website:j.target.value})),placeholder:"网站 (可选)",className:"w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"})]})]})]}),r.jsxs("div",{className:"flex items-center justify-between mt-4",children:[r.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:"评论将会被公开显示，请文明发言"}),r.jsxs("div",{className:"flex space-x-3",children:[r.jsx("button",{type:"button",onClick:()=>f(!1),className:"px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors",children:"取消"}),r.jsxs("button",{type:"submit",disabled:i,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2",children:[r.jsx(Vl,{size:16}),r.jsx("span",{children:i?"发表中...":"发表评论"})]})]})]})]}),r.jsx("div",{className:"space-y-6",children:a?r.jsx("div",{className:"space-y-4",children:[...Array(3)].map((j,L)=>r.jsx("div",{className:"animate-pulse",children:r.jsxs("div",{className:"flex space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"}),r.jsxs("div",{className:"flex-1",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"})]})]})},L))}):((s==null?void 0:s.length)||0)===0?r.jsxs("div",{className:"text-center py-8",children:[r.jsx(Qr,{className:"mx-auto mb-4 text-gray-400",size:48}),r.jsx("p",{className:"text-gray-500 dark:text-gray-400",children:"还没有评论，来发表第一条吧！"})]}):(s||[]).map(j=>r.jsx("div",{className:"border-b border-gray-200 dark:border-gray-700 pb-6 last:border-b-0 last:pb-0",children:r.jsxs("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold",children:j.author.charAt(0).toUpperCase()}),r.jsxs("div",{className:"flex-1",children:[r.jsxs("div",{className:"flex items-center justify-between mb-2",children:[r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:j.website?r.jsx("a",{href:j.website.startsWith("http")?j.website:`https://${j.website}`,target:"_blank",rel:"noopener noreferrer",className:"hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:j.author}):j.author}),r.jsxs("div",{className:"flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400",children:[r.jsx(Ge,{size:14}),r.jsx("span",{children:w(j.createdAt)})]})]}),t&&r.jsx("button",{onClick:()=>m(j.id),className:"text-gray-400 hover:text-red-500 transition-colors p-1 rounded",title:"删除评论",children:r.jsx(xs,{size:14})})]}),r.jsx("div",{className:"prose prose-sm dark:prose-dark max-w-none",children:r.jsx("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:j.content})})]})]})},j.id))})]})})}function bc(){const{slug:e}=Kt(),[t,s]=g.useState(null),[n,a]=g.useState(!0),[o,i]=g.useState(null);if(g.useEffect(()=>{(async()=>{if(e)try{a(!0),i(null);const u=await ye.getPost(e);s(u)}catch(u){console.error("获取文章详情失败:",u),i("文章加载失败，请稍后重试")}finally{a(!1)}})()},[e]),n)return r.jsx("div",{className:"max-w-4xl mx-auto",children:r.jsxs("div",{className:"animate-pulse",children:[r.jsx("div",{className:"h-6 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-8"}),r.jsx("div",{className:"h-8 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-4"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-8"}),r.jsxs("div",{className:"space-y-4",children:[r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"}),r.jsx("div",{className:"h-4 bg-gray-200 dark:bg-gray-700 rounded w-4/6"})]})]})});if(o||!t)return r.jsx("div",{className:"max-w-4xl mx-auto text-center",children:r.jsxs("div",{className:"card p-8",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:o||"文章未找到"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:o||"请检查链接是否正确，或返回首页查看其他文章"}),r.jsxs(Y,{to:"/",className:"inline-flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:underline",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"返回首页"})]})]})});const l=()=>{};return r.jsxs("article",{className:"max-w-4xl mx-auto",children:[r.jsxs("div",{className:"mb-8 flex items-center justify-between",children:[r.jsxs(Y,{to:"/",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"返回首页"})]}),r.jsx(ps,{slug:t.slug,title:t.title,onDeleted:l})]}),r.jsxs("header",{className:"mb-8",children:[t.imageUrl&&r.jsx("div",{className:"mb-8 rounded-xl overflow-hidden shadow-lg bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900",children:r.jsx("img",{src:t.imageUrl,alt:t.title,className:"w-full h-auto min-h-[300px] max-h-[600px] object-cover hover:scale-105 transition-transform duration-500",style:{aspectRatio:"16/10"},loading:"lazy",onError:c=>{console.warn("封面图片加载失败:",t.imageUrl);const d=c.target.parentElement;d&&(d.style.display="none")},onLoad:()=>{console.log("封面图片加载成功:",t.imageUrl)}})}),r.jsx("div",{className:"mb-6",children:r.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 dark:text-white leading-tight",children:t.title})}),r.jsxs("div",{className:"flex flex-wrap items-center gap-6 text-sm text-gray-600 dark:text-gray-400 mb-6",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(yt,{size:16}),r.jsx("span",{children:t.author})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(Ge,{size:16}),r.jsx("span",{children:t.date})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(st,{size:16}),r.jsxs("span",{children:[t.readTime," 分钟阅读"]})]}),t.category&&r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx(Ct,{size:16}),r.jsx("span",{children:t.category})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:t.tags.map(c=>r.jsx(Y,{to:`/tag/${c}`,className:"px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full hover:bg-blue-200 dark:hover:bg-blue-800 transition-colors",children:c},c))})]}),r.jsx("div",{className:"card p-8 mb-8",children:t.content&&t.content.trim()?r.jsx("div",{className:"prose prose-gray dark:prose-dark max-w-none prose-img:rounded-lg prose-img:shadow-md",dangerouslySetInnerHTML:{__html:t.content}}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"text-gray-400 mb-4",children:r.jsx("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),r.jsx("h3",{className:"text-lg font-medium text-gray-500 dark:text-gray-400 mb-2",children:"文章内容为空"}),r.jsx("p",{className:"text-gray-400 dark:text-gray-500 text-sm",children:"该文章可能正在编辑中，或者内容尚未加载完成"})]})}),r.jsx("footer",{className:"mt-12 pt-8 border-t border-gray-200 dark:border-gray-700",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:r.jsx("p",{children:"感谢阅读！如果您觉得这篇文章有用，请分享给更多人。"})}),r.jsxs("div",{className:"flex space-x-4",children:[r.jsx(Y,{to:"/",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"更多文章"}),r.jsx(Y,{to:"/about",className:"text-blue-600 dark:text-blue-400 hover:underline",children:"关于作者"})]})]})}),r.jsx(yc,{slug:t.slug})]})}const vc=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,kc=ii.create({name:"image",addOptions(){return{inline:!1,allowBase64:!1,HTMLAttributes:{}}},inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes(){return{src:{default:null},alt:{default:null},title:{default:null}}},parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:e}){return["img",as(this.options.HTMLAttributes,e)]},addCommands(){return{setImage:e=>({commands:t})=>t.insertContent({type:this.name,attrs:e})}},addInputRules(){return[oi({find:vc,type:this.type,getAttributes:e=>{const[,,t,s,n]=e;return{src:s,alt:t,title:n}}})]}}),jc="aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2",wc="ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2",gt=(e,t)=>{for(const s in t)e[s]=t[s];return e},bs="numeric",vs="ascii",ks="alpha",At="asciinumeric",Et="alphanumeric",js="domain",ba="emoji",Nc="scheme",Ec="slashscheme",Xr="whitespace";function Sc(e,t){return e in t||(t[e]=[]),t[e]}function et(e,t,s){t[bs]&&(t[At]=!0,t[Et]=!0),t[vs]&&(t[At]=!0,t[ks]=!0),t[At]&&(t[Et]=!0),t[ks]&&(t[Et]=!0),t[Et]&&(t[js]=!0),t[ba]&&(t[js]=!0);for(const n in t){const a=Sc(n,s);a.indexOf(e)<0&&a.push(e)}}function Cc(e,t){const s={};for(const n in t)t[n].indexOf(e)>=0&&(s[n]=!0);return s}function je(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}je.groups={};je.prototype={accepts(){return!!this.t},go(e){const t=this,s=t.j[e];if(s)return s;for(let n=0;n<t.jr.length;n++){const a=t.jr[n][0],o=t.jr[n][1];if(o&&a.test(e))return o}return t.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,s,n){for(let a=0;a<e.length;a++)this.tt(e[a],t,s,n)},tr(e,t,s,n){n=n||je.groups;let a;return t&&t.j?a=t:(a=new je(t),s&&n&&et(t,s,n)),this.jr.push([e,a]),a},ts(e,t,s,n){let a=this;const o=e.length;if(!o)return a;for(let i=0;i<o-1;i++)a=a.tt(e[i]);return a.tt(e[o-1],t,s,n)},tt(e,t,s,n){n=n||je.groups;const a=this;if(t&&t.j)return a.j[e]=t,t;const o=t;let i,l=a.go(e);if(l?(i=new je,gt(i.j,l.j),i.jr.push.apply(i.jr,l.jr),i.jd=l.jd,i.t=l.t):i=new je,o){if(n)if(i.t&&typeof i.t=="string"){const c=gt(Cc(i.t,n),s);et(o,c,n)}else s&&et(o,s,n);i.t=o}return a.j[e]=i,i}};const Q=(e,t,s,n,a)=>e.ta(t,s,n,a),oe=(e,t,s,n,a)=>e.tr(t,s,n,a),jn=(e,t,s,n,a)=>e.ts(t,s,n,a),C=(e,t,s,n,a)=>e.tt(t,s,n,a),Oe="WORD",ws="UWORD",va="ASCIINUMERICAL",ka="ALPHANUMERICAL",$t="LOCALHOST",Ns="TLD",Es="UTLD",nr="SCHEME",ut="SLASH_SCHEME",Rs="NUM",Ss="WS",Ms="NL",Rt="OPENBRACE",Mt="CLOSEBRACE",ur="OPENBRACKET",hr="CLOSEBRACKET",gr="OPENPAREN",fr="CLOSEPAREN",xr="OPENANGLEBRACKET",pr="CLOSEANGLEBRACKET",mr="FULLWIDTHLEFTPAREN",yr="FULLWIDTHRIGHTPAREN",br="LEFTCORNERBRACKET",vr="RIGHTCORNERBRACKET",kr="LEFTWHITECORNERBRACKET",jr="RIGHTWHITECORNERBRACKET",wr="FULLWIDTHLESSTHAN",Nr="FULLWIDTHGREATERTHAN",Er="AMPERSAND",Sr="APOSTROPHE",Cr="ASTERISK",ze="AT",Ar="BACKSLASH",Rr="BACKTICK",Mr="CARET",He="COLON",Ts="COMMA",Tr="DOLLAR",Re="DOT",Lr="EQUALS",Ls="EXCLAMATION",Ne="HYPHEN",Tt="PERCENT",Ir="PIPE",Or="PLUS",Pr="POUND",Lt="QUERY",Is="QUOTE",ja="FULLWIDTHMIDDLEDOT",Os="SEMI",Me="SLASH",It="TILDE",_r="UNDERSCORE",wa="EMOJI",$r="SYM";var Na=Object.freeze({__proto__:null,ALPHANUMERICAL:ka,AMPERSAND:Er,APOSTROPHE:Sr,ASCIINUMERICAL:va,ASTERISK:Cr,AT:ze,BACKSLASH:Ar,BACKTICK:Rr,CARET:Mr,CLOSEANGLEBRACKET:pr,CLOSEBRACE:Mt,CLOSEBRACKET:hr,CLOSEPAREN:fr,COLON:He,COMMA:Ts,DOLLAR:Tr,DOT:Re,EMOJI:wa,EQUALS:Lr,EXCLAMATION:Ls,FULLWIDTHGREATERTHAN:Nr,FULLWIDTHLEFTPAREN:mr,FULLWIDTHLESSTHAN:wr,FULLWIDTHMIDDLEDOT:ja,FULLWIDTHRIGHTPAREN:yr,HYPHEN:Ne,LEFTCORNERBRACKET:br,LEFTWHITECORNERBRACKET:kr,LOCALHOST:$t,NL:Ms,NUM:Rs,OPENANGLEBRACKET:xr,OPENBRACE:Rt,OPENBRACKET:ur,OPENPAREN:gr,PERCENT:Tt,PIPE:Ir,PLUS:Or,POUND:Pr,QUERY:Lt,QUOTE:Is,RIGHTCORNERBRACKET:vr,RIGHTWHITECORNERBRACKET:jr,SCHEME:nr,SEMI:Os,SLASH:Me,SLASH_SCHEME:ut,SYM:$r,TILDE:It,TLD:Ns,UNDERSCORE:_r,UTLD:Es,UWORD:ws,WORD:Oe,WS:Ss});const Le=/[a-z]/,wt=/\p{L}/u,Zr=/\p{Emoji}/u,Ie=/\d/,Yr=/\s/,wn="\r",es=`
`,Ac="️",Rc="‍",ts="￼";let Qt=null,Xt=null;function Mc(e=[]){const t={};je.groups=t;const s=new je;Qt==null&&(Qt=Nn(jc)),Xt==null&&(Xt=Nn(wc)),C(s,"'",Sr),C(s,"{",Rt),C(s,"}",Mt),C(s,"[",ur),C(s,"]",hr),C(s,"(",gr),C(s,")",fr),C(s,"<",xr),C(s,">",pr),C(s,"（",mr),C(s,"）",yr),C(s,"「",br),C(s,"」",vr),C(s,"『",kr),C(s,"』",jr),C(s,"＜",wr),C(s,"＞",Nr),C(s,"&",Er),C(s,"*",Cr),C(s,"@",ze),C(s,"`",Rr),C(s,"^",Mr),C(s,":",He),C(s,",",Ts),C(s,"$",Tr),C(s,".",Re),C(s,"=",Lr),C(s,"!",Ls),C(s,"-",Ne),C(s,"%",Tt),C(s,"|",Ir),C(s,"+",Or),C(s,"#",Pr),C(s,"?",Lt),C(s,'"',Is),C(s,"/",Me),C(s,";",Os),C(s,"~",It),C(s,"_",_r),C(s,"\\",Ar),C(s,"・",ja);const n=oe(s,Ie,Rs,{[bs]:!0});oe(n,Ie,n);const a=oe(n,Le,va,{[At]:!0}),o=oe(n,wt,ka,{[Et]:!0}),i=oe(s,Le,Oe,{[vs]:!0});oe(i,Ie,a),oe(i,Le,i),oe(a,Ie,a),oe(a,Le,a);const l=oe(s,wt,ws,{[ks]:!0});oe(l,Le),oe(l,Ie,o),oe(l,wt,l),oe(o,Ie,o),oe(o,Le),oe(o,wt,o);const c=C(s,es,Ms,{[Xr]:!0}),u=C(s,wn,Ss,{[Xr]:!0}),d=oe(s,Yr,Ss,{[Xr]:!0});C(s,ts,d),C(u,es,c),C(u,ts,d),oe(u,Yr,d),C(d,wn),C(d,es),oe(d,Yr,d),C(d,ts,d);const f=oe(s,Zr,wa,{[ba]:!0});C(f,"#"),oe(f,Zr,f),C(f,Ac,f);const b=C(f,Rc);C(b,"#"),oe(b,Zr,f);const x=[[Le,i],[Ie,a]],k=[[Le,null],[wt,l],[Ie,o]];for(let R=0;R<Qt.length;R++)De(s,Qt[R],Ns,Oe,x);for(let R=0;R<Xt.length;R++)De(s,Xt[R],Es,ws,k);et(Ns,{tld:!0,ascii:!0},t),et(Es,{utld:!0,alpha:!0},t),De(s,"file",nr,Oe,x),De(s,"mailto",nr,Oe,x),De(s,"http",ut,Oe,x),De(s,"https",ut,Oe,x),De(s,"ftp",ut,Oe,x),De(s,"ftps",ut,Oe,x),et(nr,{scheme:!0,ascii:!0},t),et(ut,{slashscheme:!0,ascii:!0},t),e=e.sort((R,m)=>R[0]>m[0]?1:-1);for(let R=0;R<e.length;R++){const m=e[R][0],j=e[R][1]?{[Nc]:!0}:{[Ec]:!0};m.indexOf("-")>=0?j[js]=!0:Le.test(m)?Ie.test(m)?j[At]=!0:j[vs]=!0:j[bs]=!0,jn(s,m,m,j)}return jn(s,"localhost",$t,{ascii:!0}),s.jd=new je($r),{start:s,tokens:gt({groups:t},Na)}}function Ea(e,t){const s=Tc(t.replace(/[A-Z]/g,l=>l.toLowerCase())),n=s.length,a=[];let o=0,i=0;for(;i<n;){let l=e,c=null,u=0,d=null,f=-1,b=-1;for(;i<n&&(c=l.go(s[i]));)l=c,l.accepts()?(f=0,b=0,d=l):f>=0&&(f+=s[i].length,b++),u+=s[i].length,o+=s[i].length,i++;o-=f,i-=b,u-=f,a.push({t:d.t,v:t.slice(o-u,o),s:o-u,e:o})}return a}function Tc(e){const t=[],s=e.length;let n=0;for(;n<s;){let a=e.charCodeAt(n),o,i=a<55296||a>56319||n+1===s||(o=e.charCodeAt(n+1))<56320||o>57343?e[n]:e.slice(n,n+2);t.push(i),n+=i.length}return t}function De(e,t,s,n,a){let o;const i=t.length;for(let l=0;l<i-1;l++){const c=t[l];e.j[c]?o=e.j[c]:(o=new je(n),o.jr=a.slice(),e.j[c]=o),e=o}return o=new je(s),o.jr=a.slice(),e.j[t[i-1]]=o,o}function Nn(e){const t=[],s=[];let n=0,a="0123456789";for(;n<e.length;){let o=0;for(;a.indexOf(e[n+o])>=0;)o++;if(o>0){t.push(s.join(""));for(let i=parseInt(e.substring(n,n+o),10);i>0;i--)s.pop();n+=o}else s.push(e[n]),n++}return t}const Dt={defaultProtocol:"http",events:null,format:En,formatHref:En,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function Ps(e,t=null){let s=gt({},Dt);e&&(s=gt(s,e instanceof Ps?e.o:e));const n=s.ignoreTags,a=[];for(let o=0;o<n.length;o++)a.push(n[o].toUpperCase());this.o=s,t&&(this.defaultRender=t),this.ignoreTags=a}Ps.prototype={o:Dt,ignoreTags:[],defaultRender(e){return e},check(e){return this.get("validate",e.toString(),e)},get(e,t,s){const n=t!=null;let a=this.o[e];return a&&(typeof a=="object"?(a=s.t in a?a[s.t]:Dt[e],typeof a=="function"&&n&&(a=a(t,s))):typeof a=="function"&&n&&(a=a(t,s.t,s)),a)},getObj(e,t,s){let n=this.o[e];return typeof n=="function"&&t!=null&&(n=n(t,s.t,s)),n},render(e){const t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}};function En(e){return e}function Sa(e,t){this.t="token",this.v=e,this.tk=t}Sa.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){const t=this.toString(),s=e.get("truncate",t,this),n=e.get("format",t,this);return s&&n.length>s?n.substring(0,s)+"…":n},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=Dt.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){const t=this,s=this.toHref(e.get("defaultProtocol")),n=e.get("formatHref",s,this),a=e.get("tagName",s,t),o=this.toFormattedString(e),i={},l=e.get("className",s,t),c=e.get("target",s,t),u=e.get("rel",s,t),d=e.getObj("attributes",s,t),f=e.getObj("events",s,t);return i.href=n,l&&(i.class=l),c&&(i.target=c),u&&(i.rel=u),d&&gt(i,d),{tagName:a,attributes:i,content:o,eventListeners:f}}};function Kr(e,t){class s extends Sa{constructor(a,o){super(a,o),this.t=e}}for(const n in t)s.prototype[n]=t[n];return s.t=e,s}const Sn=Kr("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),Cn=Kr("text"),Lc=Kr("nl"),Zt=Kr("url",{isLink:!0,toHref(e=Dt.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){const e=this.tk;return e.length>=2&&e[0].t!==$t&&e[1].t===He}}),we=e=>new je(e);function Ic({groups:e}){const t=e.domain.concat([Er,Cr,ze,Ar,Rr,Mr,Tr,Lr,Ne,Rs,Tt,Ir,Or,Pr,Me,$r,It,_r]),s=[Sr,He,Ts,Re,Ls,Tt,Lt,Is,Os,xr,pr,Rt,Mt,hr,ur,gr,fr,mr,yr,br,vr,kr,jr,wr,Nr],n=[Er,Sr,Cr,Ar,Rr,Mr,Tr,Lr,Ne,Rt,Mt,Tt,Ir,Or,Pr,Lt,Me,$r,It,_r],a=we(),o=C(a,It);Q(o,n,o),Q(o,e.domain,o);const i=we(),l=we(),c=we();Q(a,e.domain,i),Q(a,e.scheme,l),Q(a,e.slashscheme,c),Q(i,n,o),Q(i,e.domain,i);const u=C(i,ze);C(o,ze,u),C(l,ze,u),C(c,ze,u);const d=C(o,Re);Q(d,n,o),Q(d,e.domain,o);const f=we();Q(u,e.domain,f),Q(f,e.domain,f);const b=C(f,Re);Q(b,e.domain,f);const x=we(Sn);Q(b,e.tld,x),Q(b,e.utld,x),C(u,$t,x);const k=C(f,Ne);C(k,Ne,k),Q(k,e.domain,f),Q(x,e.domain,f),C(x,Re,b),C(x,Ne,k);const R=C(x,He);Q(R,e.numeric,Sn);const m=C(i,Ne),w=C(i,Re);C(m,Ne,m),Q(m,e.domain,i),Q(w,n,o),Q(w,e.domain,i);const j=we(Zt);Q(w,e.tld,j),Q(w,e.utld,j),Q(j,e.domain,i),Q(j,n,o),C(j,Re,w),C(j,Ne,m),C(j,ze,u);const L=C(j,He),H=we(Zt);Q(L,e.numeric,H);const K=we(Zt),z=we();Q(K,t,K),Q(K,s,z),Q(z,t,K),Q(z,s,z),C(j,Me,K),C(H,Me,K);const ee=C(l,He),re=C(c,He),$=C(re,Me),S=C($,Me);Q(l,e.domain,i),C(l,Re,w),C(l,Ne,m),Q(c,e.domain,i),C(c,Re,w),C(c,Ne,m),Q(ee,e.domain,K),C(ee,Me,K),C(ee,Lt,K),Q(S,e.domain,K),Q(S,t,K),C(S,Me,K);const V=[[Rt,Mt],[ur,hr],[gr,fr],[xr,pr],[mr,yr],[br,vr],[kr,jr],[wr,Nr]];for(let X=0;X<V.length;X++){const[ce,se]=V[X],he=C(K,ce);C(z,ce,he),C(he,se,K);const N=we(Zt);Q(he,t,N);const p=we();Q(he,s),Q(N,t,N),Q(N,s,p),Q(p,t,N),Q(p,s,p),C(N,se,K),C(p,se,K)}return C(a,$t,j),C(a,Ms,Lc),{start:a,tokens:Na}}function Oc(e,t,s){let n=s.length,a=0,o=[],i=[];for(;a<n;){let l=e,c=null,u=null,d=0,f=null,b=-1;for(;a<n&&!(c=l.go(s[a].t));)i.push(s[a++]);for(;a<n&&(u=c||l.go(s[a].t));)c=null,l=u,l.accepts()?(b=0,f=l):b>=0&&b++,a++,d++;if(b<0)a-=d,a<n&&(i.push(s[a]),a++);else{i.length>0&&(o.push(rs(Cn,t,i)),i=[]),a-=b,d-=b;const x=f.t,k=s.slice(a-d,a);o.push(rs(x,t,k))}}return i.length>0&&o.push(rs(Cn,t,i)),o}function rs(e,t,s){const n=s[0].s,a=s[s.length-1].e,o=t.slice(n,a);return new e(o,s)}const Pc=typeof console<"u"&&console&&console.warn||(()=>{}),_c="until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.",ne={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function $c(){return je.groups={},ne.scanner=null,ne.parser=null,ne.tokenQueue=[],ne.pluginQueue=[],ne.customSchemes=[],ne.initialized=!1,ne}function An(e,t=!1){if(ne.initialized&&Pc(`linkifyjs: already initialized - will not register custom scheme "${e}" ${_c}`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw new Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);ne.customSchemes.push([e,t])}function Dc(){ne.scanner=Mc(ne.customSchemes);for(let e=0;e<ne.tokenQueue.length;e++)ne.tokenQueue[e][1]({scanner:ne.scanner});ne.parser=Ic(ne.scanner.tokens);for(let e=0;e<ne.pluginQueue.length;e++)ne.pluginQueue[e][1]({scanner:ne.scanner,parser:ne.parser});return ne.initialized=!0,ne}function _s(e){return ne.initialized||Dc(),Oc(ne.parser.start,e,Ea(ne.scanner.start,e))}_s.scan=Ea;function Ca(e,t=null,s=null){if(t&&typeof t=="object"){if(s)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);s=t,t=null}const n=new Ps(s),a=_s(e),o=[];for(let i=0;i<a.length;i++){const l=a[i];l.isLink&&(!t||l.t===t)&&n.check(l)&&o.push(l.toFormattedObject(n))}return o}function zc(e){return e.length===1?e[0].isLink:e.length===3&&e[1].isLink?["()","[]"].includes(e[0].value+e[2].value):!1}function Uc(e){return new Ur({key:new Hr("autolink"),appendTransaction:(t,s,n)=>{const a=t.some(u=>u.docChanged)&&!s.doc.eq(n.doc),o=t.some(u=>u.getMeta("preventAutolink"));if(!a||o)return;const{tr:i}=n,l=di(s.doc,[...t]);if(ui(l).forEach(({newRange:u})=>{const d=hi(n.doc,u,x=>x.isTextblock);let f,b;if(d.length>1?(f=d[0],b=n.doc.textBetween(f.pos,f.pos+f.node.nodeSize,void 0," ")):d.length&&n.doc.textBetween(u.from,u.to," "," ").endsWith(" ")&&(f=d[0],b=n.doc.textBetween(f.pos,u.to,void 0," ")),f&&b){const x=b.split(" ").filter(w=>w!=="");if(x.length<=0)return!1;const k=x[x.length-1],R=f.pos+b.lastIndexOf(k);if(!k)return!1;const m=_s(k).map(w=>w.toObject(e.defaultProtocol));if(!zc(m))return!1;m.filter(w=>w.isLink).map(w=>({...w,from:R+w.start+1,to:R+w.end+1})).filter(w=>n.schema.marks.code?!n.doc.rangeHasMark(w.from,w.to,n.schema.marks.code):!0).filter(w=>e.validate(w.value)).filter(w=>e.shouldAutoLink(w.value)).forEach(w=>{gi(w.from,w.to,n.doc).some(j=>j.mark.type===e.type)||i.addMark(w.from,w.to,e.type.create({href:w.href}))})}}),!!i.steps.length)return i}})}function Hc(e){return new Ur({key:new Hr("handleClickLink"),props:{handleClick:(t,s,n)=>{var a,o;if(n.button!==0||!t.editable)return!1;let i=n.target;const l=[];for(;i.nodeName!=="DIV";)l.push(i),i=i.parentNode;if(!l.find(b=>b.nodeName==="A"))return!1;const c=fi(t.state,e.type.name),u=n.target,d=(a=u==null?void 0:u.href)!==null&&a!==void 0?a:c.href,f=(o=u==null?void 0:u.target)!==null&&o!==void 0?o:c.target;return u&&d?(window.open(d,f),!0):!1}}})}function Bc(e){return new Ur({key:new Hr("handlePasteLink"),props:{handlePaste:(t,s,n)=>{const{state:a}=t,{selection:o}=a,{empty:i}=o;if(i)return!1;let l="";n.content.forEach(u=>{l+=u.textContent});const c=Ca(l,{defaultProtocol:e.defaultProtocol}).find(u=>u.isLink&&u.value===l);return!l||!c?!1:e.editor.commands.setMark(e.type,{href:c.href})}}})}const Wc=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function Xe(e,t){const s=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach(n=>{const a=typeof n=="string"?n:n.scheme;a&&s.push(a)}),!e||e.replace(Wc,"").match(new RegExp(`^(?:(?:${s.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}const Fc=li.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if(typeof e=="string"){An(e);return}An(e.scheme,e.optionalSlashes)})},onDestroy(){$c()},inclusive(){return this.options.autolink},addOptions(){return{openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!Xe(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}},addAttributes(){return{href:{default:null,parseHTML(e){return e.getAttribute("href")}},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{const t=e.getAttribute("href");return!t||!this.options.isAllowedUri(t,{defaultValidate:s=>!!Xe(s,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?!1:null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:t=>!!Xe(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",as(this.options.HTMLAttributes,e),0]:["a",as(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{const{href:s}=e;return this.options.isAllowedUri(s,{defaultValidate:n=>!!Xe(n,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?t().setMark(this.name,e).setMeta("preventAutolink",!0).run():!1},toggleLink:e=>({chain:t})=>{const{href:s}=e;return this.options.isAllowedUri(s,{defaultValidate:n=>!!Xe(n,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run():!1},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[ci({find:e=>{const t=[];if(e){const{protocols:s,defaultProtocol:n}=this.options,a=Ca(e).filter(o=>o.isLink&&this.options.isAllowedUri(o.value,{defaultValidate:i=>!!Xe(i,s),protocols:s,defaultProtocol:n}));a.length&&a.forEach(o=>t.push({text:o.value,data:{href:o.href},index:o.start}))}return t},type:this.type,getAttributes:e=>{var t;return{href:(t=e.data)===null||t===void 0?void 0:t.href}}})]},addProseMirrorPlugins(){const e=[],{protocols:t,defaultProtocol:s}=this.options;return this.options.autolink&&e.push(Uc({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:n=>this.options.isAllowedUri(n,{defaultValidate:a=>!!Xe(a,t),protocols:t,defaultProtocol:s}),shouldAutoLink:this.options.shouldAutoLink})),this.options.openOnClick===!0&&e.push(Hc({type:this.type})),this.options.linkOnPaste&&e.push(Bc({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),e}});function Kc(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Aa(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(t=>{const s=e[t],n=typeof s;(n==="object"||n==="function")&&!Object.isFrozen(s)&&Aa(s)}),e}let Rn=class{constructor(t){t.data===void 0&&(t.data={}),this.data=t.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}};function Ra(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function We(e,...t){const s=Object.create(null);for(const n in e)s[n]=e[n];return t.forEach(function(n){for(const a in n)s[a]=n[a]}),s}const Gc="</span>",Mn=e=>!!e.scope,qc=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const s=e.split(".");return[`${t}${s.shift()}`,...s.map((n,a)=>`${n}${"_".repeat(a+1)}`)].join(" ")}return`${t}${e}`};let Vc=class{constructor(t,s){this.buffer="",this.classPrefix=s.classPrefix,t.walk(this)}addText(t){this.buffer+=Ra(t)}openNode(t){if(!Mn(t))return;const s=qc(t.scope,{prefix:this.classPrefix});this.span(s)}closeNode(t){Mn(t)&&(this.buffer+=Gc)}value(){return this.buffer}span(t){this.buffer+=`<span class="${t}">`}};const Tn=(e={})=>{const t={children:[]};return Object.assign(t,e),t};let Jc=class Ma{constructor(){this.rootNode=Tn(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(t){this.top.children.push(t)}openNode(t){const s=Tn({scope:t});this.add(s),this.stack.push(s)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(t){return this.constructor._walk(t,this.rootNode)}static _walk(t,s){return typeof s=="string"?t.addText(s):s.children&&(t.openNode(s),s.children.forEach(n=>this._walk(t,n)),t.closeNode(s)),t}static _collapse(t){typeof t!="string"&&t.children&&(t.children.every(s=>typeof s=="string")?t.children=[t.children.join("")]:t.children.forEach(s=>{Ma._collapse(s)}))}},Qc=class extends Jc{constructor(t){super(),this.options=t}addText(t){t!==""&&this.add(t)}startScope(t){this.openNode(t)}endScope(){this.closeNode()}__addSublanguage(t,s){const n=t.root;s&&(n.scope=`language:${s}`),this.add(n)}toHTML(){return new Vc(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}};function zt(e){return e?typeof e=="string"?e:e.source:null}function Ta(e){return it("(?=",e,")")}function Xc(e){return it("(?:",e,")*")}function Zc(e){return it("(?:",e,")?")}function it(...e){return e.map(s=>zt(s)).join("")}function Yc(e){const t=e[e.length-1];return typeof t=="object"&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}function $s(...e){return"("+(Yc(e).capture?"":"?:")+e.map(n=>zt(n)).join("|")+")"}function La(e){return new RegExp(e.toString()+"|").exec("").length-1}function ed(e,t){const s=e&&e.exec(t);return s&&s.index===0}const td=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function Ds(e,{joinWith:t}){let s=0;return e.map(n=>{s+=1;const a=s;let o=zt(n),i="";for(;o.length>0;){const l=td.exec(o);if(!l){i+=o;break}i+=o.substring(0,l.index),o=o.substring(l.index+l[0].length),l[0][0]==="\\"&&l[1]?i+="\\"+String(Number(l[1])+a):(i+=l[0],l[0]==="("&&s++)}return i}).map(n=>`(${n})`).join(t)}const rd=/\b\B/,Ia="[a-zA-Z]\\w*",zs="[a-zA-Z_]\\w*",Oa="\\b\\d+(\\.\\d+)?",Pa="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",_a="\\b(0b[01]+)",sd="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",nd=(e={})=>{const t=/^#![ ]*\//;return e.binary&&(e.begin=it(t,/.*\b/,e.binary,/\b.*/)),We({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(s,n)=>{s.index!==0&&n.ignoreMatch()}},e)},Ut={begin:"\\\\[\\s\\S]",relevance:0},ad={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[Ut]},id={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[Ut]},od={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},Gr=function(e,t,s={}){const n=We({scope:"comment",begin:e,end:t,contains:[]},s);n.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const a=$s("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return n.contains.push({begin:it(/[ ]+/,"(",a,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),n},ld=Gr("//","$"),cd=Gr("/\\*","\\*/"),dd=Gr("#","$"),ud={scope:"number",begin:Oa,relevance:0},hd={scope:"number",begin:Pa,relevance:0},gd={scope:"number",begin:_a,relevance:0},fd={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[Ut,{begin:/\[/,end:/\]/,relevance:0,contains:[Ut]}]},xd={scope:"title",begin:Ia,relevance:0},pd={scope:"title",begin:zs,relevance:0},md={begin:"\\.\\s*"+zs,relevance:0},yd=function(e){return Object.assign(e,{"on:begin":(t,s)=>{s.data._beginMatch=t[1]},"on:end":(t,s)=>{s.data._beginMatch!==t[1]&&s.ignoreMatch()}})};var Yt=Object.freeze({__proto__:null,APOS_STRING_MODE:ad,BACKSLASH_ESCAPE:Ut,BINARY_NUMBER_MODE:gd,BINARY_NUMBER_RE:_a,COMMENT:Gr,C_BLOCK_COMMENT_MODE:cd,C_LINE_COMMENT_MODE:ld,C_NUMBER_MODE:hd,C_NUMBER_RE:Pa,END_SAME_AS_BEGIN:yd,HASH_COMMENT_MODE:dd,IDENT_RE:Ia,MATCH_NOTHING_RE:rd,METHOD_GUARD:md,NUMBER_MODE:ud,NUMBER_RE:Oa,PHRASAL_WORDS_MODE:od,QUOTE_STRING_MODE:id,REGEXP_MODE:fd,RE_STARTERS_RE:sd,SHEBANG:nd,TITLE_MODE:xd,UNDERSCORE_IDENT_RE:zs,UNDERSCORE_TITLE_MODE:pd});function bd(e,t){e.input[e.index-1]==="."&&t.ignoreMatch()}function vd(e,t){e.className!==void 0&&(e.scope=e.className,delete e.className)}function kd(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=bd,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function jd(e,t){Array.isArray(e.illegal)&&(e.illegal=$s(...e.illegal))}function wd(e,t){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function Nd(e,t){e.relevance===void 0&&(e.relevance=1)}const Ed=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const s=Object.assign({},e);Object.keys(e).forEach(n=>{delete e[n]}),e.keywords=s.keywords,e.begin=it(s.beforeMatch,Ta(s.begin)),e.starts={relevance:0,contains:[Object.assign(s,{endsParent:!0})]},e.relevance=0,delete s.beforeMatch},Sd=["of","and","for","in","not","or","if","then","parent","list","value"],Cd="keyword";function $a(e,t,s=Cd){const n=Object.create(null);return typeof e=="string"?a(s,e.split(" ")):Array.isArray(e)?a(s,e):Object.keys(e).forEach(function(o){Object.assign(n,$a(e[o],t,o))}),n;function a(o,i){t&&(i=i.map(l=>l.toLowerCase())),i.forEach(function(l){const c=l.split("|");n[c[0]]=[o,Ad(c[0],c[1])]})}}function Ad(e,t){return t?Number(t):Rd(e)?0:1}function Rd(e){return Sd.includes(e.toLowerCase())}const Ln={},tt=e=>{console.error(e)},In=(e,...t)=>{console.log(`WARN: ${e}`,...t)},ct=(e,t)=>{Ln[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),Ln[`${e}/${t}`]=!0)},Dr=new Error;function Da(e,t,{key:s}){let n=0;const a=e[s],o={},i={};for(let l=1;l<=t.length;l++)i[l+n]=a[l],o[l+n]=!0,n+=La(t[l-1]);e[s]=i,e[s]._emit=o,e[s]._multi=!0}function Md(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw tt("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),Dr;if(typeof e.beginScope!="object"||e.beginScope===null)throw tt("beginScope must be object"),Dr;Da(e,e.begin,{key:"beginScope"}),e.begin=Ds(e.begin,{joinWith:""})}}function Td(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw tt("skip, excludeEnd, returnEnd not compatible with endScope: {}"),Dr;if(typeof e.endScope!="object"||e.endScope===null)throw tt("endScope must be object"),Dr;Da(e,e.end,{key:"endScope"}),e.end=Ds(e.end,{joinWith:""})}}function Ld(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function Id(e){Ld(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),Md(e),Td(e)}function Od(e){function t(i,l){return new RegExp(zt(i),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(l?"g":""))}class s{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(l,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,l]),this.matchAt+=La(l)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const l=this.regexes.map(c=>c[1]);this.matcherRe=t(Ds(l,{joinWith:"|"}),!0),this.lastIndex=0}exec(l){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(l);if(!c)return null;const u=c.findIndex((f,b)=>b>0&&f!==void 0),d=this.matchIndexes[u];return c.splice(0,u),Object.assign(c,d)}}class n{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(l){if(this.multiRegexes[l])return this.multiRegexes[l];const c=new s;return this.rules.slice(l).forEach(([u,d])=>c.addRule(u,d)),c.compile(),this.multiRegexes[l]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(l,c){this.rules.push([l,c]),c.type==="begin"&&this.count++}exec(l){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let u=c.exec(l);if(this.resumingScanAtSamePosition()&&!(u&&u.index===this.lastIndex)){const d=this.getMatcher(0);d.lastIndex=this.lastIndex+1,u=d.exec(l)}return u&&(this.regexIndex+=u.position+1,this.regexIndex===this.count&&this.considerAll()),u}}function a(i){const l=new n;return i.contains.forEach(c=>l.addRule(c.begin,{rule:c,type:"begin"})),i.terminatorEnd&&l.addRule(i.terminatorEnd,{type:"end"}),i.illegal&&l.addRule(i.illegal,{type:"illegal"}),l}function o(i,l){const c=i;if(i.isCompiled)return c;[vd,wd,Id,Ed].forEach(d=>d(i,l)),e.compilerExtensions.forEach(d=>d(i,l)),i.__beforeBegin=null,[kd,jd,Nd].forEach(d=>d(i,l)),i.isCompiled=!0;let u=null;return typeof i.keywords=="object"&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),u=i.keywords.$pattern,delete i.keywords.$pattern),u=u||/\w+/,i.keywords&&(i.keywords=$a(i.keywords,e.case_insensitive)),c.keywordPatternRe=t(u,!0),l&&(i.begin||(i.begin=/\B|\b/),c.beginRe=t(c.begin),!i.end&&!i.endsWithParent&&(i.end=/\B|\b/),i.end&&(c.endRe=t(c.end)),c.terminatorEnd=zt(c.end)||"",i.endsWithParent&&l.terminatorEnd&&(c.terminatorEnd+=(i.end?"|":"")+l.terminatorEnd)),i.illegal&&(c.illegalRe=t(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(d){return Pd(d==="self"?i:d)})),i.contains.forEach(function(d){o(d,c)}),i.starts&&o(i.starts,l),c.matcher=a(c),c}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=We(e.classNameAliases||{}),o(e)}function za(e){return e?e.endsWithParent||za(e.starts):!1}function Pd(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(t){return We(e,{variants:null},t)})),e.cachedVariants?e.cachedVariants:za(e)?We(e,{starts:e.starts?We(e.starts):null}):Object.isFrozen(e)?We(e):e}var _d="11.10.0";let $d=class extends Error{constructor(t,s){super(t),this.name="HTMLInjectionError",this.html=s}};const ss=Ra,On=We,Pn=Symbol("nomatch"),Dd=7,Ua=function(e){const t=Object.create(null),s=Object.create(null),n=[];let a=!0;const o="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let l={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:Qc};function c(p){return l.noHighlightRe.test(p)}function u(p){let E=p.className+" ";E+=p.parentNode?p.parentNode.className:"";const I=l.languageDetectRe.exec(E);if(I){const A=$(I[1]);return A||(In(o.replace("{}",I[1])),In("Falling back to no-highlight mode for this block.",p)),A?I[1]:"no-highlight"}return E.split(/\s+/).find(A=>c(A)||$(A))}function d(p,E,I){let A="",P="";typeof E=="object"?(A=p,I=E.ignoreIllegals,P=E.language):(ct("10.7.0","highlight(lang, code, ...args) has been deprecated."),ct("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),P=p,A=E),I===void 0&&(I=!0);const B={code:A,language:P};he("before:highlight",B);const O=B.result?B.result:f(B.language,B.code,I);return O.code=B.code,he("after:highlight",O),O}function f(p,E,I,A){const P=Object.create(null);function B(h,y){return h.keywords[y]}function O(){if(!G.keywords){q.addText(te);return}let h=0;G.keywordPatternRe.lastIndex=0;let y=G.keywordPatternRe.exec(te),v="";for(;y;){v+=te.substring(h,y.index);const M=be.case_insensitive?y[0].toLowerCase():y[0],U=B(G,M);if(U){const[F,me]=U;if(q.addText(v),v="",P[M]=(P[M]||0)+1,P[M]<=Dd&&(pe+=me),F.startsWith("_"))v+=y[0];else{const Qe=be.classNameAliases[F]||F;Z(y[0],Qe)}}else v+=y[0];h=G.keywordPatternRe.lastIndex,y=G.keywordPatternRe.exec(te)}v+=te.substring(h),q.addText(v)}function D(){if(te==="")return;let h=null;if(typeof G.subLanguage=="string"){if(!t[G.subLanguage]){q.addText(te);return}h=f(G.subLanguage,te,!0,ie[G.subLanguage]),ie[G.subLanguage]=h._top}else h=x(te,G.subLanguage.length?G.subLanguage:null);G.relevance>0&&(pe+=h.relevance),q.__addSublanguage(h._emitter,h.language)}function J(){G.subLanguage!=null?D():O(),te=""}function Z(h,y){h!==""&&(q.startScope(y),q.addText(h),q.endScope())}function ae(h,y){let v=1;const M=y.length-1;for(;v<=M;){if(!h._emit[v]){v++;continue}const U=be.classNameAliases[h[v]]||h[v],F=y[v];U?Z(F,U):(te=F,O(),te=""),v++}}function fe(h,y){return h.scope&&typeof h.scope=="string"&&q.openNode(be.classNameAliases[h.scope]||h.scope),h.beginScope&&(h.beginScope._wrap?(Z(te,be.classNameAliases[h.beginScope._wrap]||h.beginScope._wrap),te=""):h.beginScope._multi&&(ae(h.beginScope,y),te="")),G=Object.create(h,{parent:{value:G}}),G}function lt(h,y,v){let M=ed(h.endRe,v);if(M){if(h["on:end"]){const U=new Rn(h);h["on:end"](y,U),U.isMatchIgnored&&(M=!1)}if(M){for(;h.endsParent&&h.parent;)h=h.parent;return h}}if(h.endsWithParent)return lt(h.parent,y,v)}function bt(h){return G.matcher.regexIndex===0?(te+=h[0],1):(T=!0,0)}function vt(h){const y=h[0],v=h.rule,M=new Rn(v),U=[v.__beforeBegin,v["on:begin"]];for(const F of U)if(F&&(F(h,M),M.isMatchIgnored))return bt(y);return v.skip?te+=y:(v.excludeBegin&&(te+=y),J(),!v.returnBegin&&!v.excludeBegin&&(te=y)),fe(v,h),v.returnBegin?0:y.length}function Je(h){const y=h[0],v=E.substring(h.index),M=lt(G,h,v);if(!M)return Pn;const U=G;G.endScope&&G.endScope._wrap?(J(),Z(y,G.endScope._wrap)):G.endScope&&G.endScope._multi?(J(),ae(G.endScope,h)):U.skip?te+=y:(U.returnEnd||U.excludeEnd||(te+=y),J(),U.excludeEnd&&(te=y));do G.scope&&q.closeNode(),!G.skip&&!G.subLanguage&&(pe+=G.relevance),G=G.parent;while(G!==M.parent);return M.starts&&fe(M.starts,h),U.returnEnd?0:y.length}function _e(){const h=[];for(let y=G;y!==be;y=y.parent)y.scope&&h.unshift(y.scope);h.forEach(y=>q.openNode(y))}let Ce={};function xe(h,y){const v=y&&y[0];if(te+=h,v==null)return J(),0;if(Ce.type==="begin"&&y.type==="end"&&Ce.index===y.index&&v===""){if(te+=E.slice(y.index,y.index+1),!a){const M=new Error(`0 width match regex (${p})`);throw M.languageName=p,M.badRule=Ce.rule,M}return 1}if(Ce=y,y.type==="begin")return vt(y);if(y.type==="illegal"&&!I){const M=new Error('Illegal lexeme "'+v+'" for mode "'+(G.scope||"<unnamed>")+'"');throw M.mode=G,M}else if(y.type==="end"){const M=Je(y);if(M!==Pn)return M}if(y.type==="illegal"&&v==="")return 1;if(Ae>1e5&&Ae>y.index*3)throw new Error("potential infinite loop, way more iterations than matches");return te+=v,v.length}const be=$(p);if(!be)throw tt(o.replace("{}",p)),new Error('Unknown language: "'+p+'"');const $e=Od(be);let W="",G=A||$e;const ie={},q=new l.__emitter(l);_e();let te="",pe=0,ve=0,Ae=0,T=!1;try{if(be.__emitTokens)be.__emitTokens(E,q);else{for(G.matcher.considerAll();;){Ae++,T?T=!1:G.matcher.considerAll(),G.matcher.lastIndex=ve;const h=G.matcher.exec(E);if(!h)break;const y=E.substring(ve,h.index),v=xe(y,h);ve=h.index+v}xe(E.substring(ve))}return q.finalize(),W=q.toHTML(),{language:p,value:W,relevance:pe,illegal:!1,_emitter:q,_top:G}}catch(h){if(h.message&&h.message.includes("Illegal"))return{language:p,value:ss(E),illegal:!0,relevance:0,_illegalBy:{message:h.message,index:ve,context:E.slice(ve-100,ve+100),mode:h.mode,resultSoFar:W},_emitter:q};if(a)return{language:p,value:ss(E),illegal:!1,relevance:0,errorRaised:h,_emitter:q,_top:G};throw h}}function b(p){const E={value:ss(p),illegal:!1,relevance:0,_top:i,_emitter:new l.__emitter(l)};return E._emitter.addText(p),E}function x(p,E){E=E||l.languages||Object.keys(t);const I=b(p),A=E.filter($).filter(V).map(J=>f(J,p,!1));A.unshift(I);const P=A.sort((J,Z)=>{if(J.relevance!==Z.relevance)return Z.relevance-J.relevance;if(J.language&&Z.language){if($(J.language).supersetOf===Z.language)return 1;if($(Z.language).supersetOf===J.language)return-1}return 0}),[B,O]=P,D=B;return D.secondBest=O,D}function k(p,E,I){const A=E&&s[E]||I;p.classList.add("hljs"),p.classList.add(`language-${A}`)}function R(p){let E=null;const I=u(p);if(c(I))return;if(he("before:highlightElement",{el:p,language:I}),p.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",p);return}if(p.children.length>0&&(l.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(p)),l.throwUnescapedHTML))throw new $d("One of your code blocks includes unescaped HTML.",p.innerHTML);E=p;const A=E.textContent,P=I?d(A,{language:I,ignoreIllegals:!0}):x(A);p.innerHTML=P.value,p.dataset.highlighted="yes",k(p,I,P.language),p.result={language:P.language,re:P.relevance,relevance:P.relevance},P.secondBest&&(p.secondBest={language:P.secondBest.language,relevance:P.secondBest.relevance}),he("after:highlightElement",{el:p,result:P,text:A})}function m(p){l=On(l,p)}const w=()=>{H(),ct("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function j(){H(),ct("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let L=!1;function H(){if(document.readyState==="loading"){L=!0;return}document.querySelectorAll(l.cssSelector).forEach(R)}function K(){L&&H()}typeof window<"u"&&window.addEventListener&&window.addEventListener("DOMContentLoaded",K,!1);function z(p,E){let I=null;try{I=E(e)}catch(A){if(tt("Language definition for '{}' could not be registered.".replace("{}",p)),a)tt(A);else throw A;I=i}I.name||(I.name=p),t[p]=I,I.rawDefinition=E.bind(null,e),I.aliases&&S(I.aliases,{languageName:p})}function ee(p){delete t[p];for(const E of Object.keys(s))s[E]===p&&delete s[E]}function re(){return Object.keys(t)}function $(p){return p=(p||"").toLowerCase(),t[p]||t[s[p]]}function S(p,{languageName:E}){typeof p=="string"&&(p=[p]),p.forEach(I=>{s[I.toLowerCase()]=E})}function V(p){const E=$(p);return E&&!E.disableAutodetect}function X(p){p["before:highlightBlock"]&&!p["before:highlightElement"]&&(p["before:highlightElement"]=E=>{p["before:highlightBlock"](Object.assign({block:E.el},E))}),p["after:highlightBlock"]&&!p["after:highlightElement"]&&(p["after:highlightElement"]=E=>{p["after:highlightBlock"](Object.assign({block:E.el},E))})}function ce(p){X(p),n.push(p)}function se(p){const E=n.indexOf(p);E!==-1&&n.splice(E,1)}function he(p,E){const I=p;n.forEach(function(A){A[I]&&A[I](E)})}function N(p){return ct("10.7.0","highlightBlock will be removed entirely in v12.0"),ct("10.7.0","Please use highlightElement now."),R(p)}Object.assign(e,{highlight:d,highlightAuto:x,highlightAll:H,highlightElement:R,highlightBlock:N,configure:m,initHighlighting:w,initHighlightingOnLoad:j,registerLanguage:z,unregisterLanguage:ee,listLanguages:re,getLanguage:$,registerAliases:S,autoDetection:V,inherit:On,addPlugin:ce,removePlugin:se}),e.debugMode=function(){a=!1},e.safeMode=function(){a=!0},e.versionString=_d,e.regex={concat:it,lookahead:Ta,either:$s,optional:Zc,anyNumberOfTimes:Xc};for(const p in Yt)typeof Yt[p]=="object"&&Aa(Yt[p]);return Object.assign(e,Yt),e},ft=Ua({});ft.newInstance=()=>Ua({});var zd=ft;ft.HighlightJS=ft;ft.default=ft;var Ud=Kc(zd);function Ha(e,t=[]){return e.map(s=>{const n=[...t,...s.properties?s.properties.className:[]];return s.children?Ha(s.children,n):{text:s.value,classes:n}}).flat()}function _n(e){return e.value||e.children||[]}function Hd(e){return!!Ud.getLanguage(e)}function $n({doc:e,name:t,lowlight:s,defaultLanguage:n}){const a=[];return is(e,o=>o.type.name===t).forEach(o=>{var i;let l=o.pos+1;const c=o.node.attrs.language||n,u=s.listLanguages(),d=c&&(u.includes(c)||Hd(c)||!((i=s.registered)===null||i===void 0)&&i.call(s,c))?_n(s.highlight(c,o.node.textContent)):_n(s.highlightAuto(o.node.textContent));Ha(d).forEach(f=>{const b=l+f.text.length;if(f.classes.length){const x=pi.inline(l,b,{class:f.classes.join(" ")});a.push(x)}l=b})}),mi.create(e,a)}function Bd(e){return typeof e=="function"}function Wd({name:e,lowlight:t,defaultLanguage:s}){if(!["highlight","highlightAuto","listLanguages"].every(a=>Bd(t[a])))throw Error("You should provide an instance of lowlight to use the code-block-lowlight extension");const n=new Ur({key:new Hr("lowlight"),state:{init:(a,{doc:o})=>$n({doc:o,name:e,lowlight:t,defaultLanguage:s}),apply:(a,o,i,l)=>{const c=i.selection.$head.parent.type.name,u=l.selection.$head.parent.type.name,d=is(i.doc,b=>b.type.name===e),f=is(l.doc,b=>b.type.name===e);return a.docChanged&&([c,u].includes(e)||f.length!==d.length||a.steps.some(b=>b.from!==void 0&&b.to!==void 0&&d.some(x=>x.pos>=b.from&&x.pos+x.node.nodeSize<=b.to)))?$n({doc:a.doc,name:e,lowlight:t,defaultLanguage:s}):o.map(a.mapping,a.doc)}},props:{decorations(a){return n.getState(a)}}});return n}const Fd=xi.extend({addOptions(){var e;return{...(e=this.parent)===null||e===void 0?void 0:e.call(this),lowlight:{},languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},addProseMirrorPlugins(){var e;return[...((e=this.parent)===null||e===void 0?void 0:e.call(this))||[],Wd({name:this.name,lowlight:this.options.lowlight,defaultLanguage:this.options.defaultLanguage})]}});function Ba(e){return e instanceof Map?e.clear=e.delete=e.set=function(){throw new Error("map is read-only")}:e instanceof Set&&(e.add=e.clear=e.delete=function(){throw new Error("set is read-only")}),Object.freeze(e),Object.getOwnPropertyNames(e).forEach(t=>{const s=e[t],n=typeof s;(n==="object"||n==="function")&&!Object.isFrozen(s)&&Ba(s)}),e}class Dn{constructor(t){t.data===void 0&&(t.data={}),this.data=t.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function Wa(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function Fe(e,...t){const s=Object.create(null);for(const n in e)s[n]=e[n];return t.forEach(function(n){for(const a in n)s[a]=n[a]}),s}const Kd="</span>",zn=e=>!!e.scope,Gd=(e,{prefix:t})=>{if(e.startsWith("language:"))return e.replace("language:","language-");if(e.includes(".")){const s=e.split(".");return[`${t}${s.shift()}`,...s.map((n,a)=>`${n}${"_".repeat(a+1)}`)].join(" ")}return`${t}${e}`};class qd{constructor(t,s){this.buffer="",this.classPrefix=s.classPrefix,t.walk(this)}addText(t){this.buffer+=Wa(t)}openNode(t){if(!zn(t))return;const s=Gd(t.scope,{prefix:this.classPrefix});this.span(s)}closeNode(t){zn(t)&&(this.buffer+=Kd)}value(){return this.buffer}span(t){this.buffer+=`<span class="${t}">`}}const Un=(e={})=>{const t={children:[]};return Object.assign(t,e),t};class Us{constructor(){this.rootNode=Un(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(t){this.top.children.push(t)}openNode(t){const s=Un({scope:t});this.add(s),this.stack.push(s)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(t){return this.constructor._walk(t,this.rootNode)}static _walk(t,s){return typeof s=="string"?t.addText(s):s.children&&(t.openNode(s),s.children.forEach(n=>this._walk(t,n)),t.closeNode(s)),t}static _collapse(t){typeof t!="string"&&t.children&&(t.children.every(s=>typeof s=="string")?t.children=[t.children.join("")]:t.children.forEach(s=>{Us._collapse(s)}))}}class Vd extends Us{constructor(t){super(),this.options=t}addText(t){t!==""&&this.add(t)}startScope(t){this.openNode(t)}endScope(){this.closeNode()}__addSublanguage(t,s){const n=t.root;s&&(n.scope=`language:${s}`),this.add(n)}toHTML(){return new qd(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function Ht(e){return e?typeof e=="string"?e:e.source:null}function Fa(e){return ot("(?=",e,")")}function Jd(e){return ot("(?:",e,")*")}function Qd(e){return ot("(?:",e,")?")}function ot(...e){return e.map(s=>Ht(s)).join("")}function Xd(e){const t=e[e.length-1];return typeof t=="object"&&t.constructor===Object?(e.splice(e.length-1,1),t):{}}function Hs(...e){return"("+(Xd(e).capture?"":"?:")+e.map(n=>Ht(n)).join("|")+")"}function Ka(e){return new RegExp(e.toString()+"|").exec("").length-1}function Zd(e,t){const s=e&&e.exec(t);return s&&s.index===0}const Yd=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function Bs(e,{joinWith:t}){let s=0;return e.map(n=>{s+=1;const a=s;let o=Ht(n),i="";for(;o.length>0;){const l=Yd.exec(o);if(!l){i+=o;break}i+=o.substring(0,l.index),o=o.substring(l.index+l[0].length),l[0][0]==="\\"&&l[1]?i+="\\"+String(Number(l[1])+a):(i+=l[0],l[0]==="("&&s++)}return i}).map(n=>`(${n})`).join(t)}const eu=/\b\B/,Ga="[a-zA-Z]\\w*",Ws="[a-zA-Z_]\\w*",qa="\\b\\d+(\\.\\d+)?",Va="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Ja="\\b(0b[01]+)",tu="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",ru=(e={})=>{const t=/^#![ ]*\//;return e.binary&&(e.begin=ot(t,/.*\b/,e.binary,/\b.*/)),Fe({scope:"meta",begin:t,end:/$/,relevance:0,"on:begin":(s,n)=>{s.index!==0&&n.ignoreMatch()}},e)},Bt={begin:"\\\\[\\s\\S]",relevance:0},su={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[Bt]},nu={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[Bt]},au={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},qr=function(e,t,s={}){const n=Fe({scope:"comment",begin:e,end:t,contains:[]},s);n.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const a=Hs("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return n.contains.push({begin:ot(/[ ]+/,"(",a,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),n},iu=qr("//","$"),ou=qr("/\\*","\\*/"),lu=qr("#","$"),cu={scope:"number",begin:qa,relevance:0},du={scope:"number",begin:Va,relevance:0},uu={scope:"number",begin:Ja,relevance:0},hu={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[Bt,{begin:/\[/,end:/\]/,relevance:0,contains:[Bt]}]},gu={scope:"title",begin:Ga,relevance:0},fu={scope:"title",begin:Ws,relevance:0},xu={begin:"\\.\\s*"+Ws,relevance:0},pu=function(e){return Object.assign(e,{"on:begin":(t,s)=>{s.data._beginMatch=t[1]},"on:end":(t,s)=>{s.data._beginMatch!==t[1]&&s.ignoreMatch()}})};var er=Object.freeze({__proto__:null,APOS_STRING_MODE:su,BACKSLASH_ESCAPE:Bt,BINARY_NUMBER_MODE:uu,BINARY_NUMBER_RE:Ja,COMMENT:qr,C_BLOCK_COMMENT_MODE:ou,C_LINE_COMMENT_MODE:iu,C_NUMBER_MODE:du,C_NUMBER_RE:Va,END_SAME_AS_BEGIN:pu,HASH_COMMENT_MODE:lu,IDENT_RE:Ga,MATCH_NOTHING_RE:eu,METHOD_GUARD:xu,NUMBER_MODE:cu,NUMBER_RE:qa,PHRASAL_WORDS_MODE:au,QUOTE_STRING_MODE:nu,REGEXP_MODE:hu,RE_STARTERS_RE:tu,SHEBANG:ru,TITLE_MODE:gu,UNDERSCORE_IDENT_RE:Ws,UNDERSCORE_TITLE_MODE:fu});function mu(e,t){e.input[e.index-1]==="."&&t.ignoreMatch()}function yu(e,t){e.className!==void 0&&(e.scope=e.className,delete e.className)}function bu(e,t){t&&e.beginKeywords&&(e.begin="\\b("+e.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",e.__beforeBegin=mu,e.keywords=e.keywords||e.beginKeywords,delete e.beginKeywords,e.relevance===void 0&&(e.relevance=0))}function vu(e,t){Array.isArray(e.illegal)&&(e.illegal=Hs(...e.illegal))}function ku(e,t){if(e.match){if(e.begin||e.end)throw new Error("begin & end are not supported with match");e.begin=e.match,delete e.match}}function ju(e,t){e.relevance===void 0&&(e.relevance=1)}const wu=(e,t)=>{if(!e.beforeMatch)return;if(e.starts)throw new Error("beforeMatch cannot be used with starts");const s=Object.assign({},e);Object.keys(e).forEach(n=>{delete e[n]}),e.keywords=s.keywords,e.begin=ot(s.beforeMatch,Fa(s.begin)),e.starts={relevance:0,contains:[Object.assign(s,{endsParent:!0})]},e.relevance=0,delete s.beforeMatch},Nu=["of","and","for","in","not","or","if","then","parent","list","value"],Eu="keyword";function Qa(e,t,s=Eu){const n=Object.create(null);return typeof e=="string"?a(s,e.split(" ")):Array.isArray(e)?a(s,e):Object.keys(e).forEach(function(o){Object.assign(n,Qa(e[o],t,o))}),n;function a(o,i){t&&(i=i.map(l=>l.toLowerCase())),i.forEach(function(l){const c=l.split("|");n[c[0]]=[o,Su(c[0],c[1])]})}}function Su(e,t){return t?Number(t):Cu(e)?0:1}function Cu(e){return Nu.includes(e.toLowerCase())}const Hn={},rt=e=>{console.error(e)},Bn=(e,...t)=>{console.log(`WARN: ${e}`,...t)},dt=(e,t)=>{Hn[`${e}/${t}`]||(console.log(`Deprecated as of ${e}. ${t}`),Hn[`${e}/${t}`]=!0)},zr=new Error;function Xa(e,t,{key:s}){let n=0;const a=e[s],o={},i={};for(let l=1;l<=t.length;l++)i[l+n]=a[l],o[l+n]=!0,n+=Ka(t[l-1]);e[s]=i,e[s]._emit=o,e[s]._multi=!0}function Au(e){if(Array.isArray(e.begin)){if(e.skip||e.excludeBegin||e.returnBegin)throw rt("skip, excludeBegin, returnBegin not compatible with beginScope: {}"),zr;if(typeof e.beginScope!="object"||e.beginScope===null)throw rt("beginScope must be object"),zr;Xa(e,e.begin,{key:"beginScope"}),e.begin=Bs(e.begin,{joinWith:""})}}function Ru(e){if(Array.isArray(e.end)){if(e.skip||e.excludeEnd||e.returnEnd)throw rt("skip, excludeEnd, returnEnd not compatible with endScope: {}"),zr;if(typeof e.endScope!="object"||e.endScope===null)throw rt("endScope must be object"),zr;Xa(e,e.end,{key:"endScope"}),e.end=Bs(e.end,{joinWith:""})}}function Mu(e){e.scope&&typeof e.scope=="object"&&e.scope!==null&&(e.beginScope=e.scope,delete e.scope)}function Tu(e){Mu(e),typeof e.beginScope=="string"&&(e.beginScope={_wrap:e.beginScope}),typeof e.endScope=="string"&&(e.endScope={_wrap:e.endScope}),Au(e),Ru(e)}function Lu(e){function t(i,l){return new RegExp(Ht(i),"m"+(e.case_insensitive?"i":"")+(e.unicodeRegex?"u":"")+(l?"g":""))}class s{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(l,c){c.position=this.position++,this.matchIndexes[this.matchAt]=c,this.regexes.push([c,l]),this.matchAt+=Ka(l)+1}compile(){this.regexes.length===0&&(this.exec=()=>null);const l=this.regexes.map(c=>c[1]);this.matcherRe=t(Bs(l,{joinWith:"|"}),!0),this.lastIndex=0}exec(l){this.matcherRe.lastIndex=this.lastIndex;const c=this.matcherRe.exec(l);if(!c)return null;const u=c.findIndex((f,b)=>b>0&&f!==void 0),d=this.matchIndexes[u];return c.splice(0,u),Object.assign(c,d)}}class n{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(l){if(this.multiRegexes[l])return this.multiRegexes[l];const c=new s;return this.rules.slice(l).forEach(([u,d])=>c.addRule(u,d)),c.compile(),this.multiRegexes[l]=c,c}resumingScanAtSamePosition(){return this.regexIndex!==0}considerAll(){this.regexIndex=0}addRule(l,c){this.rules.push([l,c]),c.type==="begin"&&this.count++}exec(l){const c=this.getMatcher(this.regexIndex);c.lastIndex=this.lastIndex;let u=c.exec(l);if(this.resumingScanAtSamePosition()&&!(u&&u.index===this.lastIndex)){const d=this.getMatcher(0);d.lastIndex=this.lastIndex+1,u=d.exec(l)}return u&&(this.regexIndex+=u.position+1,this.regexIndex===this.count&&this.considerAll()),u}}function a(i){const l=new n;return i.contains.forEach(c=>l.addRule(c.begin,{rule:c,type:"begin"})),i.terminatorEnd&&l.addRule(i.terminatorEnd,{type:"end"}),i.illegal&&l.addRule(i.illegal,{type:"illegal"}),l}function o(i,l){const c=i;if(i.isCompiled)return c;[yu,ku,Tu,wu].forEach(d=>d(i,l)),e.compilerExtensions.forEach(d=>d(i,l)),i.__beforeBegin=null,[bu,vu,ju].forEach(d=>d(i,l)),i.isCompiled=!0;let u=null;return typeof i.keywords=="object"&&i.keywords.$pattern&&(i.keywords=Object.assign({},i.keywords),u=i.keywords.$pattern,delete i.keywords.$pattern),u=u||/\w+/,i.keywords&&(i.keywords=Qa(i.keywords,e.case_insensitive)),c.keywordPatternRe=t(u,!0),l&&(i.begin||(i.begin=/\B|\b/),c.beginRe=t(c.begin),!i.end&&!i.endsWithParent&&(i.end=/\B|\b/),i.end&&(c.endRe=t(c.end)),c.terminatorEnd=Ht(c.end)||"",i.endsWithParent&&l.terminatorEnd&&(c.terminatorEnd+=(i.end?"|":"")+l.terminatorEnd)),i.illegal&&(c.illegalRe=t(i.illegal)),i.contains||(i.contains=[]),i.contains=[].concat(...i.contains.map(function(d){return Iu(d==="self"?i:d)})),i.contains.forEach(function(d){o(d,c)}),i.starts&&o(i.starts,l),c.matcher=a(c),c}if(e.compilerExtensions||(e.compilerExtensions=[]),e.contains&&e.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return e.classNameAliases=Fe(e.classNameAliases||{}),o(e)}function Za(e){return e?e.endsWithParent||Za(e.starts):!1}function Iu(e){return e.variants&&!e.cachedVariants&&(e.cachedVariants=e.variants.map(function(t){return Fe(e,{variants:null},t)})),e.cachedVariants?e.cachedVariants:Za(e)?Fe(e,{starts:e.starts?Fe(e.starts):null}):Object.isFrozen(e)?Fe(e):e}var Ou="11.11.1";class Pu extends Error{constructor(t,s){super(t),this.name="HTMLInjectionError",this.html=s}}const ns=Wa,Wn=Fe,Fn=Symbol("nomatch"),_u=7,Ya=function(e){const t=Object.create(null),s=Object.create(null),n=[];let a=!0;const o="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let l={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:Vd};function c(N){return l.noHighlightRe.test(N)}function u(N){let p=N.className+" ";p+=N.parentNode?N.parentNode.className:"";const E=l.languageDetectRe.exec(p);if(E){const I=re(E[1]);return I||(Bn(o.replace("{}",E[1])),Bn("Falling back to no-highlight mode for this block.",N)),I?E[1]:"no-highlight"}return p.split(/\s+/).find(I=>c(I)||re(I))}function d(N,p,E){let I="",A="";typeof p=="object"?(I=N,E=p.ignoreIllegals,A=p.language):(dt("10.7.0","highlight(lang, code, ...args) has been deprecated."),dt("10.7.0",`Please use highlight(code, options) instead.
https://github.com/highlightjs/highlight.js/issues/2277`),A=N,I=p),E===void 0&&(E=!0);const P={code:I,language:A};se("before:highlight",P);const B=P.result?P.result:f(P.language,P.code,E);return B.code=P.code,se("after:highlight",B),B}function f(N,p,E,I){const A=Object.create(null);function P(T,h){return T.keywords[h]}function B(){if(!W.keywords){ie.addText(q);return}let T=0;W.keywordPatternRe.lastIndex=0;let h=W.keywordPatternRe.exec(q),y="";for(;h;){y+=q.substring(T,h.index);const v=xe.case_insensitive?h[0].toLowerCase():h[0],M=P(W,v);if(M){const[U,F]=M;if(ie.addText(y),y="",A[v]=(A[v]||0)+1,A[v]<=_u&&(te+=F),U.startsWith("_"))y+=h[0];else{const me=xe.classNameAliases[U]||U;J(h[0],me)}}else y+=h[0];T=W.keywordPatternRe.lastIndex,h=W.keywordPatternRe.exec(q)}y+=q.substring(T),ie.addText(y)}function O(){if(q==="")return;let T=null;if(typeof W.subLanguage=="string"){if(!t[W.subLanguage]){ie.addText(q);return}T=f(W.subLanguage,q,!0,G[W.subLanguage]),G[W.subLanguage]=T._top}else T=x(q,W.subLanguage.length?W.subLanguage:null);W.relevance>0&&(te+=T.relevance),ie.__addSublanguage(T._emitter,T.language)}function D(){W.subLanguage!=null?O():B(),q=""}function J(T,h){T!==""&&(ie.startScope(h),ie.addText(T),ie.endScope())}function Z(T,h){let y=1;const v=h.length-1;for(;y<=v;){if(!T._emit[y]){y++;continue}const M=xe.classNameAliases[T[y]]||T[y],U=h[y];M?J(U,M):(q=U,B(),q=""),y++}}function ae(T,h){return T.scope&&typeof T.scope=="string"&&ie.openNode(xe.classNameAliases[T.scope]||T.scope),T.beginScope&&(T.beginScope._wrap?(J(q,xe.classNameAliases[T.beginScope._wrap]||T.beginScope._wrap),q=""):T.beginScope._multi&&(Z(T.beginScope,h),q="")),W=Object.create(T,{parent:{value:W}}),W}function fe(T,h,y){let v=Zd(T.endRe,y);if(v){if(T["on:end"]){const M=new Dn(T);T["on:end"](h,M),M.isMatchIgnored&&(v=!1)}if(v){for(;T.endsParent&&T.parent;)T=T.parent;return T}}if(T.endsWithParent)return fe(T.parent,h,y)}function lt(T){return W.matcher.regexIndex===0?(q+=T[0],1):(Ae=!0,0)}function bt(T){const h=T[0],y=T.rule,v=new Dn(y),M=[y.__beforeBegin,y["on:begin"]];for(const U of M)if(U&&(U(T,v),v.isMatchIgnored))return lt(h);return y.skip?q+=h:(y.excludeBegin&&(q+=h),D(),!y.returnBegin&&!y.excludeBegin&&(q=h)),ae(y,T),y.returnBegin?0:h.length}function vt(T){const h=T[0],y=p.substring(T.index),v=fe(W,T,y);if(!v)return Fn;const M=W;W.endScope&&W.endScope._wrap?(D(),J(h,W.endScope._wrap)):W.endScope&&W.endScope._multi?(D(),Z(W.endScope,T)):M.skip?q+=h:(M.returnEnd||M.excludeEnd||(q+=h),D(),M.excludeEnd&&(q=h));do W.scope&&ie.closeNode(),!W.skip&&!W.subLanguage&&(te+=W.relevance),W=W.parent;while(W!==v.parent);return v.starts&&ae(v.starts,T),M.returnEnd?0:h.length}function Je(){const T=[];for(let h=W;h!==xe;h=h.parent)h.scope&&T.unshift(h.scope);T.forEach(h=>ie.openNode(h))}let _e={};function Ce(T,h){const y=h&&h[0];if(q+=T,y==null)return D(),0;if(_e.type==="begin"&&h.type==="end"&&_e.index===h.index&&y===""){if(q+=p.slice(h.index,h.index+1),!a){const v=new Error(`0 width match regex (${N})`);throw v.languageName=N,v.badRule=_e.rule,v}return 1}if(_e=h,h.type==="begin")return bt(h);if(h.type==="illegal"&&!E){const v=new Error('Illegal lexeme "'+y+'" for mode "'+(W.scope||"<unnamed>")+'"');throw v.mode=W,v}else if(h.type==="end"){const v=vt(h);if(v!==Fn)return v}if(h.type==="illegal"&&y==="")return q+=`
`,1;if(ve>1e5&&ve>h.index*3)throw new Error("potential infinite loop, way more iterations than matches");return q+=y,y.length}const xe=re(N);if(!xe)throw rt(o.replace("{}",N)),new Error('Unknown language: "'+N+'"');const be=Lu(xe);let $e="",W=I||be;const G={},ie=new l.__emitter(l);Je();let q="",te=0,pe=0,ve=0,Ae=!1;try{if(xe.__emitTokens)xe.__emitTokens(p,ie);else{for(W.matcher.considerAll();;){ve++,Ae?Ae=!1:W.matcher.considerAll(),W.matcher.lastIndex=pe;const T=W.matcher.exec(p);if(!T)break;const h=p.substring(pe,T.index),y=Ce(h,T);pe=T.index+y}Ce(p.substring(pe))}return ie.finalize(),$e=ie.toHTML(),{language:N,value:$e,relevance:te,illegal:!1,_emitter:ie,_top:W}}catch(T){if(T.message&&T.message.includes("Illegal"))return{language:N,value:ns(p),illegal:!0,relevance:0,_illegalBy:{message:T.message,index:pe,context:p.slice(pe-100,pe+100),mode:T.mode,resultSoFar:$e},_emitter:ie};if(a)return{language:N,value:ns(p),illegal:!1,relevance:0,errorRaised:T,_emitter:ie,_top:W};throw T}}function b(N){const p={value:ns(N),illegal:!1,relevance:0,_top:i,_emitter:new l.__emitter(l)};return p._emitter.addText(N),p}function x(N,p){p=p||l.languages||Object.keys(t);const E=b(N),I=p.filter(re).filter(S).map(D=>f(D,N,!1));I.unshift(E);const A=I.sort((D,J)=>{if(D.relevance!==J.relevance)return J.relevance-D.relevance;if(D.language&&J.language){if(re(D.language).supersetOf===J.language)return 1;if(re(J.language).supersetOf===D.language)return-1}return 0}),[P,B]=A,O=P;return O.secondBest=B,O}function k(N,p,E){const I=p&&s[p]||E;N.classList.add("hljs"),N.classList.add(`language-${I}`)}function R(N){let p=null;const E=u(N);if(c(E))return;if(se("before:highlightElement",{el:N,language:E}),N.dataset.highlighted){console.log("Element previously highlighted. To highlight again, first unset `dataset.highlighted`.",N);return}if(N.children.length>0&&(l.ignoreUnescapedHTML||(console.warn("One of your code blocks includes unescaped HTML. This is a potentially serious security risk."),console.warn("https://github.com/highlightjs/highlight.js/wiki/security"),console.warn("The element with unescaped HTML:"),console.warn(N)),l.throwUnescapedHTML))throw new Pu("One of your code blocks includes unescaped HTML.",N.innerHTML);p=N;const I=p.textContent,A=E?d(I,{language:E,ignoreIllegals:!0}):x(I);N.innerHTML=A.value,N.dataset.highlighted="yes",k(N,E,A.language),N.result={language:A.language,re:A.relevance,relevance:A.relevance},A.secondBest&&(N.secondBest={language:A.secondBest.language,relevance:A.secondBest.relevance}),se("after:highlightElement",{el:N,result:A,text:I})}function m(N){l=Wn(l,N)}const w=()=>{H(),dt("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")};function j(){H(),dt("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")}let L=!1;function H(){function N(){H()}if(document.readyState==="loading"){L||window.addEventListener("DOMContentLoaded",N,!1),L=!0;return}document.querySelectorAll(l.cssSelector).forEach(R)}function K(N,p){let E=null;try{E=p(e)}catch(I){if(rt("Language definition for '{}' could not be registered.".replace("{}",N)),a)rt(I);else throw I;E=i}E.name||(E.name=N),t[N]=E,E.rawDefinition=p.bind(null,e),E.aliases&&$(E.aliases,{languageName:N})}function z(N){delete t[N];for(const p of Object.keys(s))s[p]===N&&delete s[p]}function ee(){return Object.keys(t)}function re(N){return N=(N||"").toLowerCase(),t[N]||t[s[N]]}function $(N,{languageName:p}){typeof N=="string"&&(N=[N]),N.forEach(E=>{s[E.toLowerCase()]=p})}function S(N){const p=re(N);return p&&!p.disableAutodetect}function V(N){N["before:highlightBlock"]&&!N["before:highlightElement"]&&(N["before:highlightElement"]=p=>{N["before:highlightBlock"](Object.assign({block:p.el},p))}),N["after:highlightBlock"]&&!N["after:highlightElement"]&&(N["after:highlightElement"]=p=>{N["after:highlightBlock"](Object.assign({block:p.el},p))})}function X(N){V(N),n.push(N)}function ce(N){const p=n.indexOf(N);p!==-1&&n.splice(p,1)}function se(N,p){const E=N;n.forEach(function(I){I[E]&&I[E](p)})}function he(N){return dt("10.7.0","highlightBlock will be removed entirely in v12.0"),dt("10.7.0","Please use highlightElement now."),R(N)}Object.assign(e,{highlight:d,highlightAuto:x,highlightAll:H,highlightElement:R,highlightBlock:he,configure:m,initHighlighting:w,initHighlightingOnLoad:j,registerLanguage:K,unregisterLanguage:z,listLanguages:ee,getLanguage:re,registerAliases:$,autoDetection:S,inherit:Wn,addPlugin:X,removePlugin:ce}),e.debugMode=function(){a=!1},e.safeMode=function(){a=!0},e.versionString=Ou,e.regex={concat:ot,lookahead:Fa,either:Hs,optional:Qd,anyNumberOfTimes:Jd};for(const N in er)typeof er[N]=="object"&&Ba(er[N]);return Object.assign(e,er),e},xt=Ya({});xt.newInstance=()=>Ya({});var $u=xt;xt.HighlightJS=xt;xt.default=xt;const Du=ni($u),Kn={},zu="hljs-";function Uu(e){const t=Du.newInstance();return e&&o(e),{highlight:s,highlightAuto:n,listLanguages:a,register:o,registerAlias:i,registered:l};function s(c,u,d){const f=d||Kn,b=typeof f.prefix=="string"?f.prefix:zu;if(!t.getLanguage(c))throw new Error("Unknown language: `"+c+"` is not registered");t.configure({__emitter:Hu,classPrefix:b});const x=t.highlight(u,{ignoreIllegals:!0,language:c});if(x.errorRaised)throw new Error("Could not highlight with `Highlight.js`",{cause:x.errorRaised});const k=x._emitter.root,R=k.data;return R.language=x.language,R.relevance=x.relevance,k}function n(c,u){const f=(u||Kn).subset||a();let b=-1,x=0,k;for(;++b<f.length;){const R=f[b];if(!t.getLanguage(R))continue;const m=s(R,c,u);m.data&&m.data.relevance!==void 0&&m.data.relevance>x&&(x=m.data.relevance,k=m)}return k||{type:"root",children:[],data:{language:void 0,relevance:x}}}function a(){return t.listLanguages()}function o(c,u){if(typeof c=="string")t.registerLanguage(c,u);else{let d;for(d in c)Object.hasOwn(c,d)&&t.registerLanguage(d,c[d])}}function i(c,u){if(typeof c=="string")t.registerAliases(typeof u=="string"?u:[...u],{languageName:c});else{let d;for(d in c)if(Object.hasOwn(c,d)){const f=c[d];t.registerAliases(typeof f=="string"?f:[...f],{languageName:d})}}}function l(c){return!!t.getLanguage(c)}}class Hu{constructor(t){this.options=t,this.root={type:"root",children:[],data:{language:void 0,relevance:0}},this.stack=[this.root]}addText(t){if(t==="")return;const s=this.stack[this.stack.length-1],n=s.children[s.children.length-1];n&&n.type==="text"?n.value+=t:s.children.push({type:"text",value:t})}startScope(t){this.openNode(String(t))}endScope(){this.closeNode()}__addSublanguage(t,s){const n=this.stack[this.stack.length-1],a=t.root.children;s?n.children.push({type:"element",tagName:"span",properties:{className:[s]},children:a}):n.children.push(...a)}openNode(t){const s=this,n=t.split(".").map(function(i,l){return l?i+"_".repeat(l):s.options.classPrefix+i}),a=this.stack[this.stack.length-1],o={type:"element",tagName:"span",properties:{className:n},children:[]};a.children.push(o),this.stack.push(o)}closeNode(){this.stack.pop()}finalize(){}toHTML(){return""}}const Bu=Uu();function Wu({content:e,onChange:t,placeholder:s="开始写作...",coverImage:n,onCoverImageChange:a}){const[o,i]=g.useState(!1),[l,c]=g.useState(!1),[u,d]=g.useState(!1),[f,b]=g.useState(!1),[x,k]=g.useState(!1),[R,m]=g.useState([]),[w,j]=g.useState(!1),[L,H]=g.useState(!1),[K,z]=g.useState(!1),[ee,re]=g.useState(!1),$=async A=>{const P=new FormData;P.append("file",A);try{const B=await Ee(le.uploadImage,{method:"POST",body:P,headers:{}});return(await ge(B)).url}catch(B){throw console.error("图片上传失败:",B),new Error("图片上传失败")}},S=yi({extensions:[bi,kc.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),Fc.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-600 dark:text-blue-400 underline"}}),Fd.configure({lowlight:Bu,HTMLAttributes:{class:"bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto"}})],content:e,onUpdate:({editor:A})=>{t(A.getHTML())},editorProps:{attributes:{class:"prose prose-gray dark:prose-dark max-w-none focus:outline-none min-h-[400px] p-4","data-placeholder":s},handlePaste:(A,P,B)=>{var J;const D=Array.from(((J=P.clipboardData)==null?void 0:J.items)||[]).filter(Z=>Z.type.indexOf("image")===0);return D.length>0?(P.preventDefault(),D.forEach(async Z=>{const ae=Z.getAsFile();if(ae)try{d(!0);const fe=await $(ae);S==null||S.chain().focus().setImage({src:fe}).run()}catch(fe){console.error("图片上传失败:",fe),alert("图片上传失败，请重试")}finally{d(!1)}}),!0):!1},handleDrop:(A,P,B,O)=>{var Z;const J=Array.from(((Z=P.dataTransfer)==null?void 0:Z.files)||[]).filter(ae=>ae.type.indexOf("image")===0);return J.length>0?(P.preventDefault(),J.forEach(async ae=>{try{d(!0);const fe=await $(ae);S==null||S.chain().focus().setImage({src:fe}).run()}catch(fe){console.error("图片上传失败:",fe),alert("图片上传失败，请重试")}finally{d(!1)}}),!0):!1}}}),V=async()=>{if(!S)return;const A=S.state.selection.empty?"现代科技风格的文章配图":S.state.doc.textBetween(S.state.selection.from,S.state.selection.to),P=window.prompt(`AI智能配图 - 请描述您想要的配图风格和内容：

提示：可以描述场景、颜色、风格等
例如：蓝色科技风格、自然风光、商务办公等`,A);if(P===null)return;const B=P.trim()||A;i(!0);try{console.log("开始生成AI配图，提示词:",B),console.log("API端点:",le.aiImage);const O=new AbortController,D=setTimeout(()=>O.abort(),6e4),J=await Ee(le.aiImage,{method:"POST",body:JSON.stringify({prompt:B,style:"tech"}),signal:O.signal});clearTimeout(D),console.log("API响应状态:",J.status),console.log("API响应头:",Object.fromEntries(J.headers.entries()));const Z=await ge(J);if(console.log("API响应数据:",Z),Z.success&&Z.images&&Z.images.length>0){const ae=Z.images[0].url;console.log("插入AI生成图片URL:",ae),S.chain().focus().setImage({src:ae}).run(),alert("AI配图生成成功！"),b(!1)}else throw console.error("API响应数据格式错误:",Z),new Error("AI配图生成失败：没有返回图片")}catch(O){console.error("AI配图生成错误:",O),console.error("错误详情:",{name:O==null?void 0:O.name,message:O==null?void 0:O.message,stack:O==null?void 0:O.stack}),(O==null?void 0:O.name)==="AbortError"?alert("生成超时（60秒），AI图片生成通常需要15-45秒，请稍后重试"):O instanceof dr?alert(`AI配图生成失败: ${O.message}`):alert(`AI配图生成失败: ${(O==null?void 0:O.message)||"未知错误"}`)}finally{i(!1)}},X=async()=>{if(!S)return;const A=window.confirm(`选择图片类型：

确定 = 一般图片（适合所有场景）
取消 = R18图片（仅限成人内容）

注意：所有图片均来自Pixiv艺术创作平台`);if(A===null)return;const P=A?"safe":"r18";c(!0);try{console.log("开始获取Mossia API图片，类型:",P);const B=await Ee(le.aiImage,{method:"POST",body:JSON.stringify({type:"mossia",source:P,num:9})}),O=await ge(B);if(console.log("Mossia API响应数据:",O),O.success&&O.images&&O.images.length>0){const D=O.images.filter(ae=>ae.url&&ae.url.trim()!=="");if(D.length===0)throw new Error("没有可用的图片");m(D),k(!0),b(!1);const J=O.cached?"（来自缓存，快速加载）":"（实时获取）",Z=P==="r18"?"R18":"一般";console.log(`获取到 ${D.length} 张${Z}图片${J}`)}else throw new Error("Mossia API没有返回图片")}catch(B){console.error("Mossia图片获取错误:",B),alert(`配图获取失败: ${(B==null?void 0:B.message)||"未知错误"}`)}finally{c(!1)}},ce=A=>{console.log("选择图片URL:",A),ee?(a==null||a(A),k(!1),m([]),j(!1),re(!1),alert("封面图片设置成功！")):S&&(S.chain().focus().setImage({src:A}).run(),k(!1),alert("配图添加成功！"))},se=()=>{k(!1),m([]),ee&&(re(!1),j(!1))},he=()=>{if(!S)return;const A=window.prompt("请输入链接地址:");A&&S.chain().focus().setLink({href:A}).run()},N=()=>{if(!S)return;const A=window.prompt("请输入图片地址:");A&&S.chain().focus().setImage({src:A}).run()},p=async()=>{const A=window.confirm(`选择封面图片类型：

确定 = 一般图片（适合所有场景）
取消 = R18图片（仅限成人内容）

注意：所有图片均来自Pixiv艺术创作平台`);if(A===null)return;const P=A?"safe":"r18";try{H(!0),console.log("开始获取封面图片，类型:",P);const B=await Ee(le.aiImage,{method:"POST",body:JSON.stringify({type:"mossia",source:P,num:9})}),O=await ge(B);if(console.log("封面图片API响应数据:",O),O.success&&O.images&&O.images.length>0){const D=O.images.filter(ae=>ae.url&&ae.url.trim()!=="");if(console.log("有效图片数量:",D.length),D.length===0)throw new Error("没有可用的图片");m(D),re(!0),k(!0);const J=O.cached?"（来自缓存）":"（实时获取）",Z=P==="r18"?"R18":"一般";console.log(`获取到${D.length}张${Z}封面图片${J}`)}else throw console.error("API响应格式错误:",O),new Error("API没有返回可用的图片")}catch(B){console.error("获取精美封面失败:",B),alert(`获取精美封面失败: ${(B==null?void 0:B.message)||"未知错误"}`)}finally{H(!1)}},E=async()=>{const A=window.prompt(`生成文章封面图 - 请描述封面图的风格和内容：

提示：可以描述主题、颜色、风格等
例如：现代科技、蓝色调、商务风格`,"现代简约风格的文章封面");if(A!==null)try{z(!0);const P=new AbortController,B=setTimeout(()=>P.abort(),6e4),O=await Ee(le.aiImage,{method:"POST",body:JSON.stringify({prompt:A.trim()||"现代简约风格的文章封面",style:"tech"}),signal:P.signal});clearTimeout(B);const D=await ge(O);D.url&&(a==null||a(D.url),j(!1),alert("AI封面生成成功！"))}catch(P){console.error("AI生成封面失败:",P),P instanceof dr?alert(`AI生成封面失败: ${P.message}`):alert("AI生成封面失败，请重试")}finally{z(!1)}},I=()=>{a==null||a("")};return S?r.jsxs("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[r.jsxs("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:[r.jsxs("div",{className:"flex items-center justify-between mb-3",children:[r.jsx("h3",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"文章封面图"}),n&&r.jsx("button",{onClick:I,className:"text-red-600 hover:text-red-700 text-sm",title:"删除封面图",children:"删除封面"})]}),n?r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:n,alt:"文章封面",className:"w-full h-32 object-cover rounded-lg",onError:A=>{const P=A.target;P.style.display="none"}}),r.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center",children:r.jsx("div",{className:"opacity-0 hover:opacity-100 flex space-x-2",children:r.jsx("button",{onClick:()=>j(!w),className:"px-3 py-1 bg-white dark:bg-gray-800 text-gray-900 dark:text-white rounded text-sm hover:bg-gray-100 dark:hover:bg-gray-700",children:"更换封面"})})})]}):r.jsxs("div",{className:"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center",children:[r.jsx(jt,{size:32,className:"mx-auto text-gray-400 mb-2"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 text-sm mb-4",children:"添加文章封面图，让您的文章更吸引人"}),r.jsx("button",{onClick:()=>j(!w),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"添加封面图"})]}),w&&r.jsx("div",{className:"mt-4 p-4 bg-white dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600",children:r.jsxs("div",{className:"flex flex-wrap gap-3 items-center",children:[r.jsxs("button",{onClick:p,disabled:L,className:"flex items-center space-x-2 px-3 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 disabled:opacity-50 transition-colors",children:[L?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(jt,{size:16}),r.jsx("span",{children:L?"获取中":"精美图片"})]}),r.jsxs("button",{onClick:E,disabled:K||L,className:"flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 transition-colors",children:[K?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(bn,{size:16}),r.jsx("span",{children:K?"AI生成中":"AI生成"})]}),r.jsx("button",{onClick:()=>j(!1),className:"px-3 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600 rounded transition-colors",children:"取消"})]})})]}),r.jsxs("div",{className:"flex flex-wrap items-center gap-1 p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700",children:[r.jsx("button",{onClick:()=>S.chain().focus().toggleBold().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("bold")?"bg-gray-200 dark:bg-gray-700":""}`,title:"粗体",children:r.jsx(jl,{size:16})}),r.jsx("button",{onClick:()=>S.chain().focus().toggleItalic().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("italic")?"bg-gray-200 dark:bg-gray-700":""}`,title:"斜体",children:r.jsx(Ol,{size:16})}),r.jsx("button",{onClick:()=>S.chain().focus().toggleStrike().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("strike")?"bg-gray-200 dark:bg-gray-700":""}`,title:"删除线",children:r.jsx(Xl,{size:16})}),r.jsx("button",{onClick:()=>S.chain().focus().toggleCode().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("code")?"bg-gray-200 dark:bg-gray-700":""}`,title:"行内代码",children:r.jsx(El,{size:16})}),r.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),r.jsx("button",{onClick:()=>S.chain().focus().toggleHeading({level:1}).run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("heading",{level:1})?"bg-gray-200 dark:bg-gray-700":""}`,title:"标题1",children:r.jsx(Ml,{size:16})}),r.jsx("button",{onClick:()=>S.chain().focus().toggleHeading({level:2}).run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("heading",{level:2})?"bg-gray-200 dark:bg-gray-700":""}`,title:"标题2",children:r.jsx(Tl,{size:16})}),r.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),r.jsx("button",{onClick:()=>S.chain().focus().toggleBulletList().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("bulletList")?"bg-gray-200 dark:bg-gray-700":""}`,title:"无序列表",children:r.jsx($l,{size:16})}),r.jsx("button",{onClick:()=>S.chain().focus().toggleOrderedList().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("orderedList")?"bg-gray-200 dark:bg-gray-700":""}`,title:"有序列表",children:r.jsx(_l,{size:16})}),r.jsx("button",{onClick:()=>S.chain().focus().toggleBlockquote().run(),className:`p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors ${S.isActive("blockquote")?"bg-gray-200 dark:bg-gray-700":""}`,title:"引用",children:r.jsx(Kl,{size:16})}),r.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),r.jsx("button",{onClick:he,className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors",title:"插入链接",children:r.jsx(Pl,{size:16})}),r.jsx("button",{onClick:N,className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors",title:"插入图片",children:r.jsx(jt,{size:16})}),f?r.jsxs("div",{className:"flex items-center space-x-2 p-2 bg-gray-100 dark:bg-gray-700 rounded-lg",children:[r.jsxs("button",{onClick:V,disabled:o||l,className:"flex items-center space-x-1 px-3 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 transition-colors",title:o?"AI生成中，通常需要15-45秒...":"AI智能生成配图",children:[o?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(bn,{size:16}),r.jsx("span",{children:o?"AI生成中":"AI生成"})]}),r.jsxs("button",{onClick:X,disabled:o||l,className:"flex items-center space-x-1 px-3 py-2 bg-pink-600 text-white rounded hover:bg-pink-700 disabled:opacity-50 transition-colors",title:l?"获取精美图片中...":"精美图片 - 来自Pixiv等优质图源",children:[l?r.jsx("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):r.jsx(jt,{size:16}),r.jsx("span",{children:l?"获取中":"精美图片"})]}),r.jsx("button",{onClick:()=>b(!1),className:"p-2 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600 rounded transition-colors",title:"关闭配图选项",children:"×"})]}):r.jsxs("button",{onClick:()=>b(!0),disabled:o||l,className:"flex items-center space-x-1 px-3 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded hover:from-purple-700 hover:to-pink-700 disabled:opacity-50 transition-all",title:"文章配图 - 选择AI生成或精美图片",children:[r.jsx(jt,{size:16}),r.jsx("span",{children:"文章配图"})]}),u&&r.jsxs("div",{className:"flex items-center space-x-2 px-3 py-2 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded",children:[r.jsx("div",{className:"w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"}),r.jsx("span",{className:"text-sm",children:"图片上传中..."})]}),r.jsx("div",{className:"w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1"}),r.jsx("button",{onClick:()=>S.chain().focus().undo().run(),disabled:!S.can().undo(),className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors",title:"撤销",children:r.jsx(tc,{size:16})}),r.jsx("button",{onClick:()=>S.chain().focus().redo().run(),disabled:!S.can().redo(),className:"p-2 rounded hover:bg-gray-200 dark:hover:bg-gray-700 disabled:opacity-50 transition-colors",title:"重做",children:r.jsx(Gl,{size:16})})]}),r.jsx(vi,{editor:S}),x&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-white dark:bg-gray-800 rounded-lg p-6 max-w-4xl max-h-[80vh] overflow-y-auto",children:[r.jsxs("div",{className:"flex justify-between items-center mb-4",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:ee?`选择封面图片 (${R.length} 张)`:`选择文章配图 (${R.length} 张)`}),r.jsx("button",{onClick:se,className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",title:"关闭画廊",children:"✕"})]}),r.jsx("div",{className:"grid grid-cols-3 gap-4",children:R.map((A,P)=>r.jsxs("div",{className:"relative group cursor-pointer border-2 border-transparent hover:border-blue-500 rounded-lg overflow-hidden",onClick:()=>ce(A.url),children:[r.jsx("img",{src:A.url,alt:A.title||"配图选项",className:"w-full h-32 object-cover",loading:"lazy",onError:B=>{console.error("图片加载失败:",A.url),B.currentTarget.style.display="none"},onLoad:()=>{console.log("图片加载成功:",A.url)}}),r.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center",children:r.jsx("span",{className:"text-white opacity-0 group-hover:opacity-100 font-medium",children:"选择"})}),A.title&&r.jsxs("div",{className:"absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-2",children:[r.jsx("p",{className:"truncate",title:A.title,children:A.title}),A.author&&r.jsxs("p",{className:"truncate text-gray-300",title:`作者: ${A.author}`,children:["作者: ",A.author]})]})]},`${A.url}-${P}`))}),r.jsxs("div",{className:"mt-4 flex justify-between",children:[r.jsx("button",{onClick:()=>{k(!1),ee?p():X()},className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors",children:"换一批图片"}),r.jsx("button",{onClick:se,className:"px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors",children:"取消选择"})]})]})})]}):r.jsx("div",{children:"编辑器加载中..."})}function Gn(){const e=Ft(),{slug:t}=Kt(),{isAdmin:s,isLoading:n}=qt(),a=!!t,[o,i]=g.useState(""),[l,c]=g.useState({title:"",excerpt:"",tags:[],category:"",featured:!1,imageUrl:""}),[u,d]=g.useState(!1),[f,b]=g.useState(!1),[x,k]=g.useState("idle"),[R,m]=g.useState(""),[w,j]=g.useState(!1),[L,H]=g.useState(!1);g.useEffect(()=>{a&&t&&(async()=>{j(!0);try{const S=await ye.getPost(t);i(S.content||""),c({title:S.title,excerpt:S.excerpt,tags:S.tags||[],category:S.category||"",featured:S.featured||!1,imageUrl:S.imageUrl||""})}catch(S){console.error("加载文章失败:",S),alert("加载文章失败，请检查文章是否存在"),e("/")}finally{j(!1)}})()},[a,t,e]);const K=async($=0)=>{if(!l.title.trim()){alert("请输入文章标题");return}if(!o.trim()){alert("请输入文章内容");return}b(!0),k("saving"),m($>0?`正在重试保存... (${$+1}/3)`:"正在保存文章...");try{const S={...l,content:o};if(console.log("开始保存文章:",{isEditing:a,slug:t,postData:{...S,content:o.substring(0,100)+"..."}}),a)await ye.updatePost(t,S),console.log("文章更新成功"),k("success"),m("文章更新成功！"),setTimeout(()=>e(`/post/${t}`),1e3);else{const V=await ye.createPost(S);console.log("文章创建成功:",V),k("success"),m("文章创建成功！"),setTimeout(()=>e(`/post/${V.slug}`),1e3)}}catch(S){console.error("保存失败:",S);let V="保存失败，请重试",X=!0;if(S.status===400?(V="请检查文章内容格式是否正确",X=!1):S.status===401?(V="登录已过期，请重新登录",X=!1):S.status===409?(V="文章标题已存在，请修改标题",X=!1):S.status===413?(V="文章内容过大，请减少内容长度",X=!1):S.status>=500?V="服务器错误，正在重试...":S.name==="AbortError"?V="请求超时，正在重试...":S.message&&(V=`保存失败: ${S.message}`),k("error"),m(V),X&&$<2){console.log(`第 ${$+1} 次重试保存...`),setTimeout(()=>{K($+1)},1e3*($+1));return}X?setTimeout(()=>{confirm(`${V}

是否手动重试？`)?K(0):(k("idle"),m(""))},2e3):setTimeout(()=>{k("idle"),m("")},3e3)}finally{b(!1)}},z=$=>{$&&!l.tags.includes($)&&c(S=>({...S,tags:[...S.tags,$]}))},ee=$=>{c(S=>({...S,tags:S.tags.filter(V=>V!==$)}))},re=$=>{c(S=>({...S,imageUrl:$}))};return n?r.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"检查权限中..."})]}):s?w?r.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载文章中..."})]}):r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsxs("div",{className:"flex items-center justify-between mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:a?"编辑文章":"写新文章"}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("button",{onClick:()=>d(!u),title:u?"切换到编辑模式":"切换到预览模式","aria-label":u?"切换到编辑模式":"切换到预览模式",className:"flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[u?r.jsx(ha,{size:16}):r.jsx(ga,{size:16}),r.jsx("span",{children:u?"编辑":"预览"})]}),r.jsxs("button",{onClick:()=>K(0),disabled:f,title:f?"正在保存文章":"保存文章","aria-label":f?"正在保存文章":"保存文章",className:`flex items-center space-x-2 px-6 py-2 rounded-lg transition-all duration-300 ${x==="success"?"bg-green-600 hover:bg-green-700 text-white":x==="error"?"bg-red-600 hover:bg-red-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"}`,children:[x==="saving"&&r.jsx(mn,{size:16,className:"animate-spin"}),x==="success"&&r.jsx(fn,{size:16}),x==="error"&&r.jsx(gn,{size:16}),x==="idle"&&r.jsx(ql,{size:16}),r.jsx("span",{children:x==="saving"?"保存中...":x==="success"?"已保存":x==="error"?"保存失败":"保存"})]})]})]}),R&&r.jsx("div",{className:`mb-6 p-4 rounded-lg border transition-all duration-300 ${x==="success"?"bg-green-50 border-green-200 text-green-800 dark:bg-green-900/20 dark:border-green-800 dark:text-green-200":x==="error"?"bg-red-50 border-red-200 text-red-800 dark:bg-red-900/20 dark:border-red-800 dark:text-red-200":"bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-900/20 dark:border-blue-800 dark:text-blue-200"}`,children:r.jsxs("div",{className:"flex items-center space-x-2",children:[x==="saving"&&r.jsx(mn,{size:16,className:"animate-spin"}),x==="success"&&r.jsx(fn,{size:16}),x==="error"&&r.jsx(gn,{size:16}),r.jsx("span",{children:R})]})}),r.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[r.jsx("div",{className:"lg:col-span-2",children:r.jsx("div",{className:"card p-6",children:u?r.jsxs("div",{className:"prose prose-gray dark:prose-dark max-w-none",children:[r.jsx("h1",{children:l.title}),r.jsx("div",{dangerouslySetInnerHTML:{__html:o}})]}):r.jsx(Wu,{content:o,onChange:i,placeholder:"开始写作...",coverImage:l.imageUrl,onCoverImageChange:re})})}),r.jsx("div",{className:"lg:col-span-1",children:r.jsxs("div",{className:"card p-6",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"文章设置"}),r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标题"}),r.jsx("input",{type:"text",value:l.title,onChange:$=>c(S=>({...S,title:$.target.value})),className:"input w-full",placeholder:"输入文章标题"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"摘要"}),r.jsx("textarea",{value:l.excerpt,onChange:$=>c(S=>({...S,excerpt:$.target.value})),className:"input w-full h-20 resize-none",placeholder:"输入文章摘要"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"分类"}),r.jsx("input",{type:"text",value:l.category,onChange:$=>c(S=>({...S,category:$.target.value})),className:"input w-full",placeholder:"输入分类"})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"标签"}),r.jsx("div",{className:"flex flex-wrap gap-2 mb-2",children:l.tags.map($=>r.jsxs("span",{className:"inline-flex items-center px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full",children:[$,r.jsx("button",{onClick:()=>ee($),title:`移除标签: ${$}`,"aria-label":`移除标签: ${$}`,className:"ml-1 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200",children:"×"})]},$))}),r.jsx("input",{type:"text",className:"input w-full",placeholder:"输入标签后按回车",onKeyPress:$=>{$.key==="Enter"&&(z($.currentTarget.value.trim()),$.currentTarget.value="")}})]}),r.jsx("div",{children:r.jsxs("label",{className:"flex items-center space-x-2",children:[r.jsx("input",{type:"checkbox",checked:l.featured,onChange:$=>c(S=>({...S,featured:$.target.checked})),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),r.jsx("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"设为特色文章"})]})})]})]})})]})]}):r.jsxs("div",{className:"max-w-4xl mx-auto text-center py-12",children:[r.jsx("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx(ht,{className:"text-red-600 dark:text-red-400",size:32})}),r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-2",children:"需要管理员权限"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"只有管理员才能创建和编辑文章"}),r.jsxs("button",{onClick:()=>H(!0),className:"inline-flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[r.jsx(ht,{size:20}),r.jsx("span",{children:"管理员登录"})]}),r.jsx(ya,{isOpen:L,onClose:()=>H(!1)})]})}function qn(){const{category:e}=Kt(),[t,s]=g.useState([]),[n,a]=g.useState([]),[o,i]=g.useState(!0),[l,c]=g.useState(null);g.useEffect(()=>{u()},[e]);const u=async()=>{i(!0),c(null);try{if(e){const d=await ye.getPosts(1,20,e);s(d.posts)}else{const d=await ye.getPosts(1,100);s(d.posts);const f=new Map;d.posts.forEach(x=>{x.category&&f.set(x.category,(f.get(x.category)||0)+1)}),f.set("General",d.posts.length);const b=Array.from(f.entries()).map(([x,k])=>({name:x,count:k})).sort((x,k)=>x.name==="General"?-1:k.name==="General"?1:k.count-x.count);a(b)}}catch(d){console.error("加载分类数据失败:",d),c("加载失败，请重试")}finally{i(!1)}};return o?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载中..."})]}):l?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("p",{className:"text-red-600 dark:text-red-400",children:l}),r.jsx("button",{onClick:u,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]}):e?r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsx("div",{className:"mb-6",children:r.jsxs(Y,{to:"/categories",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"返回所有分类"})]})}),r.jsxs("div",{className:"mb-8",children:[r.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[r.jsx(kt,{size:24,className:"text-blue-600"}),r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e})]}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共找到 ",t.length," 篇文章"]})]}),t.length>0?r.jsx("div",{className:"space-y-6",children:t.map(d=>r.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[r.jsx("h2",{className:"text-xl font-semibold mb-3",children:r.jsx(Y,{to:`/post/${d.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:d.title})}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:d.excerpt}),r.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(yt,{size:14}),r.jsx("span",{children:d.author})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:14}),r.jsx("span",{children:d.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(st,{size:14}),r.jsxs("span",{children:[d.readTime," 分钟阅读"]})]}),d.category&&r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(kt,{size:14}),r.jsx("span",{className:"px-2 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded",children:d.category})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:d.tags.map(f=>r.jsxs(Y,{to:`/tag/${f}`,className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:["#",f]},f))})]},d.id))}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx(kt,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无相关文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"该分类下还没有文章"}),r.jsx(Y,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]}):r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsxs("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"所有分类"}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共 ",n.length," 个分类"]})]}),n.length>0?r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:n.map(({name:d,count:f})=>r.jsxs(Y,{to:`/category/${d}`,className:"group p-6 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 hover:shadow-lg transition-all",children:[r.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[r.jsx(kt,{size:20,className:"text-blue-600"}),r.jsx("h3",{className:"font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:d})]}),r.jsxs("p",{className:"text-gray-500 dark:text-gray-400 text-sm",children:[f," 篇文章"]})]},d))}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx(kt,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无分类"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"还没有任何分类"}),r.jsx(Y,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})]})}function Vn(){const{tag:e}=Kt(),[t,s]=g.useState([]),[n,a]=g.useState([]),[o,i]=g.useState(!0),[l,c]=g.useState(null);g.useEffect(()=>{u()},[e]);const u=async()=>{i(!0),c(null);try{if(e){const d=await ye.getPosts(1,20,void 0,e);s(d.posts)}else{const d=await ye.getPosts(1,100);s(d.posts);const f=new Map;d.posts.forEach(x=>{x.tags.forEach(k=>{f.set(k,(f.get(k)||0)+1)})});const b=Array.from(f.entries()).map(([x,k])=>({name:x,count:k})).sort((x,k)=>k.count-x.count);a(b)}}catch(d){console.error("加载标签数据失败:",d),c("加载失败，请重试")}finally{i(!1)}};return o?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"加载中..."})]}):l?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("p",{className:"text-red-600 dark:text-red-400",children:l}),r.jsx("button",{onClick:u,className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"重试"})]}):e?r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsx("div",{className:"mb-6",children:r.jsxs(Y,{to:"/tags",className:"inline-flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:[r.jsx(lr,{size:16}),r.jsx("span",{children:"返回所有标签"})]})}),r.jsxs("div",{className:"mb-8",children:[r.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[r.jsx(Ct,{size:24,className:"text-blue-600"}),r.jsxs("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["#",e]})]}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共找到 ",t.length," 篇文章"]})]}),t.length>0?r.jsx("div",{className:"space-y-6",children:t.map(d=>r.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[r.jsx("h2",{className:"text-xl font-semibold mb-3",children:r.jsx(Y,{to:`/post/${d.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:d.title})}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:d.excerpt}),r.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(yt,{size:14}),r.jsx("span",{children:d.author})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:14}),r.jsx("span",{children:d.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(st,{size:14}),r.jsxs("span",{children:[d.readTime," 分钟阅读"]})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:d.tags.map(f=>r.jsxs(Y,{to:`/tag/${f}`,className:`px-2 py-1 text-xs rounded transition-colors ${f===e?"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200":"bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"}`,children:["#",f]},f))})]},d.id))}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx(Ct,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无相关文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"该标签下还没有文章"}),r.jsx(Y,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]}):r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsxs("div",{className:"mb-8",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"所有标签"}),r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:["共 ",n.length," 个标签"]})]}),n.length>0?r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:n.map(({name:d,count:f})=>r.jsx(Y,{to:`/tag/${d}`,className:"group p-4 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-500 hover:shadow-lg transition-all",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("span",{className:"font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors",children:["#",d]}),r.jsx("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:f})]})},d))}):r.jsxs("div",{className:"text-center py-12",children:[r.jsx(Ct,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"暂无标签"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"还没有任何标签"}),r.jsx(Y,{to:"/write",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"写第一篇文章"})]})]})}function Fu(){const[e,t]=Ro(),[s,n]=g.useState(e.get("q")||""),[a,o]=g.useState([]),[i,l]=g.useState(!1),[c,u]=g.useState(!1);g.useEffect(()=>{const x=e.get("q");x&&(n(x),d(x))},[e]);const d=async x=>{if(x.trim()){l(!0),u(!0);try{const k=await ye.searchPosts(x);o(k)}catch(k){console.error("搜索失败:",k),o([])}finally{l(!1)}}},f=x=>{x.preventDefault(),s.trim()&&t({q:s.trim()})},b=x=>{n(x.target.value)};return r.jsxs("div",{className:"max-w-4xl mx-auto",children:[r.jsx("div",{className:"mb-8",children:r.jsxs("form",{onSubmit:f,className:"relative",children:[r.jsxs("div",{className:"relative",children:[r.jsx(_t,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400",size:20}),r.jsx("input",{type:"text",value:s,onChange:b,placeholder:"搜索文章...",className:"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),r.jsx("button",{type:"submit",className:"absolute right-2 top-1/2 transform -translate-y-1/2 px-4 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors",children:"搜索"})]})}),r.jsxs("div",{children:[i&&r.jsxs("div",{className:"text-center py-8",children:[r.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),r.jsx("p",{className:"mt-2 text-gray-600 dark:text-gray-400",children:"搜索中..."})]}),!i&&c&&r.jsx("div",{className:"mb-6",children:r.jsxs("p",{className:"text-gray-600 dark:text-gray-400",children:[a.length>0?`找到 ${a.length} 篇文章`:"没有找到相关文章",s&&` 关于 "${s}"`]})}),!i&&a.length>0&&r.jsx("div",{className:"space-y-6",children:a.map(x=>r.jsxs("article",{className:"card p-6 hover:shadow-lg transition-shadow",children:[r.jsx("h2",{className:"text-xl font-semibold mb-3",children:r.jsx(Y,{to:`/post/${x.slug}`,className:"text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors",children:x.title})}),r.jsx("p",{className:"text-gray-600 dark:text-gray-300 mb-4 line-clamp-3",children:x.excerpt}),r.jsxs("div",{className:"flex flex-wrap items-center gap-4 text-sm text-gray-500 dark:text-gray-400 mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(yt,{size:14}),r.jsx("span",{children:x.author})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ge,{size:14}),r.jsx("span",{children:x.date})]}),r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(st,{size:14}),r.jsxs("span",{children:[x.readTime," 分钟阅读"]})]}),x.category&&r.jsxs("div",{className:"flex items-center space-x-1",children:[r.jsx(Ct,{size:14}),r.jsx("span",{children:x.category})]})]}),r.jsx("div",{className:"flex flex-wrap gap-2",children:x.tags.map(k=>r.jsxs(Y,{to:`/tag/${k}`,className:"px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors",children:["#",k]},k))})]},x.id))}),!i&&c&&a.length===0&&r.jsxs("div",{className:"text-center py-12",children:[r.jsx(_t,{size:64,className:"mx-auto text-gray-300 dark:text-gray-600 mb-4"}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"没有找到相关文章"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mb-6",children:"尝试使用不同的关键词或查看所有文章"}),r.jsx(Y,{to:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"查看所有文章"})]})]})]})}function Ku(){return r.jsx("div",{className:"max-w-4xl mx-auto",children:r.jsxs("div",{className:"card p-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-6",children:"关于我"}),r.jsxs("div",{className:"prose prose-gray dark:prose-dark max-w-none",children:[r.jsx("p",{children:"欢迎来到我的个人博客！这里是我分享技术见解、开发经验和生活感悟的地方。"}),r.jsx("h2",{children:"技术栈"}),r.jsxs("ul",{children:[r.jsx("li",{children:"前端：React, TypeScript, Tailwind CSS"}),r.jsx("li",{children:"后端：Cloudflare Workers, Node.js"}),r.jsx("li",{children:"数据库：Cloudflare KV, MongoDB"}),r.jsx("li",{children:"部署：Cloudflare Pages, Vercel"})]}),r.jsx("h2",{children:"联系方式"}),r.jsx("p",{children:"如果您想与我交流或有任何问题，请通过以下方式联系我："}),r.jsxs("ul",{children:[r.jsx("li",{children:"<EMAIL>"}),r.jsx("li",{children:"https://github.com/ajkdfe2e2e"}),r.jsx("li",{children:"https://x.com/x2a1HRjxs552213"})]})]})]})})}function Gu(){const e=g.useRef(null),t=()=>{var s;(s=e.current)==null||s.toggleVisibility()};return r.jsxs(oc,{children:[r.jsx(gc,{onMusicToggle:t,children:r.jsxs(mo,{children:[r.jsx(Se,{path:"/",element:r.jsx(mc,{})}),r.jsx(Se,{path:"/post/:slug",element:r.jsx(bc,{})}),r.jsx(Se,{path:"/write",element:r.jsx(Gn,{})}),r.jsx(Se,{path:"/write/:slug",element:r.jsx(Gn,{})}),r.jsx(Se,{path:"/categories",element:r.jsx(qn,{})}),r.jsx(Se,{path:"/category/:category",element:r.jsx(qn,{})}),r.jsx(Se,{path:"/tags",element:r.jsx(Vn,{})}),r.jsx(Se,{path:"/tag/:tag",element:r.jsx(Vn,{})}),r.jsx(Se,{path:"/search",element:r.jsx(Fu,{})}),r.jsx(Se,{path:"/about",element:r.jsx(Ku,{})})]})}),r.jsx(fc,{ref:e})]})}ys.setupNetworkListener();os.createRoot(document.getElementById("root")).render(r.jsx(ai.StrictMode,{children:r.jsxs(Eo,{children:[r.jsx(Gu,{}),r.jsx(bl,{position:"top-center",toastOptions:{duration:3e3,style:{background:"#1f2937",color:"#f9fafb"}}})]})}));
//# sourceMappingURL=index-3ddf77ff.js.map
