/**
 * 缓存管理工具
 * 处理图片缓存、预加载和缓存清理
 */

export class CacheManager {
  private static instance: CacheManager
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null

  private constructor() {
    this.initServiceWorker()
  }

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance
  }

  // 初始化Service Worker
  private async initServiceWorker() {
    if ('serviceWorker' in navigator) {
      try {
        this.serviceWorkerRegistration = await navigator.serviceWorker.register('/sw.js')
        console.log('[缓存管理] Service Worker 注册成功')
        
        // 监听Service Worker更新
        this.serviceWorkerRegistration.addEventListener('updatefound', () => {
          console.log('[缓存管理] Service Worker 更新可用')
        })
      } catch (error) {
        console.error('[缓存管理] Service Worker 注册失败:', error)
      }
    }
  }

  // 预加载图片
  async preloadImages(urls: string[]): Promise<boolean> {
    if (!this.serviceWorkerRegistration || !this.serviceWorkerRegistration.active) {
      console.warn('[缓存管理] Service Worker 未激活，使用浏览器预加载')
      return this.browserPreloadImages(urls)
    }

    try {
      const messageChannel = new MessageChannel()
      
      const promise = new Promise<boolean>((resolve) => {
        messageChannel.port1.onmessage = (event) => {
          resolve(event.data.success)
        }
      })

      this.serviceWorkerRegistration.active.postMessage(
        { type: 'PRELOAD_IMAGES', urls },
        [messageChannel.port2]
      )

      return await promise
    } catch (error) {
      console.error('[缓存管理] 预加载失败:', error)
      return false
    }
  }

  // 浏览器原生预加载（Service Worker不可用时的备选方案）
  private async browserPreloadImages(urls: string[]): Promise<boolean> {
    try {
      const preloadPromises = urls.map((url) => {
        return new Promise<void>((resolve, reject) => {
          const img = new Image()
          img.onload = () => resolve()
          img.onerror = () => reject(new Error(`Failed to load ${url}`))
          img.src = url
        })
      })

      await Promise.allSettled(preloadPromises)
      console.log('[缓存管理] 浏览器预加载完成')
      return true
    } catch (error) {
      console.error('[缓存管理] 浏览器预加载失败:', error)
      return false
    }
  }

  // 清理图片缓存
  async clearImageCache(): Promise<boolean> {
    if (!this.serviceWorkerRegistration || !this.serviceWorkerRegistration.active) {
      console.warn('[缓存管理] Service Worker 未激活，无法清理缓存')
      return false
    }

    try {
      const messageChannel = new MessageChannel()
      
      const promise = new Promise<boolean>((resolve) => {
        messageChannel.port1.onmessage = (event) => {
          resolve(event.data.success)
        }
      })

      this.serviceWorkerRegistration.active.postMessage(
        { type: 'CLEAR_IMAGE_CACHE' },
        [messageChannel.port2]
      )

      return await promise
    } catch (error) {
      console.error('[缓存管理] 清理缓存失败:', error)
      return false
    }
  }

  // 获取缓存大小估算
  async getCacheSize(): Promise<number> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate()
        return estimate.usage || 0
      } catch (error) {
        console.error('[缓存管理] 获取缓存大小失败:', error)
        return 0
      }
    }
    return 0
  }

  // 检查缓存配额
  async checkCacheQuota(): Promise<{ used: number; available: number; percentage: number }> {
    if ('storage' in navigator && 'estimate' in navigator.storage) {
      try {
        const estimate = await navigator.storage.estimate()
        const used = estimate.usage || 0
        const quota = estimate.quota || 0
        const available = quota - used
        const percentage = quota > 0 ? (used / quota) * 100 : 0

        return { used, available, percentage }
      } catch (error) {
        console.error('[缓存管理] 检查缓存配额失败:', error)
      }
    }
    
    return { used: 0, available: 0, percentage: 0 }
  }

  // 智能预加载（根据用户行为预测需要的图片）
  async smartPreload(currentPostSlug?: string) {
    try {
      // 获取相关文章的封面图片
      const relatedImages: string[] = []
      
      // 如果在文章页面，预加载相关文章的封面
      if (currentPostSlug) {
        // 这里可以调用API获取相关文章
        // const relatedPosts = await BlogService.getRelatedPosts(currentPostSlug)
        // relatedImages.push(...relatedPosts.map(post => post.imageUrl).filter(Boolean))
      }
      
      // 预加载首页特色文章图片
      const featuredImages = this.getFeaturedImages()
      relatedImages.push(...featuredImages)
      
      if (relatedImages.length > 0) {
        console.log('[缓存管理] 开始智能预加载:', relatedImages.length, '张图片')
        await this.preloadImages(relatedImages)
      }
    } catch (error) {
      console.error('[缓存管理] 智能预加载失败:', error)
    }
  }

  // 获取特色图片列表（从localStorage或其他来源）
  private getFeaturedImages(): string[] {
    try {
      const cached = localStorage.getItem('featured_images')
      return cached ? JSON.parse(cached) : []
    } catch {
      return []
    }
  }

  // 缓存特色图片列表
  cacheFeaturedImages(images: string[]) {
    try {
      localStorage.setItem('featured_images', JSON.stringify(images))
    } catch (error) {
      console.error('[缓存管理] 缓存特色图片列表失败:', error)
    }
  }

  // 检查网络状态并调整缓存策略
  adaptToNetworkCondition() {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      
      if (connection) {
        const { effectiveType, downlink } = connection
        
        // 根据网络状况调整预加载策略
        if (effectiveType === 'slow-2g' || effectiveType === '2g' || downlink < 0.5) {
          console.log('[缓存管理] 检测到慢速网络，减少预加载')
          return 'conservative'
        } else if (effectiveType === '4g' && downlink > 2) {
          console.log('[缓存管理] 检测到快速网络，增加预加载')
          return 'aggressive'
        }
      }
    }
    
    return 'normal'
  }

  // 监听网络状态变化
  setupNetworkListener() {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      
      if (connection) {
        connection.addEventListener('change', () => {
          const strategy = this.adaptToNetworkCondition()
          console.log('[缓存管理] 网络状态变化，调整策略为:', strategy)
        })
      }
    }
    
    // 监听在线/离线状态
    window.addEventListener('online', () => {
      console.log('[缓存管理] 网络已连接')
    })
    
    window.addEventListener('offline', () => {
      console.log('[缓存管理] 网络已断开')
    })
  }
}

// 导出单例实例
export const cacheManager = CacheManager.getInstance()
