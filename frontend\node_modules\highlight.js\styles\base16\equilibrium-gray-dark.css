pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Equilibrium Gray Dark
  Author: <PERSON>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme equilibrium-gray-dark
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #111111  Default Background
base01  #1b1b1b  Lighter Background (Used for status bars, line number and folding marks)
base02  #262626  Selection Background
base03  #777777  Comments, Invisibles, Line Highlighting
base04  #919191  Dark Foreground (Used for status bars)
base05  #ababab  Default Foreground, Caret, Delimiters, Operators
base06  #c6c6c6  Light Foreground (Not often used)
base07  #e2e2e2  Light Background (Not often used)
base08  #f04339  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #df5923  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #bb8801  Classes, Markup Bold, Search Text Background
base0B  #7f8b00  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #00948b  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #008dd1  Functions, Methods, Attribute IDs, Headings
base0E  #6a7fd2  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #e3488e  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #ababab;
  background: #111111
}
.hljs::selection,
.hljs ::selection {
  background-color: #262626;
  color: #ababab
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #777777 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #777777
}
/* base04 - #919191 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #919191
}
/* base05 - #ababab -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #ababab
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #f04339
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #df5923
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #bb8801
}
.hljs-strong {
  font-weight: bold;
  color: #bb8801
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #7f8b00
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #00948b
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #008dd1
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #6a7fd2
}
.hljs-emphasis {
  color: #6a7fd2;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #e3488e
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}