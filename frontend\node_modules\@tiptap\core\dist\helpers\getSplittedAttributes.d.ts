import { ExtensionAttribute } from '../types.js';
/**
 * Return attributes of an extension that should be splitted by keepOnSplit flag
 * @param extensionAttributes Array of extension attributes
 * @param typeName The type of the extension
 * @param attributes The attributes of the extension
 * @returns The splitted attributes
 */
export declare function getSplittedAttributes(extensionAttributes: ExtensionAttribute[], typeName: string, attributes: Record<string, any>): Record<string, any>;
//# sourceMappingURL=getSplittedAttributes.d.ts.map