pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: <PERSON><PERSON><PERSON>
  Author: <PERSON> <<EMAIL>>
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme ros-pine-moon
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #232136  Default Background
base01  #2a273f  Lighter Background (Used for status bars, line number and folding marks)
base02  #393552  Selection Background
base03  #59546d  Comments, Invisibles, Line Highlighting
base04  #817c9c  Dark Foreground (Used for status bars)
base05  #e0def4  Default Foreground, Caret, Delimiters, Operators
base06  #f5f5f7  Light Foreground (Not often used)
base07  #d9d7e1  Light Background (Not often used)
base08  #ecebf0  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #eb6f92  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #f6c177  Classes, Markup Bold, Search Text Background
base0B  #ea9a97  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #3e8fb0  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #9ccfd8  Functions, Methods, Attribute IDs, Headings
base0E  #c4a7e7  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b9b9bc  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #e0def4;
  background: #232136
}
.hljs::selection,
.hljs ::selection {
  background-color: #393552;
  color: #e0def4
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #59546d -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #59546d
}
/* base04 - #817c9c -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #817c9c
}
/* base05 - #e0def4 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #e0def4
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #ecebf0
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #eb6f92
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #f6c177
}
.hljs-strong {
  font-weight: bold;
  color: #f6c177
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #ea9a97
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #3e8fb0
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #9ccfd8
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #c4a7e7
}
.hljs-emphasis {
  color: #c4a7e7;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b9b9bc
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}