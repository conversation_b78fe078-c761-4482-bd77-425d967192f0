import { Link } from 'react-router-dom'
import { Calendar, Clock, User, Eye, Heart, MessageCircle, Star } from 'lucide-react'
import { Post } from '../services/blogService'
import { PostActions } from './PostActions'
import { AdaptiveImage } from './AdaptiveImage'
import { useImagePreview } from './ImagePreview'

interface PostCardProps {
  post: Post
  variant?: 'default' | 'featured' | 'compact' | 'list'
  showActions?: boolean
  showStats?: boolean
  onDeleted?: () => void
  className?: string
}

export function PostCard({
  post,
  variant = 'default',
  showActions = false,
  showStats = false,
  onDeleted,
  className = ""
}: PostCardProps) {
  const { openPreview, PreviewComponent } = useImagePreview()
  
  // 特色文章样式
  if (variant === 'featured') {
    return (
      <div className={`card p-8 hover:shadow-lg transition-all duration-300 relative group ${className}`}>
        {/* 特色标识 */}
        <div className="absolute top-4 left-4 z-10">
          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200">
            <Star size={12} className="mr-1" />
            特色文章
          </span>
        </div>

        {/* 文章操作按钮 */}
        {showActions && (
          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10">
            <PostActions 
              slug={post.slug}
              title={post.title}
              onDeleted={onDeleted}
            />
          </div>
        )}
        
        <Link to={`/post/${post.slug}`} className="block">
          {/* 封面图片 */}
          {post.imageUrl && (
            <div className="mb-6 rounded-xl overflow-hidden shadow-lg group">
              <AdaptiveImage
                src={post.imageUrl}
                alt={post.title}
                className="group-hover:scale-105 transition-transform duration-500"
                containerClassName="rounded-xl"
                maxHeight={400}
                minHeight={200}
                priority="auto"
                lazy={false}
                preload={true}
                quality={90}
                width={800}
                height={400}
                aspectRatio={16/9}
                showSkeleton={true}
                enablePreview={true}
                retryCount={3}
                onPreview={(src) => openPreview(src, post.title)}
                onError={() => {
                  console.warn('特色文章封面加载失败:', post.imageUrl)
                }}
              />
            </div>
          )}
          
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 pr-12 leading-tight">
            {post.title}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4 text-lg">
            {post.excerpt}
          </p>
          
          {/* 元信息 */}
          <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mb-4">
            <div className="flex items-center space-x-1">
              <User size={16} />
              <span>{post.author}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Calendar size={16} />
              <span>{post.date}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Clock size={16} />
              <span>{post.readTime} 分钟阅读</span>
            </div>
            {showStats && (
              <>
                <div className="flex items-center space-x-1">
                  <Eye size={16} />
                  <span>{(post as any).views || 0}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Heart size={16} />
                  <span>{(post as any).likes || 0}</span>
                </div>
              </>
            )}
          </div>
          
          {/* 标签 */}
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <span
                key={tag}
                className="px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        </Link>
      </div>
    )
  }

  // 紧凑样式
  if (variant === 'compact') {
    return (
      <div className={`card p-4 hover:shadow-md transition-all duration-300 group relative ${className}`}>
        {showActions && (
          <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity z-10">
            <PostActions 
              slug={post.slug}
              title={post.title}
              onDeleted={onDeleted}
            />
          </div>
        )}
        
        <Link to={`/post/${post.slug}`} className="block">
          <div className="flex space-x-4">
            {/* 小图片 */}
            {post.imageUrl && (
              <div className="flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden">
                <AdaptiveImage
                  src={post.imageUrl}
                  alt={post.title}
                  className="group-hover:scale-110 transition-transform duration-300"
                  containerClassName="rounded-lg"
                  maxHeight={80}
                  minHeight={80}
                  priority="cover"
                  lazy={true}
                  quality={75}
                  width={80}
                  height={80}
                  aspectRatio={1}
                  showSkeleton={true}
                  enablePreview={true}
                  onPreview={(src) => openPreview(src, post.title)}
                />
              </div>
            )}
            
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-2 line-clamp-2">
                {post.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-2 line-clamp-2">
                {post.excerpt}
              </p>
              <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
                <span className="flex items-center space-x-1">
                  <Calendar size={10} />
                  <span>{post.date}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Clock size={10} />
                  <span>{post.readTime}分钟</span>
                </span>
              </div>
            </div>
          </div>
        </Link>
      </div>
    )
  }

  // 列表样式
  if (variant === 'list') {
    return (
      <div className={`border-b border-gray-200 dark:border-gray-700 py-6 group relative ${className}`}>
        {showActions && (
          <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10">
            <PostActions 
              slug={post.slug}
              title={post.title}
              onDeleted={onDeleted}
            />
          </div>
        )}
        
        <Link to={`/post/${post.slug}`} className="block">
          <div className="flex space-x-6">
            <div className="flex-1">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-2">
                {post.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {post.excerpt}
              </p>
              
              {/* 标签 */}
              <div className="flex flex-wrap gap-2 mb-3">
                {post.tags.slice(0, 3).map((tag) => (
                  <span
                    key={tag}
                    className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded"
                  >
                    {tag}
                  </span>
                ))}
              </div>
              
              {/* 元信息 */}
              <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                <span className="flex items-center space-x-1">
                  <User size={14} />
                  <span>{post.author}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Calendar size={14} />
                  <span>{post.date}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Clock size={14} />
                  <span>{post.readTime} 分钟</span>
                </span>
                {showStats && (
                  <>
                    <span className="flex items-center space-x-1">
                      <Eye size={14} />
                      <span>{(post as any).views || 0}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <MessageCircle size={14} />
                      <span>{(post as any).comments || 0}</span>
                    </span>
                  </>
                )}
              </div>
            </div>
            
            {/* 右侧图片 */}
            {post.imageUrl && (
              <div className="flex-shrink-0 w-32 h-24 rounded-lg overflow-hidden">
                <AdaptiveImage
                  src={post.imageUrl}
                  alt={post.title}
                  className="group-hover:scale-110 transition-transform duration-300"
                  containerClassName="rounded-lg"
                  maxHeight={96}
                  minHeight={96}
                  priority="cover"
                  lazy={true}
                  quality={75}
                  width={128}
                  height={96}
                  aspectRatio={4/3}
                  showSkeleton={true}
                  enablePreview={true}
                  onPreview={(src) => openPreview(src, post.title)}
                />
              </div>
            )}
          </div>
        </Link>
      </div>
    )
  }

  // 默认卡片样式
  return (
    <div className={`card p-0 hover:shadow-lg transition-all duration-300 group h-full relative overflow-hidden rounded-lg ${className}`}>
      {showActions && (
        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10">
          <PostActions 
            slug={post.slug}
            title={post.title}
            onDeleted={onDeleted}
          />
        </div>
      )}
      
      <Link to={`/post/${post.slug}`} className="block h-full">
        {/* 封面图片 */}
        {post.imageUrl && (
          <div className="w-full h-48 overflow-hidden bg-gray-100 dark:bg-gray-800">
            <AdaptiveImage
              src={post.imageUrl}
              alt={post.title}
              className="group-hover:scale-110 transition-transform duration-500"
              containerClassName="rounded-t-lg w-full h-full"
              maxHeight={192}
              minHeight={192}
              priority="contain"
              lazy={true}
              quality={80}
              width={400}
              height={192}
              aspectRatio={25/12}
              showSkeleton={true}
              enablePreview={true}
              onPreview={(src) => openPreview(src, post.title)}
              onError={() => {
                console.warn('文章封面加载失败:', post.imageUrl)
              }}
            />
          </div>
        )}
        
        <div className="p-6 flex flex-col h-full">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 line-clamp-2">
            {post.title}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3 flex-grow">
            {post.excerpt}
          </p>
          
          <div className="mt-auto">
            {/* 标签 */}
            <div className="flex flex-wrap gap-2 mb-3">
              {post.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full"
                >
                  {tag}
                </span>
              ))}
              {post.tags.length > 3 && (
                <span className="px-2 py-1 text-xs text-gray-400 dark:text-gray-500">
                  +{post.tags.length - 3}
                </span>
              )}
            </div>
            
            {/* 元信息 */}
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center space-x-3">
                <span className="flex items-center space-x-1">
                  <Calendar size={12} />
                  <span>{post.date}</span>
                </span>
                <span className="flex items-center space-x-1">
                  <Clock size={12} />
                  <span>{post.readTime} 分钟</span>
                </span>
              </div>
              {showStats && (
                <div className="flex items-center space-x-3">
                  <span className="flex items-center space-x-1">
                    <Eye size={12} />
                    <span>{(post as any).views || 0}</span>
                  </span>
                  <span className="flex items-center space-x-1">
                    <Heart size={12} />
                    <span>{(post as any).likes || 0}</span>
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      </Link>

      {/* 图片预览组件 */}
      {PreviewComponent}
    </div>
  )
}
