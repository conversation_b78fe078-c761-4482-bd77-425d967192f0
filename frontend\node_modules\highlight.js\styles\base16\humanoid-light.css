pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Humanoid light
  Author: <PERSON> (tasmo) Friese
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme humanoid-light
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #f8f8f2  Default Background
base01  #efefe9  Lighter Background (Used for status bars, line number and folding marks)
base02  #deded8  Selection Background
base03  #c0c0bd  Comments, Invisibles, Line Highlighting
base04  #60615d  Dark Foreground (Used for status bars)
base05  #232629  Default Foreground, Caret, Delimiters, Operators
base06  #2f3337  Light Foreground (Not often used)
base07  #070708  Light Background (Not often used)
base08  #b0151a  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ff3d00  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #ffb627  Classes, Markup Bold, Search Text Background
base0B  #388e3c  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #008e8e  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #0082c9  Functions, Methods, Attribute IDs, Headings
base0E  #700f98  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b27701  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #232629;
  background: #f8f8f2
}
.hljs::selection,
.hljs ::selection {
  background-color: #deded8;
  color: #232629
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #c0c0bd -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #c0c0bd
}
/* base04 - #60615d -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #60615d
}
/* base05 - #232629 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #232629
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #b0151a
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ff3d00
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #ffb627
}
.hljs-strong {
  font-weight: bold;
  color: #ffb627
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #388e3c
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #008e8e
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #0082c9
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #700f98
}
.hljs-emphasis {
  color: #700f98;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b27701
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}