{"name": "@esbuild-plugins/node-globals-polyfill", "version": "0.2.3", "description": "", "preferUnplugged": true, "sideEffects": false, "main": "dist/index.js", "module": "esm/index.js", "types": "dist/index.d.ts", "repository": "https://github.com/remorses/esbuild-plugins.git", "scripts": {"build": "tsc && tsc -m es6 --outDir esm", "watch": "tsc -w"}, "files": ["dist", "src", "esm", "Buffer.js", "process.js", "_buffer.js", "_process.js"], "keywords": [], "author": "<PERSON><PERSON><PERSON>, morse <<EMAIL>>", "license": "ISC", "devDependencies": {"test-support": "*"}, "dependencies": {}, "peerDependencies": {"esbuild": "*"}}