import { useState, useEffect, useMemo, useCallback } from 'react'
import { Link } from 'react-router-dom'
import { Calendar, Clock, User } from 'lucide-react'
import { BlogService, Post } from '../services/blogService'
import { ApiError } from '../config/api'
import { PostActions } from '../components/PostActions'
import { PostFilters, FilterState } from '../components/PostFilters'
import { AdaptiveImage } from '../components/AdaptiveImage'
import { cacheManager } from '../utils/cacheManager'

export function HomePage() {
  const [posts, setPosts] = useState<Post[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<FilterState>({
    category: '',
    tag: '',
    sortBy: 'date',
    sortOrder: 'desc'
  })

  const loadPosts = async () => {
    try {
      setLoading(true)
      setError(null)
      
      console.log('开始加载文章列表...')
      
      // 获取文章列表
      const response = await BlogService.getPosts(1, 10)
      console.log('获取文章响应:', response)
      
      setPosts(response.posts || [])

      if (!response.posts || response.posts.length === 0) {
        console.warn('API返回空文章列表')
      } else {
        // 缓存特色图片并启动智能预加载
        const imageUrls = response.posts
          .map(post => post.imageUrl)
          .filter(Boolean) as string[]

        if (imageUrls.length > 0) {
          cacheManager.cacheFeaturedImages(imageUrls)
          // 异步预加载，不阻塞UI
          cacheManager.smartPreload().catch(error => {
            console.warn('智能预加载失败:', error)
          })
        }
      }
      
    } catch (err) {
      console.error('加载文章失败详情:', err)
      
      if (err instanceof ApiError) {
        setError(`API错误 (${err.status}): ${err.message}`)
      } else if (err instanceof Error) {
        setError(`网络错误: ${err.message}`)
      } else {
        setError('未知错误，请检查网络连接')
      }
      
      // 设置空文章列表但不隐藏错误信息
      setPosts([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadPosts()
  }, [])

  const handlePostDeleted = () => {
    // 文章删除后重新加载列表
    loadPosts()
  }

  const handleFilterChange = useCallback((newFilters: FilterState) => {
    setFilters(newFilters)
  }, [])

  // 获取可用的分类和标签
  const availableCategories = useMemo(() => {
    const categories = posts
      .filter(post => post.category)
      .map(post => post.category!)
    return Array.from(new Set(categories))
  }, [posts])

  const availableTags = useMemo(() => {
    const tags = posts.flatMap(post => post.tags)
    return Array.from(new Set(tags))
  }, [posts])

  // 筛选和排序文章
  const filteredAndSortedPosts = useMemo(() => {
    let filtered = [...posts]

    // 按分类筛选
    if (filters.category) {
      filtered = filtered.filter(post => post.category === filters.category)
    }

    // 按标签筛选
    if (filters.tag) {
      filtered = filtered.filter(post => post.tags.includes(filters.tag))
    }

    // 排序
    filtered.sort((a, b) => {
      let comparison = 0
      
      switch (filters.sortBy) {
        case 'date':
          comparison = new Date(a.date).getTime() - new Date(b.date).getTime()
          break
        case 'readTime':
          comparison = a.readTime - b.readTime
          break
        case 'featured':
          comparison = (a.featured ? 1 : 0) - (b.featured ? 1 : 0)
          break
      }

      return filters.sortOrder === 'desc' ? -comparison : comparison
    })

    return filtered
  }, [posts, filters])

  const featuredPost = filteredAndSortedPosts.find((post: Post) => post.featured)
  const regularPosts = filteredAndSortedPosts.filter((post: Post) => !post.featured)

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="text-center py-12">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto mb-4 animate-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"></div>
        </div>
        <div className="card p-8 animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-12">
      {/* 欢迎标题 */}
      <div className="text-center py-12">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          欢迎来到我的博客
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          分享技术见解、开发经验和生活感悟的个人空间
        </p>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="card p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-red-800 dark:text-red-200 font-medium mb-2">
                文章加载失败
              </h3>
              <p className="text-red-700 dark:text-red-300 text-sm mb-4">
                {error}
              </p>
              <div className="text-xs text-red-600 dark:text-red-400 mb-4">
                请打开浏览器开发者工具的控制台查看详细错误信息
              </div>
            </div>
            <button
              onClick={loadPosts}
              className="ml-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
              disabled={loading}
            >
              {loading ? '重试中...' : '重试'}
            </button>
          </div>
        </div>
      )}

      {/* 筛选组件 */}
      {posts.length > 0 && (
        <PostFilters
          onFilterChange={handleFilterChange}
          availableCategories={availableCategories}
          availableTags={availableTags}
        />
      )}

      {/* 空状态 */}
      {!loading && posts.length === 0 && (
        <div className="text-center py-16">
          <div className="card p-8 max-w-md mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              还没有文章
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              开始写作，分享你的想法和经验吧！
            </p>
            <Link
              to="/write"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              写第一篇文章
            </Link>
          </div>
        </div>
      )}

      {/* 筛选无结果 */}
      {!loading && posts.length > 0 && filteredAndSortedPosts.length === 0 && (
        <div className="text-center py-16">
          <div className="card p-8 max-w-md mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              没有找到匹配的文章
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              试试调整筛选条件，或者浏览全部文章
            </p>
            <button
              onClick={() => setFilters({
                category: '',
                tag: '',
                sortBy: 'date',
                sortOrder: 'desc'
              })}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              清除筛选条件
            </button>
          </div>
        </div>
      )}

      {/* 特色文章 */}
      {featuredPost && (
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            ✨ 特色文章
          </h2>
          <div className="card p-8 hover:shadow-lg transition-all duration-300 relative group">
            {/* 文章操作按钮 */}
            <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
              <PostActions 
                slug={featuredPost.slug}
                title={featuredPost.title}
                onDeleted={handlePostDeleted}
              />
            </div>
            
            <Link to={`/post/${featuredPost.slug}`} className="block">
              {/* 特色文章封面图片 */}
              {featuredPost.imageUrl && (
                <div className="mb-6 rounded-xl overflow-hidden shadow-lg">
                  <AdaptiveImage
                    src={featuredPost.imageUrl}
                    alt={featuredPost.title}
                    className="group-hover:scale-105 transition-transform duration-500"
                    maxHeight={400}
                    minHeight={200}
                    priority="auto"
                    lazy={false}
                    preload={true}
                    quality={90}
                    width={800}
                    height={400}
                    onError={() => {
                      console.warn('特色文章封面加载失败:', featuredPost.imageUrl)
                    }}
                  />
                </div>
              )}
              
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 pr-12 leading-tight">
                {featuredPost.title}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                {featuredPost.excerpt}
              </p>
              <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-1">
                  <User size={16} />
                  <span>{featuredPost.author}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Calendar size={16} />
                  <span>{featuredPost.date}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock size={16} />
                  <span>{featuredPost.readTime} 分钟阅读</span>
                </div>
              </div>
              <div className="flex flex-wrap gap-2 mt-4">
                {featuredPost.tags.map((tag) => (
                  <span
                    key={tag}
                    className="px-3 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </Link>
          </div>
        </div>
      )}

      {/* 最新文章 */}
      {regularPosts.length > 0 && (
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            📚 最新文章
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {regularPosts.map((post) => (
              <div key={post.id} className="card p-0 hover:shadow-lg transition-all duration-300 group h-full relative overflow-hidden rounded-lg">
                {/* 文章操作按钮 */}
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10">
                  <PostActions 
                    slug={post.slug}
                    title={post.title}
                    onDeleted={handlePostDeleted}
                  />
                </div>
                
                <Link to={`/post/${post.slug}`} className="block h-full">
                  {/* 文章封面图片 */}
                  {post.imageUrl && (
                    <div className="w-full h-48 overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900">
                      <img
                        src={post.imageUrl}
                        alt={post.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                        loading="lazy"
                        onError={(e) => {
                          console.warn('文章封面加载失败:', post.imageUrl)
                          const target = e.target as HTMLImageElement
                          const container = target.parentElement
                          if (container) {
                            container.style.display = 'none'
                          }
                        }}
                      />
                    </div>
                  )}
                  
                  <div className="p-6 flex flex-col h-full">
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white group-hover:text-blue-600 transition-colors mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4 line-clamp-3 flex-grow">
                      {post.excerpt}
                    </p>
                    
                    <div className="mt-auto">
                      <div className="flex flex-wrap gap-2 mb-3">
                        {post.tags.slice(0, 3).map((tag) => (
                          <span
                            key={tag}
                            className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                        {post.tags.length > 3 && (
                          <span className="px-2 py-1 text-xs text-gray-400 dark:text-gray-500">
                            +{post.tags.length - 3}
                          </span>
                        )}
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <div className="flex items-center space-x-1">
                          <Calendar size={12} />
                          <span>{post.date}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock size={12} />
                          <span>{post.readTime} 分钟</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 如果有文章但没有特色文章或常规文章时的提示 */}
      {posts.length > 0 && !featuredPost && regularPosts.length === 0 && (
        <div className="text-center py-8">
          <p className="text-gray-600 dark:text-gray-400">
            文章加载中...
          </p>
        </div>
      )}
    </div>
  )
} 