import { useState, useEffect, useMemo, useCallback } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Grid, List } from 'lucide-react'
import { BlogService, Post } from '../services/blogService'
import { ApiError } from '../config/api'
import { PostFilters, FilterState } from '../components/PostFilters'
import { cacheManager } from '../utils/cacheManager'
import { SearchBar } from '../components/SearchBar'
import { Pagination, PageSizeSelector } from '../components/Pagination'
import { PostCard } from '../components/PostCard'
import { FeaturedPostCard } from '../components/FeaturedPostCard'
import { createToastHelpers } from '../components/Toast'
import { useImagePreview } from '../components/ImagePreview'

export function HomePage() {
  const [posts, setPosts] = useState<Post[]>([])
  const [filteredPosts, setFilteredPosts] = useState<Post[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState<FilterState>({
    category: '',
    tag: '',
    sortBy: 'date',
    sortOrder: 'desc'
  })

  // 新增状态
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(12)

  const toast = createToastHelpers()
  const { PreviewComponent } = useImagePreview()

  const loadPosts = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('开始加载文章列表...')

      // 获取文章列表 - 加载更多文章以支持分页
      const response = await BlogService.getPosts(1, 50)
      console.log('获取文章响应:', response)

      const loadedPosts = response.posts || []
      setPosts(loadedPosts)
      setFilteredPosts(loadedPosts)

      if (loadedPosts.length === 0) {
        console.warn('API返回空文章列表')
      } else {
        // 缓存特色图片并启动智能预加载
        const imageUrls = loadedPosts
          .map(post => post.imageUrl)
          .filter(Boolean) as string[]

        if (imageUrls.length > 0) {
          cacheManager.cacheFeaturedImages(imageUrls)
          // 异步预加载，不阻塞UI
          cacheManager.smartPreload().catch(error => {
            console.warn('智能预加载失败:', error)
          })
        }

        console.log(`文章加载完成，共加载 ${loadedPosts.length} 篇文章`)
      }

    } catch (err) {
      console.error('加载文章失败详情:', err)

      let errorMessage = '加载文章失败'
      if (err instanceof ApiError) {
        errorMessage = `API错误 (${err.status}): ${err.message}`
      } else if (err instanceof Error) {
        errorMessage = `网络错误: ${err.message}`
      } else {
        errorMessage = '未知错误，请检查网络连接'
      }

      setError(errorMessage)
      toast.error('加载失败', errorMessage)
      setPosts([])
      setFilteredPosts([])
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadPosts()
  }, [])

  const handlePostDeleted = () => {
    // 文章删除后重新加载列表
    loadPosts()
  }

  const handleFilterChange = useCallback((newFilters: FilterState) => {
    setFilters(newFilters)
    setCurrentPage(1) // 重置到第一页
  }, [])

  // 搜索处理
  const handleSearchResults = useCallback((results: Post[]) => {
    setFilteredPosts(results)
    setCurrentPage(1) // 重置到第一页
  }, [])

  // 页面变化处理
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page)
    // 滚动到顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }, [])

  // 页面大小变化处理
  const handlePageSizeChange = useCallback((size: number) => {
    setPageSize(size)
    setCurrentPage(1)
  }, [])

  // 获取可用的分类和标签
  const availableCategories = useMemo(() => {
    const categories = posts
      .filter(post => post.category)
      .map(post => post.category!)
    return Array.from(new Set(categories))
  }, [posts])

  const availableTags = useMemo(() => {
    const tags = posts.flatMap(post => post.tags)
    return Array.from(new Set(tags))
  }, [posts])

  // 筛选和排序文章
  const filteredAndSortedPosts = useMemo(() => {
    // 首先应用搜索结果，如果没有搜索则使用所有文章
    let filtered = [...filteredPosts]

    // 按分类筛选
    if (filters.category) {
      filtered = filtered.filter(post => post.category === filters.category)
    }

    // 按标签筛选
    if (filters.tag) {
      filtered = filtered.filter(post => post.tags.includes(filters.tag))
    }

    // 智能排序
    filtered.sort((a, b) => {
      // 如果用户选择了特定的排序方式，使用用户选择的排序
      if (filters.sortBy !== 'date') {
        let comparison = 0
        switch (filters.sortBy) {
          case 'readTime':
            comparison = a.readTime - b.readTime
            break
          case 'featured':
            comparison = (a.featured ? 1 : 0) - (b.featured ? 1 : 0)
            break
        }
        return filters.sortOrder === 'desc' ? -comparison : comparison
      }

      // 默认的智能排序（按日期排序时）
      // 提取系列名称和课程编号的函数
      const extractSeriesInfo = (title: string) => {
        // 匹配模式：系列名-第X课、系列名-课程X、系列名 第X课等
        const patterns = [
          /^(.+?)-第(\d+)课/,
          /^(.+?)-课程(\d+)/,
          /^(.+?)\s+第(\d+)课/,
          /^(.+?)-(\d+)/,
          /^(.+?)\s+(\d+)/
        ]

        for (const pattern of patterns) {
          const match = title.match(pattern)
          if (match) {
            return {
              series: match[1].trim(),
              number: parseInt(match[2]),
              hasNumber: true
            }
          }
        }

        return {
          series: title,
          number: 0,
          hasNumber: false
        }
      }

      const aInfo = extractSeriesInfo(a.title)
      const bInfo = extractSeriesInfo(b.title)

      // 如果是同一系列且都有编号，按编号排序
      if (aInfo.series === bInfo.series && aInfo.hasNumber && bInfo.hasNumber) {
        return filters.sortOrder === 'desc' ? bInfo.number - aInfo.number : aInfo.number - bInfo.number
      }

      // 如果是同一系列但编号情况不同，有编号的排在前面
      if (aInfo.series === bInfo.series) {
        if (aInfo.hasNumber && !bInfo.hasNumber) return filters.sortOrder === 'desc' ? -1 : 1
        if (!aInfo.hasNumber && bInfo.hasNumber) return filters.sortOrder === 'desc' ? 1 : -1
      }

      // 不同系列或都没有编号，按日期排序
      const dateComparison = new Date(a.date).getTime() - new Date(b.date).getTime()
      return filters.sortOrder === 'desc' ? -dateComparison : dateComparison
    })

    return filtered
  }, [filteredPosts, filters])

  // 分页逻辑
  const totalPages = Math.ceil(filteredAndSortedPosts.length / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const currentPosts = filteredAndSortedPosts.slice(startIndex, endIndex)

  // 特色文章（只在第一页显示）
  const featuredPost = currentPage === 1 ? filteredAndSortedPosts.find((post: Post) => post.featured) : null
  const regularPosts = currentPosts.filter((post: Post) => !post.featured || currentPage > 1)

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="text-center py-12">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-64 mx-auto mb-4 animate-pulse"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-96 mx-auto animate-pulse"></div>
        </div>
        <div className="card p-8 animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-12">
      {/* 欢迎标题 */}
      <div className="text-center py-12">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">
          欢迎来到杨博文的博客
        </h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto mb-8">
          分享技术见解、开发经验和生活感悟的个人空间
        </p>

        {/* 搜索栏 */}
        <div className="max-w-md mx-auto">
          <SearchBar
            posts={posts}
            onSearchResults={handleSearchResults}
            placeholder="搜索文章、标签或分类..."
            className="w-full"
          />
        </div>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="card p-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className="text-red-800 dark:text-red-200 font-medium mb-2">
                文章加载失败
              </h3>
              <p className="text-red-700 dark:text-red-300 text-sm mb-4">
                {error}
              </p>
              <div className="text-xs text-red-600 dark:text-red-400 mb-4">
                请打开浏览器开发者工具的控制台查看详细错误信息
              </div>
            </div>
            <button
              onClick={loadPosts}
              className="ml-4 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
              disabled={loading}
            >
              {loading ? '重试中...' : '重试'}
            </button>
          </div>
        </div>
      )}

      {/* 筛选和控制区域 */}
      {posts.length > 0 && (
        <div className="space-y-6">
          {/* 筛选组件 */}
          <PostFilters
            onFilterChange={handleFilterChange}
            availableCategories={availableCategories}
            availableTags={availableTags}
          />

          {/* 视图控制和统计 */}
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                共 {filteredAndSortedPosts.length} 篇文章
              </span>
              <PageSizeSelector
                pageSize={pageSize}
                onPageSizeChange={handlePageSizeChange}
                options={[6, 12, 24, 48]}
              />
            </div>

            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400 mr-2">视图:</span>
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
                title="网格视图"
              >
                <Grid size={16} />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list'
                    ? 'bg-blue-600 text-white'
                    : 'bg-white dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-600'
                }`}
                title="列表视图"
              >
                <List size={16} />
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 空状态 */}
      {!loading && posts.length === 0 && (
        <div className="text-center py-16">
          <div className="card p-8 max-w-md mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              还没有文章
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              开始写作，分享你的想法和经验吧！
            </p>
            <Link
              to="/write"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              写第一篇文章
            </Link>
          </div>
        </div>
      )}

      {/* 筛选无结果 */}
      {!loading && posts.length > 0 && filteredAndSortedPosts.length === 0 && (
        <div className="text-center py-16">
          <div className="card p-8 max-w-md mx-auto">
            <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
              没有找到匹配的文章
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              试试调整筛选条件，或者浏览全部文章
            </p>
            <button
              onClick={() => setFilters({
                category: '',
                tag: '',
                sortBy: 'date',
                sortOrder: 'desc'
              })}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              清除筛选条件
            </button>
          </div>
        </div>
      )}

      {/* 特色文章 */}
      {featuredPost && (
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              ✨ 精选推荐
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              为您精心挑选的优质内容
            </p>
          </div>
          <FeaturedPostCard
            post={featuredPost}
            showActions={true}
            onDeleted={handlePostDeleted}
          />
        </div>
      )}

      {/* 文章列表 */}
      {regularPosts.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
              {currentPage === 1 && featuredPost ? '📚 最新文章' : '📚 文章列表'}
            </h2>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              第 {currentPage} 页，共 {totalPages} 页
            </span>
          </div>

          {/* 文章网格/列表 */}
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {regularPosts.map((post) => (
                <PostCard
                  key={post.id}
                  post={post}
                  variant="default"
                  showActions={true}
                  showStats={false}
                  onDeleted={handlePostDeleted}
                />
              ))}
            </div>
          ) : (
            <div className="space-y-0">
              {regularPosts.map((post) => (
                <PostCard
                  key={post.id}
                  post={post}
                  variant="list"
                  showActions={true}
                  showStats={false}
                  onDeleted={handlePostDeleted}
                />
              ))}
            </div>
          )}
        </div>
      )}

      {/* 分页 */}
      {filteredAndSortedPosts.length > pageSize && (
        <div className="mt-12">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredAndSortedPosts.length}
            itemsPerPage={pageSize}
            onPageChange={handlePageChange}
            showInfo={true}
          />
        </div>
      )}

      {/* 如果有文章但没有特色文章或常规文章时的提示 */}
      {posts.length > 0 && !featuredPost && regularPosts.length === 0 && currentPage === 1 && (
        <div className="text-center py-8">
          <p className="text-gray-600 dark:text-gray-400">
            当前页面没有文章
          </p>
        </div>
      )}

      {/* 全局图片预览 */}
      {PreviewComponent}
    </div>
  )
}