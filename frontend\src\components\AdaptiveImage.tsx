import { useState, useRef, useEffect, useCallback } from 'react'
import { ImageIcon, RefreshCw, AlertCircle, Eye } from 'lucide-react'

interface AdaptiveImageProps {
  src: string
  alt: string
  className?: string
  containerClassName?: string
  maxHeight?: number
  minHeight?: number
  priority?: 'cover' | 'contain' | 'auto'
  lazy?: boolean
  preload?: boolean
  quality?: number
  width?: number
  height?: number
  aspectRatio?: number // 新增：期望的宽高比
  showSkeleton?: boolean // 新增：是否显示骨架屏
  enablePreview?: boolean // 新增：是否启用点击预览
  fallbackSrc?: string // 新增：备用图片URL
  retryCount?: number // 新增：重试次数
  onError?: () => void
  onLoad?: () => void
  onPreview?: (src: string) => void // 新增：预览回调
}

// 骨架屏组件
function ImageSkeleton({ className = '' }: { className?: string }) {
  return (
    <div className={`animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 dark:from-gray-700 dark:via-gray-600 dark:to-gray-700 ${className}`}>
      <div className="flex items-center justify-center h-full">
        <ImageIcon size={32} className="text-gray-400 dark:text-gray-500" />
      </div>
    </div>
  )
}

// 错误占位符组件
function ErrorPlaceholder({
  onRetry,
  showRetry = true,
  className = ''
}: {
  onRetry?: () => void
  showRetry?: boolean
  className?: string
}) {
  return (
    <div className={`flex flex-col items-center justify-center h-full bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400 ${className}`}>
      <AlertCircle size={32} className="mb-2" />
      <p className="text-sm text-center mb-2">图片加载失败</p>
      {showRetry && onRetry && (
        <button
          onClick={onRetry}
          className="flex items-center space-x-1 px-3 py-1 text-xs bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 rounded transition-colors"
        >
          <RefreshCw size={12} />
          <span>重试</span>
        </button>
      )}
    </div>
  )
}

export function AdaptiveImage({
  src,
  alt,
  className = '',
  containerClassName = '',
  maxHeight = 400,
  minHeight = 200,
  priority = 'auto',
  lazy = true,
  preload = false,
  quality = 85,
  width,
  height,
  aspectRatio,
  showSkeleton = true,
  enablePreview = false,
  fallbackSrc,
  retryCount = 2,
  onError,
  onLoad,
  onPreview
}: AdaptiveImageProps) {
  const [objectFit, setObjectFit] = useState<'cover' | 'contain'>('cover')
  const [isLoaded, setIsLoaded] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [hasError, setHasError] = useState(false)
  const [isInView, setIsInView] = useState(!lazy)
  const [currentSrc, setCurrentSrc] = useState<string>('')
  const [currentRetry, setCurrentRetry] = useState(0)
  const [imageNaturalSize, setImageNaturalSize] = useState<{ width: number; height: number } | null>(null)
  const imgRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 生成优化的图片URL
  const generateOptimizedUrl = useCallback((originalSrc: string) => {
    // 如果是外部URL或已经是优化URL，直接返回
    if (!originalSrc.includes('pub-a1a2.r2.dev') || originalSrc.includes('/api/image-proxy/')) {
      return originalSrc
    }

    // 提取文件路径
    const urlParts = originalSrc.split('pub-a1a2.r2.dev/')
    if (urlParts.length < 2) return originalSrc

    const imagePath = urlParts[1]
    const params = new URLSearchParams()

    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    if (quality !== 85) params.set('q', quality.toString())
    params.set('f', 'auto') // 自动选择最佳格式

    const baseUrl = window.location.origin.includes('localhost')
      ? 'http://localhost:8787'
      : 'https://blog.fddfffff.site'

    return `${baseUrl}/api/image-proxy/${imagePath}?${params.toString()}`
  }, [width, height, quality])

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true)
            observer.disconnect()
          }
        })
      },
      {
        rootMargin: '50px', // 提前50px开始加载
        threshold: 0.1
      }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [lazy, isInView])

  // 设置图片源和加载状态
  useEffect(() => {
    if (isInView && src) {
      setIsLoading(true)
      setHasError(false)
      setIsLoaded(false)

      const optimizedSrc = generateOptimizedUrl(src)
      setCurrentSrc(optimizedSrc)

      // 预加载图片
      if (preload) {
        const img = new Image()
        img.src = optimizedSrc
        img.onload = () => {
          setImageNaturalSize({ width: img.naturalWidth, height: img.naturalHeight })
        }
      }
    }
  }, [isInView, src, generateOptimizedUrl, preload])

  // 重试加载
  const retryLoad = useCallback(() => {
    if (currentRetry < retryCount) {
      setCurrentRetry(prev => prev + 1)
      setHasError(false)
      setIsLoading(true)

      // 尝试使用备用图片或原始图片
      if (currentRetry === 0 && fallbackSrc) {
        setCurrentSrc(fallbackSrc)
      } else if (currentSrc !== src) {
        setCurrentSrc(src)
      } else {
        // 强制重新加载
        setCurrentSrc(src + '?retry=' + Date.now())
      }
    }
  }, [currentRetry, retryCount, fallbackSrc, currentSrc, src])

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget
    setIsLoaded(true)
    setIsLoading(false)
    setHasError(false)
    setCurrentRetry(0) // 重置重试计数

    // 记录图片自然尺寸
    setImageNaturalSize({ width: img.naturalWidth, height: img.naturalHeight })

    if (priority === 'auto') {
      const imgAspectRatio = img.naturalWidth / img.naturalHeight
      const containerAspectRatio = aspectRatio || (width && height ? width / height : 16 / 9)

      // 智能选择显示方式
      if (Math.abs(imgAspectRatio - containerAspectRatio) > 0.5) {
        // 如果图片比例与容器比例差异较大，使用 contain 模式
        setObjectFit('contain')
      } else if (imgAspectRatio > 2.5 || imgAspectRatio < 0.4) {
        // 极宽或极高的图片使用 contain 模式
        setObjectFit('contain')
      } else {
        // 正常比例的图片使用 cover 模式
        setObjectFit('cover')
      }
    } else {
      setObjectFit(priority === 'contain' ? 'contain' : 'cover')
    }

    onLoad?.()
  }

  const handleImageError = () => {
    console.warn('图片加载失败:', currentSrc, '重试次数:', currentRetry)
    setIsLoading(false)
    setIsLoaded(false)

    if (currentRetry < retryCount) {
      // 自动重试
      setTimeout(() => {
        retryLoad()
      }, 1000 * (currentRetry + 1)) // 递增延迟
    } else {
      // 达到最大重试次数，显示错误状态
      setHasError(true)
      onError?.()
    }
  }

  // 处理图片点击预览
  const handleImageClick = () => {
    if (enablePreview && isLoaded && currentSrc) {
      onPreview?.(currentSrc)
    }
  }

  // 计算容器样式
  const containerStyle = {
    minHeight: `${minHeight}px`,
    maxHeight: `${maxHeight}px`,
    height: objectFit === 'contain' ? 'auto' : `${maxHeight}px`,
    aspectRatio: aspectRatio ? aspectRatio.toString() : undefined
  }

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 rounded-lg ${objectFit === 'contain' ? 'flex items-center justify-center' : ''} ${containerClassName}`}
      style={containerStyle}
    >
      {/* 骨架屏加载状态 */}
      {!isInView && showSkeleton && (
        <ImageSkeleton className="absolute inset-0" />
      )}

      {/* 加载中状态 */}
      {isInView && isLoading && !isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="flex flex-col items-center space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-xs text-gray-500 dark:text-gray-400">加载中...</span>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {hasError && (
        <ErrorPlaceholder
          onRetry={currentRetry < retryCount ? retryLoad : undefined}
          showRetry={currentRetry < retryCount}
          className="absolute inset-0"
        />
      )}

      {/* 实际图片 */}
      {isInView && currentSrc && !hasError && (
        <img
          ref={imgRef}
          src={currentSrc}
          alt={alt}
          className={`transition-all duration-700 ${
            objectFit === 'contain'
              ? 'max-w-full max-h-full object-contain'
              : 'w-full h-full object-cover'
          } ${className} ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-105'} ${
            enablePreview ? 'cursor-pointer hover:scale-105' : ''
          }`}
          loading={lazy ? "lazy" : "eager"}
          onLoad={handleImageLoad}
          onError={handleImageError}
          onClick={handleImageClick}
          style={{
            maxHeight: `${maxHeight}px`,
            minHeight: objectFit === 'contain' ? `${minHeight}px` : 'auto'
          }}
        />
      )}

      {/* 预览图标 */}
      {enablePreview && isLoaded && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="bg-black/50 text-white p-1 rounded">
            <Eye size={16} />
          </div>
        </div>
      )}

      {/* 图片信息显示（开发模式） */}
      {process.env.NODE_ENV === 'development' && imageNaturalSize && isLoaded && (
        <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded opacity-0 hover:opacity-100 transition-opacity">
          {imageNaturalSize.width} × {imageNaturalSize.height}
          {objectFit && ` • ${objectFit}`}
        </div>
      )}

      {/* 如果是 contain 模式，添加一个微妙的边框 */}
      {objectFit === 'contain' && isLoaded && (
        <div className="absolute inset-0 border border-gray-200/30 dark:border-gray-700/30 rounded-lg pointer-events-none" />
      )}
    </div>
  )
}