import { useState, useRef, useEffect, useCallback } from 'react'

interface AdaptiveImageProps {
  src: string
  alt: string
  className?: string
  containerClassName?: string
  maxHeight?: number
  minHeight?: number
  priority?: 'cover' | 'contain' | 'auto'
  lazy?: boolean
  preload?: boolean
  quality?: number
  width?: number
  height?: number
  onError?: () => void
  onLoad?: () => void
}

export function AdaptiveImage({
  src,
  alt,
  className = '',
  containerClassName = '',
  maxHeight = 400,
  minHeight = 200,
  priority = 'auto',
  lazy = true,
  preload = false,
  quality = 85,
  width,
  height,
  onError,
  onLoad
}: AdaptiveImageProps) {
  const [objectFit, setObjectFit] = useState<'cover' | 'contain'>('cover')
  const [isLoaded, setIsLoaded] = useState(false)
  const [isInView, setIsInView] = useState(!lazy)
  const [currentSrc, setCurrentSrc] = useState<string>('')
  const imgRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // 生成优化的图片URL
  const generateOptimizedUrl = useCallback((originalSrc: string) => {
    // 如果是外部URL或已经是优化URL，直接返回
    if (!originalSrc.includes('pub-a1a2.r2.dev') || originalSrc.includes('/api/image-proxy/')) {
      return originalSrc
    }

    // 提取文件路径
    const urlParts = originalSrc.split('pub-a1a2.r2.dev/')
    if (urlParts.length < 2) return originalSrc

    const imagePath = urlParts[1]
    const params = new URLSearchParams()

    if (width) params.set('w', width.toString())
    if (height) params.set('h', height.toString())
    if (quality !== 85) params.set('q', quality.toString())
    params.set('f', 'auto') // 自动选择最佳格式

    const baseUrl = window.location.origin.includes('localhost')
      ? 'http://localhost:8787'
      : 'https://blog.fddfffff.site'

    return `${baseUrl}/api/image-proxy/${imagePath}?${params.toString()}`
  }, [width, height, quality])

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isInView) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true)
            observer.disconnect()
          }
        })
      },
      {
        rootMargin: '50px', // 提前50px开始加载
        threshold: 0.1
      }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [lazy, isInView])

  // 设置图片源
  useEffect(() => {
    if (isInView && src) {
      const optimizedSrc = generateOptimizedUrl(src)
      setCurrentSrc(optimizedSrc)

      // 预加载图片
      if (preload) {
        const img = new Image()
        img.src = optimizedSrc
      }
    }
  }, [isInView, src, generateOptimizedUrl, preload])

  const handleImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const img = e.currentTarget
    setIsLoaded(true)

    if (priority === 'auto') {
      const aspectRatio = img.naturalWidth / img.naturalHeight

      // 根据图片比例智能选择显示方式
      if (aspectRatio > 2.5 || aspectRatio < 0.4) {
        // 极宽或极高的图片使用 contain 模式
        setObjectFit('contain')
      } else {
        // 正常比例的图片使用 cover 模式
        setObjectFit('cover')
      }
    } else {
      setObjectFit(priority === 'contain' ? 'contain' : 'cover')
    }

    onLoad?.()
  }

  const handleImageError = () => {
    console.warn('图片加载失败:', currentSrc)
    // 如果优化URL失败，尝试原始URL
    if (currentSrc !== src) {
      setCurrentSrc(src)
    } else {
      onError?.()
    }
  }

  return (
    <div
      ref={containerRef}
      className={`relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 ${containerClassName}`}
      style={{
        minHeight: `${minHeight}px`,
        maxHeight: `${maxHeight}px`,
        height: objectFit === 'contain' ? 'auto' : `${maxHeight}px`
      }}
    >
      {/* 加载中的占位符 */}
      {(!isLoaded || !isInView) && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* 实际图片 */}
      {isInView && currentSrc && (
        <img
          ref={imgRef}
          src={currentSrc}
          alt={alt}
          className={`w-full transition-all duration-500 ${
            objectFit === 'contain'
              ? 'h-auto object-contain'
              : 'h-full object-cover'
          } ${className} ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
          loading={lazy ? "lazy" : "eager"}
          onLoad={handleImageLoad}
          onError={handleImageError}
          style={{
            maxHeight: `${maxHeight}px`,
            minHeight: objectFit === 'contain' ? `${minHeight}px` : 'auto'
          }}
        />
      )}

      {/* 如果是 contain 模式，添加一个微妙的边框 */}
      {objectFit === 'contain' && isLoaded && (
        <div className="absolute inset-0 border border-gray-200/50 dark:border-gray-700/50 rounded-inherit pointer-events-none" />
      )}
    </div>
  )
}