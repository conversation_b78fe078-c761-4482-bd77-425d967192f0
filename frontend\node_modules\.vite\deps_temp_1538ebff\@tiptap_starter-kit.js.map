{"version": 3, "sources": ["../../@tiptap/extension-blockquote/src/blockquote.ts", "../../@tiptap/extension-bold/src/bold.ts", "../../@tiptap/extension-bullet-list/src/bullet-list.ts", "../../@tiptap/extension-code/src/code.ts", "../../@tiptap/extension-document/src/document.ts", "../../prosemirror-dropcursor/dist/index.js", "../../@tiptap/extension-dropcursor/src/dropcursor.ts", "../../prosemirror-gapcursor/dist/index.js", "../../@tiptap/extension-gapcursor/src/gapcursor.ts", "../../@tiptap/extension-hard-break/src/hard-break.ts", "../../@tiptap/extension-heading/src/heading.ts", "../../rope-sequence/dist/index.js", "../../prosemirror-history/dist/index.js", "../../@tiptap/extension-history/src/history.ts", "../../@tiptap/extension-horizontal-rule/src/horizontal-rule.ts", "../../@tiptap/extension-italic/src/italic.ts", "../../@tiptap/extension-list-item/src/list-item.ts", "../../@tiptap/extension-ordered-list/src/ordered-list.ts", "../../@tiptap/extension-paragraph/src/paragraph.ts", "../../@tiptap/extension-strike/src/strike.ts", "../../@tiptap/extension-text/src/text.ts", "../../@tiptap/starter-kit/src/starter-kit.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nexport interface BlockquoteOptions {\n  /**\n   * HTML attributes to add to the blockquote element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blockQuote: {\n      /**\n       * Set a blockquote node\n       */\n      setBlockquote: () => ReturnType,\n      /**\n       * Toggle a blockquote node\n       */\n      toggleBlockquote: () => ReturnType,\n      /**\n       * Unset a blockquote node\n       */\n      unsetBlockquote: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a blockquote to a `>` as input.\n */\nexport const inputRegex = /^\\s*>\\s$/\n\n/**\n * This extension allows you to create blockquotes.\n * @see https://tiptap.dev/api/nodes/blockquote\n */\nexport const Blockquote = Node.create<BlockquoteOptions>({\n\n  name: 'blockquote',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'block+',\n\n  group: 'block',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      { tag: 'blockquote' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['blockquote', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBlockquote: () => ({ commands }) => {\n        return commands.wrapIn(this.name)\n      },\n      toggleBlockquote: () => ({ commands }) => {\n        return commands.toggleWrap(this.name)\n      },\n      unsetBlockquote: () => ({ commands }) => {\n        return commands.lift(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-b': () => this.editor.commands.toggleBlockquote(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface BoldOptions {\n  /**\n   * HTML attributes to add to the bold element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bold: {\n      /**\n       * Set a bold mark\n       */\n      setBold: () => ReturnType,\n      /**\n       * Toggle a bold mark\n       */\n      toggleBold: () => ReturnType,\n      /**\n       * Unset a bold mark\n       */\n      unsetBold: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches bold text via `**` as input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/\n\n/**\n * Matches bold text via `**` while pasting.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g\n\n/**\n * Matches bold text via `__` as input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/\n\n/**\n * Matches bold text via `__` while pasting.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g\n\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nexport const Bold = Mark.create<BoldOptions>({\n  name: 'bold',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'strong',\n      },\n      {\n        tag: 'b',\n        getAttrs: node => (node as HTMLElement).style.fontWeight !== 'normal' && null,\n      },\n      {\n        style: 'font-weight=400',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-weight',\n        getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value as string) && null,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['strong', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setBold: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleBold: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetBold: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-b': () => this.editor.commands.toggleBold(),\n      'Mod-B': () => this.editor.commands.toggleBold(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface BulletListOptions {\n  /**\n   * The node name for the list items\n   * @default 'listItem'\n   * @example 'paragraph'\n   */\n  itemTypeName: string,\n\n  /**\n   * HTML attributes to add to the bullet list element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * Keep the marks when splitting the list\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean,\n\n  /**\n   * Keep the attributes when splitting the list\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bulletList: {\n      /**\n       * Toggle a bullet list\n       */\n      toggleBulletList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a bullet list to a dash or asterisk.\n */\nexport const inputRegex = /^\\s*([-+*])\\s$/\n\n/**\n * This extension allows you to create bullet lists.\n * This requires the ListItem extension\n * @see https://tiptap.dev/api/nodes/bullet-list\n * @see https://tiptap.dev/api/nodes/list-item.\n */\nexport const BulletList = Node.create<BulletListOptions>({\n  name: 'bulletList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [\n      { tag: 'ul' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleBulletList: () => ({ commands, chain }) => {\n        if (this.options.keepAttributes) {\n          return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run()\n        }\n        return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: inputRegex,\n      type: this.type,\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: () => { return this.editor.getAttributes(TextStyleName) },\n        editor: this.editor,\n      })\n    }\n    return [\n      inputRule,\n    ]\n  },\n})\n", "import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface CodeOptions {\n  /**\n   * The HTML attributes applied to the code element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    code: {\n      /**\n       * Set a code mark\n       */\n      setCode: () => ReturnType,\n      /**\n       * Toggle inline code\n       */\n      toggleCode: () => ReturnType,\n      /**\n       * Unset a code mark\n       */\n      unsetCode: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Regular expressions to match inline code blocks enclosed in backticks.\n *  It matches:\n *     - An opening backtick, followed by\n *     - Any text that doesn't include a backtick (captured for marking), followed by\n *     - A closing backtick.\n *  This ensures that any text between backticks is formatted as code,\n *  regardless of the surrounding characters (exception being another backtick).\n */\nexport const inputRegex = /(^|[^`])`([^`]+)`(?!`)/\n\n/**\n * Matches inline code while pasting.\n */\nexport const pasteRegex = /(^|[^`])`([^`]+)`(?!`)/g\n\n/**\n * This extension allows you to mark text as inline code.\n * @see https://tiptap.dev/api/marks/code\n */\nexport const Code = Mark.create<CodeOptions>({\n  name: 'code',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  excludes: '_',\n\n  code: true,\n\n  exitable: true,\n\n  parseHTML() {\n    return [\n      { tag: 'code' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['code', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setCode: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleCode: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetCode: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-e': () => this.editor.commands.toggleCode(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Node } from '@tiptap/core'\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nexport const Document = Node.create({\n  name: 'doc',\n  topNode: true,\n  content: 'block+',\n})\n", "import { Plugin } from 'prosemirror-state';\nimport { dropPoint } from 'prosemirror-transform';\n\n/**\nCreate a plugin that, when added to a ProseMirror instance,\ncauses a decoration to show up at the drop position when something\nis dragged over the editor.\n\nNodes may add a `disableDropCursor` property to their spec to\ncontrol the showing of a drop cursor inside them. This may be a\nboolean or a function, which will be called with a view and a\nposition, and should return a boolean.\n*/\nfunction dropCursor(options = {}) {\n    return new Plugin({\n        view(editorView) { return new DropCursorView(editorView, options); }\n    });\n}\nclass DropCursorView {\n    constructor(editorView, options) {\n        var _a;\n        this.editorView = editorView;\n        this.cursorPos = null;\n        this.element = null;\n        this.timeout = -1;\n        this.width = (_a = options.width) !== null && _a !== void 0 ? _a : 1;\n        this.color = options.color === false ? undefined : (options.color || \"black\");\n        this.class = options.class;\n        this.handlers = [\"dragover\", \"dragend\", \"drop\", \"dragleave\"].map(name => {\n            let handler = (e) => { this[name](e); };\n            editorView.dom.addEventListener(name, handler);\n            return { name, handler };\n        });\n    }\n    destroy() {\n        this.handlers.forEach(({ name, handler }) => this.editorView.dom.removeEventListener(name, handler));\n    }\n    update(editorView, prevState) {\n        if (this.cursorPos != null && prevState.doc != editorView.state.doc) {\n            if (this.cursorPos > editorView.state.doc.content.size)\n                this.setCursor(null);\n            else\n                this.updateOverlay();\n        }\n    }\n    setCursor(pos) {\n        if (pos == this.cursorPos)\n            return;\n        this.cursorPos = pos;\n        if (pos == null) {\n            this.element.parentNode.removeChild(this.element);\n            this.element = null;\n        }\n        else {\n            this.updateOverlay();\n        }\n    }\n    updateOverlay() {\n        let $pos = this.editorView.state.doc.resolve(this.cursorPos);\n        let isBlock = !$pos.parent.inlineContent, rect;\n        let editorDOM = this.editorView.dom, editorRect = editorDOM.getBoundingClientRect();\n        let scaleX = editorRect.width / editorDOM.offsetWidth, scaleY = editorRect.height / editorDOM.offsetHeight;\n        if (isBlock) {\n            let before = $pos.nodeBefore, after = $pos.nodeAfter;\n            if (before || after) {\n                let node = this.editorView.nodeDOM(this.cursorPos - (before ? before.nodeSize : 0));\n                if (node) {\n                    let nodeRect = node.getBoundingClientRect();\n                    let top = before ? nodeRect.bottom : nodeRect.top;\n                    if (before && after)\n                        top = (top + this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top) / 2;\n                    let halfWidth = (this.width / 2) * scaleY;\n                    rect = { left: nodeRect.left, right: nodeRect.right, top: top - halfWidth, bottom: top + halfWidth };\n                }\n            }\n        }\n        if (!rect) {\n            let coords = this.editorView.coordsAtPos(this.cursorPos);\n            let halfWidth = (this.width / 2) * scaleX;\n            rect = { left: coords.left - halfWidth, right: coords.left + halfWidth, top: coords.top, bottom: coords.bottom };\n        }\n        let parent = this.editorView.dom.offsetParent;\n        if (!this.element) {\n            this.element = parent.appendChild(document.createElement(\"div\"));\n            if (this.class)\n                this.element.className = this.class;\n            this.element.style.cssText = \"position: absolute; z-index: 50; pointer-events: none;\";\n            if (this.color) {\n                this.element.style.backgroundColor = this.color;\n            }\n        }\n        this.element.classList.toggle(\"prosemirror-dropcursor-block\", isBlock);\n        this.element.classList.toggle(\"prosemirror-dropcursor-inline\", !isBlock);\n        let parentLeft, parentTop;\n        if (!parent || parent == document.body && getComputedStyle(parent).position == \"static\") {\n            parentLeft = -pageXOffset;\n            parentTop = -pageYOffset;\n        }\n        else {\n            let rect = parent.getBoundingClientRect();\n            let parentScaleX = rect.width / parent.offsetWidth, parentScaleY = rect.height / parent.offsetHeight;\n            parentLeft = rect.left - parent.scrollLeft * parentScaleX;\n            parentTop = rect.top - parent.scrollTop * parentScaleY;\n        }\n        this.element.style.left = (rect.left - parentLeft) / scaleX + \"px\";\n        this.element.style.top = (rect.top - parentTop) / scaleY + \"px\";\n        this.element.style.width = (rect.right - rect.left) / scaleX + \"px\";\n        this.element.style.height = (rect.bottom - rect.top) / scaleY + \"px\";\n    }\n    scheduleRemoval(timeout) {\n        clearTimeout(this.timeout);\n        this.timeout = setTimeout(() => this.setCursor(null), timeout);\n    }\n    dragover(event) {\n        if (!this.editorView.editable)\n            return;\n        let pos = this.editorView.posAtCoords({ left: event.clientX, top: event.clientY });\n        let node = pos && pos.inside >= 0 && this.editorView.state.doc.nodeAt(pos.inside);\n        let disableDropCursor = node && node.type.spec.disableDropCursor;\n        let disabled = typeof disableDropCursor == \"function\"\n            ? disableDropCursor(this.editorView, pos, event)\n            : disableDropCursor;\n        if (pos && !disabled) {\n            let target = pos.pos;\n            if (this.editorView.dragging && this.editorView.dragging.slice) {\n                let point = dropPoint(this.editorView.state.doc, target, this.editorView.dragging.slice);\n                if (point != null)\n                    target = point;\n            }\n            this.setCursor(target);\n            this.scheduleRemoval(5000);\n        }\n    }\n    dragend() {\n        this.scheduleRemoval(20);\n    }\n    drop() {\n        this.scheduleRemoval(20);\n    }\n    dragleave(event) {\n        if (!this.editorView.dom.contains(event.relatedTarget))\n            this.setCursor(null);\n    }\n}\n\nexport { dropCursor };\n", "import { Extension } from '@tiptap/core'\nimport { dropCursor } from '@tiptap/pm/dropcursor'\n\nexport interface DropcursorOptions {\n  /**\n   * The color of the drop cursor\n   * @default 'currentColor'\n   * @example 'red'\n   */\n  color: string | undefined,\n\n  /**\n   * The width of the drop cursor\n   * @default 1\n   * @example 2\n  */\n  width: number | undefined,\n\n  /**\n   * The class of the drop cursor\n   * @default undefined\n   * @example 'drop-cursor'\n  */\n  class: string | undefined,\n}\n\n/**\n * This extension allows you to add a drop cursor to your editor.\n * A drop cursor is a line that appears when you drag and drop content\n * inbetween nodes.\n * @see https://tiptap.dev/api/extensions/dropcursor\n */\nexport const Dropcursor = Extension.create<DropcursorOptions>({\n  name: 'dropCursor',\n\n  addOptions() {\n    return {\n      color: 'currentColor',\n      width: 1,\n      class: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      dropCursor(this.options),\n    ]\n  },\n})\n", "import { keydown<PERSON><PERSON><PERSON> } from 'prosemirror-keymap';\nimport { Selection, NodeSelection, TextSelection, Plugin } from 'prosemirror-state';\nimport { Slice, Fragment } from 'prosemirror-model';\nimport { DecorationSet, Decoration } from 'prosemirror-view';\n\n/**\nGap cursor selections are represented using this class. Its\n`$anchor` and `$head` properties both point at the cursor position.\n*/\nclass GapCursor extends Selection {\n    /**\n    Create a gap cursor.\n    */\n    constructor($pos) {\n        super($pos, $pos);\n    }\n    map(doc, mapping) {\n        let $pos = doc.resolve(mapping.map(this.head));\n        return GapCursor.valid($pos) ? new GapCursor($pos) : Selection.near($pos);\n    }\n    content() { return Slice.empty; }\n    eq(other) {\n        return other instanceof GapCursor && other.head == this.head;\n    }\n    toJSON() {\n        return { type: \"gapcursor\", pos: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for GapCursor.fromJSON\");\n        return new GapCursor(doc.resolve(json.pos));\n    }\n    /**\n    @internal\n    */\n    getBookmark() { return new GapBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static valid($pos) {\n        let parent = $pos.parent;\n        if (parent.isTextblock || !closedBefore($pos) || !closedAfter($pos))\n            return false;\n        let override = parent.type.spec.allowGapCursor;\n        if (override != null)\n            return override;\n        let deflt = parent.contentMatchAt($pos.index()).defaultType;\n        return deflt && deflt.isTextblock;\n    }\n    /**\n    @internal\n    */\n    static findGapCursorFrom($pos, dir, mustMove = false) {\n        search: for (;;) {\n            if (!mustMove && GapCursor.valid($pos))\n                return $pos;\n            let pos = $pos.pos, next = null;\n            // Scan up from this position\n            for (let d = $pos.depth;; d--) {\n                let parent = $pos.node(d);\n                if (dir > 0 ? $pos.indexAfter(d) < parent.childCount : $pos.index(d) > 0) {\n                    next = parent.child(dir > 0 ? $pos.indexAfter(d) : $pos.index(d) - 1);\n                    break;\n                }\n                else if (d == 0) {\n                    return null;\n                }\n                pos += dir;\n                let $cur = $pos.doc.resolve(pos);\n                if (GapCursor.valid($cur))\n                    return $cur;\n            }\n            // And then down into the next node\n            for (;;) {\n                let inside = dir > 0 ? next.firstChild : next.lastChild;\n                if (!inside) {\n                    if (next.isAtom && !next.isText && !NodeSelection.isSelectable(next)) {\n                        $pos = $pos.doc.resolve(pos + next.nodeSize * dir);\n                        mustMove = false;\n                        continue search;\n                    }\n                    break;\n                }\n                next = inside;\n                pos += dir;\n                let $cur = $pos.doc.resolve(pos);\n                if (GapCursor.valid($cur))\n                    return $cur;\n            }\n            return null;\n        }\n    }\n}\nGapCursor.prototype.visible = false;\nGapCursor.findFrom = GapCursor.findGapCursorFrom;\nSelection.jsonID(\"gapcursor\", GapCursor);\nclass GapBookmark {\n    constructor(pos) {\n        this.pos = pos;\n    }\n    map(mapping) {\n        return new GapBookmark(mapping.map(this.pos));\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.pos);\n        return GapCursor.valid($pos) ? new GapCursor($pos) : Selection.near($pos);\n    }\n}\nfunction closedBefore($pos) {\n    for (let d = $pos.depth; d >= 0; d--) {\n        let index = $pos.index(d), parent = $pos.node(d);\n        // At the start of this parent, look at next one\n        if (index == 0) {\n            if (parent.type.spec.isolating)\n                return true;\n            continue;\n        }\n        // See if the node before (or its first ancestor) is closed\n        for (let before = parent.child(index - 1);; before = before.lastChild) {\n            if ((before.childCount == 0 && !before.inlineContent) || before.isAtom || before.type.spec.isolating)\n                return true;\n            if (before.inlineContent)\n                return false;\n        }\n    }\n    // Hit start of document\n    return true;\n}\nfunction closedAfter($pos) {\n    for (let d = $pos.depth; d >= 0; d--) {\n        let index = $pos.indexAfter(d), parent = $pos.node(d);\n        if (index == parent.childCount) {\n            if (parent.type.spec.isolating)\n                return true;\n            continue;\n        }\n        for (let after = parent.child(index);; after = after.firstChild) {\n            if ((after.childCount == 0 && !after.inlineContent) || after.isAtom || after.type.spec.isolating)\n                return true;\n            if (after.inlineContent)\n                return false;\n        }\n    }\n    return true;\n}\n\n/**\nCreate a gap cursor plugin. When enabled, this will capture clicks\nnear and arrow-key-motion past places that don't have a normally\nselectable position nearby, and create a gap cursor selection for\nthem. The cursor is drawn as an element with class\n`ProseMirror-gapcursor`. You can either include\n`style/gapcursor.css` from the package's directory or add your own\nstyles to make it visible.\n*/\nfunction gapCursor() {\n    return new Plugin({\n        props: {\n            decorations: drawGapCursor,\n            createSelectionBetween(_view, $anchor, $head) {\n                return $anchor.pos == $head.pos && GapCursor.valid($head) ? new GapCursor($head) : null;\n            },\n            handleClick,\n            handleKeyDown,\n            handleDOMEvents: { beforeinput: beforeinput }\n        }\n    });\n}\nconst handleKeyDown = keydownHandler({\n    \"ArrowLeft\": arrow(\"horiz\", -1),\n    \"ArrowRight\": arrow(\"horiz\", 1),\n    \"ArrowUp\": arrow(\"vert\", -1),\n    \"ArrowDown\": arrow(\"vert\", 1)\n});\nfunction arrow(axis, dir) {\n    const dirStr = axis == \"vert\" ? (dir > 0 ? \"down\" : \"up\") : (dir > 0 ? \"right\" : \"left\");\n    return function (state, dispatch, view) {\n        let sel = state.selection;\n        let $start = dir > 0 ? sel.$to : sel.$from, mustMove = sel.empty;\n        if (sel instanceof TextSelection) {\n            if (!view.endOfTextblock(dirStr) || $start.depth == 0)\n                return false;\n            mustMove = false;\n            $start = state.doc.resolve(dir > 0 ? $start.after() : $start.before());\n        }\n        let $found = GapCursor.findGapCursorFrom($start, dir, mustMove);\n        if (!$found)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(new GapCursor($found)));\n        return true;\n    };\n}\nfunction handleClick(view, pos, event) {\n    if (!view || !view.editable)\n        return false;\n    let $pos = view.state.doc.resolve(pos);\n    if (!GapCursor.valid($pos))\n        return false;\n    let clickPos = view.posAtCoords({ left: event.clientX, top: event.clientY });\n    if (clickPos && clickPos.inside > -1 && NodeSelection.isSelectable(view.state.doc.nodeAt(clickPos.inside)))\n        return false;\n    view.dispatch(view.state.tr.setSelection(new GapCursor($pos)));\n    return true;\n}\n// This is a hack that, when a composition starts while a gap cursor\n// is active, quickly creates an inline context for the composition to\n// happen in, to avoid it being aborted by the DOM selection being\n// moved into a valid position.\nfunction beforeinput(view, event) {\n    if (event.inputType != \"insertCompositionText\" || !(view.state.selection instanceof GapCursor))\n        return false;\n    let { $from } = view.state.selection;\n    let insert = $from.parent.contentMatchAt($from.index()).findWrapping(view.state.schema.nodes.text);\n    if (!insert)\n        return false;\n    let frag = Fragment.empty;\n    for (let i = insert.length - 1; i >= 0; i--)\n        frag = Fragment.from(insert[i].createAndFill(null, frag));\n    let tr = view.state.tr.replace($from.pos, $from.pos, new Slice(frag, 0, 0));\n    tr.setSelection(TextSelection.near(tr.doc.resolve($from.pos + 1)));\n    view.dispatch(tr);\n    return false;\n}\nfunction drawGapCursor(state) {\n    if (!(state.selection instanceof GapCursor))\n        return null;\n    let node = document.createElement(\"div\");\n    node.className = \"ProseMirror-gapcursor\";\n    return DecorationSet.create(state.doc, [Decoration.widget(state.selection.head, node, { key: \"gapcursor\" })]);\n}\n\nexport { GapCursor, gapCursor };\n", "import {\n  callOrReturn,\n  Extension,\n  getExtensionField,\n  ParentConfig,\n} from '@tiptap/core'\nimport { gapCursor } from '@tiptap/pm/gapcursor'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options, Storage> {\n    /**\n     * A function to determine whether the gap cursor is allowed at the current position. Must return `true` or `false`.\n     * @default null\n     */\n    allowGapCursor?:\n      | boolean\n      | null\n      | ((this: {\n        name: string,\n        options: Options,\n        storage: Storage,\n        parent: ParentConfig<NodeConfig<Options>>['allowGapCursor'],\n      }) => boolean | null),\n  }\n}\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nexport const Gapcursor = Extension.create({\n  name: 'gapCursor',\n\n  addProseMirrorPlugins() {\n    return [\n      gapCursor(),\n    ]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      allowGapCursor: callOrReturn(getExtensionField(extension, 'allowGapCursor', context)) ?? null,\n    }\n  },\n})\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface HardBreakOptions {\n  /**\n   * Controls if marks should be kept after being split by a hard break.\n   * @default true\n   * @example false\n   */\n  keepMarks: boolean,\n\n  /**\n   * HTML attributes to add to the hard break element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    hardBreak: {\n      /**\n       * Add a hard break\n       * @example editor.commands.setHardBreak()\n       */\n      setHardBreak: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to insert hard breaks.\n * @see https://www.tiptap.dev/api/nodes/hard-break\n */\nexport const HardBreak = Node.create<HardBreakOptions>({\n  name: 'hardBreak',\n\n  addOptions() {\n    return {\n      keepMarks: true,\n      HTMLAttributes: {},\n    }\n  },\n\n  inline: true,\n\n  group: 'inline',\n\n  selectable: false,\n\n  linebreakReplacement: true,\n\n  parseHTML() {\n    return [\n      { tag: 'br' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['br', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  renderText() {\n    return '\\n'\n  },\n\n  addCommands() {\n    return {\n      setHardBreak: () => ({\n        commands,\n        chain,\n        state,\n        editor,\n      }) => {\n        return commands.first([\n          () => commands.exitCode(),\n          () => commands.command(() => {\n            const { selection, storedMarks } = state\n\n            if (selection.$from.parent.type.spec.isolating) {\n              return false\n            }\n\n            const { keepMarks } = this.options\n            const { splittableMarks } = editor.extensionManager\n            const marks = storedMarks\n              || (selection.$to.parentOffset && selection.$from.marks())\n\n            return chain()\n              .insertContent({ type: this.name })\n              .command(({ tr, dispatch }) => {\n                if (dispatch && marks && keepMarks) {\n                  const filteredMarks = marks\n                    .filter(mark => splittableMarks.includes(mark.type.name))\n\n                  tr.ensureMarks(filteredMarks)\n                }\n\n                return true\n              })\n              .run()\n          }),\n        ])\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Enter': () => this.editor.commands.setHardBreak(),\n      'Shift-Enter': () => this.editor.commands.setHardBreak(),\n    }\n  },\n})\n", "import { mergeAttributes, Node, textblockTypeInputRule } from '@tiptap/core'\n\n/**\n * The heading level options.\n */\nexport type Level = 1 | 2 | 3 | 4 | 5 | 6\n\nexport interface HeadingOptions {\n  /**\n   * The available heading levels.\n   * @default [1, 2, 3, 4, 5, 6]\n   * @example [1, 2, 3]\n   */\n  levels: Level[],\n\n  /**\n   * The HTML attributes for a heading node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    heading: {\n      /**\n       * Set a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.setHeading({ level: 1 })\n       */\n      setHeading: (attributes: { level: Level }) => ReturnType,\n      /**\n       * Toggle a heading node\n       * @param attributes The heading attributes\n       * @example editor.commands.toggleHeading({ level: 1 })\n       */\n      toggleHeading: (attributes: { level: Level }) => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create headings.\n * @see https://www.tiptap.dev/api/nodes/heading\n */\nexport const Heading = Node.create<HeadingOptions>({\n  name: 'heading',\n\n  addOptions() {\n    return {\n      levels: [1, 2, 3, 4, 5, 6],\n      HTMLAttributes: {},\n    }\n  },\n\n  content: 'inline*',\n\n  group: 'block',\n\n  defining: true,\n\n  addAttributes() {\n    return {\n      level: {\n        default: 1,\n        rendered: false,\n      },\n    }\n  },\n\n  parseHTML() {\n    return this.options.levels\n      .map((level: Level) => ({\n        tag: `h${level}`,\n        attrs: { level },\n      }))\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    const hasLevel = this.options.levels.includes(node.attrs.level)\n    const level = hasLevel\n      ? node.attrs.level\n      : this.options.levels[0]\n\n    return [`h${level}`, mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setHeading: attributes => ({ commands }) => {\n        if (!this.options.levels.includes(attributes.level)) {\n          return false\n        }\n\n        return commands.setNode(this.name, attributes)\n      },\n      toggleHeading: attributes => ({ commands }) => {\n        if (!this.options.levels.includes(attributes.level)) {\n          return false\n        }\n\n        return commands.toggleNode(this.name, 'paragraph', attributes)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return this.options.levels.reduce((items, level) => ({\n      ...items,\n      ...{\n        [`Mod-Alt-${level}`]: () => this.editor.commands.toggleHeading({ level }),\n      },\n    }), {})\n  },\n\n  addInputRules() {\n    return this.options.levels.map(level => {\n      return textblockTypeInputRule({\n        find: new RegExp(`^(#{${Math.min(...this.options.levels)},${level}})\\\\s$`),\n        type: this.type,\n        getAttributes: {\n          level,\n        },\n      })\n    })\n  },\n})\n", "var GOOD_LEAF_SIZE = 200;\n\n// :: class<T> A rope sequence is a persistent sequence data structure\n// that supports appending, prepending, and slicing without doing a\n// full copy. It is represented as a mostly-balanced tree.\nvar RopeSequence = function RopeSequence () {};\n\nRopeSequence.prototype.append = function append (other) {\n  if (!other.length) { return this }\n  other = RopeSequence.from(other);\n\n  return (!this.length && other) ||\n    (other.length < GOOD_LEAF_SIZE && this.leafAppend(other)) ||\n    (this.length < GOOD_LEAF_SIZE && other.leafPrepend(this)) ||\n    this.appendInner(other)\n};\n\n// :: (union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Prepend an array or other rope to this one, returning a new rope.\nRopeSequence.prototype.prepend = function prepend (other) {\n  if (!other.length) { return this }\n  return RopeSequence.from(other).append(this)\n};\n\nRopeSequence.prototype.appendInner = function appendInner (other) {\n  return new Append(this, other)\n};\n\n// :: (?number, ?number) → RopeSequence<T>\n// Create a rope repesenting a sub-sequence of this rope.\nRopeSequence.prototype.slice = function slice (from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  if (from >= to) { return RopeSequence.empty }\n  return this.sliceInner(Math.max(0, from), Math.min(this.length, to))\n};\n\n// :: (number) → T\n// Retrieve the element at the given position from this rope.\nRopeSequence.prototype.get = function get (i) {\n  if (i < 0 || i >= this.length) { return undefined }\n  return this.getInner(i)\n};\n\n// :: ((element: T, index: number) → ?bool, ?number, ?number)\n// Call the given function for each element between the given\n// indices. This tends to be more efficient than looping over the\n// indices and calling `get`, because it doesn't have to descend the\n// tree for every element.\nRopeSequence.prototype.forEach = function forEach (f, from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  if (from <= to)\n    { this.forEachInner(f, from, to, 0); }\n  else\n    { this.forEachInvertedInner(f, from, to, 0); }\n};\n\n// :: ((element: T, index: number) → U, ?number, ?number) → [U]\n// Map the given functions over the elements of the rope, producing\n// a flat array.\nRopeSequence.prototype.map = function map (f, from, to) {\n    if ( from === void 0 ) from = 0;\n    if ( to === void 0 ) to = this.length;\n\n  var result = [];\n  this.forEach(function (elt, i) { return result.push(f(elt, i)); }, from, to);\n  return result\n};\n\n// :: (?union<[T], RopeSequence<T>>) → RopeSequence<T>\n// Create a rope representing the given array, or return the rope\n// itself if a rope was given.\nRopeSequence.from = function from (values) {\n  if (values instanceof RopeSequence) { return values }\n  return values && values.length ? new Leaf(values) : RopeSequence.empty\n};\n\nvar Leaf = /*@__PURE__*/(function (RopeSequence) {\n  function Leaf(values) {\n    RopeSequence.call(this);\n    this.values = values;\n  }\n\n  if ( RopeSequence ) Leaf.__proto__ = RopeSequence;\n  Leaf.prototype = Object.create( RopeSequence && RopeSequence.prototype );\n  Leaf.prototype.constructor = Leaf;\n\n  var prototypeAccessors = { length: { configurable: true },depth: { configurable: true } };\n\n  Leaf.prototype.flatten = function flatten () {\n    return this.values\n  };\n\n  Leaf.prototype.sliceInner = function sliceInner (from, to) {\n    if (from == 0 && to == this.length) { return this }\n    return new Leaf(this.values.slice(from, to))\n  };\n\n  Leaf.prototype.getInner = function getInner (i) {\n    return this.values[i]\n  };\n\n  Leaf.prototype.forEachInner = function forEachInner (f, from, to, start) {\n    for (var i = from; i < to; i++)\n      { if (f(this.values[i], start + i) === false) { return false } }\n  };\n\n  Leaf.prototype.forEachInvertedInner = function forEachInvertedInner (f, from, to, start) {\n    for (var i = from - 1; i >= to; i--)\n      { if (f(this.values[i], start + i) === false) { return false } }\n  };\n\n  Leaf.prototype.leafAppend = function leafAppend (other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE)\n      { return new Leaf(this.values.concat(other.flatten())) }\n  };\n\n  Leaf.prototype.leafPrepend = function leafPrepend (other) {\n    if (this.length + other.length <= GOOD_LEAF_SIZE)\n      { return new Leaf(other.flatten().concat(this.values)) }\n  };\n\n  prototypeAccessors.length.get = function () { return this.values.length };\n\n  prototypeAccessors.depth.get = function () { return 0 };\n\n  Object.defineProperties( Leaf.prototype, prototypeAccessors );\n\n  return Leaf;\n}(RopeSequence));\n\n// :: RopeSequence\n// The empty rope sequence.\nRopeSequence.empty = new Leaf([]);\n\nvar Append = /*@__PURE__*/(function (RopeSequence) {\n  function Append(left, right) {\n    RopeSequence.call(this);\n    this.left = left;\n    this.right = right;\n    this.length = left.length + right.length;\n    this.depth = Math.max(left.depth, right.depth) + 1;\n  }\n\n  if ( RopeSequence ) Append.__proto__ = RopeSequence;\n  Append.prototype = Object.create( RopeSequence && RopeSequence.prototype );\n  Append.prototype.constructor = Append;\n\n  Append.prototype.flatten = function flatten () {\n    return this.left.flatten().concat(this.right.flatten())\n  };\n\n  Append.prototype.getInner = function getInner (i) {\n    return i < this.left.length ? this.left.get(i) : this.right.get(i - this.left.length)\n  };\n\n  Append.prototype.forEachInner = function forEachInner (f, from, to, start) {\n    var leftLen = this.left.length;\n    if (from < leftLen &&\n        this.left.forEachInner(f, from, Math.min(to, leftLen), start) === false)\n      { return false }\n    if (to > leftLen &&\n        this.right.forEachInner(f, Math.max(from - leftLen, 0), Math.min(this.length, to) - leftLen, start + leftLen) === false)\n      { return false }\n  };\n\n  Append.prototype.forEachInvertedInner = function forEachInvertedInner (f, from, to, start) {\n    var leftLen = this.left.length;\n    if (from > leftLen &&\n        this.right.forEachInvertedInner(f, from - leftLen, Math.max(to, leftLen) - leftLen, start + leftLen) === false)\n      { return false }\n    if (to < leftLen &&\n        this.left.forEachInvertedInner(f, Math.min(from, leftLen), to, start) === false)\n      { return false }\n  };\n\n  Append.prototype.sliceInner = function sliceInner (from, to) {\n    if (from == 0 && to == this.length) { return this }\n    var leftLen = this.left.length;\n    if (to <= leftLen) { return this.left.slice(from, to) }\n    if (from >= leftLen) { return this.right.slice(from - leftLen, to - leftLen) }\n    return this.left.slice(from, leftLen).append(this.right.slice(0, to - leftLen))\n  };\n\n  Append.prototype.leafAppend = function leafAppend (other) {\n    var inner = this.right.leafAppend(other);\n    if (inner) { return new Append(this.left, inner) }\n  };\n\n  Append.prototype.leafPrepend = function leafPrepend (other) {\n    var inner = this.left.leafPrepend(other);\n    if (inner) { return new Append(inner, this.right) }\n  };\n\n  Append.prototype.appendInner = function appendInner (other) {\n    if (this.left.depth >= Math.max(this.right.depth, other.depth) + 1)\n      { return new Append(this.left, new Append(this.right, other)) }\n    return new Append(this, other)\n  };\n\n  return Append;\n}(RopeSequence));\n\nexport default RopeSequence;\n", "import RopeSequence from 'rope-sequence';\nimport { Mapping } from 'prosemirror-transform';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>, Plugin } from 'prosemirror-state';\n\n// ProseMirror's history isn't simply a way to roll back to a previous\n// state, because ProseMirror supports applying changes without adding\n// them to the history (for example during collaboration).\n//\n// To this end, each 'Branch' (one for the undo history and one for\n// the redo history) keeps an array of 'Items', which can optionally\n// hold a step (an actual undoable change), and always hold a position\n// map (which is needed to move changes below them to apply to the\n// current document).\n//\n// An item that has both a step and a selection bookmark is the start\n// of an 'event' — a group of changes that will be undone or redone at\n// once. (It stores only the bookmark, since that way we don't have to\n// provide a document until the selection is actually applied, which\n// is useful when compressing.)\n// Used to schedule history compression\nconst max_empty_items = 500;\nclass Branch {\n    constructor(items, eventCount) {\n        this.items = items;\n        this.eventCount = eventCount;\n    }\n    // Pop the latest event off the branch's history and apply it\n    // to a document transform.\n    popEvent(state, preserveItems) {\n        if (this.eventCount == 0)\n            return null;\n        let end = this.items.length;\n        for (;; end--) {\n            let next = this.items.get(end - 1);\n            if (next.selection) {\n                --end;\n                break;\n            }\n        }\n        let remap, mapFrom;\n        if (preserveItems) {\n            remap = this.remapping(end, this.items.length);\n            mapFrom = remap.maps.length;\n        }\n        let transform = state.tr;\n        let selection, remaining;\n        let addAfter = [], addBefore = [];\n        this.items.forEach((item, i) => {\n            if (!item.step) {\n                if (!remap) {\n                    remap = this.remapping(end, i + 1);\n                    mapFrom = remap.maps.length;\n                }\n                mapFrom--;\n                addBefore.push(item);\n                return;\n            }\n            if (remap) {\n                addBefore.push(new Item(item.map));\n                let step = item.step.map(remap.slice(mapFrom)), map;\n                if (step && transform.maybeStep(step).doc) {\n                    map = transform.mapping.maps[transform.mapping.maps.length - 1];\n                    addAfter.push(new Item(map, undefined, undefined, addAfter.length + addBefore.length));\n                }\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n            }\n            else {\n                transform.maybeStep(item.step);\n            }\n            if (item.selection) {\n                selection = remap ? item.selection.map(remap.slice(mapFrom)) : item.selection;\n                remaining = new Branch(this.items.slice(0, end).append(addBefore.reverse().concat(addAfter)), this.eventCount - 1);\n                return false;\n            }\n        }, this.items.length, 0);\n        return { remaining: remaining, transform, selection: selection };\n    }\n    // Create a new branch with the given transform added.\n    addTransform(transform, selection, histOptions, preserveItems) {\n        let newItems = [], eventCount = this.eventCount;\n        let oldItems = this.items, lastItem = !preserveItems && oldItems.length ? oldItems.get(oldItems.length - 1) : null;\n        for (let i = 0; i < transform.steps.length; i++) {\n            let step = transform.steps[i].invert(transform.docs[i]);\n            let item = new Item(transform.mapping.maps[i], step, selection), merged;\n            if (merged = lastItem && lastItem.merge(item)) {\n                item = merged;\n                if (i)\n                    newItems.pop();\n                else\n                    oldItems = oldItems.slice(0, oldItems.length - 1);\n            }\n            newItems.push(item);\n            if (selection) {\n                eventCount++;\n                selection = undefined;\n            }\n            if (!preserveItems)\n                lastItem = item;\n        }\n        let overflow = eventCount - histOptions.depth;\n        if (overflow > DEPTH_OVERFLOW) {\n            oldItems = cutOffEvents(oldItems, overflow);\n            eventCount -= overflow;\n        }\n        return new Branch(oldItems.append(newItems), eventCount);\n    }\n    remapping(from, to) {\n        let maps = new Mapping;\n        this.items.forEach((item, i) => {\n            let mirrorPos = item.mirrorOffset != null && i - item.mirrorOffset >= from\n                ? maps.maps.length - item.mirrorOffset : undefined;\n            maps.appendMap(item.map, mirrorPos);\n        }, from, to);\n        return maps;\n    }\n    addMaps(array) {\n        if (this.eventCount == 0)\n            return this;\n        return new Branch(this.items.append(array.map(map => new Item(map))), this.eventCount);\n    }\n    // When the collab module receives remote changes, the history has\n    // to know about those, so that it can adjust the steps that were\n    // rebased on top of the remote changes, and include the position\n    // maps for the remote changes in its array of items.\n    rebased(rebasedTransform, rebasedCount) {\n        if (!this.eventCount)\n            return this;\n        let rebasedItems = [], start = Math.max(0, this.items.length - rebasedCount);\n        let mapping = rebasedTransform.mapping;\n        let newUntil = rebasedTransform.steps.length;\n        let eventCount = this.eventCount;\n        this.items.forEach(item => { if (item.selection)\n            eventCount--; }, start);\n        let iRebased = rebasedCount;\n        this.items.forEach(item => {\n            let pos = mapping.getMirror(--iRebased);\n            if (pos == null)\n                return;\n            newUntil = Math.min(newUntil, pos);\n            let map = mapping.maps[pos];\n            if (item.step) {\n                let step = rebasedTransform.steps[pos].invert(rebasedTransform.docs[pos]);\n                let selection = item.selection && item.selection.map(mapping.slice(iRebased + 1, pos));\n                if (selection)\n                    eventCount++;\n                rebasedItems.push(new Item(map, step, selection));\n            }\n            else {\n                rebasedItems.push(new Item(map));\n            }\n        }, start);\n        let newMaps = [];\n        for (let i = rebasedCount; i < newUntil; i++)\n            newMaps.push(new Item(mapping.maps[i]));\n        let items = this.items.slice(0, start).append(newMaps).append(rebasedItems);\n        let branch = new Branch(items, eventCount);\n        if (branch.emptyItemCount() > max_empty_items)\n            branch = branch.compress(this.items.length - rebasedItems.length);\n        return branch;\n    }\n    emptyItemCount() {\n        let count = 0;\n        this.items.forEach(item => { if (!item.step)\n            count++; });\n        return count;\n    }\n    // Compressing a branch means rewriting it to push the air (map-only\n    // items) out. During collaboration, these naturally accumulate\n    // because each remote change adds one. The `upto` argument is used\n    // to ensure that only the items below a given level are compressed,\n    // because `rebased` relies on a clean, untouched set of items in\n    // order to associate old items with rebased steps.\n    compress(upto = this.items.length) {\n        let remap = this.remapping(0, upto), mapFrom = remap.maps.length;\n        let items = [], events = 0;\n        this.items.forEach((item, i) => {\n            if (i >= upto) {\n                items.push(item);\n                if (item.selection)\n                    events++;\n            }\n            else if (item.step) {\n                let step = item.step.map(remap.slice(mapFrom)), map = step && step.getMap();\n                mapFrom--;\n                if (map)\n                    remap.appendMap(map, mapFrom);\n                if (step) {\n                    let selection = item.selection && item.selection.map(remap.slice(mapFrom));\n                    if (selection)\n                        events++;\n                    let newItem = new Item(map.invert(), step, selection), merged, last = items.length - 1;\n                    if (merged = items.length && items[last].merge(newItem))\n                        items[last] = merged;\n                    else\n                        items.push(newItem);\n                }\n            }\n            else if (item.map) {\n                mapFrom--;\n            }\n        }, this.items.length, 0);\n        return new Branch(RopeSequence.from(items.reverse()), events);\n    }\n}\nBranch.empty = new Branch(RopeSequence.empty, 0);\nfunction cutOffEvents(items, n) {\n    let cutPoint;\n    items.forEach((item, i) => {\n        if (item.selection && (n-- == 0)) {\n            cutPoint = i;\n            return false;\n        }\n    });\n    return items.slice(cutPoint);\n}\nclass Item {\n    constructor(\n    // The (forward) step map for this item.\n    map, \n    // The inverted step\n    step, \n    // If this is non-null, this item is the start of a group, and\n    // this selection is the starting selection for the group (the one\n    // that was active before the first step was applied)\n    selection, \n    // If this item is the inverse of a previous mapping on the stack,\n    // this points at the inverse's offset\n    mirrorOffset) {\n        this.map = map;\n        this.step = step;\n        this.selection = selection;\n        this.mirrorOffset = mirrorOffset;\n    }\n    merge(other) {\n        if (this.step && other.step && !other.selection) {\n            let step = other.step.merge(this.step);\n            if (step)\n                return new Item(step.getMap().invert(), step, this.selection);\n        }\n    }\n}\n// The value of the state field that tracks undo/redo history for that\n// state. Will be stored in the plugin state when the history plugin\n// is active.\nclass HistoryState {\n    constructor(done, undone, prevRanges, prevTime, prevComposition) {\n        this.done = done;\n        this.undone = undone;\n        this.prevRanges = prevRanges;\n        this.prevTime = prevTime;\n        this.prevComposition = prevComposition;\n    }\n}\nconst DEPTH_OVERFLOW = 20;\n// Record a transformation in undo history.\nfunction applyTransaction(history, state, tr, options) {\n    let historyTr = tr.getMeta(historyKey), rebased;\n    if (historyTr)\n        return historyTr.historyState;\n    if (tr.getMeta(closeHistoryKey))\n        history = new HistoryState(history.done, history.undone, null, 0, -1);\n    let appended = tr.getMeta(\"appendedTransaction\");\n    if (tr.steps.length == 0) {\n        return history;\n    }\n    else if (appended && appended.getMeta(historyKey)) {\n        if (appended.getMeta(historyKey).redo)\n            return new HistoryState(history.done.addTransform(tr, undefined, options, mustPreserveItems(state)), history.undone, rangesFor(tr.mapping.maps), history.prevTime, history.prevComposition);\n        else\n            return new HistoryState(history.done, history.undone.addTransform(tr, undefined, options, mustPreserveItems(state)), null, history.prevTime, history.prevComposition);\n    }\n    else if (tr.getMeta(\"addToHistory\") !== false && !(appended && appended.getMeta(\"addToHistory\") === false)) {\n        // Group transforms that occur in quick succession into one event.\n        let composition = tr.getMeta(\"composition\");\n        let newGroup = history.prevTime == 0 ||\n            (!appended && history.prevComposition != composition &&\n                (history.prevTime < (tr.time || 0) - options.newGroupDelay || !isAdjacentTo(tr, history.prevRanges)));\n        let prevRanges = appended ? mapRanges(history.prevRanges, tr.mapping) : rangesFor(tr.mapping.maps);\n        return new HistoryState(history.done.addTransform(tr, newGroup ? state.selection.getBookmark() : undefined, options, mustPreserveItems(state)), Branch.empty, prevRanges, tr.time, composition == null ? history.prevComposition : composition);\n    }\n    else if (rebased = tr.getMeta(\"rebased\")) {\n        // Used by the collab module to tell the history that some of its\n        // content has been rebased.\n        return new HistoryState(history.done.rebased(tr, rebased), history.undone.rebased(tr, rebased), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n    else {\n        return new HistoryState(history.done.addMaps(tr.mapping.maps), history.undone.addMaps(tr.mapping.maps), mapRanges(history.prevRanges, tr.mapping), history.prevTime, history.prevComposition);\n    }\n}\nfunction isAdjacentTo(transform, prevRanges) {\n    if (!prevRanges)\n        return false;\n    if (!transform.docChanged)\n        return true;\n    let adjacent = false;\n    transform.mapping.maps[0].forEach((start, end) => {\n        for (let i = 0; i < prevRanges.length; i += 2)\n            if (start <= prevRanges[i + 1] && end >= prevRanges[i])\n                adjacent = true;\n    });\n    return adjacent;\n}\nfunction rangesFor(maps) {\n    let result = [];\n    for (let i = maps.length - 1; i >= 0 && result.length == 0; i--)\n        maps[i].forEach((_from, _to, from, to) => result.push(from, to));\n    return result;\n}\nfunction mapRanges(ranges, mapping) {\n    if (!ranges)\n        return null;\n    let result = [];\n    for (let i = 0; i < ranges.length; i += 2) {\n        let from = mapping.map(ranges[i], 1), to = mapping.map(ranges[i + 1], -1);\n        if (from <= to)\n            result.push(from, to);\n    }\n    return result;\n}\n// Apply the latest event from one branch to the document and shift the event\n// onto the other branch.\nfunction histTransaction(history, state, redo) {\n    let preserveItems = mustPreserveItems(state);\n    let histOptions = historyKey.get(state).spec.config;\n    let pop = (redo ? history.undone : history.done).popEvent(state, preserveItems);\n    if (!pop)\n        return null;\n    let selection = pop.selection.resolve(pop.transform.doc);\n    let added = (redo ? history.done : history.undone).addTransform(pop.transform, state.selection.getBookmark(), histOptions, preserveItems);\n    let newHist = new HistoryState(redo ? added : pop.remaining, redo ? pop.remaining : added, null, 0, -1);\n    return pop.transform.setSelection(selection).setMeta(historyKey, { redo, historyState: newHist });\n}\nlet cachedPreserveItems = false, cachedPreserveItemsPlugins = null;\n// Check whether any plugin in the given state has a\n// `historyPreserveItems` property in its spec, in which case we must\n// preserve steps exactly as they came in, so that they can be\n// rebased.\nfunction mustPreserveItems(state) {\n    let plugins = state.plugins;\n    if (cachedPreserveItemsPlugins != plugins) {\n        cachedPreserveItems = false;\n        cachedPreserveItemsPlugins = plugins;\n        for (let i = 0; i < plugins.length; i++)\n            if (plugins[i].spec.historyPreserveItems) {\n                cachedPreserveItems = true;\n                break;\n            }\n    }\n    return cachedPreserveItems;\n}\n/**\nSet a flag on the given transaction that will prevent further steps\nfrom being appended to an existing history event (so that they\nrequire a separate undo command to undo).\n*/\nfunction closeHistory(tr) {\n    return tr.setMeta(closeHistoryKey, true);\n}\nconst historyKey = new PluginKey(\"history\");\nconst closeHistoryKey = new PluginKey(\"closeHistory\");\n/**\nReturns a plugin that enables the undo history for an editor. The\nplugin will track undo and redo stacks, which can be used with the\n[`undo`](https://prosemirror.net/docs/ref/#history.undo) and [`redo`](https://prosemirror.net/docs/ref/#history.redo) commands.\n\nYou can set an `\"addToHistory\"` [metadata\nproperty](https://prosemirror.net/docs/ref/#state.Transaction.setMeta) of `false` on a transaction\nto prevent it from being rolled back by undo.\n*/\nfunction history(config = {}) {\n    config = { depth: config.depth || 100,\n        newGroupDelay: config.newGroupDelay || 500 };\n    return new Plugin({\n        key: historyKey,\n        state: {\n            init() {\n                return new HistoryState(Branch.empty, Branch.empty, null, 0, -1);\n            },\n            apply(tr, hist, state) {\n                return applyTransaction(hist, state, tr, config);\n            }\n        },\n        config,\n        props: {\n            handleDOMEvents: {\n                beforeinput(view, e) {\n                    let inputType = e.inputType;\n                    let command = inputType == \"historyUndo\" ? undo : inputType == \"historyRedo\" ? redo : null;\n                    if (!command)\n                        return false;\n                    e.preventDefault();\n                    return command(view.state, view.dispatch);\n                }\n            }\n        }\n    });\n}\nfunction buildCommand(redo, scroll) {\n    return (state, dispatch) => {\n        let hist = historyKey.getState(state);\n        if (!hist || (redo ? hist.undone : hist.done).eventCount == 0)\n            return false;\n        if (dispatch) {\n            let tr = histTransaction(hist, state, redo);\n            if (tr)\n                dispatch(scroll ? tr.scrollIntoView() : tr);\n        }\n        return true;\n    };\n}\n/**\nA command function that undoes the last change, if any.\n*/\nconst undo = buildCommand(false, true);\n/**\nA command function that redoes the last undone change, if any.\n*/\nconst redo = buildCommand(true, true);\n/**\nA command function that undoes the last change. Don't scroll the\nselection into view.\n*/\nconst undoNoScroll = buildCommand(false, false);\n/**\nA command function that redoes the last undone change. Don't\nscroll the selection into view.\n*/\nconst redoNoScroll = buildCommand(true, false);\n/**\nThe amount of undoable events available in a given state.\n*/\nfunction undoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.done.eventCount : 0;\n}\n/**\nThe amount of redoable events available in a given editor state.\n*/\nfunction redoDepth(state) {\n    let hist = historyKey.getState(state);\n    return hist ? hist.undone.eventCount : 0;\n}\n\nexport { closeHistory, history, redo, redoDepth, redoNoScroll, undo, undoDepth, undoNoScroll };\n", "import { Extension } from '@tiptap/core'\nimport { history, redo, undo } from '@tiptap/pm/history'\n\nexport interface HistoryOptions {\n  /**\n   * The amount of history events that are collected before the oldest events are discarded.\n   * @default 100\n   * @example 50\n   */\n  depth: number,\n\n  /**\n   * The delay (in milliseconds) between changes after which a new group should be started.\n   * @default 500\n   * @example 1000\n   */\n  newGroupDelay: number,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    history: {\n      /**\n       * Undo recent changes\n       * @example editor.commands.undo()\n       */\n      undo: () => ReturnType,\n      /**\n       * Reapply reverted changes\n       * @example editor.commands.redo()\n       */\n      redo: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to undo and redo recent changes.\n * @see https://www.tiptap.dev/api/extensions/history\n *\n * **Important**: If the `@tiptap/extension-collaboration` package is used, make sure to remove\n * the `history` extension, as it is not compatible with the `collaboration` extension.\n *\n * `@tiptap/extension-collaboration` uses its own history implementation.\n */\nexport const History = Extension.create<HistoryOptions>({\n  name: 'history',\n\n  addOptions() {\n    return {\n      depth: 100,\n      newGroupDelay: 500,\n    }\n  },\n\n  addCommands() {\n    return {\n      undo: () => ({ state, dispatch }) => {\n        return undo(state, dispatch)\n      },\n      redo: () => ({ state, dispatch }) => {\n        return redo(state, dispatch)\n      },\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      history(this.options),\n    ]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-z': () => this.editor.commands.undo(),\n      'Shift-Mod-z': () => this.editor.commands.redo(),\n      'Mod-y': () => this.editor.commands.redo(),\n\n      // Russian keyboard layouts\n      'Mod-я': () => this.editor.commands.undo(),\n      'Shift-Mod-я': () => this.editor.commands.redo(),\n    }\n  },\n})\n", "import {\n  isNodeSelection, mergeAttributes, Node, nodeInputRule,\n} from '@tiptap/core'\nimport { NodeSelection, TextSelection } from '@tiptap/pm/state'\n\nexport interface HorizontalRuleOptions {\n  /**\n   * The HTML attributes for a horizontal rule node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    horizontalRule: {\n      /**\n       * Add a horizontal rule\n       * @example editor.commands.setHorizontalRule()\n       */\n      setHorizontalRule: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nexport const HorizontalRule = Node.create<HorizontalRuleOptions>({\n  name: 'horizontalRule',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'hr' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['hr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setHorizontalRule:\n        () => ({ chain, state }) => {\n          const { selection } = state\n          const { $from: $originFrom, $to: $originTo } = selection\n\n          const currentChain = chain()\n\n          if ($originFrom.parentOffset === 0) {\n            currentChain.insertContentAt(\n              {\n                from: Math.max($originFrom.pos - 1, 0),\n                to: $originTo.pos,\n              },\n              {\n                type: this.name,\n              },\n            )\n          } else if (isNodeSelection(selection)) {\n            currentChain.insertContentAt($originTo.pos, {\n              type: this.name,\n            })\n          } else {\n            currentChain.insertContent({ type: this.name })\n          }\n\n          return (\n            currentChain\n              // set cursor after horizontal rule\n              .command(({ tr, dispatch }) => {\n                if (dispatch) {\n                  const { $to } = tr.selection\n                  const posAfter = $to.end()\n\n                  if ($to.nodeAfter) {\n                    if ($to.nodeAfter.isTextblock) {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos + 1))\n                    } else if ($to.nodeAfter.isBlock) {\n                      tr.setSelection(NodeSelection.create(tr.doc, $to.pos))\n                    } else {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos))\n                    }\n                  } else {\n                    // add node after horizontal rule if it’s the end of the document\n                    const node = $to.parent.type.contentMatch.defaultType?.create()\n\n                    if (node) {\n                      tr.insert(posAfter, node)\n                      tr.setSelection(TextSelection.create(tr.doc, posAfter + 1))\n                    }\n                  }\n\n                  tr.scrollIntoView()\n                }\n\n                return true\n              })\n              .run()\n          )\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface ItalicOptions {\n  /**\n   * HTML attributes to add to the italic element.\n   * @default {}\n   * @example { class: 'foo' }\n  */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    italic: {\n      /**\n       * Set an italic mark\n       * @example editor.commands.setItalic()\n       */\n      setItalic: () => ReturnType,\n      /**\n       * Toggle an italic mark\n       * @example editor.commands.toggleItalic()\n       */\n      toggleItalic: () => ReturnType,\n      /**\n       * Unset an italic mark\n       * @example editor.commands.unsetItalic()\n       */\n      unsetItalic: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an italic to a *italic* on input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))$/\n\n/**\n * Matches an italic to a *italic* on paste.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*(?!\\s+\\*)((?:[^*]+))\\*(?!\\s+\\*))/g\n\n/**\n * Matches an italic to a _italic_ on input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))$/\n\n/**\n * Matches an italic to a _italic_ on paste.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(_(?!\\s+_)((?:[^_]+))_(?!\\s+_))/g\n\n/**\n * This extension allows you to create italic text.\n * @see https://www.tiptap.dev/api/marks/italic\n */\nexport const Italic = Mark.create<ItalicOptions>({\n  name: 'italic',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'em',\n      },\n      {\n        tag: 'i',\n        getAttrs: node => (node as HTMLElement).style.fontStyle !== 'normal' && null,\n      },\n      {\n        style: 'font-style=normal',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-style=italic',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['em', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setItalic: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleItalic: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetItalic: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-i': () => this.editor.commands.toggleItalic(),\n      'Mod-I': () => this.editor.commands.toggleItalic(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ListItemOptions {\n  /**\n   * The HTML attributes for a list item node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * The node type for bulletList nodes\n   * @default 'bulletList'\n   * @example 'myCustomBulletList'\n   */\n  bulletListTypeName: string\n\n  /**\n   * The node type for orderedList nodes\n   * @default 'orderedList'\n   * @example 'myCustomOrderedList'\n   */\n  orderedListTypeName: string\n}\n\n/**\n * This extension allows you to create list items.\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const ListItem = Node.create<ListItemOptions>({\n  name: 'listItem',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      bulletListTypeName: 'bulletList',\n      orderedListTypeName: 'orderedList',\n    }\n  },\n\n  content: 'paragraph block*',\n\n  defining: true,\n\n  parseHTML() {\n    return [\n      {\n        tag: 'li',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['li', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Enter: () => this.editor.commands.splitListItem(this.name),\n      Tab: () => this.editor.commands.sinkListItem(this.name),\n      'Shift-Tab': () => this.editor.commands.liftListItem(this.name),\n    }\n  },\n})\n", "import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface OrderedListOptions {\n  /**\n   * The node type name for list items.\n   * @default 'listItem'\n   * @example 'myListItem'\n   */\n  itemTypeName: string,\n\n  /**\n   * The HTML attributes for an ordered list node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * Keep the marks when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean,\n\n  /**\n   * Keep the attributes when splitting a list item.\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    orderedList: {\n      /**\n       * Toggle an ordered list\n       * @example editor.commands.toggleOrderedList()\n       */\n      toggleOrderedList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches an ordered list to a 1. on input (or any number followed by a dot).\n */\nexport const inputRegex = /^(\\d+)\\.\\s$/\n\n/**\n * This extension allows you to create ordered lists.\n * This requires the ListItem extension\n * @see https://www.tiptap.dev/api/nodes/ordered-list\n * @see https://www.tiptap.dev/api/nodes/list-item\n */\nexport const OrderedList = Node.create<OrderedListOptions>({\n  name: 'orderedList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  addAttributes() {\n    return {\n      start: {\n        default: 1,\n        parseHTML: element => {\n          return element.hasAttribute('start')\n            ? parseInt(element.getAttribute('start') || '', 10)\n            : 1\n        },\n      },\n      type: {\n        default: null,\n        parseHTML: element => element.getAttribute('type'),\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'ol',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    const { start, ...attributesWithoutStart } = HTMLAttributes\n\n    return start === 1\n      ? ['ol', mergeAttributes(this.options.HTMLAttributes, attributesWithoutStart), 0]\n      : ['ol', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleOrderedList: () => ({ commands, chain }) => {\n        if (this.options.keepAttributes) {\n          return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run()\n        }\n        return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-7': () => this.editor.commands.toggleOrderedList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: inputRegex,\n      type: this.type,\n      getAttributes: match => ({ start: +match[1] }),\n      joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: match => ({ start: +match[1], ...this.editor.getAttributes(TextStyleName) }),\n        joinPredicate: (match, node) => node.childCount + node.attrs.start === +match[1],\n        editor: this.editor,\n      })\n    }\n    return [\n      inputRule,\n    ]\n  },\n})\n", "import { mergeAttributes, Node } from '@tiptap/core'\n\nexport interface ParagraphOptions {\n  /**\n   * The HTML attributes for a paragraph node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    paragraph: {\n      /**\n       * Toggle a paragraph\n       * @example editor.commands.toggleParagraph()\n       */\n      setParagraph: () => ReturnType,\n    }\n  }\n}\n\n/**\n * This extension allows you to create paragraphs.\n * @see https://www.tiptap.dev/api/nodes/paragraph\n */\nexport const Paragraph = Node.create<ParagraphOptions>({\n  name: 'paragraph',\n\n  priority: 1000,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  content: 'inline*',\n\n  parseHTML() {\n    return [\n      { tag: 'p' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['p', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setParagraph: () => ({ commands }) => {\n        return commands.setNode(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Alt-0': () => this.editor.commands.setParagraph(),\n    }\n  },\n})\n", "import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface StrikeOptions {\n  /**\n   * HTML attributes to add to the strike element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    strike: {\n      /**\n       * Set a strike mark\n       * @example editor.commands.setStrike()\n       */\n      setStrike: () => ReturnType,\n      /**\n       * Toggle a strike mark\n       * @example editor.commands.toggleStrike()\n       */\n      toggleStrike: () => ReturnType,\n      /**\n       * Unset a strike mark\n       * @example editor.commands.unsetStrike()\n       */\n      unsetStrike: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a strike to a ~~strike~~ on input.\n */\nexport const inputRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))$/\n\n/**\n * Matches a strike to a ~~strike~~ on paste.\n */\nexport const pasteRegex = /(?:^|\\s)(~~(?!\\s+~~)((?:[^~]+))~~(?!\\s+~~))/g\n\n/**\n * This extension allows you to create strike text.\n * @see https://www.tiptap.dev/api/marks/strike\n */\nexport const Strike = Mark.create<StrikeOptions>({\n  name: 'strike',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 's',\n      },\n      {\n        tag: 'del',\n      },\n      {\n        tag: 'strike',\n      },\n      {\n        style: 'text-decoration',\n        consuming: false,\n        getAttrs: style => ((style as string).includes('line-through') ? {} : false),\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['s', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setStrike: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleStrike: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetStrike: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-s': () => this.editor.commands.toggleStrike(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n", "import { Extension } from '@tiptap/core'\nimport { Blockquote, BlockquoteOptions } from '@tiptap/extension-blockquote'\nimport { Bold, BoldOptions } from '@tiptap/extension-bold'\nimport { BulletList, BulletListOptions } from '@tiptap/extension-bullet-list'\nimport { Code, CodeOptions } from '@tiptap/extension-code'\nimport { CodeBlock, CodeBlockOptions } from '@tiptap/extension-code-block'\nimport { Document } from '@tiptap/extension-document'\nimport { Dropcursor, DropcursorOptions } from '@tiptap/extension-dropcursor'\nimport { Gapcursor } from '@tiptap/extension-gapcursor'\nimport { HardBreak, HardBreakOptions } from '@tiptap/extension-hard-break'\nimport { Heading, HeadingOptions } from '@tiptap/extension-heading'\nimport { History, HistoryOptions } from '@tiptap/extension-history'\nimport { HorizontalRule, HorizontalRuleOptions } from '@tiptap/extension-horizontal-rule'\nimport { Italic, ItalicOptions } from '@tiptap/extension-italic'\nimport { ListItem, ListItemOptions } from '@tiptap/extension-list-item'\nimport { OrderedList, OrderedListOptions } from '@tiptap/extension-ordered-list'\nimport { Paragraph, ParagraphOptions } from '@tiptap/extension-paragraph'\nimport { Strike, StrikeOptions } from '@tiptap/extension-strike'\nimport { Text } from '@tiptap/extension-text'\n\nexport interface StarterKitOptions {\n  /**\n   * If set to false, the blockquote extension will not be registered\n   * @example blockquote: false\n   */\n  blockquote: Partial<BlockquoteOptions> | false,\n\n  /**\n   * If set to false, the bold extension will not be registered\n   * @example bold: false\n   */\n  bold: Partial<BoldOptions> | false,\n\n  /**\n   * If set to false, the bulletList extension will not be registered\n   * @example bulletList: false\n   */\n  bulletList: Partial<BulletListOptions> | false,\n\n  /**\n   * If set to false, the code extension will not be registered\n   * @example code: false\n   */\n  code: Partial<CodeOptions> | false,\n\n  /**\n   * If set to false, the codeBlock extension will not be registered\n   * @example codeBlock: false\n   */\n  codeBlock: Partial<CodeBlockOptions> | false,\n\n  /**\n   * If set to false, the document extension will not be registered\n   * @example document: false\n   */\n  document: false,\n\n  /**\n   * If set to false, the dropcursor extension will not be registered\n   * @example dropcursor: false\n   */\n  dropcursor: Partial<DropcursorOptions> | false,\n\n  /**\n   * If set to false, the gapcursor extension will not be registered\n   * @example gapcursor: false\n   */\n  gapcursor: false,\n\n  /**\n   * If set to false, the hardBreak extension will not be registered\n   * @example hardBreak: false\n   */\n  hardBreak: Partial<HardBreakOptions> | false,\n\n  /**\n   * If set to false, the heading extension will not be registered\n   * @example heading: false\n   */\n  heading: Partial<HeadingOptions> | false,\n\n  /**\n   * If set to false, the history extension will not be registered\n   * @example history: false\n   */\n  history: Partial<HistoryOptions> | false,\n\n  /**\n   * If set to false, the horizontalRule extension will not be registered\n   * @example horizontalRule: false\n   */\n  horizontalRule: Partial<HorizontalRuleOptions> | false,\n\n  /**\n   * If set to false, the italic extension will not be registered\n   * @example italic: false\n   */\n  italic: Partial<ItalicOptions> | false,\n\n  /**\n   * If set to false, the listItem extension will not be registered\n   * @example listItem: false\n   */\n  listItem: Partial<ListItemOptions> | false,\n\n  /**\n   * If set to false, the orderedList extension will not be registered\n   * @example orderedList: false\n   */\n  orderedList: Partial<OrderedListOptions> | false,\n\n  /**\n   * If set to false, the paragraph extension will not be registered\n   * @example paragraph: false\n   */\n  paragraph: Partial<ParagraphOptions> | false,\n\n  /**\n   * If set to false, the strike extension will not be registered\n   * @example strike: false\n   */\n  strike: Partial<StrikeOptions> | false,\n\n  /**\n   * If set to false, the text extension will not be registered\n   * @example text: false\n   */\n  text: false,\n}\n\n/**\n * The starter kit is a collection of essential editor extensions.\n *\n * It’s a good starting point for building your own editor.\n */\nexport const StarterKit = Extension.create<StarterKitOptions>({\n  name: 'starterKit',\n\n  addExtensions() {\n    const extensions = []\n\n    if (this.options.bold !== false) {\n      extensions.push(Bold.configure(this.options.bold))\n    }\n\n    if (this.options.blockquote !== false) {\n      extensions.push(Blockquote.configure(this.options.blockquote))\n    }\n\n    if (this.options.bulletList !== false) {\n      extensions.push(BulletList.configure(this.options.bulletList))\n    }\n\n    if (this.options.code !== false) {\n      extensions.push(Code.configure(this.options.code))\n    }\n\n    if (this.options.codeBlock !== false) {\n      extensions.push(CodeBlock.configure(this.options.codeBlock))\n    }\n\n    if (this.options.document !== false) {\n      extensions.push(Document.configure(this.options.document))\n    }\n\n    if (this.options.dropcursor !== false) {\n      extensions.push(Dropcursor.configure(this.options.dropcursor))\n    }\n\n    if (this.options.gapcursor !== false) {\n      extensions.push(Gapcursor.configure(this.options.gapcursor))\n    }\n\n    if (this.options.hardBreak !== false) {\n      extensions.push(HardBreak.configure(this.options.hardBreak))\n    }\n\n    if (this.options.heading !== false) {\n      extensions.push(Heading.configure(this.options.heading))\n    }\n\n    if (this.options.history !== false) {\n      extensions.push(History.configure(this.options.history))\n    }\n\n    if (this.options.horizontalRule !== false) {\n      extensions.push(HorizontalRule.configure(this.options.horizontalRule))\n    }\n\n    if (this.options.italic !== false) {\n      extensions.push(Italic.configure(this.options.italic))\n    }\n\n    if (this.options.listItem !== false) {\n      extensions.push(ListItem.configure(this.options.listItem))\n    }\n\n    if (this.options.orderedList !== false) {\n      extensions.push(OrderedList.configure(this.options.orderedList))\n    }\n\n    if (this.options.paragraph !== false) {\n      extensions.push(Paragraph.configure(this.options.paragraph))\n    }\n\n    if (this.options.strike !== false) {\n      extensions.push(Strike.configure(this.options.strike))\n    }\n\n    if (this.options.text !== false) {\n      extensions.push(Text.configure(this.options.text))\n    }\n\n    return extensions\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCO,IAAM,aAAa;AAMb,IAAA,aAAa,KAAK,OAA0B;EAEvD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,SAAS;EAET,OAAO;EAEP,UAAU;EAEV,YAAS;AACP,WAAO;MACL,EAAE,KAAK,aAAY;;;EAIvB,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,cAAc,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGvF,cAAW;AACT,WAAO;MACL,eAAe,MAAM,CAAC,EAAE,SAAQ,MAAM;AACpC,eAAO,SAAS,OAAO,KAAK,IAAI;;MAElC,kBAAkB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACvC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,iBAAiB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACtC,eAAO,SAAS,KAAK,KAAK,IAAI;;;;EAKpC,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,iBAAgB;;;EAI9D,gBAAa;AACX,WAAO;MACL,kBAAkB;QAChB,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;;;ACvDM,IAAM,iBAAiB;AAKvB,IAAM,iBAAiB;AAKvB,IAAM,uBAAuB;AAK7B,IAAM,uBAAuB;AAMvB,IAAA,OAAO,KAAK,OAAoB;EAC3C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;MACD;QACE,KAAK;QACL,UAAU,UAAS,KAAqB,MAAM,eAAe,YAAY;MAC1E;MACD;QACE,OAAO;QACP,WAAW,UAAQ,KAAK,KAAK,SAAS,KAAK;MAC5C;MACD;QACE,OAAO;QACP,UAAU,WAAS,4BAA4B,KAAK,KAAe,KAAK;MACzE;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,UAAU,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGnF,cAAW;AACT,WAAO;MACL,SAAS,MAAM,CAAC,EAAE,SAAQ,MAAM;AAC9B,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,YAAY,MAAM,CAAC,EAAE,SAAQ,MAAM;AACjC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,WAAW,MAAM,CAAC,EAAE,SAAQ,MAAM;AAChC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,WAAU;MAC9C,SAAS,MAAM,KAAK,OAAO,SAAS,WAAU;;;EAIlD,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;MACD,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;EAIL,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;MACD,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;;;ACxID,IAAM,eAAe;AACrB,IAAM,gBAAgB;AA8Cf,IAAMA,cAAa;AAQb,IAAA,aAAa,KAAK,OAA0B;EACvD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,cAAc;MACd,gBAAgB,CAAA;MAChB,WAAW;MACX,gBAAgB;;;EAIpB,OAAO;EAEP,UAAO;AACL,WAAO,GAAG,KAAK,QAAQ,YAAY;;EAGrC,YAAS;AACP,WAAO;MACL,EAAE,KAAK,KAAI;;;EAIf,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG/E,cAAW;AACT,WAAO;MACL,kBAAkB,MAAM,CAAC,EAAE,UAAU,MAAK,MAAM;AAC9C,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,iBAAO,MAAK,EAAG,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,EAAE,iBAAiB,cAAc,KAAK,OAAO,cAAc,aAAa,CAAC,EAAE,IAAG;;AAEtK,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS;;;;EAK7F,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,iBAAgB;;;EAI9D,gBAAa;AACX,QAAI,YAAY,kBAAkB;MAChC,MAAMA;MACN,MAAM,KAAK;IACZ,CAAA;AAED,QAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,gBAAgB;AACzD,kBAAY,kBAAkB;QAC5B,MAAMA;QACN,MAAM,KAAK;QACX,WAAW,KAAK,QAAQ;QACxB,gBAAgB,KAAK,QAAQ;QAC7B,eAAe,MAAK;AAAG,iBAAO,KAAK,OAAO,cAAc,aAAa;QAAC;QACtE,QAAQ,KAAK;MACd,CAAA;;AAEH,WAAO;MACL;;;AAGL,CAAA;;;AC9EM,IAAMC,cAAa;AAKnB,IAAM,aAAa;AAMb,IAAA,OAAO,KAAK,OAAoB;EAC3C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,UAAU;EAEV,MAAM;EAEN,UAAU;EAEV,YAAS;AACP,WAAO;MACL,EAAE,KAAK,OAAM;;;EAIjB,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,QAAQ,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGjF,cAAW;AACT,WAAO;MACL,SAAS,MAAM,CAAC,EAAE,SAAQ,MAAM;AAC9B,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,YAAY,MAAM,CAAC,EAAE,SAAQ,MAAM;AACjC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,WAAW,MAAM,CAAC,EAAE,SAAQ,MAAM;AAChC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,WAAU;;;EAIlD,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAMA;QACN,MAAM,KAAK;OACZ;;;EAIL,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;;;AC/GY,IAAA,WAAW,KAAK,OAAO;EAClC,MAAM;EACN,SAAS;EACT,SAAS;AACV,CAAA;;;ACGD,SAAS,WAAW,UAAU,CAAC,GAAG;AAC9B,SAAO,IAAI,OAAO;AAAA,IACd,KAAK,YAAY;AAAE,aAAO,IAAI,eAAe,YAAY,OAAO;AAAA,IAAG;AAAA,EACvE,CAAC;AACL;AACA,IAAM,iBAAN,MAAqB;AAAA,EACjB,YAAY,YAAY,SAAS;AAC7B,QAAI;AACJ,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,SAAS,KAAK,QAAQ,WAAW,QAAQ,OAAO,SAAS,KAAK;AACnE,SAAK,QAAQ,QAAQ,UAAU,QAAQ,SAAa,QAAQ,SAAS;AACrE,SAAK,QAAQ,QAAQ;AACrB,SAAK,WAAW,CAAC,YAAY,WAAW,QAAQ,WAAW,EAAE,IAAI,UAAQ;AACrE,UAAI,UAAU,CAAC,MAAM;AAAE,aAAK,IAAI,EAAE,CAAC;AAAA,MAAG;AACtC,iBAAW,IAAI,iBAAiB,MAAM,OAAO;AAC7C,aAAO,EAAE,MAAM,QAAQ;AAAA,IAC3B,CAAC;AAAA,EACL;AAAA,EACA,UAAU;AACN,SAAK,SAAS,QAAQ,CAAC,EAAE,MAAM,QAAQ,MAAM,KAAK,WAAW,IAAI,oBAAoB,MAAM,OAAO,CAAC;AAAA,EACvG;AAAA,EACA,OAAO,YAAY,WAAW;AAC1B,QAAI,KAAK,aAAa,QAAQ,UAAU,OAAO,WAAW,MAAM,KAAK;AACjE,UAAI,KAAK,YAAY,WAAW,MAAM,IAAI,QAAQ;AAC9C,aAAK,UAAU,IAAI;AAAA;AAEnB,aAAK,cAAc;AAAA,IAC3B;AAAA,EACJ;AAAA,EACA,UAAU,KAAK;AACX,QAAI,OAAO,KAAK;AACZ;AACJ,SAAK,YAAY;AACjB,QAAI,OAAO,MAAM;AACb,WAAK,QAAQ,WAAW,YAAY,KAAK,OAAO;AAChD,WAAK,UAAU;AAAA,IACnB,OACK;AACD,WAAK,cAAc;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,gBAAgB;AACZ,QAAI,OAAO,KAAK,WAAW,MAAM,IAAI,QAAQ,KAAK,SAAS;AAC3D,QAAI,UAAU,CAAC,KAAK,OAAO,eAAe;AAC1C,QAAI,YAAY,KAAK,WAAW,KAAK,aAAa,UAAU,sBAAsB;AAClF,QAAI,SAAS,WAAW,QAAQ,UAAU,aAAa,SAAS,WAAW,SAAS,UAAU;AAC9F,QAAI,SAAS;AACT,UAAI,SAAS,KAAK,YAAY,QAAQ,KAAK;AAC3C,UAAI,UAAU,OAAO;AACjB,YAAI,OAAO,KAAK,WAAW,QAAQ,KAAK,aAAa,SAAS,OAAO,WAAW,EAAE;AAClF,YAAI,MAAM;AACN,cAAI,WAAW,KAAK,sBAAsB;AAC1C,cAAI,MAAM,SAAS,SAAS,SAAS,SAAS;AAC9C,cAAI,UAAU;AACV,mBAAO,MAAM,KAAK,WAAW,QAAQ,KAAK,SAAS,EAAE,sBAAsB,EAAE,OAAO;AACxF,cAAI,YAAa,KAAK,QAAQ,IAAK;AACnC,iBAAO,EAAE,MAAM,SAAS,MAAM,OAAO,SAAS,OAAO,KAAK,MAAM,WAAW,QAAQ,MAAM,UAAU;AAAA,QACvG;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,CAAC,MAAM;AACP,UAAI,SAAS,KAAK,WAAW,YAAY,KAAK,SAAS;AACvD,UAAI,YAAa,KAAK,QAAQ,IAAK;AACnC,aAAO,EAAE,MAAM,OAAO,OAAO,WAAW,OAAO,OAAO,OAAO,WAAW,KAAK,OAAO,KAAK,QAAQ,OAAO,OAAO;AAAA,IACnH;AACA,QAAI,SAAS,KAAK,WAAW,IAAI;AACjC,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,UAAU,OAAO,YAAY,SAAS,cAAc,KAAK,CAAC;AAC/D,UAAI,KAAK;AACL,aAAK,QAAQ,YAAY,KAAK;AAClC,WAAK,QAAQ,MAAM,UAAU;AAC7B,UAAI,KAAK,OAAO;AACZ,aAAK,QAAQ,MAAM,kBAAkB,KAAK;AAAA,MAC9C;AAAA,IACJ;AACA,SAAK,QAAQ,UAAU,OAAO,gCAAgC,OAAO;AACrE,SAAK,QAAQ,UAAU,OAAO,iCAAiC,CAAC,OAAO;AACvE,QAAI,YAAY;AAChB,QAAI,CAAC,UAAU,UAAU,SAAS,QAAQ,iBAAiB,MAAM,EAAE,YAAY,UAAU;AACrF,mBAAa,CAAC;AACd,kBAAY,CAAC;AAAA,IACjB,OACK;AACD,UAAIC,QAAO,OAAO,sBAAsB;AACxC,UAAI,eAAeA,MAAK,QAAQ,OAAO,aAAa,eAAeA,MAAK,SAAS,OAAO;AACxF,mBAAaA,MAAK,OAAO,OAAO,aAAa;AAC7C,kBAAYA,MAAK,MAAM,OAAO,YAAY;AAAA,IAC9C;AACA,SAAK,QAAQ,MAAM,QAAQ,KAAK,OAAO,cAAc,SAAS;AAC9D,SAAK,QAAQ,MAAM,OAAO,KAAK,MAAM,aAAa,SAAS;AAC3D,SAAK,QAAQ,MAAM,SAAS,KAAK,QAAQ,KAAK,QAAQ,SAAS;AAC/D,SAAK,QAAQ,MAAM,UAAU,KAAK,SAAS,KAAK,OAAO,SAAS;AAAA,EACpE;AAAA,EACA,gBAAgB,SAAS;AACrB,iBAAa,KAAK,OAAO;AACzB,SAAK,UAAU,WAAW,MAAM,KAAK,UAAU,IAAI,GAAG,OAAO;AAAA,EACjE;AAAA,EACA,SAAS,OAAO;AACZ,QAAI,CAAC,KAAK,WAAW;AACjB;AACJ,QAAI,MAAM,KAAK,WAAW,YAAY,EAAE,MAAM,MAAM,SAAS,KAAK,MAAM,QAAQ,CAAC;AACjF,QAAI,OAAO,OAAO,IAAI,UAAU,KAAK,KAAK,WAAW,MAAM,IAAI,OAAO,IAAI,MAAM;AAChF,QAAI,oBAAoB,QAAQ,KAAK,KAAK,KAAK;AAC/C,QAAI,WAAW,OAAO,qBAAqB,aACrC,kBAAkB,KAAK,YAAY,KAAK,KAAK,IAC7C;AACN,QAAI,OAAO,CAAC,UAAU;AAClB,UAAI,SAAS,IAAI;AACjB,UAAI,KAAK,WAAW,YAAY,KAAK,WAAW,SAAS,OAAO;AAC5D,YAAI,QAAQ,UAAU,KAAK,WAAW,MAAM,KAAK,QAAQ,KAAK,WAAW,SAAS,KAAK;AACvF,YAAI,SAAS;AACT,mBAAS;AAAA,MACjB;AACA,WAAK,UAAU,MAAM;AACrB,WAAK,gBAAgB,GAAI;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,UAAU;AACN,SAAK,gBAAgB,EAAE;AAAA,EAC3B;AAAA,EACA,OAAO;AACH,SAAK,gBAAgB,EAAE;AAAA,EAC3B;AAAA,EACA,UAAU,OAAO;AACb,QAAI,CAAC,KAAK,WAAW,IAAI,SAAS,MAAM,aAAa;AACjD,WAAK,UAAU,IAAI;AAAA,EAC3B;AACJ;;;AC/Ga,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,OAAO;MACP,OAAO;MACP,OAAO;;;EAIX,wBAAqB;AACnB,WAAO;MACL,WAAW,KAAK,OAAO;;;AAG5B,CAAA;;;ACvCD,IAAM,YAAN,MAAM,mBAAkB,UAAU;AAAA;AAAA;AAAA;AAAA,EAI9B,YAAY,MAAM;AACd,UAAM,MAAM,IAAI;AAAA,EACpB;AAAA,EACA,IAAI,KAAK,SAAS;AACd,QAAI,OAAO,IAAI,QAAQ,QAAQ,IAAI,KAAK,IAAI,CAAC;AAC7C,WAAO,WAAU,MAAM,IAAI,IAAI,IAAI,WAAU,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,EAC5E;AAAA,EACA,UAAU;AAAE,WAAO,MAAM;AAAA,EAAO;AAAA,EAChC,GAAG,OAAO;AACN,WAAO,iBAAiB,cAAa,MAAM,QAAQ,KAAK;AAAA,EAC5D;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,aAAa,KAAK,KAAK,KAAK;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,KAAK,MAAM;AACvB,QAAI,OAAO,KAAK,OAAO;AACnB,YAAM,IAAI,WAAW,sCAAsC;AAC/D,WAAO,IAAI,WAAU,IAAI,QAAQ,KAAK,GAAG,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc;AAAE,WAAO,IAAI,YAAY,KAAK,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIrD,OAAO,MAAM,MAAM;AACf,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,eAAe,CAAC,aAAa,IAAI,KAAK,CAAC,YAAY,IAAI;AAC9D,aAAO;AACX,QAAI,WAAW,OAAO,KAAK,KAAK;AAChC,QAAI,YAAY;AACZ,aAAO;AACX,QAAI,QAAQ,OAAO,eAAe,KAAK,MAAM,CAAC,EAAE;AAChD,WAAO,SAAS,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,kBAAkB,MAAM,KAAK,WAAW,OAAO;AAClD;AAAQ,iBAAS;AACb,YAAI,CAAC,YAAY,WAAU,MAAM,IAAI;AACjC,iBAAO;AACX,YAAI,MAAM,KAAK,KAAK,OAAO;AAE3B,iBAAS,IAAI,KAAK,SAAQ,KAAK;AAC3B,cAAI,SAAS,KAAK,KAAK,CAAC;AACxB,cAAI,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,OAAO,aAAa,KAAK,MAAM,CAAC,IAAI,GAAG;AACtE,mBAAO,OAAO,MAAM,MAAM,IAAI,KAAK,WAAW,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC;AACpE;AAAA,UACJ,WACS,KAAK,GAAG;AACb,mBAAO;AAAA,UACX;AACA,iBAAO;AACP,cAAI,OAAO,KAAK,IAAI,QAAQ,GAAG;AAC/B,cAAI,WAAU,MAAM,IAAI;AACpB,mBAAO;AAAA,QACf;AAEA,mBAAS;AACL,cAAI,SAAS,MAAM,IAAI,KAAK,aAAa,KAAK;AAC9C,cAAI,CAAC,QAAQ;AACT,gBAAI,KAAK,UAAU,CAAC,KAAK,UAAU,CAAC,cAAc,aAAa,IAAI,GAAG;AAClE,qBAAO,KAAK,IAAI,QAAQ,MAAM,KAAK,WAAW,GAAG;AACjD,yBAAW;AACX,uBAAS;AAAA,YACb;AACA;AAAA,UACJ;AACA,iBAAO;AACP,iBAAO;AACP,cAAI,OAAO,KAAK,IAAI,QAAQ,GAAG;AAC/B,cAAI,WAAU,MAAM,IAAI;AACpB,mBAAO;AAAA,QACf;AACA,eAAO;AAAA,MACX;AAAA,EACJ;AACJ;AACA,UAAU,UAAU,UAAU;AAC9B,UAAU,WAAW,UAAU;AAC/B,UAAU,OAAO,aAAa,SAAS;AACvC,IAAM,cAAN,MAAM,aAAY;AAAA,EACd,YAAY,KAAK;AACb,SAAK,MAAM;AAAA,EACf;AAAA,EACA,IAAI,SAAS;AACT,WAAO,IAAI,aAAY,QAAQ,IAAI,KAAK,GAAG,CAAC;AAAA,EAChD;AAAA,EACA,QAAQ,KAAK;AACT,QAAI,OAAO,IAAI,QAAQ,KAAK,GAAG;AAC/B,WAAO,UAAU,MAAM,IAAI,IAAI,IAAI,UAAU,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,EAC5E;AACJ;AACA,SAAS,aAAa,MAAM;AACxB,WAAS,IAAI,KAAK,OAAO,KAAK,GAAG,KAAK;AAClC,QAAI,QAAQ,KAAK,MAAM,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC;AAE/C,QAAI,SAAS,GAAG;AACZ,UAAI,OAAO,KAAK,KAAK;AACjB,eAAO;AACX;AAAA,IACJ;AAEA,aAAS,SAAS,OAAO,MAAM,QAAQ,CAAC,KAAI,SAAS,OAAO,WAAW;AACnE,UAAK,OAAO,cAAc,KAAK,CAAC,OAAO,iBAAkB,OAAO,UAAU,OAAO,KAAK,KAAK;AACvF,eAAO;AACX,UAAI,OAAO;AACP,eAAO;AAAA,IACf;AAAA,EACJ;AAEA,SAAO;AACX;AACA,SAAS,YAAY,MAAM;AACvB,WAAS,IAAI,KAAK,OAAO,KAAK,GAAG,KAAK;AAClC,QAAI,QAAQ,KAAK,WAAW,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC;AACpD,QAAI,SAAS,OAAO,YAAY;AAC5B,UAAI,OAAO,KAAK,KAAK;AACjB,eAAO;AACX;AAAA,IACJ;AACA,aAAS,QAAQ,OAAO,MAAM,KAAK,KAAI,QAAQ,MAAM,YAAY;AAC7D,UAAK,MAAM,cAAc,KAAK,CAAC,MAAM,iBAAkB,MAAM,UAAU,MAAM,KAAK,KAAK;AACnF,eAAO;AACX,UAAI,MAAM;AACN,eAAO;AAAA,IACf;AAAA,EACJ;AACA,SAAO;AACX;AAWA,SAAS,YAAY;AACjB,SAAO,IAAI,OAAO;AAAA,IACd,OAAO;AAAA,MACH,aAAa;AAAA,MACb,uBAAuB,OAAO,SAAS,OAAO;AAC1C,eAAO,QAAQ,OAAO,MAAM,OAAO,UAAU,MAAM,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI;AAAA,MACvF;AAAA,MACA;AAAA,MACA;AAAA,MACA,iBAAiB,EAAE,YAAyB;AAAA,IAChD;AAAA,EACJ,CAAC;AACL;AACA,IAAM,gBAAgB,eAAe;AAAA,EACjC,aAAa,MAAM,SAAS,EAAE;AAAA,EAC9B,cAAc,MAAM,SAAS,CAAC;AAAA,EAC9B,WAAW,MAAM,QAAQ,EAAE;AAAA,EAC3B,aAAa,MAAM,QAAQ,CAAC;AAChC,CAAC;AACD,SAAS,MAAM,MAAM,KAAK;AACtB,QAAM,SAAS,QAAQ,SAAU,MAAM,IAAI,SAAS,OAAS,MAAM,IAAI,UAAU;AACjF,SAAO,SAAU,OAAO,UAAU,MAAM;AACpC,QAAI,MAAM,MAAM;AAChB,QAAI,SAAS,MAAM,IAAI,IAAI,MAAM,IAAI,OAAO,WAAW,IAAI;AAC3D,QAAI,eAAe,eAAe;AAC9B,UAAI,CAAC,KAAK,eAAe,MAAM,KAAK,OAAO,SAAS;AAChD,eAAO;AACX,iBAAW;AACX,eAAS,MAAM,IAAI,QAAQ,MAAM,IAAI,OAAO,MAAM,IAAI,OAAO,OAAO,CAAC;AAAA,IACzE;AACA,QAAI,SAAS,UAAU,kBAAkB,QAAQ,KAAK,QAAQ;AAC9D,QAAI,CAAC;AACD,aAAO;AACX,QAAI;AACA,eAAS,MAAM,GAAG,aAAa,IAAI,UAAU,MAAM,CAAC,CAAC;AACzD,WAAO;AAAA,EACX;AACJ;AACA,SAAS,YAAY,MAAM,KAAK,OAAO;AACnC,MAAI,CAAC,QAAQ,CAAC,KAAK;AACf,WAAO;AACX,MAAI,OAAO,KAAK,MAAM,IAAI,QAAQ,GAAG;AACrC,MAAI,CAAC,UAAU,MAAM,IAAI;AACrB,WAAO;AACX,MAAI,WAAW,KAAK,YAAY,EAAE,MAAM,MAAM,SAAS,KAAK,MAAM,QAAQ,CAAC;AAC3E,MAAI,YAAY,SAAS,SAAS,MAAM,cAAc,aAAa,KAAK,MAAM,IAAI,OAAO,SAAS,MAAM,CAAC;AACrG,WAAO;AACX,OAAK,SAAS,KAAK,MAAM,GAAG,aAAa,IAAI,UAAU,IAAI,CAAC,CAAC;AAC7D,SAAO;AACX;AAKA,SAAS,YAAY,MAAM,OAAO;AAC9B,MAAI,MAAM,aAAa,2BAA2B,EAAE,KAAK,MAAM,qBAAqB;AAChF,WAAO;AACX,MAAI,EAAE,MAAM,IAAI,KAAK,MAAM;AAC3B,MAAI,SAAS,MAAM,OAAO,eAAe,MAAM,MAAM,CAAC,EAAE,aAAa,KAAK,MAAM,OAAO,MAAM,IAAI;AACjG,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,SAAS;AACpB,WAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG;AACpC,WAAO,SAAS,KAAK,OAAO,CAAC,EAAE,cAAc,MAAM,IAAI,CAAC;AAC5D,MAAI,KAAK,KAAK,MAAM,GAAG,QAAQ,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC;AAC1E,KAAG,aAAa,cAAc,KAAK,GAAG,IAAI,QAAQ,MAAM,MAAM,CAAC,CAAC,CAAC;AACjE,OAAK,SAAS,EAAE;AAChB,SAAO;AACX;AACA,SAAS,cAAc,OAAO;AAC1B,MAAI,EAAE,MAAM,qBAAqB;AAC7B,WAAO;AACX,MAAI,OAAO,SAAS,cAAc,KAAK;AACvC,OAAK,YAAY;AACjB,SAAO,cAAc,OAAO,MAAM,KAAK,CAAC,WAAW,OAAO,MAAM,UAAU,MAAM,MAAM,EAAE,KAAK,YAAY,CAAC,CAAC,CAAC;AAChH;;;ACzMa,IAAA,YAAY,UAAU,OAAO;EACxC,MAAM;EAEN,wBAAqB;AACnB,WAAO;MACL,UAAS;;;EAIb,iBAAiB,WAAS;;AACxB,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,SAAS,UAAU;MACnB,SAAS,UAAU;;AAGrB,WAAO;MACL,iBAAgB,KAAA,aAAa,kBAAkB,WAAW,kBAAkB,OAAO,CAAC,OAAC,QAAA,OAAA,SAAA,KAAI;;;AAG9F,CAAA;;;AClBY,IAAA,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,WAAW;MACX,gBAAgB,CAAA;;;EAIpB,QAAQ;EAER,OAAO;EAEP,YAAY;EAEZ,sBAAsB;EAEtB,YAAS;AACP,WAAO;MACL,EAAE,KAAK,KAAI;;;EAIf,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;;EAG5E,aAAU;AACR,WAAO;;EAGT,cAAW;AACT,WAAO;MACL,cAAc,MAAM,CAAC,EACnB,UACA,OACA,OACA,OAAM,MACH;AACH,eAAO,SAAS,MAAM;UACpB,MAAM,SAAS,SAAQ;UACvB,MAAM,SAAS,QAAQ,MAAK;AAC1B,kBAAM,EAAE,WAAW,YAAW,IAAK;AAEnC,gBAAI,UAAU,MAAM,OAAO,KAAK,KAAK,WAAW;AAC9C,qBAAO;;AAGT,kBAAM,EAAE,UAAS,IAAK,KAAK;AAC3B,kBAAM,EAAE,gBAAe,IAAK,OAAO;AACnC,kBAAM,QAAQ,eACR,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAK;AAEzD,mBAAO,MAAK,EACT,cAAc,EAAE,MAAM,KAAK,KAAI,CAAE,EACjC,QAAQ,CAAC,EAAE,IAAI,SAAQ,MAAM;AAC5B,kBAAI,YAAY,SAAS,WAAW;AAClC,sBAAM,gBAAgB,MACnB,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAE1D,mBAAG,YAAY,aAAa;;AAG9B,qBAAO;YACT,CAAC,EACA,IAAG;UACR,CAAC;QACF,CAAA;;;;EAKP,uBAAoB;AAClB,WAAO;MACL,aAAa,MAAM,KAAK,OAAO,SAAS,aAAY;MACpD,eAAe,MAAM,KAAK,OAAO,SAAS,aAAY;;;AAG3D,CAAA;;;ACnEY,IAAA,UAAU,KAAK,OAAuB;EACjD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,QAAQ,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;MACzB,gBAAgB,CAAA;;;EAIpB,SAAS;EAET,OAAO;EAEP,UAAU;EAEV,gBAAa;AACX,WAAO;MACL,OAAO;QACL,SAAS;QACT,UAAU;MACX;;;EAIL,YAAS;AACP,WAAO,KAAK,QAAQ,OACjB,IAAI,CAAC,WAAkB;MACtB,KAAK,IAAI,KAAK;MACd,OAAO,EAAE,MAAK;IACf,EAAC;;EAGN,WAAW,EAAE,MAAM,eAAc,GAAE;AACjC,UAAM,WAAW,KAAK,QAAQ,OAAO,SAAS,KAAK,MAAM,KAAK;AAC9D,UAAM,QAAQ,WACV,KAAK,MAAM,QACX,KAAK,QAAQ,OAAO,CAAC;AAEzB,WAAO,CAAC,IAAI,KAAK,IAAI,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGtF,cAAW;AACT,WAAO;MACL,YAAY,gBAAc,CAAC,EAAE,SAAQ,MAAM;AACzC,YAAI,CAAC,KAAK,QAAQ,OAAO,SAAS,WAAW,KAAK,GAAG;AACnD,iBAAO;;AAGT,eAAO,SAAS,QAAQ,KAAK,MAAM,UAAU;;MAE/C,eAAe,gBAAc,CAAC,EAAE,SAAQ,MAAM;AAC5C,YAAI,CAAC,KAAK,QAAQ,OAAO,SAAS,WAAW,KAAK,GAAG;AACnD,iBAAO;;AAGT,eAAO,SAAS,WAAW,KAAK,MAAM,aAAa,UAAU;;;;EAKnE,uBAAoB;AAClB,WAAO,KAAK,QAAQ,OAAO,OAAO,CAAC,OAAO,WAAW;MACnD,GAAG;MACH,GAAG;QACD,CAAC,WAAW,KAAK,EAAE,GAAG,MAAM,KAAK,OAAO,SAAS,cAAc,EAAE,MAAK,CAAE;MACzE;QACC,CAAA,CAAE;;EAGR,gBAAa;AACX,WAAO,KAAK,QAAQ,OAAO,IAAI,WAAQ;AACrC,aAAO,uBAAuB;QAC5B,MAAM,IAAI,OAAO,OAAO,KAAK,IAAI,GAAG,KAAK,QAAQ,MAAM,CAAC,IAAI,KAAK,QAAQ;QACzE,MAAM,KAAK;QACX,eAAe;UACb;QACD;MACF,CAAA;IACH,CAAC;;AAEJ,CAAA;;;AC/HD,IAAI,iBAAiB;AAKrB,IAAI,eAAe,SAASC,gBAAgB;AAAC;AAE7C,aAAa,UAAU,SAAS,SAAS,OAAQ,OAAO;AACtD,MAAI,CAAC,MAAM,QAAQ;AAAE,WAAO;AAAA,EAAK;AACjC,UAAQ,aAAa,KAAK,KAAK;AAE/B,SAAQ,CAAC,KAAK,UAAU,SACrB,MAAM,SAAS,kBAAkB,KAAK,WAAW,KAAK,KACtD,KAAK,SAAS,kBAAkB,MAAM,YAAY,IAAI,KACvD,KAAK,YAAY,KAAK;AAC1B;AAIA,aAAa,UAAU,UAAU,SAAS,QAAS,OAAO;AACxD,MAAI,CAAC,MAAM,QAAQ;AAAE,WAAO;AAAA,EAAK;AACjC,SAAO,aAAa,KAAK,KAAK,EAAE,OAAO,IAAI;AAC7C;AAEA,aAAa,UAAU,cAAc,SAAS,YAAa,OAAO;AAChE,SAAO,IAAI,OAAO,MAAM,KAAK;AAC/B;AAIA,aAAa,UAAU,QAAQ,SAAS,MAAOC,OAAM,IAAI;AACrD,MAAKA,UAAS;AAAS,IAAAA,QAAO;AAC9B,MAAK,OAAO;AAAS,SAAK,KAAK;AAEjC,MAAIA,SAAQ,IAAI;AAAE,WAAO,aAAa;AAAA,EAAM;AAC5C,SAAO,KAAK,WAAW,KAAK,IAAI,GAAGA,KAAI,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC;AACrE;AAIA,aAAa,UAAU,MAAM,SAAS,IAAK,GAAG;AAC5C,MAAI,IAAI,KAAK,KAAK,KAAK,QAAQ;AAAE,WAAO;AAAA,EAAU;AAClD,SAAO,KAAK,SAAS,CAAC;AACxB;AAOA,aAAa,UAAU,UAAU,SAAS,QAAS,GAAGA,OAAM,IAAI;AAC5D,MAAKA,UAAS;AAAS,IAAAA,QAAO;AAC9B,MAAK,OAAO;AAAS,SAAK,KAAK;AAEjC,MAAIA,SAAQ,IACV;AAAE,SAAK,aAAa,GAAGA,OAAM,IAAI,CAAC;AAAA,EAAG,OAErC;AAAE,SAAK,qBAAqB,GAAGA,OAAM,IAAI,CAAC;AAAA,EAAG;AACjD;AAKA,aAAa,UAAU,MAAM,SAAS,IAAK,GAAGA,OAAM,IAAI;AACpD,MAAKA,UAAS;AAAS,IAAAA,QAAO;AAC9B,MAAK,OAAO;AAAS,SAAK,KAAK;AAEjC,MAAI,SAAS,CAAC;AACd,OAAK,QAAQ,SAAU,KAAK,GAAG;AAAE,WAAO,OAAO,KAAK,EAAE,KAAK,CAAC,CAAC;AAAA,EAAG,GAAGA,OAAM,EAAE;AAC3E,SAAO;AACT;AAKA,aAAa,OAAO,SAAS,KAAM,QAAQ;AACzC,MAAI,kBAAkB,cAAc;AAAE,WAAO;AAAA,EAAO;AACpD,SAAO,UAAU,OAAO,SAAS,IAAI,KAAK,MAAM,IAAI,aAAa;AACnE;AAEA,IAAI,OAAqB,SAAUD,eAAc;AAC/C,WAASE,MAAK,QAAQ;AACpB,IAAAF,cAAa,KAAK,IAAI;AACtB,SAAK,SAAS;AAAA,EAChB;AAEA,MAAKA;AAAe,IAAAE,MAAK,YAAYF;AACrC,EAAAE,MAAK,YAAY,OAAO,OAAQF,iBAAgBA,cAAa,SAAU;AACvE,EAAAE,MAAK,UAAU,cAAcA;AAE7B,MAAI,qBAAqB,EAAE,QAAQ,EAAE,cAAc,KAAK,GAAE,OAAO,EAAE,cAAc,KAAK,EAAE;AAExF,EAAAA,MAAK,UAAU,UAAU,SAAS,UAAW;AAC3C,WAAO,KAAK;AAAA,EACd;AAEA,EAAAA,MAAK,UAAU,aAAa,SAAS,WAAYD,OAAM,IAAI;AACzD,QAAIA,SAAQ,KAAK,MAAM,KAAK,QAAQ;AAAE,aAAO;AAAA,IAAK;AAClD,WAAO,IAAIC,MAAK,KAAK,OAAO,MAAMD,OAAM,EAAE,CAAC;AAAA,EAC7C;AAEA,EAAAC,MAAK,UAAU,WAAW,SAAS,SAAU,GAAG;AAC9C,WAAO,KAAK,OAAO,CAAC;AAAA,EACtB;AAEA,EAAAA,MAAK,UAAU,eAAe,SAAS,aAAc,GAAGD,OAAM,IAAI,OAAO;AACvE,aAAS,IAAIA,OAAM,IAAI,IAAI,KACzB;AAAE,UAAI,EAAE,KAAK,OAAO,CAAC,GAAG,QAAQ,CAAC,MAAM,OAAO;AAAE,eAAO;AAAA,MAAM;AAAA,IAAE;AAAA,EACnE;AAEA,EAAAC,MAAK,UAAU,uBAAuB,SAAS,qBAAsB,GAAGD,OAAM,IAAI,OAAO;AACvF,aAAS,IAAIA,QAAO,GAAG,KAAK,IAAI,KAC9B;AAAE,UAAI,EAAE,KAAK,OAAO,CAAC,GAAG,QAAQ,CAAC,MAAM,OAAO;AAAE,eAAO;AAAA,MAAM;AAAA,IAAE;AAAA,EACnE;AAEA,EAAAC,MAAK,UAAU,aAAa,SAAS,WAAY,OAAO;AACtD,QAAI,KAAK,SAAS,MAAM,UAAU,gBAChC;AAAE,aAAO,IAAIA,MAAK,KAAK,OAAO,OAAO,MAAM,QAAQ,CAAC,CAAC;AAAA,IAAE;AAAA,EAC3D;AAEA,EAAAA,MAAK,UAAU,cAAc,SAAS,YAAa,OAAO;AACxD,QAAI,KAAK,SAAS,MAAM,UAAU,gBAChC;AAAE,aAAO,IAAIA,MAAK,MAAM,QAAQ,EAAE,OAAO,KAAK,MAAM,CAAC;AAAA,IAAE;AAAA,EAC3D;AAEA,qBAAmB,OAAO,MAAM,WAAY;AAAE,WAAO,KAAK,OAAO;AAAA,EAAO;AAExE,qBAAmB,MAAM,MAAM,WAAY;AAAE,WAAO;AAAA,EAAE;AAEtD,SAAO,iBAAkBA,MAAK,WAAW,kBAAmB;AAE5D,SAAOA;AACT,EAAE,YAAY;AAId,aAAa,QAAQ,IAAI,KAAK,CAAC,CAAC;AAEhC,IAAI,SAAuB,SAAUF,eAAc;AACjD,WAASG,QAAO,MAAM,OAAO;AAC3B,IAAAH,cAAa,KAAK,IAAI;AACtB,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,SAAS,KAAK,SAAS,MAAM;AAClC,SAAK,QAAQ,KAAK,IAAI,KAAK,OAAO,MAAM,KAAK,IAAI;AAAA,EACnD;AAEA,MAAKA;AAAe,IAAAG,QAAO,YAAYH;AACvC,EAAAG,QAAO,YAAY,OAAO,OAAQH,iBAAgBA,cAAa,SAAU;AACzE,EAAAG,QAAO,UAAU,cAAcA;AAE/B,EAAAA,QAAO,UAAU,UAAU,SAAS,UAAW;AAC7C,WAAO,KAAK,KAAK,QAAQ,EAAE,OAAO,KAAK,MAAM,QAAQ,CAAC;AAAA,EACxD;AAEA,EAAAA,QAAO,UAAU,WAAW,SAAS,SAAU,GAAG;AAChD,WAAO,IAAI,KAAK,KAAK,SAAS,KAAK,KAAK,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,KAAK,MAAM;AAAA,EACtF;AAEA,EAAAA,QAAO,UAAU,eAAe,SAAS,aAAc,GAAGF,OAAM,IAAI,OAAO;AACzE,QAAI,UAAU,KAAK,KAAK;AACxB,QAAIA,QAAO,WACP,KAAK,KAAK,aAAa,GAAGA,OAAM,KAAK,IAAI,IAAI,OAAO,GAAG,KAAK,MAAM,OACpE;AAAE,aAAO;AAAA,IAAM;AACjB,QAAI,KAAK,WACL,KAAK,MAAM,aAAa,GAAG,KAAK,IAAIA,QAAO,SAAS,CAAC,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAE,IAAI,SAAS,QAAQ,OAAO,MAAM,OACpH;AAAE,aAAO;AAAA,IAAM;AAAA,EACnB;AAEA,EAAAE,QAAO,UAAU,uBAAuB,SAAS,qBAAsB,GAAGF,OAAM,IAAI,OAAO;AACzF,QAAI,UAAU,KAAK,KAAK;AACxB,QAAIA,QAAO,WACP,KAAK,MAAM,qBAAqB,GAAGA,QAAO,SAAS,KAAK,IAAI,IAAI,OAAO,IAAI,SAAS,QAAQ,OAAO,MAAM,OAC3G;AAAE,aAAO;AAAA,IAAM;AACjB,QAAI,KAAK,WACL,KAAK,KAAK,qBAAqB,GAAG,KAAK,IAAIA,OAAM,OAAO,GAAG,IAAI,KAAK,MAAM,OAC5E;AAAE,aAAO;AAAA,IAAM;AAAA,EACnB;AAEA,EAAAE,QAAO,UAAU,aAAa,SAAS,WAAYF,OAAM,IAAI;AAC3D,QAAIA,SAAQ,KAAK,MAAM,KAAK,QAAQ;AAAE,aAAO;AAAA,IAAK;AAClD,QAAI,UAAU,KAAK,KAAK;AACxB,QAAI,MAAM,SAAS;AAAE,aAAO,KAAK,KAAK,MAAMA,OAAM,EAAE;AAAA,IAAE;AACtD,QAAIA,SAAQ,SAAS;AAAE,aAAO,KAAK,MAAM,MAAMA,QAAO,SAAS,KAAK,OAAO;AAAA,IAAE;AAC7E,WAAO,KAAK,KAAK,MAAMA,OAAM,OAAO,EAAE,OAAO,KAAK,MAAM,MAAM,GAAG,KAAK,OAAO,CAAC;AAAA,EAChF;AAEA,EAAAE,QAAO,UAAU,aAAa,SAAS,WAAY,OAAO;AACxD,QAAI,QAAQ,KAAK,MAAM,WAAW,KAAK;AACvC,QAAI,OAAO;AAAE,aAAO,IAAIA,QAAO,KAAK,MAAM,KAAK;AAAA,IAAE;AAAA,EACnD;AAEA,EAAAA,QAAO,UAAU,cAAc,SAAS,YAAa,OAAO;AAC1D,QAAI,QAAQ,KAAK,KAAK,YAAY,KAAK;AACvC,QAAI,OAAO;AAAE,aAAO,IAAIA,QAAO,OAAO,KAAK,KAAK;AAAA,IAAE;AAAA,EACpD;AAEA,EAAAA,QAAO,UAAU,cAAc,SAASC,aAAa,OAAO;AAC1D,QAAI,KAAK,KAAK,SAAS,KAAK,IAAI,KAAK,MAAM,OAAO,MAAM,KAAK,IAAI,GAC/D;AAAE,aAAO,IAAID,QAAO,KAAK,MAAM,IAAIA,QAAO,KAAK,OAAO,KAAK,CAAC;AAAA,IAAE;AAChE,WAAO,IAAIA,QAAO,MAAM,KAAK;AAAA,EAC/B;AAEA,SAAOA;AACT,EAAE,YAAY;AAEd,IAAO,eAAQ;;;AC1Lf,IAAM,kBAAkB;AACxB,IAAM,SAAN,MAAM,QAAO;AAAA,EACT,YAAY,OAAO,YAAY;AAC3B,SAAK,QAAQ;AACb,SAAK,aAAa;AAAA,EACtB;AAAA;AAAA;AAAA,EAGA,SAAS,OAAO,eAAe;AAC3B,QAAI,KAAK,cAAc;AACnB,aAAO;AACX,QAAI,MAAM,KAAK,MAAM;AACrB,aAAQ,OAAO;AACX,UAAI,OAAO,KAAK,MAAM,IAAI,MAAM,CAAC;AACjC,UAAI,KAAK,WAAW;AAChB,UAAE;AACF;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,OAAO;AACX,QAAI,eAAe;AACf,cAAQ,KAAK,UAAU,KAAK,KAAK,MAAM,MAAM;AAC7C,gBAAU,MAAM,KAAK;AAAA,IACzB;AACA,QAAI,YAAY,MAAM;AACtB,QAAI,WAAW;AACf,QAAI,WAAW,CAAC,GAAG,YAAY,CAAC;AAChC,SAAK,MAAM,QAAQ,CAAC,MAAM,MAAM;AAC5B,UAAI,CAAC,KAAK,MAAM;AACZ,YAAI,CAAC,OAAO;AACR,kBAAQ,KAAK,UAAU,KAAK,IAAI,CAAC;AACjC,oBAAU,MAAM,KAAK;AAAA,QACzB;AACA;AACA,kBAAU,KAAK,IAAI;AACnB;AAAA,MACJ;AACA,UAAI,OAAO;AACP,kBAAU,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC;AACjC,YAAI,OAAO,KAAK,KAAK,IAAI,MAAM,MAAM,OAAO,CAAC,GAAGE;AAChD,YAAI,QAAQ,UAAU,UAAU,IAAI,EAAE,KAAK;AACvC,UAAAA,OAAM,UAAU,QAAQ,KAAK,UAAU,QAAQ,KAAK,SAAS,CAAC;AAC9D,mBAAS,KAAK,IAAI,KAAKA,MAAK,QAAW,QAAW,SAAS,SAAS,UAAU,MAAM,CAAC;AAAA,QACzF;AACA;AACA,YAAIA;AACA,gBAAM,UAAUA,MAAK,OAAO;AAAA,MACpC,OACK;AACD,kBAAU,UAAU,KAAK,IAAI;AAAA,MACjC;AACA,UAAI,KAAK,WAAW;AAChB,oBAAY,QAAQ,KAAK,UAAU,IAAI,MAAM,MAAM,OAAO,CAAC,IAAI,KAAK;AACpE,oBAAY,IAAI,QAAO,KAAK,MAAM,MAAM,GAAG,GAAG,EAAE,OAAO,UAAU,QAAQ,EAAE,OAAO,QAAQ,CAAC,GAAG,KAAK,aAAa,CAAC;AACjH,eAAO;AAAA,MACX;AAAA,IACJ,GAAG,KAAK,MAAM,QAAQ,CAAC;AACvB,WAAO,EAAE,WAAsB,WAAW,UAAqB;AAAA,EACnE;AAAA;AAAA,EAEA,aAAa,WAAW,WAAW,aAAa,eAAe;AAC3D,QAAI,WAAW,CAAC,GAAG,aAAa,KAAK;AACrC,QAAI,WAAW,KAAK,OAAO,WAAW,CAAC,iBAAiB,SAAS,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,IAAI;AAC9G,aAAS,IAAI,GAAG,IAAI,UAAU,MAAM,QAAQ,KAAK;AAC7C,UAAI,OAAO,UAAU,MAAM,CAAC,EAAE,OAAO,UAAU,KAAK,CAAC,CAAC;AACtD,UAAI,OAAO,IAAI,KAAK,UAAU,QAAQ,KAAK,CAAC,GAAG,MAAM,SAAS,GAAG;AACjE,UAAI,SAAS,YAAY,SAAS,MAAM,IAAI,GAAG;AAC3C,eAAO;AACP,YAAI;AACA,mBAAS,IAAI;AAAA;AAEb,qBAAW,SAAS,MAAM,GAAG,SAAS,SAAS,CAAC;AAAA,MACxD;AACA,eAAS,KAAK,IAAI;AAClB,UAAI,WAAW;AACX;AACA,oBAAY;AAAA,MAChB;AACA,UAAI,CAAC;AACD,mBAAW;AAAA,IACnB;AACA,QAAI,WAAW,aAAa,YAAY;AACxC,QAAI,WAAW,gBAAgB;AAC3B,iBAAW,aAAa,UAAU,QAAQ;AAC1C,oBAAc;AAAA,IAClB;AACA,WAAO,IAAI,QAAO,SAAS,OAAO,QAAQ,GAAG,UAAU;AAAA,EAC3D;AAAA,EACA,UAAUC,OAAM,IAAI;AAChB,QAAI,OAAO,IAAI;AACf,SAAK,MAAM,QAAQ,CAAC,MAAM,MAAM;AAC5B,UAAI,YAAY,KAAK,gBAAgB,QAAQ,IAAI,KAAK,gBAAgBA,QAChE,KAAK,KAAK,SAAS,KAAK,eAAe;AAC7C,WAAK,UAAU,KAAK,KAAK,SAAS;AAAA,IACtC,GAAGA,OAAM,EAAE;AACX,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,OAAO;AACX,QAAI,KAAK,cAAc;AACnB,aAAO;AACX,WAAO,IAAI,QAAO,KAAK,MAAM,OAAO,MAAM,IAAI,CAAAD,SAAO,IAAI,KAAKA,IAAG,CAAC,CAAC,GAAG,KAAK,UAAU;AAAA,EACzF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,kBAAkB,cAAc;AACpC,QAAI,CAAC,KAAK;AACN,aAAO;AACX,QAAI,eAAe,CAAC,GAAG,QAAQ,KAAK,IAAI,GAAG,KAAK,MAAM,SAAS,YAAY;AAC3E,QAAI,UAAU,iBAAiB;AAC/B,QAAI,WAAW,iBAAiB,MAAM;AACtC,QAAI,aAAa,KAAK;AACtB,SAAK,MAAM,QAAQ,UAAQ;AAAE,UAAI,KAAK;AAClC;AAAA,IAAc,GAAG,KAAK;AAC1B,QAAI,WAAW;AACf,SAAK,MAAM,QAAQ,UAAQ;AACvB,UAAI,MAAM,QAAQ,UAAU,EAAE,QAAQ;AACtC,UAAI,OAAO;AACP;AACJ,iBAAW,KAAK,IAAI,UAAU,GAAG;AACjC,UAAIA,OAAM,QAAQ,KAAK,GAAG;AAC1B,UAAI,KAAK,MAAM;AACX,YAAI,OAAO,iBAAiB,MAAM,GAAG,EAAE,OAAO,iBAAiB,KAAK,GAAG,CAAC;AACxE,YAAI,YAAY,KAAK,aAAa,KAAK,UAAU,IAAI,QAAQ,MAAM,WAAW,GAAG,GAAG,CAAC;AACrF,YAAI;AACA;AACJ,qBAAa,KAAK,IAAI,KAAKA,MAAK,MAAM,SAAS,CAAC;AAAA,MACpD,OACK;AACD,qBAAa,KAAK,IAAI,KAAKA,IAAG,CAAC;AAAA,MACnC;AAAA,IACJ,GAAG,KAAK;AACR,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,cAAc,IAAI,UAAU;AACrC,cAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,CAAC,CAAC,CAAC;AAC1C,QAAI,QAAQ,KAAK,MAAM,MAAM,GAAG,KAAK,EAAE,OAAO,OAAO,EAAE,OAAO,YAAY;AAC1E,QAAI,SAAS,IAAI,QAAO,OAAO,UAAU;AACzC,QAAI,OAAO,eAAe,IAAI;AAC1B,eAAS,OAAO,SAAS,KAAK,MAAM,SAAS,aAAa,MAAM;AACpE,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB;AACb,QAAI,QAAQ;AACZ,SAAK,MAAM,QAAQ,UAAQ;AAAE,UAAI,CAAC,KAAK;AACnC;AAAA,IAAS,CAAC;AACd,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,OAAO,KAAK,MAAM,QAAQ;AAC/B,QAAI,QAAQ,KAAK,UAAU,GAAG,IAAI,GAAG,UAAU,MAAM,KAAK;AAC1D,QAAI,QAAQ,CAAC,GAAG,SAAS;AACzB,SAAK,MAAM,QAAQ,CAAC,MAAM,MAAM;AAC5B,UAAI,KAAK,MAAM;AACX,cAAM,KAAK,IAAI;AACf,YAAI,KAAK;AACL;AAAA,MACR,WACS,KAAK,MAAM;AAChB,YAAI,OAAO,KAAK,KAAK,IAAI,MAAM,MAAM,OAAO,CAAC,GAAGA,OAAM,QAAQ,KAAK,OAAO;AAC1E;AACA,YAAIA;AACA,gBAAM,UAAUA,MAAK,OAAO;AAChC,YAAI,MAAM;AACN,cAAI,YAAY,KAAK,aAAa,KAAK,UAAU,IAAI,MAAM,MAAM,OAAO,CAAC;AACzE,cAAI;AACA;AACJ,cAAI,UAAU,IAAI,KAAKA,KAAI,OAAO,GAAG,MAAM,SAAS,GAAG,QAAQ,OAAO,MAAM,SAAS;AACrF,cAAI,SAAS,MAAM,UAAU,MAAM,IAAI,EAAE,MAAM,OAAO;AAClD,kBAAM,IAAI,IAAI;AAAA;AAEd,kBAAM,KAAK,OAAO;AAAA,QAC1B;AAAA,MACJ,WACS,KAAK,KAAK;AACf;AAAA,MACJ;AAAA,IACJ,GAAG,KAAK,MAAM,QAAQ,CAAC;AACvB,WAAO,IAAI,QAAO,aAAa,KAAK,MAAM,QAAQ,CAAC,GAAG,MAAM;AAAA,EAChE;AACJ;AACA,OAAO,QAAQ,IAAI,OAAO,aAAa,OAAO,CAAC;AAC/C,SAAS,aAAa,OAAO,GAAG;AAC5B,MAAI;AACJ,QAAM,QAAQ,CAAC,MAAM,MAAM;AACvB,QAAI,KAAK,aAAc,OAAO,GAAI;AAC9B,iBAAW;AACX,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACD,SAAO,MAAM,MAAM,QAAQ;AAC/B;AACA,IAAM,OAAN,MAAM,MAAK;AAAA,EACP,YAEAA,MAEA,MAIA,WAGA,cAAc;AACV,SAAK,MAAMA;AACX,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,eAAe;AAAA,EACxB;AAAA,EACA,MAAM,OAAO;AACT,QAAI,KAAK,QAAQ,MAAM,QAAQ,CAAC,MAAM,WAAW;AAC7C,UAAI,OAAO,MAAM,KAAK,MAAM,KAAK,IAAI;AACrC,UAAI;AACA,eAAO,IAAI,MAAK,KAAK,OAAO,EAAE,OAAO,GAAG,MAAM,KAAK,SAAS;AAAA,IACpE;AAAA,EACJ;AACJ;AAIA,IAAM,eAAN,MAAmB;AAAA,EACf,YAAY,MAAM,QAAQ,YAAY,UAAU,iBAAiB;AAC7D,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,kBAAkB;AAAA,EAC3B;AACJ;AACA,IAAM,iBAAiB;AAEvB,SAAS,iBAAiBE,UAAS,OAAO,IAAI,SAAS;AACnD,MAAI,YAAY,GAAG,QAAQ,UAAU,GAAG;AACxC,MAAI;AACA,WAAO,UAAU;AACrB,MAAI,GAAG,QAAQ,eAAe;AAC1B,IAAAA,WAAU,IAAI,aAAaA,SAAQ,MAAMA,SAAQ,QAAQ,MAAM,GAAG,EAAE;AACxE,MAAI,WAAW,GAAG,QAAQ,qBAAqB;AAC/C,MAAI,GAAG,MAAM,UAAU,GAAG;AACtB,WAAOA;AAAA,EACX,WACS,YAAY,SAAS,QAAQ,UAAU,GAAG;AAC/C,QAAI,SAAS,QAAQ,UAAU,EAAE;AAC7B,aAAO,IAAI,aAAaA,SAAQ,KAAK,aAAa,IAAI,QAAW,SAAS,kBAAkB,KAAK,CAAC,GAAGA,SAAQ,QAAQ,UAAU,GAAG,QAAQ,IAAI,GAAGA,SAAQ,UAAUA,SAAQ,eAAe;AAAA;AAE1L,aAAO,IAAI,aAAaA,SAAQ,MAAMA,SAAQ,OAAO,aAAa,IAAI,QAAW,SAAS,kBAAkB,KAAK,CAAC,GAAG,MAAMA,SAAQ,UAAUA,SAAQ,eAAe;AAAA,EAC5K,WACS,GAAG,QAAQ,cAAc,MAAM,SAAS,EAAE,YAAY,SAAS,QAAQ,cAAc,MAAM,QAAQ;AAExG,QAAI,cAAc,GAAG,QAAQ,aAAa;AAC1C,QAAI,WAAWA,SAAQ,YAAY,KAC9B,CAAC,YAAYA,SAAQ,mBAAmB,gBACpCA,SAAQ,YAAY,GAAG,QAAQ,KAAK,QAAQ,iBAAiB,CAAC,aAAa,IAAIA,SAAQ,UAAU;AAC1G,QAAI,aAAa,WAAW,UAAUA,SAAQ,YAAY,GAAG,OAAO,IAAI,UAAU,GAAG,QAAQ,IAAI;AACjG,WAAO,IAAI,aAAaA,SAAQ,KAAK,aAAa,IAAI,WAAW,MAAM,UAAU,YAAY,IAAI,QAAW,SAAS,kBAAkB,KAAK,CAAC,GAAG,OAAO,OAAO,YAAY,GAAG,MAAM,eAAe,OAAOA,SAAQ,kBAAkB,WAAW;AAAA,EAClP,WACS,UAAU,GAAG,QAAQ,SAAS,GAAG;AAGtC,WAAO,IAAI,aAAaA,SAAQ,KAAK,QAAQ,IAAI,OAAO,GAAGA,SAAQ,OAAO,QAAQ,IAAI,OAAO,GAAG,UAAUA,SAAQ,YAAY,GAAG,OAAO,GAAGA,SAAQ,UAAUA,SAAQ,eAAe;AAAA,EACxL,OACK;AACD,WAAO,IAAI,aAAaA,SAAQ,KAAK,QAAQ,GAAG,QAAQ,IAAI,GAAGA,SAAQ,OAAO,QAAQ,GAAG,QAAQ,IAAI,GAAG,UAAUA,SAAQ,YAAY,GAAG,OAAO,GAAGA,SAAQ,UAAUA,SAAQ,eAAe;AAAA,EAChM;AACJ;AACA,SAAS,aAAa,WAAW,YAAY;AACzC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,CAAC,UAAU;AACX,WAAO;AACX,MAAI,WAAW;AACf,YAAU,QAAQ,KAAK,CAAC,EAAE,QAAQ,CAAC,OAAO,QAAQ;AAC9C,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACxC,UAAI,SAAS,WAAW,IAAI,CAAC,KAAK,OAAO,WAAW,CAAC;AACjD,mBAAW;AAAA,EACvB,CAAC;AACD,SAAO;AACX;AACA,SAAS,UAAU,MAAM;AACrB,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,KAAK,SAAS,GAAG,KAAK,KAAK,OAAO,UAAU,GAAG;AACxD,SAAK,CAAC,EAAE,QAAQ,CAAC,OAAO,KAAKD,OAAM,OAAO,OAAO,KAAKA,OAAM,EAAE,CAAC;AACnE,SAAO;AACX;AACA,SAAS,UAAU,QAAQ,SAAS;AAChC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACvC,QAAIA,QAAO,QAAQ,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,QAAQ,IAAI,OAAO,IAAI,CAAC,GAAG,EAAE;AACxE,QAAIA,SAAQ;AACR,aAAO,KAAKA,OAAM,EAAE;AAAA,EAC5B;AACA,SAAO;AACX;AAGA,SAAS,gBAAgBC,UAAS,OAAOC,OAAM;AAC3C,MAAI,gBAAgB,kBAAkB,KAAK;AAC3C,MAAI,cAAc,WAAW,IAAI,KAAK,EAAE,KAAK;AAC7C,MAAI,OAAOA,QAAOD,SAAQ,SAASA,SAAQ,MAAM,SAAS,OAAO,aAAa;AAC9E,MAAI,CAAC;AACD,WAAO;AACX,MAAI,YAAY,IAAI,UAAU,QAAQ,IAAI,UAAU,GAAG;AACvD,MAAI,SAASC,QAAOD,SAAQ,OAAOA,SAAQ,QAAQ,aAAa,IAAI,WAAW,MAAM,UAAU,YAAY,GAAG,aAAa,aAAa;AACxI,MAAI,UAAU,IAAI,aAAaC,QAAO,QAAQ,IAAI,WAAWA,QAAO,IAAI,YAAY,OAAO,MAAM,GAAG,EAAE;AACtG,SAAO,IAAI,UAAU,aAAa,SAAS,EAAE,QAAQ,YAAY,EAAE,MAAAA,OAAM,cAAc,QAAQ,CAAC;AACpG;AACA,IAAI,sBAAsB;AAA1B,IAAiC,6BAA6B;AAK9D,SAAS,kBAAkB,OAAO;AAC9B,MAAI,UAAU,MAAM;AACpB,MAAI,8BAA8B,SAAS;AACvC,0BAAsB;AACtB,iCAA6B;AAC7B,aAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAChC,UAAI,QAAQ,CAAC,EAAE,KAAK,sBAAsB;AACtC,8BAAsB;AACtB;AAAA,MACJ;AAAA,EACR;AACA,SAAO;AACX;AASA,IAAM,aAAa,IAAI,UAAU,SAAS;AAC1C,IAAM,kBAAkB,IAAI,UAAU,cAAc;AAUpD,SAAS,QAAQ,SAAS,CAAC,GAAG;AAC1B,WAAS;AAAA,IAAE,OAAO,OAAO,SAAS;AAAA,IAC9B,eAAe,OAAO,iBAAiB;AAAA,EAAI;AAC/C,SAAO,IAAI,OAAO;AAAA,IACd,KAAK;AAAA,IACL,OAAO;AAAA,MACH,OAAO;AACH,eAAO,IAAI,aAAa,OAAO,OAAO,OAAO,OAAO,MAAM,GAAG,EAAE;AAAA,MACnE;AAAA,MACA,MAAM,IAAI,MAAM,OAAO;AACnB,eAAO,iBAAiB,MAAM,OAAO,IAAI,MAAM;AAAA,MACnD;AAAA,IACJ;AAAA,IACA;AAAA,IACA,OAAO;AAAA,MACH,iBAAiB;AAAA,QACb,YAAY,MAAM,GAAG;AACjB,cAAI,YAAY,EAAE;AAClB,cAAI,UAAU,aAAa,gBAAgB,OAAO,aAAa,gBAAgB,OAAO;AACtF,cAAI,CAAC;AACD,mBAAO;AACX,YAAE,eAAe;AACjB,iBAAO,QAAQ,KAAK,OAAO,KAAK,QAAQ;AAAA,QAC5C;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,aAAaC,OAAM,QAAQ;AAChC,SAAO,CAAC,OAAO,aAAa;AACxB,QAAI,OAAO,WAAW,SAAS,KAAK;AACpC,QAAI,CAAC,SAASA,QAAO,KAAK,SAAS,KAAK,MAAM,cAAc;AACxD,aAAO;AACX,QAAI,UAAU;AACV,UAAI,KAAK,gBAAgB,MAAM,OAAOA,KAAI;AAC1C,UAAI;AACA,iBAAS,SAAS,GAAG,eAAe,IAAI,EAAE;AAAA,IAClD;AACA,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,OAAO,aAAa,OAAO,IAAI;AAIrC,IAAM,OAAO,aAAa,MAAM,IAAI;AAKpC,IAAM,eAAe,aAAa,OAAO,KAAK;AAK9C,IAAM,eAAe,aAAa,MAAM,KAAK;;;AChYhC,IAAA,UAAU,UAAU,OAAuB;EACtD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,OAAO;MACP,eAAe;;;EAInB,cAAW;AACT,WAAO;MACL,MAAM,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAClC,eAAO,KAAK,OAAO,QAAQ;;MAE7B,MAAM,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAClC,eAAO,KAAK,OAAO,QAAQ;;;;EAKjC,wBAAqB;AACnB,WAAO;MACL,QAAQ,KAAK,OAAO;;;EAIxB,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,KAAI;MACxC,eAAe,MAAM,KAAK,OAAO,SAAS,KAAI;MAC9C,SAAS,MAAM,KAAK,OAAO,SAAS,KAAI;;MAGxC,SAAS,MAAM,KAAK,OAAO,SAAS,KAAI;MACxC,eAAe,MAAM,KAAK,OAAO,SAAS,KAAI;;;AAGnD,CAAA;;;ACrDY,IAAA,iBAAiB,KAAK,OAA8B;EAC/D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,OAAO;EAEP,YAAS;AACP,WAAO,CAAC,EAAE,KAAK,KAAI,CAAE;;EAGvB,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;;EAG5E,cAAW;AACT,WAAO;MACL,mBACE,MAAM,CAAC,EAAE,OAAO,MAAK,MAAM;AACzB,cAAM,EAAE,UAAS,IAAK;AACtB,cAAM,EAAE,OAAO,aAAa,KAAK,UAAS,IAAK;AAE/C,cAAM,eAAe,MAAK;AAE1B,YAAI,YAAY,iBAAiB,GAAG;AAClC,uBAAa,gBACX;YACE,MAAM,KAAK,IAAI,YAAY,MAAM,GAAG,CAAC;YACrC,IAAI,UAAU;aAEhB;YACE,MAAM,KAAK;UACZ,CAAA;mBAEM,gBAAgB,SAAS,GAAG;AACrC,uBAAa,gBAAgB,UAAU,KAAK;YAC1C,MAAM,KAAK;UACZ,CAAA;eACI;AACL,uBAAa,cAAc,EAAE,MAAM,KAAK,KAAI,CAAE;;AAGhD,eACE,aAEG,QAAQ,CAAC,EAAE,IAAI,SAAQ,MAAM;;AAC5B,cAAI,UAAU;AACZ,kBAAM,EAAE,IAAG,IAAK,GAAG;AACnB,kBAAM,WAAW,IAAI,IAAG;AAExB,gBAAI,IAAI,WAAW;AACjB,kBAAI,IAAI,UAAU,aAAa;AAC7B,mBAAG,aAAa,cAAc,OAAO,GAAG,KAAK,IAAI,MAAM,CAAC,CAAC;yBAChD,IAAI,UAAU,SAAS;AAChC,mBAAG,aAAa,cAAc,OAAO,GAAG,KAAK,IAAI,GAAG,CAAC;qBAChD;AACL,mBAAG,aAAa,cAAc,OAAO,GAAG,KAAK,IAAI,GAAG,CAAC;;mBAElD;AAEL,oBAAM,QAAO,KAAA,IAAI,OAAO,KAAK,aAAa,iBAAa,QAAA,OAAA,SAAA,SAAA,GAAA,OAAM;AAE7D,kBAAI,MAAM;AACR,mBAAG,OAAO,UAAU,IAAI;AACxB,mBAAG,aAAa,cAAc,OAAO,GAAG,KAAK,WAAW,CAAC,CAAC;;;AAI9D,eAAG,eAAc;;AAGnB,iBAAO;QACT,CAAC,EACA,IAAG;;;;EAMhB,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;;;AChFM,IAAMC,kBAAiB;AAKvB,IAAMC,kBAAiB;AAKvB,IAAMC,wBAAuB;AAK7B,IAAMC,wBAAuB;AAMvB,IAAA,SAAS,KAAK,OAAsB;EAC/C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;MACD;QACE,KAAK;QACL,UAAU,UAAS,KAAqB,MAAM,cAAc,YAAY;MACzE;MACD;QACE,OAAO;QACP,WAAW,UAAQ,KAAK,KAAK,SAAS,KAAK;MAC5C;MACD;QACE,OAAO;MACR;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG/E,cAAW;AACT,WAAO;MACL,WAAW,MAAM,CAAC,EAAE,SAAQ,MAAM;AAChC,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,cAAc,MAAM,CAAC,EAAE,SAAQ,MAAM;AACnC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,aAAa,MAAM,CAAC,EAAE,SAAQ,MAAM;AAClC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,aAAY;MAChD,SAAS,MAAM,KAAK,OAAO,SAAS,aAAY;;;EAIpD,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAMH;QACN,MAAM,KAAK;OACZ;MACD,cAAc;QACZ,MAAME;QACN,MAAM,KAAK;OACZ;;;EAIL,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAMD;QACN,MAAM,KAAK;OACZ;MACD,cAAc;QACZ,MAAME;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;;;AC/GY,IAAA,WAAW,KAAK,OAAwB;EACnD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;MAChB,oBAAoB;MACpB,qBAAqB;;;EAIzB,SAAS;EAET,UAAU;EAEV,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG/E,uBAAoB;AAClB,WAAO;MACL,OAAO,MAAM,KAAK,OAAO,SAAS,cAAc,KAAK,IAAI;MACzD,KAAK,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;MACtD,aAAa,MAAM,KAAK,OAAO,SAAS,aAAa,KAAK,IAAI;;;AAGnE,CAAA;;;AC7DD,IAAMC,gBAAe;AACrB,IAAMC,iBAAgB;AA+Cf,IAAMC,cAAa;AAQb,IAAA,cAAc,KAAK,OAA2B;EACzD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,cAAc;MACd,gBAAgB,CAAA;MAChB,WAAW;MACX,gBAAgB;;;EAIpB,OAAO;EAEP,UAAO;AACL,WAAO,GAAG,KAAK,QAAQ,YAAY;;EAGrC,gBAAa;AACX,WAAO;MACL,OAAO;QACL,SAAS;QACT,WAAW,aAAU;AACnB,iBAAO,QAAQ,aAAa,OAAO,IAC/B,SAAS,QAAQ,aAAa,OAAO,KAAK,IAAI,EAAE,IAChD;;MAEP;MACD,MAAM;QACJ,SAAS;QACT,WAAW,aAAW,QAAQ,aAAa,MAAM;MAClD;;;EAIL,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,UAAM,EAAE,OAAO,GAAG,uBAAsB,IAAK;AAE7C,WAAO,UAAU,IACb,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,sBAAsB,GAAG,CAAC,IAC9E,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG5E,cAAW;AACT,WAAO;MACL,mBAAmB,MAAM,CAAC,EAAE,UAAU,MAAK,MAAM;AAC/C,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,iBAAO,MAAK,EAAG,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,EAAE,iBAAiBF,eAAc,KAAK,OAAO,cAAcC,cAAa,CAAC,EAAE,IAAG;;AAEtK,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS;;;;EAK7F,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,kBAAiB;;;EAI/D,gBAAa;AACX,QAAI,YAAY,kBAAkB;MAChC,MAAMC;MACN,MAAM,KAAK;MACX,eAAe,YAAU,EAAE,OAAO,CAAC,MAAM,CAAC,EAAC;MAC3C,eAAe,CAAC,OAAO,SAAS,KAAK,aAAa,KAAK,MAAM,UAAU,CAAC,MAAM,CAAC;IAChF,CAAA;AAED,QAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,gBAAgB;AACzD,kBAAY,kBAAkB;QAC5B,MAAMA;QACN,MAAM,KAAK;QACX,WAAW,KAAK,QAAQ;QACxB,gBAAgB,KAAK,QAAQ;QAC7B,eAAe,YAAU,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,OAAO,cAAcD,cAAa,EAAC;QACxF,eAAe,CAAC,OAAO,SAAS,KAAK,aAAa,KAAK,MAAM,UAAU,CAAC,MAAM,CAAC;QAC/E,QAAQ,KAAK;MACd,CAAA;;AAEH,WAAO;MACL;;;AAGL,CAAA;;;AC1HY,IAAA,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,UAAU;EAEV,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,OAAO;EAEP,SAAS;EAET,YAAS;AACP,WAAO;MACL,EAAE,KAAK,IAAG;;;EAId,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG9E,cAAW;AACT,WAAO;MACL,cAAc,MAAM,CAAC,EAAE,SAAQ,MAAM;AACnC,eAAO,SAAS,QAAQ,KAAK,IAAI;;;;EAKvC,uBAAoB;AAClB,WAAO;MACL,aAAa,MAAM,KAAK,OAAO,SAAS,aAAY;;;AAGzD,CAAA;;;ACxBM,IAAME,cAAa;AAKnB,IAAMC,cAAa;AAMb,IAAA,SAAS,KAAK,OAAsB;EAC/C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;MACD;QACE,KAAK;MACN;MACD;QACE,KAAK;MACN;MACD;QACE,OAAO;QACP,WAAW;QACX,UAAU,WAAW,MAAiB,SAAS,cAAc,IAAI,CAAA,IAAK;MACvE;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,KAAK,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG9E,cAAW;AACT,WAAO;MACL,WAAW,MAAM,CAAC,EAAE,SAAQ,MAAM;AAChC,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,cAAc,MAAM,CAAC,EAAE,SAAQ,MAAM;AACnC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,aAAa,MAAM,CAAC,EAAE,SAAQ,MAAM;AAClC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,aAAY;;;EAI1D,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAMD;QACN,MAAM,KAAK;OACZ;;;EAIL,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAMC;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;;;ACnHY,IAAA,OAAO,KAAK,OAAO;EAC9B,MAAM;EACN,OAAO;AACR,CAAA;;;AC8HY,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,gBAAa;AACX,UAAM,aAAa,CAAA;AAEnB,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;;AAGnD,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;;AAG/D,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;;AAG/D,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;;AAGnD,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;;AAG7D,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;;AAG3D,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,iBAAW,KAAK,WAAW,UAAU,KAAK,QAAQ,UAAU,CAAC;;AAG/D,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;;AAG7D,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;;AAG7D,QAAI,KAAK,QAAQ,YAAY,OAAO;AAClC,iBAAW,KAAK,QAAQ,UAAU,KAAK,QAAQ,OAAO,CAAC;;AAGzD,QAAI,KAAK,QAAQ,YAAY,OAAO;AAClC,iBAAW,KAAK,QAAQ,UAAU,KAAK,QAAQ,OAAO,CAAC;;AAGzD,QAAI,KAAK,QAAQ,mBAAmB,OAAO;AACzC,iBAAW,KAAK,eAAe,UAAU,KAAK,QAAQ,cAAc,CAAC;;AAGvE,QAAI,KAAK,QAAQ,WAAW,OAAO;AACjC,iBAAW,KAAK,OAAO,UAAU,KAAK,QAAQ,MAAM,CAAC;;AAGvD,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,iBAAW,KAAK,SAAS,UAAU,KAAK,QAAQ,QAAQ,CAAC;;AAG3D,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,iBAAW,KAAK,YAAY,UAAU,KAAK,QAAQ,WAAW,CAAC;;AAGjE,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,iBAAW,KAAK,UAAU,UAAU,KAAK,QAAQ,SAAS,CAAC;;AAG7D,QAAI,KAAK,QAAQ,WAAW,OAAO;AACjC,iBAAW,KAAK,OAAO,UAAU,KAAK,QAAQ,MAAM,CAAC;;AAGvD,QAAI,KAAK,QAAQ,SAAS,OAAO;AAC/B,iBAAW,KAAK,KAAK,UAAU,KAAK,QAAQ,IAAI,CAAC;;AAGnD,WAAO;;AAEV,CAAA;", "names": ["inputRegex", "inputRegex", "rect", "RopeSequence", "from", "Leaf", "Append", "appendInner", "map", "from", "history", "redo", "redo", "starInputRegex", "starPasteRegex", "underscoreInputRegex", "underscorePasteRegex", "ListItemName", "TextStyleName", "inputRegex", "inputRegex", "pasteRegex"]}